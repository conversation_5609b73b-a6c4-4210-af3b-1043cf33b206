#应用固定环境变量 start -------------------------------------------------------------------------------------------------
server:
  port: 8080
  servlet:
    context-path: /ddd-isale-manage-js-server

spring.profiles.include: stand-ind
spring.main.allow-bean-definition-overriding: true
spring.application.name: ddd-isale-manage-js-server
spring.servlet.multipart.max-file-size: 100GB
spring.servlet.multipart.max-request-size: 100GB

logging:
  level:
    com.inspur: info
    com.lambo: info
    com.lamboegg: info
    com.tobacco.app: debug
    root: info
  charset:
    console: utf-8
  pattern:
    console: '[%d{yyyy-MM-dd HH:mm:ss:SSS}] [%-5p] [method:%logger] [%t]%m%n '
    file: '[%d{yyyy-MM-dd HH:mm:ss:SSS}] [%X{EagleEye-TraceID}] [%-5p] [method:%logger] [%t]%m%n '
  file:
    path: '/opt/lambologs/ddd-isale-manage-js-server'

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    #设置当查询结果值为null时，同样映射该查询字段给map。
    call-setters-on-nulls: true
    mapper-locations: classpath*:/*Mapper.xml

#应用固定环境变量 end ---------------------------------------------------------------------------------------------------




#应用专属环境变量 start -------------------------------------------------------------------------------------------------
szyx:
  version:
    #使用中台
    usemp: 1
ESB:
  user:
    id: ${rest.center.ESB.user.id:yx}
    key: ${rest.center.ESB.user.key:yx#clmis@202509}
#应用专属环境变量 end -------------------------------------------------------------------------------------------------


#引用标准环境变量 start -------------------------------------------------------------------------------------------------
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: ${db.app.isale.driver-class-name:com.mysql.cj.jdbc.Driver}
          url: ${db.app.isale.url:******************************************************************************************************************************************************************************}
          username: ${db.app.isale.username:root}
          password: ${db.app.isale.password:gyyingxiaodbPassw0rd}
        mkt:
          driver-class-name: ${db.app.mkt.driver-class-name:com.mysql.cj.jdbc.Driver}
          url: ${db.app.mkt.url:*****************************************************************************************************************************************************************************}
          username: ${db.app.mkt.username:root}
          password: ${db.app.mkt.password:gyyingxiaodbPassw0rd}
        union:
          driver-class-name: ${db.app.induniondb.driver-class-name:com.mysql.cj.jdbc.Driver}
          url: ${db.app.induniondb.url:********************************************************************************************************************************************************************************}
          username: ${db.app.induniondb.username:root}
          password: ${db.app.induniondb.password:gyyingxiaodbPassw0rd}
        dws:
          driverClassName: ${db.app.dws.driver-class-name:org.postgresql.Driver}
          url: ${db.app.dws.url:*********************************************}
          username: ${db.app.dws.username:ind}
          password: ${db.app.dws.password:123456a?!A}
#        ck:
#          driverClassName: ${db.app.ck.driver-class-name:com.clickhouse.jdbc.ClickHouseDriver}
#          url: ${db.app.ck.url:***************************************************************************************}
#          username: ${db.app.ck.username:sale_admin}
#          password: ${db.app.ck.password:Hbsale@1234}
#          druid:
#            filters: stat
        ck:
          driverClassName: ${db.app.ck.driver-class-name:org.postgresql.Driver}
          url: ${db.app.ck.url:*********************************************}
          username: ${db.app.ck.username:ind}
          password: ${db.app.ck.password:123456a?!A}
          druid:
            filters: stat

  redis:
    host: ${redis.sc.scredis.host:************}
    port: ${redis.sc.scredis.port:6379}
    database: 0
    password: ${redis.sc.scredis.password:GY@8080YingXiao}
center:
  consumer:
    app:
      id: ${rest.center.consumer.app.id:2c93740e8c391762018c391762aa0000}
      key: ${rest.center.consumer.app.key:95a1151c1d64678b2534ac305e446e69}
app:
  ibp:
    upms:
      provider:
        server: ${rest.app.ibp.upms.provider.server:http://215.dev.yingxiao.ind.lambo.top/user-manage-server}
  uc:
    provider:
      server: ${rest.app.uc.provider.server:http://706.dev.x1.lambo.top/gateway/uc-server}
  ac:
    provider:
      server: ${rest.app.ac.provider.server:http://706.dev.x1.lambo.top/gateway/ac-server}
  mc:
    provider:
      server: ${rest.app.mc.provider.server:http://706.dev.x1.lambo.top/gateway/mc-server}
  doc:
    provider:
      server: ${rest.app.doc.provider.server:http://706.dev.x1.lambo.top/gateway/doc-server}
  bpm:
    provider:
      server: ${rest.app.bpm.provider.server:http://706.dev.x1.lambo.top/gateway/bpm-server}
  ehc:
    provider:
      server: ${rest.app.ehc.provider.server:http://706.dev.x1.lambo.top/gateway/ehc-server}
  dida:
    provider:
      server: ${rest.app.dida.provider.server:http://706.dev.x1.lambo.top/gateway/dida-runtime-server}
  imarket: # 信息采集服务
    provider:
      server: ${rest.app.imarket.provider.server:http://ddd-imarket-manage-server.ind-app:8080/ddd-imarket-manage-server}
  sc:
    user:
      provider:
        server: ${rest.app.sc.user.provider.server:http://sc-user-server.ind-sc:8080/sc-user-server}
    dc:
      provider:
        server: ${rest.app.sc.dc.provider.server:http://sc-dc-server.ind-sc:8080/sc-dc-server}
    mc:
      provider:
        server: ${rest.app.sc.mc.provider.server:http://sc-mc-server.ind-sc:8080/sc-mc-server}
    icommodity:
      provider:
        server: ${rest.app.sc.icommodity.provider.server:http://ddd-sc-icommodity-server.ind-sc:8080/ddd-sc-icommodity-server}
    icust:
      provider:
        server: ${rest.app.sc.icust.provider.server:http://ddd-sc-icust-server.ind-sc:8080/ddd-sc-icust-server}
    dsc:
      provider:
        server: ${rest.app.sc.dsc.provider.server:http://sc-dsc-server.ind-sc:8080/sc-dsc-server}
    indcommon:
      provider:
        server: ${rest.app.sc.indcommon.provider.server:http://ind-common-server.ind-app:8080/ind-common-server}

  resttemplate:
    read-timeout: 300000
management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: true
security:
  authorization:
    excludeTargets: '/rest/api/**:anon'
    use:
      default: 0
  usual:
    pwd: 123456,password

inter-context:
  host: http://ddd-inter-server
  port: 8080
  path:
    pull: /ddd-inter-server/api/threepartysys/pull
    push: /ddd-inter-server/api/threepartysys/push
feign:
  httpclient:
    enabled: true
    connection-timeout: ${rest.center.feign.httpclient.connection-timeout:300000}
    max-connections: ${rest.center.feign.httpclient.max-connections:500}
    max-connections-per-route: ${rest.center.feign.httpclient.max-connections-per-route:200}
  client:
    url:
      isale-center: ${rest.center.feign.client.url.isale-center:https://215.dev.yingxiao.ind.lambo.top/ddd-isale-center-server}
      inter-server: ${rest.center.feign.client.url.inter-center:http://localhost:8080/ddd-inter-server}
simulate:
  nationSystem:
    enabled: 0
#引用标准环境变量 end ---------------------------------------------------------------------------------------------------