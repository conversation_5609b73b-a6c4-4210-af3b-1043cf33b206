/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.yearplan;


import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmBrandPlanItemModel;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmBrandPlanItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 年度品牌规划明细转换器
 * <AUTHOR> loongxi
 * @date ： 2025/08/12
 */
@Mapper
public interface Mc04IslmBrandPlanItemConverter extends StructureBaseConverter<Mc04IslmBrandPlanItemDO, Mc04IslmBrandPlanItemModel> {

    Mc04IslmBrandPlanItemConverter INSTANCE = Mappers.getMapper(Mc04IslmBrandPlanItemConverter.class);

}
