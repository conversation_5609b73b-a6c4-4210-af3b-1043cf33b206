package com.tobacco.app.isale.infrastructure.repository.basic;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.inspur.ind.tree.entity.ComTree;
import com.tobacco.app.isale.domain.model.basic.ManageIslmcForwardWhseCom;
import com.tobacco.app.isale.domain.repository.basic.ManageIslmcForwardWhseComRepository;
import com.tobacco.app.isale.infrastructure.converter.basic.IslmcForwardWhseComToManageIslmcForwardWhseComDOConvert;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcForwardWhseComDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcForwardWhseComService;
import com.tobacco.app.isale.infrastructure.tunnel.database.basic.IslmcForwardWhseComMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/7/28
 * @description
 */
@Component("ISaleManageForwardWhseComRepositoryImpl")
@Slf4j
public class ManageIslmcForwardWhseComRepositoryImpl implements ManageIslmcForwardWhseComRepository {

    @Autowired
    private Mc04IslmcForwardWhseComService mc04IslmcForwardWhseComService;

    @Autowired
    private IslmcForwardWhseComMapper islmcForwardWhseComMapper;

    @Override
    public List<ComTree> queryIslmcContDelivWhse(String icomCode) {
        return islmcForwardWhseComMapper.queryIslmcContDelivWhse(icomCode);
    }

    @Override
    public Boolean deleteIslmcForwardWhseCom(List<String> cgtInStorehouseCodeList) {
        return mc04IslmcForwardWhseComService.remove(new LambdaQueryWrapper<Mc04IslmcForwardWhseComDO>()
                .in(Mc04IslmcForwardWhseComDO::getMd02CgtInStorehouseCode, cgtInStorehouseCodeList));
    }

    @Override
    public Boolean insertIslmcForwardWhseCom(List<ManageIslmcForwardWhseCom> param) {
        return mc04IslmcForwardWhseComService.saveBatch(IslmcForwardWhseComToManageIslmcForwardWhseComDOConvert
                .INSTANCE.converterModelsToDos(param));
    }

    @Override
    public List<ManageIslmcForwardWhseCom> queryIslmcForwardWhseCom(ManageIslmcForwardWhseCom queryModel) {
        List<Mc04IslmcForwardWhseComDO> list = mc04IslmcForwardWhseComService.list(new LambdaQueryWrapper<Mc04IslmcForwardWhseComDO>()
                .eq(StrUtil.isNotBlank(queryModel.getBaComOrgCode()), Mc04IslmcForwardWhseComDO::getBaComOrgCode, queryModel.getBaComOrgCode())
                .eq(StrUtil.isNotBlank(queryModel.getMd02CgtInStorehouseCode()), Mc04IslmcForwardWhseComDO::getMd02CgtInStorehouseCode, queryModel.getMd02CgtInStorehouseCode())
                .eq(StrUtil.isNotBlank(queryModel.getMd02CgtOutStorehouseCode()), Mc04IslmcForwardWhseComDO::getMd02CgtOutStorehouseCode, queryModel.getMd02CgtOutStorehouseCode())
                .eq(StrUtil.isNotBlank(queryModel.getIcomCode()), Mc04IslmcForwardWhseComDO::getIcomCode, queryModel.getIcomCode()));
        return IslmcForwardWhseComToManageIslmcForwardWhseComDOConvert.INSTANCE.converterDosToModels(list);
    }

    @Override
    public Long queryIslmcForwardComWhseCount(ManageIslmcForwardWhseCom queryModel) {
        return mc04IslmcForwardWhseComService.count(new LambdaQueryWrapper<Mc04IslmcForwardWhseComDO>()
                .eq(Mc04IslmcForwardWhseComDO::getBaComOrgCode, queryModel.getBaComOrgCode()));
    }

    @Override
    public Boolean updateIslmcComForwardWhse(List<ManageIslmcForwardWhseCom> updateModel) {
        return mc04IslmcForwardWhseComService.update(new LambdaUpdateWrapper<Mc04IslmcForwardWhseComDO>()
                .eq(Mc04IslmcForwardWhseComDO::getBaComOrgCode, updateModel.get(0).getBaComOrgCode())
                .set(Mc04IslmcForwardWhseComDO::getMd02CgtOutStorehouseCode, updateModel.get(0).getMd02CgtOutStorehouseCode())
                .set(Mc04IslmcForwardWhseComDO::getMd02CgtInStorehouseCode, updateModel.get(0).getMd02CgtInStorehouseCode()));
    }

    @Override
    public Boolean deleteIslmcComForwardWhse(ManageIslmcForwardWhseCom model) {
        return mc04IslmcForwardWhseComService.remove(new LambdaUpdateWrapper<Mc04IslmcForwardWhseComDO>()
                .eq(Mc04IslmcForwardWhseComDO::getBaComOrgCode, model.getBaComOrgCode())
                .eq(StrUtil.isNotBlank(model.getMd02CgtOutStorehouseCode()), Mc04IslmcForwardWhseComDO::getMd02CgtOutStorehouseCode, model.getMd02CgtOutStorehouseCode())
                .eq(StrUtil.isNotBlank(model.getMd02CgtInStorehouseCode()), Mc04IslmcForwardWhseComDO::getMd02CgtInStorehouseCode, model.getMd02CgtInStorehouseCode()));
    }
}
