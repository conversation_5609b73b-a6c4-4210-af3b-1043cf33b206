/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter;

import java.util.List;

/**
 * @Author: jinfuli
 * @Date: 2025/6/10
 * @Description:
 */
public interface StructureBaseConverter<DO,MODEL> {
    /**
     * 领域对象转为数据对象
     * @param model 领域对象
     * @return 数据对象
     */
    DO converterModelToDo(MODEL model);
    /**
     * 数据对象转为领域对象
     * @param DO 数据对象
     * @return 领域对象
     */
    MODEL converterDoToModel(DO DO);

    /**
     * 批量领域对象转为数据对象
     * @param modelList 领域对象集合
     * @return 数据对象集合
     */
    List<DO> converterModelsToDos(List<MODEL> modelList);

    /**
     * 批量数据对象转为领域对象
     * @param DOList 数据对象集合
     * @return 领域对象集合
     */
    List<MODEL> converterDosToModels(List<DO> DOList);
}
