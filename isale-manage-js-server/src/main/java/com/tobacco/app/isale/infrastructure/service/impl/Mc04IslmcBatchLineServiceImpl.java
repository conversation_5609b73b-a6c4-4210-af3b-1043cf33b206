/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcBatchLineDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcBatchLineMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcBatchLineService;
import org.springframework.stereotype.Service;

/**
 * @Description: 服务实现类
 *
 * @Author: renyong<PERSON>
 * @Since: 2025-04-24
 * @Email: <EMAIL>
 * @Create: 2025-04-24
 */
@Service
public class Mc04IslmcBatchLineServiceImpl extends ServiceImpl<Mc04IslmcBatchLineMapper, Mc04IslmcBatchLineDO> implements Mc04IslmcBatchLineService {

}