/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.monthplan;


import com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlanAdj;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanAdjDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jinfuli
 * @Date: 2025/6/13
 * @Description:
 */
@Mapper
public interface MonthPlanAdjConverter extends StructureBaseConverter<Mc04IslmMonthSalePlanAdjDO, Mc04IslmMonthSalePlanAdj> {

    MonthPlanAdjConverter INSTANCE = Mappers.getMapper(MonthPlanAdjConverter.class);


}
