/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.order;

import com.tobacco.app.isale.domain.model.basic.ContReachWhse;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContReachWhseDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jinfuli
 * @Date: 2025/7/31
 * @Description:
 */
@Mapper
public interface ContReachWhseConverter extends StructureBaseConverter<Mc04IslmcContReachWhseDO, ContReachWhse> {
    ContReachWhseConverter INSTANCE = Mappers.getMapper(ContReachWhseConverter.class);
}
