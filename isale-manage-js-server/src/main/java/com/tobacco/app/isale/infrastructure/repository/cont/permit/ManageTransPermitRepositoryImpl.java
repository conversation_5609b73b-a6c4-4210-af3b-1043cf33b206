package com.tobacco.app.isale.infrastructure.repository.cont.permit;


import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import com.inspur.ind.base.CustomException;
import com.tobacco.app.isale.domain.model.cont.permit.ManagePermitTransPermitListParam;
import com.tobacco.app.isale.domain.model.cont.permit.ManageTransPermit;
import com.tobacco.app.isale.domain.model.cont.permit.TransPermitToThird;
import com.tobacco.app.isale.domain.repository.cont.permit.ManageTransPermitRepository;
import com.tobacco.app.isale.infrastructure.converter.cont.permit.Mc04IslmcNcStockMoveNavicertDOToManageTransPermitConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcNcStockMoveNavicertDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcNcStockMoveNavicertService;
import com.tobacco.app.isale.infrastructure.tunnel.database.cont.permit.ManageTransPermitCommonMapper;
import com.tobacco.app.isale.tools.utils.CustUtil;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: wangluhao01
 * @Since: 2025-09-05
 */
@Component("ISaleManageManageTransPermitDORepository")
public class ManageTransPermitRepositoryImpl implements ManageTransPermitRepository {

    private Mc04IslmcNcStockMoveNavicertService mc04IslmcNcStockMoveNavicertService;

    @Autowired
    public void setMc04IslmcNcStockMoveNavicertService(Mc04IslmcNcStockMoveNavicertService mc04IslmcNcStockMoveNavicertService) {
        this.mc04IslmcNcStockMoveNavicertService = mc04IslmcNcStockMoveNavicertService;
    }

    @Resource
    private ManageTransPermitCommonMapper manageTransPermitCommonMapper;


    /**
     * 查询准运证列表
     *
     * @param param 查询参数
     * @return List<ManageTransPermit>
     */
    @Override
    public List<ManageTransPermit> list(ManagePermitTransPermitListParam param) {
        List<ManageTransPermit> permitList = manageTransPermitCommonMapper.listPermit(param);
        //补充一下comName
        List<String> baComOrgCodeList = permitList.stream()
                .map(ManageTransPermit::getBaComOrgCode).collect(Collectors.toList());
        Map<String, BusiComDto> busiComDtoMap = CustUtil.getBusiComDtoMap(baComOrgCodeList);
        permitList.forEach(permit -> {
            Assert.isTrue(busiComDtoMap.containsKey(permit.getBaComOrgCode()),
                    () -> new CustomException("公司编码" + permit.getBaComOrgCode() + "不存在"));
            BusiComDto busiComDto = busiComDtoMap.get(permit.getBaComOrgCode());
            permit.setBaComOrgName(busiComDto.getBaComOrgName());
            permit.setBaComOrgShortName(busiComDto.getMc04ComOrgShortName());
        });
        return permitList;
    }

    /**
     * 删除准运证列表
     *
     * @param contOrderCodeList 删除的订单编号列表
     */
    @Override
    public void removeByContOrderCodeList(List<String> contOrderCodeList) {
        mc04IslmcNcStockMoveNavicertService.lambdaUpdate()
                .in(Mc04IslmcNcStockMoveNavicertDO::getMc04ContOrderCode, contOrderCodeList)
                .remove();
    }

    /**
     * 批量保存准运证列表
     *
     * @param manageTransPermits 准运证列表
     * @return Boolean
     */
    @Override
    public Boolean batchSave(List<ManageTransPermit> manageTransPermits) {
        List<Mc04IslmcNcStockMoveNavicertDO> mc04IslmcNcStockMoveNavicertDOList = manageTransPermits.stream()
                .map(permit -> {
                    Mc04IslmcNcStockMoveNavicertDO mc04IslmcNcStockMoveNavicertDO =
                            Mc04IslmcNcStockMoveNavicertDOToManageTransPermitConverter.
                                    INSTANCE.converterModelToDo(permit);
                    mc04IslmcNcStockMoveNavicertDO.setMc04NcStockMoveNavicertId(IdUtil.fastSimpleUUID());
                    return mc04IslmcNcStockMoveNavicertDO;
                }).collect(Collectors.toList());
        return mc04IslmcNcStockMoveNavicertService.saveBatch(mc04IslmcNcStockMoveNavicertDOList);
    }

    /**
     * 获取推送第三方的准运证列表
     *
     * @param contOrderCodeList 订单编号列表
     * @return List<TransPermitToThird>
     */
    @Override
    public List<TransPermitToThird> getThirdUploadTransPermitList(List<String> contOrderCodeList) {
        return manageTransPermitCommonMapper.getThirdUploadTransPermitList(contOrderCodeList);
    }

    /**
     * 更新状态
     *
     * @param contOrderCodeList 订单编号列表
     * @param currentStatus     当前状态
     * @param nextStatus        下一个状态
     */
    @Override
    public void updateStatus(List<String> contOrderCodeList, String currentStatus, String nextStatus) {
        mc04IslmcNcStockMoveNavicertService.lambdaUpdate()
                .in(Mc04IslmcNcStockMoveNavicertDO::getMc04ContOrderCode, contOrderCodeList)
                .eq(Mc04IslmcNcStockMoveNavicertDO::getMc04NcStockMoveNavicertStatus, currentStatus)
                .set(Mc04IslmcNcStockMoveNavicertDO::getMc04NcStockMoveNavicertStatus, nextStatus)
                .update();
    }
}