/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcXyDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcXyMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcXyService;
import org.springframework.stereotype.Service;

/**
 * @Description: 服务实现类
 *
 * @Author: renyong<PERSON>
 * @Since: 2025-05-06
 * @Email: <EMAIL>
 * @Create: 2025-05-06
 */
@Service
public class Mc04IslmcXyServiceImpl extends ServiceImpl<Mc04IslmcXyMapper, Mc04IslmcXyDO> implements Mc04IslmcXyService {

}