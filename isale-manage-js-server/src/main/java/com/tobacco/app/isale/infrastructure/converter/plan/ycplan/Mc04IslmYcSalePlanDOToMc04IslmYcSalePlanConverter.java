/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.ycplan;

import com.tobacco.app.isale.domain.model.plan.ycplan.Mc04IslmYcSalePlan;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmYcSalePlanDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: loongxi
 * @Email: <EMAIL>
 * @Create: 2025-08-07
 */

@Mapper

public interface Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter extends StructureBaseConverter<Mc04IslmYcSalePlanDO, Mc04IslmYcSalePlan> {

        Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter INSTANCE =
            Mappers.getMapper(Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter.class);

}