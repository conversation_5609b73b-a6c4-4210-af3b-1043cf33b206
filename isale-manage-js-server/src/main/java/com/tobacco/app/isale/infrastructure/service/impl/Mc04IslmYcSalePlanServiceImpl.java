/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmYcSalePlanDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmYcSalePlanMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmYcSalePlanService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: qintian
 * @Since: 2025-07-18
 * @Email: <EMAIL>
 * @Create: 2025-07-18
        */
@Service
public class Mc04IslmYcSalePlanServiceImpl extends ServiceImpl<Mc04IslmYcSalePlanMapper, Mc04IslmYcSalePlanDO> implements Mc04IslmYcSalePlanService {

}