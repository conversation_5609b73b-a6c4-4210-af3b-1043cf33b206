package com.tobacco.app.isale.infrastructure.converter.basic;

import com.tobacco.app.isale.domain.model.basic.ManageIslmcForwardWhseCom;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcForwardWhseComDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @create 2025/7/28
 * @description
 */
@Mapper
public interface IslmcForwardWhseComToManageIslmcForwardWhseComDOConvert
        extends StructureBaseConverter<Mc04IslmcForwardWhseComDO, ManageIslmcForwardWhseCom> {
    IslmcForwardWhseComToManageIslmcForwardWhseComDOConvert INSTANCE =
            Mappers.getMapper(IslmcForwardWhseComToManageIslmcForwardWhseComDOConvert.class);
}
