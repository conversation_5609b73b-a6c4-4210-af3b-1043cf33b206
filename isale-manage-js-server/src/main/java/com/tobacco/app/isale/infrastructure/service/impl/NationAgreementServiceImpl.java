/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.NationAgreementDO;
import com.tobacco.app.isale.infrastructure.mapper.NationAgreementMapper;
import com.tobacco.app.isale.infrastructure.service.api.NationAgreementService;
import org.springframework.stereotype.Service;

/**
 * @Description: 服务实现类
 *
 * @Author: renyonghui
 * @Since: 2025-05-07
 * @Email: <EMAIL>
 * @Create: 2025-05-07
 */
@Service
public class NationAgreementServiceImpl extends ServiceImpl<NationAgreementMapper, NationAgreementDO> implements NationAgreementService {

}