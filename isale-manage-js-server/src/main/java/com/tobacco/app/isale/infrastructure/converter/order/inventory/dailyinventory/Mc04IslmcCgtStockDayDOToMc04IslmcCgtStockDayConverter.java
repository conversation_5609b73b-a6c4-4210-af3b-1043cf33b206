/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.order.inventory.dailyinventory;

import com.alibaba.bizworks.extension.core.converter.IExtensionConverter;
import com.tobacco.app.isale.domain.model.order.inventory.dailyinventory.Mc04IslmcCgtStockDay;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtStockDayDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * @Author: hujiarong
 * @Email: <EMAIL>
 * @Create: 2025-07-21
 */

@Mapper

public interface Mc04IslmcCgtStockDayDOToMc04IslmcCgtStockDayConverter extends IExtensionConverter<Mc04IslmcCgtStockDayDO, Mc04IslmcCgtStockDay>{

        Mc04IslmcCgtStockDayDOToMc04IslmcCgtStockDayConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcCgtStockDayDOToMc04IslmcCgtStockDayConverter.class);

    @Mappings({})
        Mc04IslmcCgtStockDayDO Mc04IslmcCgtStockDayToMc04IslmcCgtStockDayDO(Mc04IslmcCgtStockDay mc04IslmcCgtStockDay);

    @Mappings({})
    List<Mc04IslmcCgtStockDayDO> Mc04IslmcCgtStockDayListToMc04IslmcCgtStockDayDOList(List<Mc04IslmcCgtStockDay> mc04IslmcCgtStockDayList);

    @Mappings({})
        Mc04IslmcCgtStockDay Mc04IslmcCgtStockDayDOToMc04IslmcCgtStockDay(Mc04IslmcCgtStockDayDO Mc04IslmcCgtStockDayDO);

    @Mappings({})
    List<Mc04IslmcCgtStockDay> Mc04IslmcCgtStockDayDOListToCgtStockDayList(List<Mc04IslmcCgtStockDayDO> Mc04IslmcCgtStockDayDOList);

}