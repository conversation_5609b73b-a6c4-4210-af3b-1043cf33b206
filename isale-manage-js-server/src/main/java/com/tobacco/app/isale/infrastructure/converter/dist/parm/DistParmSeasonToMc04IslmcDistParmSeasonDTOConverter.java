/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.parm;

import com.tobacco.app.isale.domain.model.order.dist.parm.DistParmSeason;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isalecenter.client.dto.order.distParm.Mc04IslmcDistParmSeasonDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> liuwan<PERSON>
 * @create_time : 2025/06/03 19:18
 * @description : 配货参数数据转化
 */
@Mapper
public interface DistParmSeasonToMc04IslmcDistParmSeasonDTOConverter extends StructureBaseConverter<Mc04IslmcDistParmSeasonDTO, DistParmSeason> {
    DistParmSeasonToMc04IslmcDistParmSeasonDTOConverter INSTANCE = Mappers.getMapper(DistParmSeasonToMc04IslmcDistParmSeasonDTOConverter.class);
}
