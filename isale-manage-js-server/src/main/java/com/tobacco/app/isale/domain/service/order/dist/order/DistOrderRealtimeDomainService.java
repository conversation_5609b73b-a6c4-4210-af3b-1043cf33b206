/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.domain.service.order.dist.order;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.ddd.DomainService;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.base.CustomPage;
import com.inspur.ind.constant.CommonConstants;
import com.inspur.ind.icom.IcomUtils;
import com.inspur.ind.imsysTask.entity.ImsysFlowInstance;
import com.inspur.ind.imsysflow.ImSysFlowUtils;
import com.inspur.ind.imsysflow.entity.ImsysFlowNodeReq;
import com.inspur.ind.imsysflow.entity.req.GetFlowNextNodeREQ;
import com.inspur.ind.util.DateUtils;
import com.inspur.ind.util.IDUtils;
import com.inspur.x1.ac.rule.utils.AcRuleUtil;
import com.tobacco.app.isale.domain.constants.DistConstants;
import com.tobacco.app.isale.domain.enums.common.ProdTradeTypeCodeEnum;
import com.tobacco.app.isale.domain.enums.order.dist.order.DistOrderStatusEnum;
import com.tobacco.app.isale.domain.enums.order.dist.order.DistOrderTypeEnum;
import com.tobacco.app.isale.domain.model.date.Mc04IndDataPeriod;
import com.tobacco.app.isale.domain.model.order.dist.order.*;
import com.tobacco.app.isale.domain.model.order.inventory.realtimeinventory.Mc04IslmcCgtStock;
import com.tobacco.app.isale.domain.repository.nation.subsys.ISaleThreePartyRepository;
import com.tobacco.app.isale.domain.repository.order.dist.order.DistOrderRealtimeRepository;
import com.tobacco.app.isale.domain.service.order.inventory.realtimeinventory.CgtStockDomainService;
import com.tobacco.app.isale.tools.utils.CustUtil;
import com.tobacco.app.isale.tools.utils.DistUtils;
import com.tobacco.app.isale.tools.utils.FlowUtil;
import com.tobacco.app.isale.tools.utils.ISalePermissionUtils;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liuwancheng
 * @create_time : 2025/07/28 13:58
 * @description : 实时配货领域服务
 */
@Component("ISaleDistOrderRealtimeDomainDS")
@DomainService(name = "实时配货领域服务", desc = "实时配货领域服务")
@Slf4j
public class DistOrderRealtimeDomainService {

    @Resource
    private DistOrderRealtimeRepository distOrderRealtimeRepository;

    @Resource
    private ISaleThreePartyRepository iSaleThreePartyRepository;
    @Resource
    private CgtStockDomainService cgtStockDomainService;

    /**
     * 设置协议周期
     *
     * @param distOrder 配货订单
     */
    private void setMc04CgtXyPeriodCode(DistOrder distOrder) {
        // 调拨计划类型
        String mc04MonthSalePlanType = distOrder.getMc04MonthSalePlanType();
        // 选择的月份
        String ma02PlanMonth = distOrder.getMa02PlanMonth();
        String mc04CgtXyPeriodCode;
        // 下转协议，取上个月的所在半年协议
        if (DistConstants.MONTH_SALE_PLAN_TYPE_20.equals(mc04MonthSalePlanType)) {
            String lastMonth = DateUtils.getBeforeOrNextMonth(ma02PlanMonth, -1);
            mc04CgtXyPeriodCode = DateUtils.getAgreementPeriodCode(lastMonth);
        } else {
            // 选择的协议号
            String md02CgtXyNo = distOrder.getMd02CgtXyNo();
            if (StrUtil.isBlank(md02CgtXyNo)) {
                mc04CgtXyPeriodCode = DateUtils.getAgreementPeriodCode(ma02PlanMonth);
            } else {
                // 根据协议号取协议周期
                mc04CgtXyPeriodCode = distOrderRealtimeRepository.getMc04CgtXyPeriodCode(md02CgtXyNo);
            }
        }
        distOrder.setMc04CgtXyPeriodCode(mc04CgtXyPeriodCode);
    }

    /**
     * 设置合同零点类型
     *
     * @param distOrder 配货订单
     */
    private void setMc04ContZeroClockType(DistOrder distOrder) {
        String distType = distOrder.getMc04CgtDistOrderType();
        // 期望发货日期
        String ecxpecOutDate = distOrder.getMc04ContExpecOutDate();
        // 调拨计划类型
        String mc04MonthSalePlanType = distOrder.getMc04MonthSalePlanType();
        // 合同零点类型 0:非备货,1:BHYK,3:LDFH 默认0
        String mc04ContZeroClockType = distOrder.getMc04ContZeroClockType() == null ? DistConstants.CONT_ZEROCLOCK_TYPE_0 : distOrder.getMc04ContZeroClockType();
        // 日常配货里可能存在零点发货
        if (DistOrderTypeEnum.NORMAL.getCode().equals(distType)) {
            // 零点发货  发货日期是1月1号或者 调拨计划类型是30零点
            if (ecxpecOutDate.endsWith(DistConstants.DATE_FIRST_DAY) || DistConstants.MONTH_SALE_PLAN_TYPE_30.equals(mc04MonthSalePlanType)) {
                mc04ContZeroClockType = DistConstants.CONT_ZEROCLOCK_TYPE_LDFH;
            }
        } else if (DistOrderTypeEnum.PRESHIPMENT.getCode().equals(distType)) {
            // 移库备货
            mc04ContZeroClockType = DistConstants.CONT_ZEROCLOCK_TYPE_BHYK;
        }
        distOrder.setMc04ContZeroClockType(mc04ContZeroClockType);
    }

    /**
     * 处理订单数据
     *
     * @param distOrder 配货订单
     */
    private void processDistOrder(DistOrder distOrder, String curStatus) {
        // 配货订单代码
        String mc04CgtDistOrderCode = distOrder.getMc04CgtDistOrderCode();
        // 期望发货日期 空的话默认今日
        String ecxpecOutDate = distOrder.getMc04ContExpecOutDate() == null ? DateUtils.getToday() : distOrder.getMc04ContExpecOutDate();
        distOrder.setMc04ContExpecOutDate(ecxpecOutDate);

        // 为空的话，使用流水号生成一个
        distOrder.setMc04CgtDistOrderCode(StrUtil.isBlank(mc04CgtDistOrderCode) ? IcomUtils.getNextId(DistConstants.DIST_ORDER_KEY) : mc04CgtDistOrderCode);
        // 配货类型默认10实时配货
        String distType = distOrder.getMc04CgtDistOrderType() == null ? DistOrderTypeEnum.REALTIME.getCode() : distOrder.getMc04CgtDistOrderType();
        distOrder.setMc04CgtDistOrderType(distType);
        // 烟草制品交易业务类型代码 默认0卷烟  (0:国产卷烟,1:国产雪茄烟)
        String itemType = distOrder.getMa02TobaProdTradeTypeCode() == null ? ProdTradeTypeCodeEnum.PROD_TRADE_TYPE_CODE_0.getCode() : distOrder.getMa02TobaProdTradeTypeCode();
        distOrder.setMa02TobaProdTradeTypeCode(itemType);
        // 配货日默认今日
        String distDate = distOrder.getMc04CgtDistOrderDistDate() == null ? DateUtils.getToday() : distOrder.getMc04CgtDistOrderDistDate();
        distOrder.setMc04CgtDistOrderDistDate(distDate);

        // 设置合同零点类型
        setMc04ContZeroClockType(distOrder);

        // 调拨计划类型
        String mc04MonthSalePlanType = distOrder.getMc04MonthSalePlanType() == null ? DistConstants.MONTH_SALE_PLAN_TYPE_10 : distOrder.getMc04MonthSalePlanType();
        distOrder.setMc04MonthSalePlanType(mc04MonthSalePlanType);

        // 设置协议周期
        setMc04CgtXyPeriodCode(distOrder);

        distOrder.getDistOrderItems().forEach(item -> {
            // 配货订单编码
            String mc04CgtDistOrderId = item.getMc04CgtDistOrderId();
            // 为空的话，生成一个
            item.setMc04CgtDistOrderId(StrUtil.isBlank(mc04CgtDistOrderId) ? IDUtils.randomUUID(DistConstants.ID_LEN) : mc04CgtDistOrderId);
            // 处理最终量
            getQty(item, curStatus);
        });
    }


    /**
     * 获取下一个状态
     *
     * @param distType  配货订单类型
     * @param curStatus 当前状态
     * @param isReject  是否驳回
     */
    private String getNextStatus(String distType, String curStatus, boolean isReject) {
        return isReject ? DistUtils.getRejectStatus(DistConstants.BUSITYPE_20, StrUtil.isBlank(distType) ? DistOrderTypeEnum.REALTIME.getCode() : distType, curStatus) : DistUtils.getNextStatus(DistConstants.BUSITYPE_20, StrUtil.isBlank(distType) ? DistOrderTypeEnum.REALTIME.getCode() : distType, curStatus);
    }

    /**
     * 获取填报数量
     *
     * @param item      配货订单明细
     * @param curStatus 当前状态
     */
    private BigDecimal getQty(DistOrderItem item, String curStatus) {
        BigDecimal qty;
        // 根据状态判断对应字段值
        switch (curStatus) {
            case "10":
                // 销区提报
                qty = item.getMc04CgtDistOrderSaleAreaReqQty();
                break;
            case "20":
                // 销区审核
                qty = item.getMc04CgtDistOrderSaleAreaAuditQty();
                break;
            case "30":
                // 中心审核
                qty = item.getMc04CgtDistOrderSaleCenterAuditQty();
                break;
            default:
                qty = BigDecimal.ZERO;
        }
        // 因前端有精确度问题，后端保留6位小数
        qty = qty == null ? BigDecimal.ZERO : qty.setScale(6, RoundingMode.HALF_UP);
        // 精度处理后，如果之前有值重新设置对应状态的字段值
        if (DistOrderStatusEnum.SALE_SUBMIT.getCode().equals(curStatus) && item.getMc04CgtDistOrderSaleAreaReqQty() != null) {
            item.setMc04CgtDistOrderSaleAreaReqQty(qty);
        } else if (DistOrderStatusEnum.SALE_VERIFY.getCode().equals(curStatus) && item.getMc04CgtDistOrderSaleAreaAuditQty() != null) {
            item.setMc04CgtDistOrderSaleAreaAuditQty(qty);
        } else if (DistOrderStatusEnum.CENTER_VERIFY.getCode().equals(curStatus) && item.getMc04CgtDistOrderSaleCenterAuditQty() != null) {
            item.setMc04CgtDistOrderSaleCenterAuditQty(qty);
        }
        // 最终量
        item.setMd02CgtDistConfirmQty(qty);
        return qty;
    }

    /**
     * 获取批量审核或者驳回需要更新的配货订单数据
     *
     * @param distOrders 配货订单列表
     * @param curStatus  当前装
     * @param nextStatus 下一个状态
     * @param isReject   是否拒绝
     */
    private List<Mc04IslmCgtDistOrder> getCgtDistOrders(List<Mc04IslmCgtDistOrder> distOrders, String curStatus, String nextStatus, boolean isReject) {
        List<Mc04IslmCgtDistOrder> items = new ArrayList<>();
        for (Mc04IslmCgtDistOrder distOrder : distOrders) {
            Mc04IslmCgtDistOrder distOrderDo = getMc04IslmCgtDistOrder(nextStatus, distOrder);
            if (!isReject) {
                // 非驳回时才设置对应环节的值
                setQty(distOrder, distOrderDo, curStatus);
            }
            items.add(distOrderDo);
        }
        return items;
    }

    /**
     * 批量审核时设置对象的量
     *
     * @param item        配货订单明细
     * @param distOrderDo 配货订单数据
     * @param nextStatus  下一状态
     */
    private void setQty(Mc04IslmCgtDistOrder item, Mc04IslmCgtDistOrder distOrderDo, String nextStatus) {
        // 取最终量
        BigDecimal qty = item.getMd02CgtDistConfirmQty();
        // 设置对应状态的字段值（如果原值不为null取其值 空的话取确认量）
        if (DistOrderStatusEnum.SALE_SUBMIT.getCode().equals(nextStatus)) {
            distOrderDo.setMc04CgtDistOrderSaleAreaReqQty(item.getMc04CgtDistOrderSaleAreaReqQty() != null ? item.getMc04CgtDistOrderSaleAreaReqQty() : qty);
        } else if (DistOrderStatusEnum.SALE_VERIFY.getCode().equals(nextStatus)) {
            distOrderDo.setMc04CgtDistOrderSaleAreaAuditQty(item.getMc04CgtDistOrderSaleAreaAuditQty() != null ? item.getMc04CgtDistOrderSaleAreaAuditQty() : qty);
        } else if (DistOrderStatusEnum.CENTER_VERIFY.getCode().equals(nextStatus)) {
            distOrderDo.setMc04CgtDistOrderSaleCenterAuditQty(item.getMc04CgtDistOrderSaleCenterAuditQty() != null ? item.getMc04CgtDistOrderSaleCenterAuditQty() : qty);
        }
    }

    /**
     * 获取批量审核需要更新的配货订单数据
     *
     * @param nextStatus 下一个状态
     * @param item       配货订单
     */
    private Mc04IslmCgtDistOrder getMc04IslmCgtDistOrder(String nextStatus, Mc04IslmCgtDistOrder item) {
        Mc04IslmCgtDistOrder distOrderDo = new Mc04IslmCgtDistOrder();
        // 使用的字段 状态 主键 配货单号 条码 二级牌号
        distOrderDo.setMc04CgtDistOrderStatus(nextStatus);
        distOrderDo.setMc04CgtDistOrderId(item.getMc04CgtDistOrderId());
        distOrderDo.setMc04CgtDistOrderCode(item.getMc04CgtDistOrderCode());
        distOrderDo.setAcTwoLevelCigCode(item.getAcTwoLevelCigCode());
        distOrderDo.setAcCgtCartonCode(item.getAcCgtCartonCode());
        return distOrderDo;
    }


    /**
     * 检测配货订单剩余量
     *
     * @param distOrderItems 配货订单明细
     * @param agreementData  协议(月份、周)数据
     * @param curStatus      当前订单状态
     * @param colName        列名称
     */
    private void checkDistOrderItemRemainainQty(List<DistOrderItem> distOrderItems, List<DistOrderCols> agreementData, String curStatus, String colName) {
        StringBuffer errorMsg = new StringBuffer();
        // 剩余量
        Map<String, BigDecimal> remainingQtyMap = agreementData.stream().collect(Collectors.toMap(DistOrderCols::getAcCgtCartonCode, DistOrderCols::getRemainQty));
        int seq = 1;
        for (DistOrderItem item : distOrderItems) {
            BigDecimal qty = getQty(item, curStatus);
            BigDecimal remainingQty = remainingQtyMap.get(item.getAcCgtCartonCode()) == null ? BigDecimal.ZERO : remainingQtyMap.get(item.getAcCgtCartonCode());
            if (qty.compareTo(remainingQty) > 0) {
                String msg = String.format("第%d行%s填报量%s万支不能超过%s剩余量%s万支!", seq, item.getAcCgtName(), qty, colName, remainingQty);
                errorMsg.append(msg).append("\n");
            }
            seq++;
        }
        Assert.isTrue(StrUtil.isBlank(errorMsg.toString()), () -> new CustomException(errorMsg.toString()));
    }

    /**
     * 检测填报量托盘倍数
     *
     * @param distOrderItems 配货订单明细
     * @param curStatus      当前订单状态
     */
    private void checkDistOrderItemTray(List<DistOrderItem> distOrderItems, String curStatus) {
        int seq = 1;
        for (DistOrderItem item : distOrderItems) {
            checkDistOrderItemTray(item, curStatus, seq);
            seq++;
        }
    }

    /**
     * 检测填报量托盘倍数
     *
     * @param item      配货订单明细
     * @param curStatus 当前订单状态
     * @param seq       序号
     */
    private void checkDistOrderItemTray(DistOrderItem item, String curStatus, int seq) {
        StringBuffer errorMsg = new StringBuffer();
        BigDecimal trayQty = getQty(item, curStatus);
        BigDecimal itemTrayCapacity = item.getMa02LogtIcTrayPalletTransQty();
        String trayType = item.getMd03LogtTrayCombTspTrayType();
        if (CommonConstants.YES.equals(trayType)) {
            if (itemTrayCapacity.compareTo(BigDecimal.ZERO) == 0) {
                String msg = String.format("第%d行%s托盘容量未设置!", seq, item.getAcCgtName());
                errorMsg.append(msg).append("\n");
            }
            // 取模和余数，判断余数为0
            BigDecimal[] remainder = trayQty.divideAndRemainder(itemTrayCapacity);
            if (remainder[1].compareTo(BigDecimal.ZERO) != 0) {
                String msg = String.format("第%d行%s托盘量%s万支必须是托盘容量%s万支的倍数!", seq, item.getAcCgtName(), trayQty, itemTrayCapacity);
                errorMsg.append(msg).append("\n");
            }
        }
        Assert.isTrue(StrUtil.isBlank(errorMsg.toString()), () -> new CustomException(errorMsg.toString()));
    }

    /**
     * 检测填报量不能超过库存
     *
     * @param distOrderItems 配货订单明细
     * @param curStatus      当前订单状态
     * @param distStockList  二级牌号库存列表
     */
    private void checkDistOrderItemStock(List<DistOrderItem> distOrderItems, String curStatus, List<Mc04IslmcCgtStock> distStockList) {
        StringBuffer errorMsg = new StringBuffer();
        // 剩余量
        Map<String, BigDecimal> stockQtyMap = distStockList.stream().collect(Collectors.toMap(Mc04IslmcCgtStock::getAcTwoLevelCigCode, Mc04IslmcCgtStock::getMc05CigFreeStkQty));
        int seq = 1;
        for (DistOrderItem item : distOrderItems) {
            BigDecimal qty = getQty(item, curStatus);
            BigDecimal stockQty = stockQtyMap.get(item.getAcTwoLevelCigCode()) == null ? BigDecimal.ZERO : stockQtyMap.get(item.getAcTwoLevelCigCode());
            if (qty.compareTo(stockQty) > 0) {
                String msg = String.format("第%d行%s填报量%s万支不能超过库存剩余量%s万支!", seq, item.getAcCgtName(), qty, stockQty);
                errorMsg.append(msg).append("\n");
            }
            seq++;
        }
        Assert.isTrue(StrUtil.isBlank(errorMsg.toString()), () -> new CustomException(errorMsg.toString()));
    }

    /**
     * 验证配货行业营销管控子系统数据
     *
     * @param distOrder 配货订单数据
     * @param curStatus 当前订单状态
     */
    private void checkNationItem(DistOrder distOrder, String curStatus) {

        String distType = distOrder.getMc04CgtDistOrderType();
        // 实时配货才会校验
        if (DistConstants.DIST_TYPE_10.equals(distType)) {
            List<DistOrderItem> distOrderItems = distOrder.getDistOrderItems();
            NationDistPreview nationDistPreview = new NationDistPreview();
            nationDistPreview.setReqmemberCode(distOrder.getBaComOrgCode());
            nationDistPreview.setDistregionCode(distOrder.getMd02CgtDistRegionCode());
            nationDistPreview.setMd02DistReceiveregionCode(distOrder.getMd02DistReceiveregionCode());
            nationDistPreview.setBillDate(distOrder.getMc04CgtDistOrderDistDate());
            Map<String, BigDecimal> minCalcQtyMap = new HashMap<>(16);
            Map<String, BigDecimal> maxCalcQtyMap = new HashMap<>(16);
            // 取网配预览数据
            NationDistPreview distPreview = getDistPreview(nationDistPreview);
            if (ObjectUtil.isNotNull(distPreview) && CollectionUtil.isNotEmpty(distPreview.getNationDistPreviewItemList())) {
                minCalcQtyMap = distPreview.getNationDistPreviewItemList().stream().collect(Collectors.toMap(NationDistPreviewItem::getProductCode, NationDistPreviewItem::getMd02CgtDistQtyRateMin));
                maxCalcQtyMap = distPreview.getNationDistPreviewItemList().stream().collect(Collectors.toMap(NationDistPreviewItem::getProductCode, NationDistPreviewItem::getMd02CgtDistQtyRateMax));
            }
            checkNationItem(distOrderItems, minCalcQtyMap, maxCalcQtyMap, curStatus);
        }
    }

    /**
     * 验证配货行业营销管控子系统规格数据
     *
     * @param distOrderItems 配货订单数据
     * @param minCalcQtyMap  最小阈值
     * @param maxCalcQtyMap  最大阈值
     * @param curStatus      当前订单状态
     */
    private void checkNationItem(List<DistOrderItem> distOrderItems, Map<String, BigDecimal> minCalcQtyMap, Map<String, BigDecimal> maxCalcQtyMap, String curStatus) {
        StringBuffer errorMsg = new StringBuffer();
        int seq = 1;
        for (DistOrderItem item : distOrderItems) {
            String md02CgtDistInduAbnormalRemark = item.getMd02CgtDistInduAbnormalRemark();
            BigDecimal qty = getQty(item, curStatus);
            // 是否填量但是未设置网配参数
            String isParam = item.getIsDistParamProduct();
            if (qty.compareTo(BigDecimal.ZERO) > 0 && !CommonConstants.YES.equals(isParam)) {
                String msg = String.format("第%d行%s未设置配货参数!", seq, item.getAcCgtName());
                errorMsg.append(msg).append("\n");
            }
            // 判断网配建议量最大最小值
            BigDecimal minCalcQty = minCalcQtyMap.get(item.getAcCgtCartonCode());
            BigDecimal maxCalcQty = maxCalcQtyMap.get(item.getAcCgtCartonCode());
            if (qty.compareTo(BigDecimal.ZERO) > 0 && StrUtil.isBlank(md02CgtDistInduAbnormalRemark)) {
                if (maxCalcQty != null && qty.compareTo(maxCalcQty) > 0) {
                    String msg = String.format("第%d行%s填报量%s万支不能超过网配建议量最大值%s万支，请调整或填写异常说明!", seq, item.getAcCgtName(), qty, maxCalcQty);
                    errorMsg.append(msg).append("\n");
                }
                if (minCalcQty != null && qty.compareTo(minCalcQty) < 0) {
                    String msg = String.format("第%d行%s填报量%s万支不能超过网配建议量最小值%s万支，请调整或填写异常说明!", seq, item.getAcCgtName(), qty, minCalcQty);
                    errorMsg.append(msg).append("\n");
                }
            }
            seq++;
        }
        Assert.isTrue(StrUtil.isBlank(errorMsg.toString()), () -> new CustomException(errorMsg.toString()));
    }

    /**
     * 验证配货单位倍数,默认发货仓库不能为空，总量不能为0
     *
     * @param distOrder 配货订单
     * @param curStatus 当前订单状态
     */
    private void checkDistOrderItemBaseInfo(DistOrder distOrder, String curStatus) {
        StringBuffer errorMsg = new StringBuffer();
        List<DistOrderItem> distOrderItems = distOrder.getDistOrderItems();
        int seq = 1;
        BigDecimal sum = BigDecimal.ZERO;
        // 十万
        final BigDecimal tenW = new BigDecimal("100000");
        // 因前端不传递的值,需要重新设置配货单位、是否设置配货参数、
        distOrderRealtimeRepository.setCgtInfo(distOrder, null);
        for (DistOrderItem item : distOrderItems) {
            if (StrUtil.isBlank(item.getMd02CgtOutStorehouseCode())) {
                String msg = String.format("第%d行%s默认发货点不能为空!", seq, item.getAcCgtName());
                errorMsg.append(msg).append("\n");
            }

            BigDecimal qty = getQty(item, curStatus);
            BigDecimal distSize = item.getDistSize();
            if (distSize == null || distSize.compareTo(BigDecimal.ZERO) == 0) {
                String msg = String.format("第%d行%s卷烟配货单位不能为0或空!", seq, item.getAcCgtName());
                errorMsg.append(msg).append("\n");
            } else {
                // 配货单位倍数校验：qty * 100000 对 distSize * 100000 取余，判断是否整除
                BigDecimal[] remainder = qty.multiply(tenW).divideAndRemainder(distSize.multiply(tenW));
                if (remainder[1].compareTo(BigDecimal.ZERO) != 0) {
                    String msg = String.format("第%d行%s填报量%s万支必须是卷烟配货单位%s万支的倍数!", seq, item.getAcCgtName(), qty, distSize);
                    errorMsg.append(msg).append("\n");
                }
            }
            sum = sum.add(qty);
            seq++;
        }

        if (sum.compareTo(BigDecimal.ZERO) == 0) {
            String msg = "填报总量不能为0!";
            errorMsg.append(msg).append("\n");
        }
        Assert.isTrue(StrUtil.isBlank(errorMsg.toString()), () -> new CustomException(errorMsg.toString()));
    }

    /**
     * 是否发送给行业子系统
     *
     * @param mc04CgtDistOrderStatus 配货订单状态
     * @return 是否发送给行业子系统
     */
    private boolean isSendToIndustry(String mc04CgtDistOrderStatus) {
        return StrUtil.equals(mc04CgtDistOrderStatus, DistOrderStatusEnum.COMMERCE_CONFIRM.getCode());
    }

    /**
     * 是否发送给物流
     *
     * @param mc04CgtDistOrderStatus 配货订单状态
     * @param sendToLogisticsStatus  发给物流的状态
     * @return 是否发送给行业子系统
     */
    private boolean isSendToLogistics(String mc04CgtDistOrderStatus, String sendToLogisticsStatus) {
        return StrUtil.equals(mc04CgtDistOrderStatus, sendToLogisticsStatus);
    }


    /**
     * 处理预览表卷烟阈值最大最小值
     *
     * @param distPreview 预览表
     */
    private void processDistViewItemMaxMin(NationDistPreview distPreview) {
        List<NationDistPreviewItem> nationDistPreviewItemList = distPreview.getNationDistPreviewItemList();
        if (CollectionUtil.isNotEmpty(nationDistPreviewItemList)) {
            BigDecimal hundred = new BigDecimal(100);
            for (NationDistPreviewItem item : nationDistPreviewItemList) {
                // 计算配货量
                BigDecimal calcdistnum = item.getCalcdistnum();
                // 网配建议量比例阈值最大值
                BigDecimal md02CgtDistQtyRateMax = item.getMd02CgtDistQtyRateMax();
                // 网配建议量比例阈值最小值
                BigDecimal md02CgtDistQtyRateMin = item.getMd02CgtDistQtyRateMin();
                if (calcdistnum != null && md02CgtDistQtyRateMax != null) {
                    item.setMd02CgtDistQtyRateMax(calcdistnum.multiply(md02CgtDistQtyRateMax).divide(hundred, 4, RoundingMode.HALF_UP));
                }
                if (calcdistnum != null && md02CgtDistQtyRateMin != null) {
                    item.setMd02CgtDistQtyRateMin(calcdistnum.multiply(md02CgtDistQtyRateMin).divide(hundred, 4, RoundingMode.HALF_UP));
                }
            }
        }
    }


    /**
     * 检测配货订单基础信息
     *
     * @param distOrder 配货订单
     * @param curStatus 当前订单状态
     */
    @Method(value = "检测配货订单基础信息", name = "检测配货订单基础信息")
    public void checkDistOrderBaseInfo(@Parameter(name = "配货订单数据", required = true) DistOrder distOrder, @Parameter(name = "当前状态", required = true) String curStatus) {
        Assert.notNull(distOrder, () -> new CustomException("配货订单数据不能为空"));
        Assert.notEmpty(distOrder.getDistOrderItems(), () -> new CustomException("配货订单明细数据不能为空"));
        // 验证必需的字段 商业公司 月份 到货日 发货日 到货仓库
        Assert.notBlank(distOrder.getBaComOrgCode(), () -> new CustomException("公司不能为空"));
        Assert.notBlank(distOrder.getMa02PlanMonth(), () -> new CustomException("月份不能为空"));
        String mc04ContExpecOutDate = distOrder.getMc04ContExpecOutDate();
        String mc04ContExpecArrivalDate = distOrder.getMc04ContExpecArrivalDate();
        Assert.notBlank(mc04ContExpecArrivalDate, () -> new CustomException("到货日期不能为空"));
        Assert.notBlank(mc04ContExpecOutDate, () -> new CustomException("发货日期不能为空"));
        Assert.isTrue(mc04ContExpecOutDate.compareTo(mc04ContExpecArrivalDate) <= 0, () -> new CustomException("到货日期不能早于发货日期"));
        Assert.notBlank(distOrder.getMd02CgtInStorehouseCode(), () -> new CustomException("收货仓库不能为空"));
        // 判断当前商业公司是否在权限范围内
        String baComOrgCode = distOrder.getBaComOrgCode();
        Assert.isTrue(ISalePermissionUtils.hasPermission(baComOrgCode), () -> new CustomException("当前无权限"));
        // 按配货订单代码查询数据库 如果有判断其状态是否与当前状态一致
        String mc04CgtDistOrderCode = distOrder.getMc04CgtDistOrderCode();
        if (StrUtil.isNotBlank(mc04CgtDistOrderCode)) {
            DistOrder dbDistOrder = distOrderRealtimeRepository.getDistOrder(mc04CgtDistOrderCode);
            Assert.notNull(dbDistOrder, () -> new CustomException("配货订单异常"));
            // 判断当前状态正确
            Assert.isTrue(curStatus.equals(dbDistOrder.getMc04CgtDistOrderStatus()), () -> new CustomException("配货订单状态异常"));
        }
    }

    /**
     * 检测配货订单数据
     *
     * @param distOrder 配货订单
     * @param curStatus 当前订单状态
     */
    @Method(value = "检测配货订单数据", name = "检测配货订单数据")
    public void checkDistOrderData(@Parameter(name = "配货订单数据", required = true) DistOrder distOrder, @Parameter(name = "当前状态", required = true) String curStatus) {
        // 处理业务规则
        distOrderRealtimeRepository.addRules(distOrder);

        // 校验配货单位的倍数,默认发货仓库不能为空，总量不能为0
        checkDistOrderItemBaseInfo(distOrder, curStatus);

        // 如果传给用友，校验必需设置网配参数并且不能超过阈值范围
        checkNationItem(distOrder, curStatus);

        List<DistOrderItem> distOrderItems = distOrder.getDistOrderItems();
        DistOrderColsParam distOrderColsParam = DistOrderColsParam.builder().baComOrgCode(distOrder.getBaComOrgCode()).build();
        if (CommonConstants.YES.equals(distOrder.getCtrWeek())) {
            // TODO 可能要限制发货日期必须在周期内
            // 控制周计划
            distOrderColsParam.setMc04DatePeriodCode(distOrder.getMc04DatePeriodCode());
            List<DistOrderCols> weekPlanData = distOrderRealtimeRepository.queryWeekPlanData(distOrderColsParam);
            // 校验周计划剩余量
            checkDistOrderItemRemainainQty(distOrderItems, weekPlanData, curStatus, "周计划");
        }
        if (CommonConstants.YES.equals(distOrder.getCtrMonth())) {
            // 控制月计划
            distOrderColsParam.setMa02PlanMonth(distOrder.getMa02PlanMonth());
            distOrderColsParam.setMc04MonthSalePlanType(distOrder.getMc04MonthSalePlanType());
            List<DistOrderCols> monthPlanData = distOrderRealtimeRepository.queryMonthPlanData(distOrderColsParam);
            // 校验月计划剩余量
            checkDistOrderItemRemainainQty(distOrderItems, monthPlanData, curStatus, "月计划");
        }
        if (CommonConstants.YES.equals(distOrder.getCtrAgree())) {
            // 控制协议
            distOrderColsParam.setMc04CgtXyPeriodCode(distOrder.getMc04CgtXyPeriodCode());
            List<DistOrderCols> agreementData = distOrderRealtimeRepository.queryAgreementData(distOrderColsParam);
            // 校验协议剩余量
            checkDistOrderItemRemainainQty(distOrderItems, agreementData, curStatus, "协议");
        }
        if (CommonConstants.YES.equals(distOrder.getCtrStock())) {
            // 控制库存
            List<Mc04IslmcCgtStock> distStockList = cgtStockDomainService.getDistStockList();
            // 校验库存
            checkDistOrderItemStock(distOrderItems, curStatus, distStockList);
        }
        if (CommonConstants.YES.equals(distOrder.getCtrTray())) {
            // 控制托盘
            distOrderRealtimeRepository.setTrayCap(distOrder);
            // 校验托盘容量倍数
            checkDistOrderItemTray(distOrderItems, curStatus);
        }
    }

    /**
     * 保存配货订单前操作
     *
     * @param distOrder 配货订单
     * @param curStatus 当前状态
     * @param isSave    是否保存
     * @return 配货订单
     */
    @Method(value = "保存配货订单前操作", name = "保存配货订单前操作")
    @ReturnValue(value = "配货订单明细数据", name = "配货订单明细数据")
    public DistOrder saveDistOrderPre(@Parameter(name = "配货订单数据", required = true) DistOrder distOrder, @Parameter(name = "当前状态", required = true) String curStatus, @Parameter(name = "是否保存", required = true) boolean isSave) {
        List<DistOrderItem> items = distOrder.getDistOrderItems();
        Assert.notEmpty(items, () -> new CustomException("配货订单卷烟列表不能为空"));

        // 取得下一个状态
        String distType = distOrder.getMc04CgtDistOrderType();
        String nextStatus = isSave ? curStatus : getNextStatus(distType, curStatus, false);
        Assert.notBlank(nextStatus, () -> new CustomException("取下一个状态异常"));
        distOrder.setMc04CgtDistOrderStatus(nextStatus);
        // 处理订单数据
        processDistOrder(distOrder, curStatus);
        // 判断是否发送给行业子系统
        if (isSendToIndustry(nextStatus)) {
            String msg = distOrderRealtimeRepository.pushDistToNation(distOrder.getMc04CgtDistOrderCode());
            // 推送失败状态改回当前状态
            if (StrUtil.isNotBlank(msg)) {
                distOrder.setMc04CgtDistOrderStatus(curStatus);
                distOrder.setSendToIndustryErrorMsg(msg);
            }
        }
        // 判断是否发送给物流
        String sendLogStatus = AcRuleUtil.getRuleValue(DistConstants.ISALE_ORDER_DIST_PUSH_LOG, distOrder.getIcomCode());
        if (isSendToLogistics(nextStatus, sendLogStatus)) {
            String msg = iSaleThreePartyRepository.pushDistToLogistics(distOrder.getMc04CgtDistOrderCode());
            // 推送失败状态改回当前状态
            if (StrUtil.isNotBlank(msg)) {
                distOrder.setMc04CgtDistOrderStatus(curStatus);
                distOrder.setSendToIndustryErrorMsg(msg);
            }
        }
        return distOrder;
    }

    /**
     * 保存配货订单
     *
     * @param distOrder 配货订单
     * @param curStatus 当前状态
     * @param isSave    是否保存
     * @return 配货订单
     */
    @Transactional(rollbackFor = Exception.class)
    @Method(value = "保存配货订单", name = "保存配货订单")
    @ReturnValue(value = "配货订单明细数据", name = "配货订单明细数据")
    public DistOrder saveDistOrder(@Parameter(name = "配货订单数据", required = true) DistOrder distOrder, @Parameter(name = "当前状态", required = true) String curStatus, @Parameter(name = "是否保存", required = true) boolean isSave) {
        return distOrderRealtimeRepository.saveDistOrder(distOrder, curStatus, isSave);
    }

    /**
     * 批量更新配货订单之前的操作，可能传递物流或者用友
     *
     * @param mc04CgtDistOrderCodes 配货订单代码列表
     * @param distType              配货订单类型
     * @param curStatus             配货订单当前状态
     * @param isReject              是否驳回
     * @param mf04ApprovalInfo      驳回信息
     * @return 处理后的配货订单状态列表
     */
    @Method(value = "批量更新配货订单之前的操作", name = "批量更新配货订单之前的操作")
    @ReturnValue(value = "配货订单保存结果", name = "配货订单保存结果")
    public List<Mc04IslmCgtDistOrder> saveBatchDistOrderPre(@Parameter(name = "配货订单数据", required = true) List<String> mc04CgtDistOrderCodes, @Parameter(name = "当前状态", required = true) String distType, String curStatus, @Parameter(name = "是否驳回", required = true) boolean isReject, @Parameter(name = "驳回原因", required = true) String mf04ApprovalInfo) {

        Assert.notEmpty(mc04CgtDistOrderCodes, () -> new CustomException("请选择待审核的配货订单"));
        Assert.notBlank(curStatus, () -> new CustomException("配货订单状态不能为空"));
        List<Mc04IslmCgtDistOrder> cgtDistOrders = distOrderRealtimeRepository.getDistOrderList(mc04CgtDistOrderCodes);
        // TODO 调用流程中心
        Map<String, BusiComDto> busiComDtoMap = CustUtil.getBusiComDtoMap(cgtDistOrders.stream().map(Mc04IslmCgtDistOrder::getBaComOrgCode).collect(Collectors.toList()));
        for (Mc04IslmCgtDistOrder cgtDistOrder : cgtDistOrders) {
            processDistOrderFlow(cgtDistOrder.getMc04CgtDistOrderCode(), distType, cgtDistOrder.getBaComOrgCode(), busiComDtoMap, curStatus, isReject, mf04ApprovalInfo);
        }
        List<String> distOrderCodes = cgtDistOrders.stream().map(Mc04IslmCgtDistOrder::getMc04CgtDistOrderCode).distinct().collect(Collectors.toList());
        Assert.notEmpty(cgtDistOrders, () -> new CustomException("待审核的配货订单为空"));
        Assert.isTrue(distOrderCodes.size() == mc04CgtDistOrderCodes.size(), () -> new CustomException("部分配货订单状态异常"));
        // 驳回统一到草稿
        String nextStatus = getNextStatus(distType, curStatus, isReject);
        Assert.notBlank(nextStatus, () -> new CustomException("取下一个状态异常"));
        // 组织需要保存的数据
        List<Mc04IslmCgtDistOrder> items = getCgtDistOrders(cgtDistOrders, curStatus, nextStatus, isReject);
        // 成功和失败的配货订单代码列表
        List<String> successMc04CgtDistOrderCodes = new ArrayList<>();
        // 判断是否禁用发给行业子系统，默认否 ，否的话发送给行业子系统
        if (isSendToIndustry(nextStatus)) {
            for (String mc04CgtDistOrderCode : mc04CgtDistOrderCodes) {
                String msg = distOrderRealtimeRepository.pushDistToNation(mc04CgtDistOrderCode);
                // 推送成功
                if (StrUtil.isBlank(msg)) {
                    successMc04CgtDistOrderCodes.add(mc04CgtDistOrderCode);
                }
            }
        } else {
            successMc04CgtDistOrderCodes = mc04CgtDistOrderCodes;
        }
        if (CollectionUtil.isNotEmpty(successMc04CgtDistOrderCodes)) {
            // 判断是否发送给物流
            String sendLogStatus = AcRuleUtil.getRuleValue(DistConstants.ISALE_ORDER_DIST_PUSH_LOG, IcomUtils.getIcomCode());
            if (isSendToLogistics(nextStatus, sendLogStatus)) {
                for (String mc04CgtDistOrderCode : successMc04CgtDistOrderCodes) {
                    String msg = iSaleThreePartyRepository.pushDistToLogistics(mc04CgtDistOrderCode);
                    if (StrUtil.isNotBlank(msg)) {
                        // 失败的删除掉该配货单号
                        successMc04CgtDistOrderCodes.remove(mc04CgtDistOrderCode);
                    }
                }
            }
        }
        final List<String> finalSuccessMc04CgtDistOrderCodes = successMc04CgtDistOrderCodes;
        // 过滤成功地配货订单
        items = items.stream().filter(item -> finalSuccessMc04CgtDistOrderCodes.contains(item.getMc04CgtDistOrderCode())).collect(Collectors.toList());
        return items;
    }

    /**
     * 批量更新配货订单状态（审核时更新对应的值）
     *
     * @param mc04CgtDistOrderCodes 配货订单代码列表
     * @param items                 要保存的配货订单数据
     * @param isReject              是否驳回
     * @param curStatus             当前状态
     * @param mf04ApprovalInfo      审批意见
     * @return 处理后的配货订单状态列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Method(value = "批量更新配货订单状态", name = "批量更新配货订单状态")
    @ReturnValue(value = "配货订单保存结果", name = "配货订单保存结果")
    public DistOrderStatus saveBatchDistOrderStatus(@Parameter(name = "配货订单数据", required = true) List<String> mc04CgtDistOrderCodes, @Parameter(name = "要保存的配货订单数据", required = true) List<Mc04IslmCgtDistOrder> items, @Parameter(name = "是否驳回", required = true) boolean isReject, @Parameter(name = "当前状态", required = true) String curStatus, @Parameter(name = "审批意见") String mf04ApprovalInfo) {

        // 成功和失败的配货订单代码列表
        List<String> successMc04CgtDistOrderCodes = items.stream().map(Mc04IslmCgtDistOrder::getMc04CgtDistOrderCode).distinct().collect(Collectors.toList());
        List<String> failMc04CgtDistOrderCodes = mc04CgtDistOrderCodes.stream().filter(item -> !successMc04CgtDistOrderCodes.contains(item)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(items)) {
            String nextStatus = items.get(0).getMc04CgtDistOrderStatus();
            // 驳回不改量只改状态
            if (isReject) {
                distOrderRealtimeRepository.saveBatchDistOrderStatus(successMc04CgtDistOrderCodes, curStatus, nextStatus, mf04ApprovalInfo);
            } else {
                // 批量保存配货订单状态和数量
                distOrderRealtimeRepository.saveBatchDistOrderStatusAndQty(items, curStatus);
            }
        }
        DistOrderStatus distOrderStatus = new DistOrderStatus();
        distOrderStatus.setMc04CgtDistOrderCodes(mc04CgtDistOrderCodes);
        distOrderStatus.setSuccessMc04CgtDistOrderCodes(successMc04CgtDistOrderCodes);
        distOrderStatus.setFailMc04CgtDistOrderCodes(failMc04CgtDistOrderCodes);
        return distOrderStatus;
    }

    /**
     * 作废配货订单
     *
     * @param mc04CgtDistOrderCode 配货订单代码
     * @return 错误信息 空表示成功
     */
    @Method(value = "作废配货订单", name = "作废配货订单")
    @ReturnValue(value = "错误信息", name = "错误信息")
    public String cancel(@Parameter(name = "配货订单代码", required = true) String mc04CgtDistOrderCode) {
        String msg = distOrderRealtimeRepository.cancel(mc04CgtDistOrderCode);
        if (StrUtil.isBlank(msg)) {
            // 推送删除配货到到行业
            return distOrderRealtimeRepository.pushDeleteDistToNation(mc04CgtDistOrderCode);
        }
        return msg;
    }

    /**
     * 反馈配货订单给行业子系统
     *
     * @param mc04CgtDistOrderCode 配货订单代码
     * @return 错误信息 空表示成功
     */
    @Method(value = "反馈配货订单给行业子系统", name = "反馈配货订单给行业子系统")
    @ReturnValue(value = "错误信息", name = "错误信息")
    public String feedback(@Parameter(name = "配货订单代码", required = true) String mc04CgtDistOrderCode) {
        String errorMsg = distOrderRealtimeRepository.pushDistToNation(mc04CgtDistOrderCode);
        if (StrUtil.isBlank(errorMsg)) {
            // 调用流程中心
            CompletableFuture.runAsync(() -> {
                List<Mc04IslmCgtDistOrder> distOrderList = distOrderRealtimeRepository.getDistOrderList(Arrays.asList(mc04CgtDistOrderCode));
                Mc04IslmCgtDistOrder mc04IslmCgtDistOrder = distOrderList.get(0);
                processDistOrderFlow(mc04CgtDistOrderCode, mc04IslmCgtDistOrder.getMc04CgtDistOrderType(), mc04IslmCgtDistOrder.getBaComOrgCode(), null, mc04IslmCgtDistOrder.getMc04CgtDistOrderStatus(), false, "");
            });
            distOrderRealtimeRepository.updateDistOrderStatus(mc04CgtDistOrderCode, DistOrderStatusEnum.SALE_FEEDBACK.getCode(), DistOrderStatusEnum.COMMERCE_CONFIRM.getCode());
        }
        return errorMsg;
    }

    /**
     * 反馈多个配货订单给行业子系统
     *
     * @param mc04CgtDistOrderCodes 配货订单代码列表
     * @return 错误信息 空表示成功
     */
    @Method(value = "反馈多个配货订单给行业子系统", name = "反馈多个配货订单给行业子系统")
    @ReturnValue(value = "错误信息", name = "错误信息")
    public List<String> feedbackDists(@Parameter(name = "配货订单代码列表", required = true) List<String> mc04CgtDistOrderCodes) {
        List<String> errorMsgs = new ArrayList<>();
        mc04CgtDistOrderCodes.forEach(mc04CgtDistOrderCode -> {
            String errorMsg = feedback(mc04CgtDistOrderCode);
            if (StrUtil.isNotBlank(errorMsg)) {
                errorMsgs.add(errorMsg);
            }
        });
        return errorMsgs;
    }

    /**
     * 获取配货公司列表
     *
     * @param distOrder 请求参数
     * @return 配货公司列表
     */
    public List<DistOrderCom> queryDistComList(DistOrder distOrder) {
        // 查询有权限的地市
        distOrder.setBaComOrgCodes(ISalePermissionUtils.getPermissionComList(distOrder.getBaComOrgCodes()));
        return distOrderRealtimeRepository.queryDistComList(distOrder);
    }

    /**
     * 查询配货公司列表是否显示额外列（发货地区列，收货地区列）
     *
     * @param distOrder 请求参数
     * @return 是否显示额外列（发货地区列，收货地区列）
     */
    public DistOrderInitStatus queryDistComExtCols(DistOrder distOrder) {
        return distOrderRealtimeRepository.queryDistComExtCols(distOrder);
    }

    /**
     * 获取配货公司列表
     *
     * @param domainReq 获取配货公司列表请求参数
     * @return 获取配货公司列表结果
     */
    public CustomPage<DistOrder> queryDistOrderPage(DistOrderPage domainReq) {
        // 查询有权限的地市
        domainReq.setBaComOrgCodes(ISalePermissionUtils.getPermissionComList(domainReq.getBaComOrgCodes()));
        return distOrderRealtimeRepository.queryDistOrderPage(domainReq);
    }

    /**
     * 查询配货订单信息
     *
     * @param distOrder 查询配货订单信息
     * @return 查询配货订单信息
     */
    @Method(value = "查询配货订单信息", name = "查询配货订单信息")
    @ReturnValue(value = "查询配货订单信息", name = "查询配货订单信息")
    public List<DistOrder> queryDistOrderList(DistOrder distOrder) {
        // 查询有权限的地市
        distOrder.setBaComOrgCodes(ISalePermissionUtils.getPermissionComList(distOrder.getBaComOrgCodes()));
        List<DistOrder> distOrders = distOrderRealtimeRepository.queryDistOrderList(distOrder);
        List<String> baComOrgCodes = distOrders.stream().map(DistOrder::getBaComOrgCode).collect(Collectors.toList());
        Map<String, BusiComDto> busiComDtoMap = CustUtil.getBusiComDtoMap(baComOrgCodes);
        distOrders.forEach(item -> {
            BusiComDto busiComDto = busiComDtoMap.get(item.getBaComOrgCode());
            if (busiComDto != null) {
                item.setBaComOrgName(busiComDto.getMc04ComOrgShortName());
            }
        });
        return distOrders;
    }

    /**
     * 查询月计划数据
     *
     * @param distOrderColsParam 查询请求参数
     * @return 月计划数据
     */
    @Method(value = "查询月计划数据", name = "查询月计划数据")
    @ReturnValue(value = "月计划数据", name = "月计划数据")
    public List<DistOrderCols> queryMonthPlanData(@Parameter(name = "查询请求参数", required = true) DistOrderColsParam distOrderColsParam) {
        Assert.isTrue(ISalePermissionUtils.hasPermission(distOrderColsParam.getBaComOrgCode()), () -> new CustomException("当前无权限"));
        return distOrderRealtimeRepository.queryMonthPlanData(distOrderColsParam);
    }

    /**
     * 查询协议数据
     *
     * @param distOrderColsParam 查询请求参数
     * @return 协议数据
     */
    @Method(value = "查询协议数据", name = "查询协议数据")
    @ReturnValue(value = "协议数据", name = "协议数据")
    public List<DistOrderCols> queryAgreementData(@Parameter(name = "查询请求参数", required = true) DistOrderColsParam distOrderColsParam) {
        Assert.isTrue(ISalePermissionUtils.hasPermission(distOrderColsParam.getBaComOrgCode()), () -> new CustomException("当前无权限"));
        return distOrderRealtimeRepository.queryAgreementData(distOrderColsParam);
    }


    /**
     * 查询周计划数据
     *
     * @param distOrderColsParam 查询请求参数
     * @return 周计划数据
     */
    @Method(value = "查询周计划数据", name = "查询周计划数据")
    @ReturnValue(value = "周计划数据", name = "周计划数据")
    public List<DistOrderCols> queryWeekPlanData(@Parameter(name = "查询请求参数", required = true) DistOrderColsParam distOrderColsParam) {
        Assert.isTrue(ISalePermissionUtils.hasPermission(distOrderColsParam.getBaComOrgCode()), () -> new CustomException("当前无权限"));
        return distOrderRealtimeRepository.queryWeekPlanData(distOrderColsParam);
    }

    /**
     * 查询月计划类型
     *
     * @param monthCode 月份编码
     * @return 月计划类型 map K:月计划类型编码 V:月计划类型名称
     */
    @Method(value = "查询月计划类型", name = "查询月计划类型")
    @ReturnValue(value = "月计划类型", name = "月计划类型")
    public List<Map<String, String>> queryMonthPlanType(@Parameter(name = "查询请求参数", required = true) String monthCode, @Parameter(name = "卷烟类型", required = true) String cgtType) {
        return distOrderRealtimeRepository.queryMonthPlanType(monthCode, cgtType);
    }

    /**
     * 查询数据周期列表
     *
     * @param mc04IndDataPeriod 查询数据周期列表请求参数
     * @return 查询数据周期列表结果
     */
    @Method(value = "查询数据周期列表", name = "查询数据周期列表")
    @ReturnValue(value = "查询数据周期列表结果", name = "查询数据周期列表结果")
    public List<Mc04IndDataPeriod> queryDataPeriodList(@Parameter(name = "查询数据周期列表请求参数", required = true) Mc04IndDataPeriod mc04IndDataPeriod) {
        return distOrderRealtimeRepository.queryDataPeriodList(mc04IndDataPeriod);
    }

    /**
     * 初始化配货订单
     *
     * @param baComOrgCode              业务公司组织编码
     * @param md02CgtDistRegionCode     配货发运地区编码
     * @param md02DistReceiveregionCode 配货收货地区编码
     * @param mc04CgtDistOrderCode      配货订单编码
     * @return 配货订单
     */
    @Method(value = "初始化配货订单", name = "初始化配货订单")
    @ReturnValue(value = "配货订单", name = "配货订单")
    public DistOrder initDistOrderRealTime(@Parameter(name = "业务公司组织编码") String baComOrgCode, @Parameter(name = "配货发运地区编码") String md02CgtDistRegionCode, @Parameter(name = "配货收货地区编码") String md02DistReceiveregionCode, @Parameter(name = "配货订单编码") String mc04CgtDistOrderCode) {
        DistOrder distOrder;
        if (StrUtil.isNotBlank(baComOrgCode)) {
            Assert.isTrue(ISalePermissionUtils.hasPermission(baComOrgCode), () -> new CustomException("当前无权限"));
            distOrder = distOrderRealtimeRepository.initDistOrderRealTime(baComOrgCode, md02CgtDistRegionCode, md02DistReceiveregionCode, mc04CgtDistOrderCode);
        } else {
            distOrder = distOrderRealtimeRepository.initDistOrderRealTime(baComOrgCode, md02CgtDistRegionCode, md02DistReceiveregionCode, mc04CgtDistOrderCode);
            Assert.isTrue(ISalePermissionUtils.hasPermission(distOrder.getBaComOrgCode()), () -> new CustomException("当前无权限"));
        }
        return distOrder;
    }

    /**
     * 配货单预览表查询
     *
     * @param nationDistPreview 获取配货单预览表参数
     * @return 配货单预览表
     */
    @Method(value = "配货单预览表查询", name = "配货单预览表查询")
    @ReturnValue(value = "配货单预览表", name = "配货单预览表")
    public NationDistPreview getDistPreview(@Parameter(name = "获取配货单预览表参数") NationDistPreview nationDistPreview) {
        Assert.isTrue(ISalePermissionUtils.hasPermission(nationDistPreview.getReqmemberCode()), () -> new CustomException("当前无权限"));
        NationDistPreview distPreview = distOrderRealtimeRepository.getDistPreview(nationDistPreview);
        if (ObjectUtil.isEmpty(distPreview)) {
            return distPreview;
        }
        // 处理规格的最大值最小值,因其本身是百分比 需要重新计算
        processDistViewItemMaxMin(distPreview);
        return distPreview;
    }

    /**
     * 获取配货单操作日志
     *
     * @param req 获取配货单操作日志
     * @return 配货单操作日志
     */
    @Method(value = "获取配货单操作日志", name = "获取配货单操作日志")
    @ReturnValue(name = "配货单操作日志", value = "配货单操作日志")
    public List<DistOrderLog> getDistOrderLog(@Parameter(name = "配货单操作日志请求参数") DistOrderLog req) {
        return distOrderRealtimeRepository.getDistOrderLog(req);
    }


    /**
     * 开启流程或驳回后重启流程
     *
     * @param distType             配货单类型
     * @param baComOrgCode         公司编码
     * @param busiComDtoMap        业务公司map
     * @param mc04CgtDistOrderCode 配货单编码
     * @param curStatus            当前状态
     * @param isReject             是否驳回
     * @param rejectMsg            驳回原因
     */
    @Method(value = "开启流程或驳回后重启流程", name = "开启流程或驳回后重启流程")
    @ReturnValue(value = "开启流程或驳回后重启流程", name = "开启流程或驳回后重启流程")
    public void processDistOrderFlow(@Parameter(name = "配货单编码", required = true) String mc04CgtDistOrderCode, @Parameter(name = "配货单类型", required = true) String distType, @Parameter(name = "业务公司组织编码", required = true) String baComOrgCode, @Parameter(name = "业务公司map") Map<String, BusiComDto> busiComDtoMap,@Parameter(name = "当前状态", required = true) String curStatus,@Parameter(name = "是否驳回") Boolean isReject, @Parameter(name = "驳回原因") String rejectMsg) {
        // 流程不影响自己业务增加捕捉异常
        try {
            BusiComDto busiComDto = busiComDtoMap.get(baComOrgCode);
            if (busiComDto == null) {
                busiComDto = CustUtil.getBusiComDto(baComOrgCode);
            }
            Assert.isTrue(busiComDto != null, () -> new CustomException("未找到公司信息"));
            String flowId = getFlowId(distType);
            // 流程节点信息
            Map<String, ImsysFlowNodeReq> currentAndNextNodeMap = FlowUtil.currentAndNextNode(flowId, DistOrderStatusEnum.SALE_SUBMIT.getCode());
            ImsysFlowNodeReq currentFlowNode = currentAndNextNodeMap.get(FlowUtil.NODE_STATUS_CURRENT);
            ImsysFlowNodeReq nextFlowNode = currentAndNextNodeMap.get(FlowUtil.NODE_STATUS_NEXT);
            String currentTitle = currentFlowNode.getImsysFlowNodeName() + "【" + busiComDto.getMc04ComOrgShortName() + "】";
            String nextTitle = nextFlowNode.getImsysFlowNodeName() + "【" + busiComDto.getMc04ComOrgShortName() + "】";
            // 获取实例详情
            GetFlowNextNodeREQ getFlowNextNodeReq = new GetFlowNextNodeREQ();
            getFlowNextNodeReq.setImsysFlowCode(flowId);
            getFlowNextNodeReq.setImsysBusiPk(mc04CgtDistOrderCode);
            ImsysFlowInstance instanceDetail = isReject || DistOrderStatusEnum.SALE_SUBMIT.getCode().equals(curStatus) ? ImSysFlowUtils.getInstanceDetail(getFlowNextNodeReq) : null;
            if (isReject) {
                // 驳回操作
                FlowUtil.rejectProcess(flowId, mc04CgtDistOrderCode, "{}", "", "", currentTitle, nextTitle, FlowUtil.TASK_DEAL_CHANNEL_PC, false, instanceDetail.getImsysFlowInstanceId(), StrUtil.isBlank(rejectMsg) ? "驳回" : rejectMsg, null);
                return;
            }
            // 销区提报提交操作，开起流程
            if (DistOrderStatusEnum.SALE_SUBMIT.getCode().equals(curStatus)) {
                // 取消审核后销区进行提交操作
                if (!Objects.isNull(instanceDetail)) {
                    FlowUtil.nextProcess(flowId, mc04CgtDistOrderCode, "{}", "{}", "{}", currentTitle, nextTitle, FlowUtil.TASK_DEAL_CHANNEL_PC, false, "提交", null);
                } else {
                    FlowUtil.startProcess(flowId, mc04CgtDistOrderCode, "{}", "{}", "{}", currentTitle, nextTitle, FlowUtil.TASK_DEAL_CHANNEL_PC, false, "提交", null);
                }
            } else { // 审核操作进入到下一个流程
                // 流程节点信息
                FlowUtil.nextProcess(flowId, mc04CgtDistOrderCode, "{}", "{}", "{}", currentTitle, nextTitle, FlowUtil.TASK_DEAL_CHANNEL_PC, false, "通过", null);
            }
        } catch (Throwable e) {
            log.error("开启流程或驳回后重启流程异常", e);
            log.error("开启流程或驳回后重启流程异常,流程实例: {},网配类型：{},", mc04CgtDistOrderCode,distType);
        }
    }


    /**
     * 获取流程ID
     *
     * @param distType 配货单类型
     * @return 流程ID
     */
    private String getFlowId(String distType) {
        switch (distType) {
            case DistConstants.DIST_TYPE_10:
                return DistConstants.REALTIME_ORDER_PROCESS_ID;
            case DistConstants.DIST_TYPE_40:
                return DistConstants.NORMAL_ORDER_PROCESS_ID;
            case DistConstants.DIST_TYPE_50:
                return DistConstants.DAILY_ORDER_PROCESS_ID;
            default:
                Assert.isTrue(false, () -> new CustomException("未知的配货单类型"));
        }
        return null;
    }
}
