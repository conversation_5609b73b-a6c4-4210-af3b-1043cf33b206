/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.entity;

import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DataObject;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * @description : 协议单位卷烟进销存日;
 *
 * <AUTHOR> wangluhao01
 * @since : 2025-05-13
 * @email : <EMAIL>
 * @create_time : 2025-05-13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("mc04_ind_xy_org_cgt_purch_sale_stk_day")
@DataObject(name = "协议单位卷烟进销存日;", desc = "协议单位卷烟进销存日;")
public class Mc04IndXyOrgCgtPurchSaleStkDayDO extends BaseFactor {

    private static final long serialVersionUID = 1L;
                /**
             * 商业公司卷烟进销存业务日期;一号工程数据商业公司卷烟进销存日期，用于记录商业公司卷烟进销存业务日期
             */

    @Field(value = "商业公司卷烟进销存业务日期;一号工程数据商业公司卷烟进销存日期，用于记录商业公司卷烟进销存业务日期", name = "商业公司卷烟进销存业务日期;一号工程数据商业公司卷烟进销存日期，用于记录商业公司卷烟进销存业务日期")
    private String mc04PurchaseSaleStkDate;
                                /**
             * 商业公司卷烟进销存业务所在周;一号工程数据商业公司卷烟进销存业务日期所在自然周编码，用于记录商业公司卷烟进销存业务日期所属的自然周
             */

    @Field(value = "商业公司卷烟进销存业务所在周;一号工程数据商业公司卷烟进销存业务日期所在自然周编码，用于记录商业公司卷烟进销存业务日期所属的自然周", name = "商业公司卷烟进销存业务所在周;一号工程数据商业公司卷烟进销存业务日期所在自然周编码，用于记录商业公司卷烟进销存业务日期所属的自然周")
    private String mc04PurchaseSaleStkWeek;
                                /**
             * 商业公司卷烟进销存业务所在周名称;一号工程数据商业公司卷烟进销存业务日期所在周的中文名称
             */

    @Field(value = "商业公司卷烟进销存业务所在周名称;一号工程数据商业公司卷烟进销存业务日期所在周的中文名称", name = "商业公司卷烟进销存业务所在周名称;一号工程数据商业公司卷烟进销存业务日期所在周的中文名称")
    private String mc04PurchaseSaleStkWeekName;
                                /**
             * 商业公司卷烟进销存业务所在周开始日期;一号工程数据商业公司卷烟进销存业务日期所在周的周一的日期
             */

    @Field(value = "商业公司卷烟进销存业务所在周开始日期;一号工程数据商业公司卷烟进销存业务日期所在周的周一的日期", name = "商业公司卷烟进销存业务所在周开始日期;一号工程数据商业公司卷烟进销存业务日期所在周的周一的日期")
    private String mc04PurchaseSaleStkWeekBeginDate;
                                /**
             * 商业公司卷烟进销存业务所在周结束日期;一号工程数据商业公司卷烟进销存业务日期所在周的周天的日期
             */

    @Field(value = "商业公司卷烟进销存业务所在周结束日期;一号工程数据商业公司卷烟进销存业务日期所在周的周天的日期", name = "商业公司卷烟进销存业务所在周结束日期;一号工程数据商业公司卷烟进销存业务日期所在周的周天的日期")
    private String mc04PurchaseSaleStkWeekEndDate;
                                /**
             * 商业公司卷烟进销存业务所在月;一号工程数据商业公司卷烟进销存业务日期所在月份编码，用于记录商业公司卷烟进销存业务日期所属的月份
             */

    @Field(value = "商业公司卷烟进销存业务所在月;一号工程数据商业公司卷烟进销存业务日期所在月份编码，用于记录商业公司卷烟进销存业务日期所属的月份", name = "商业公司卷烟进销存业务所在月;一号工程数据商业公司卷烟进销存业务日期所在月份编码，用于记录商业公司卷烟进销存业务日期所属的月份")
    private String mc04PurchaseSaleStkMonth;
                                /**
             * 商业公司卷烟进销存业务所在月结束日期;一号工程数据商业公司卷烟进销存业务日期所在月的月末日期
             */

    @Field(value = "商业公司卷烟进销存业务所在月结束日期;一号工程数据商业公司卷烟进销存业务日期所在月的月末日期", name = "商业公司卷烟进销存业务所在月结束日期;一号工程数据商业公司卷烟进销存业务日期所在月的月末日期")
    private String mc04PurchaseSaleStkMonthEndDate;
                                /**
             * 商业公司卷烟进销存业务所在季度;一号工程数据商业公司卷烟进销存业务日期所在季度编码，用于记录商业公司卷烟进销存业务日期所属的季度
             */

    @Field(value = "商业公司卷烟进销存业务所在季度;一号工程数据商业公司卷烟进销存业务日期所在季度编码，用于记录商业公司卷烟进销存业务日期所属的季度", name = "商业公司卷烟进销存业务所在季度;一号工程数据商业公司卷烟进销存业务日期所在季度编码，用于记录商业公司卷烟进销存业务日期所属的季度")
    private String mc04PurchaseSaleStkQuarter;
                                /**
             * 商业公司卷烟进销存业务所在季度名称;一号工程数据商业公司卷烟进销存业务日期所在季度名称
             */

    @Field(value = "商业公司卷烟进销存业务所在季度名称;一号工程数据商业公司卷烟进销存业务日期所在季度名称", name = "商业公司卷烟进销存业务所在季度名称;一号工程数据商业公司卷烟进销存业务日期所在季度名称")
    private String mc04PurchaseSaleStkQuarterName;
                                /**
             * 商业公司卷烟进销存业务所在年;一号工程数据商业公司卷烟进销存业务日期所在季度编码，用于记录商业公司卷烟进销存业务日期所属的年份
             */

    @Field(value = "商业公司卷烟进销存业务所在年;一号工程数据商业公司卷烟进销存业务日期所在季度编码，用于记录商业公司卷烟进销存业务日期所属的年份", name = "商业公司卷烟进销存业务所在年;一号工程数据商业公司卷烟进销存业务日期所在季度编码，用于记录商业公司卷烟进销存业务日期所属的年份")
    private String mc04PurchaseSaleStkYear;
                                /**
             * 省级公司代码;商业公司的省级公司代码，用于条零关联上报、条零系统注册时使用
             */

    @Field(value = "省级公司代码;商业公司的省级公司代码，用于条零关联上报、条零系统注册时使用", name = "省级公司代码;商业公司的省级公司代码，用于条零关联上报、条零系统注册时使用")
    private String baProvOrgCode;
                                /**
             * 省公司简称;组织机构简称
             */

    @Field(value = "省公司简称;组织机构简称", name = "省公司简称;组织机构简称")
    private String baProvOrgShortName;
                                /**
             * 省级公司地图名称;省级商业公司地图上显示的名称
             */

    @Field(value = "省级公司地图名称;省级商业公司地图上显示的名称", name = "省级公司地图名称;省级商业公司地图上显示的名称")
    private String mc04ProvOrgGcName;
                                /**
             * 协议单位代码;记录与本工业有协议的单位编码
             */

    @Field(value = "协议单位代码;记录与本工业有协议的单位编码", name = "协议单位代码;记录与本工业有协议的单位编码")
    private String mc04XyOrgCode;
                                /**
             * 协议单位简称;记录与本工业有协议的单位简称
             */

    @Field(value = "协议单位简称;记录与本工业有协议的单位简称", name = "协议单位简称;记录与本工业有协议的单位简称")
    private String mc04XyOrgShortName;
                                /**
             * 协议单位地图名称;记录与本工业有协议的地图名称
             */

    @Field(value = "协议单位地图名称;记录与本工业有协议的地图名称", name = "协议单位地图名称;记录与本工业有协议的地图名称")
    private String mc04XyOrgGcName;
                                /**
             * 商业公司是否为统购分销;用于记录商业公司是否为统购分销
             */

    @Field(value = "商业公司是否为统购分销;用于记录商业公司是否为统购分销", name = "商业公司是否为统购分销;用于记录商业公司是否为统购分销")
    private String mc04ComOrgIsMonopolyPuh;
                                /**
             * 商业公司是否为直辖市;用于记录商业公司是否为直辖市
             */

    @Field(value = "商业公司是否为直辖市;用于记录商业公司是否为直辖市", name = "商业公司是否为直辖市;用于记录商业公司是否为直辖市")
    private String mc04ComOrgIsMcity;
                                /**
             * 商业公司大区代码;用于记录商业公司大区代码
             */

    @Field(value = "商业公司大区代码;用于记录商业公司大区代码", name = "商业公司大区代码;用于记录商业公司大区代码")
    private String mc04SaleRegionCode;
                                /**
             * 商业公司大区名称;用于记录商业公司大区名称
             */

    @Field(value = "商业公司大区名称;用于记录商业公司大区名称", name = "商业公司大区名称;用于记录商业公司大区名称")
    private String mc04SaleRegionName;
                                /**
             * 商业公司销区代码;用于记录商业公司销区代码
             */

    @Field(value = "商业公司销区代码;用于记录商业公司销区代码", name = "商业公司销区代码;用于记录商业公司销区代码")
    private String mc04SaleAreaCode;
                                /**
             * 商业公司销区名称;用于记录商业公司销区名称
             */

    @Field(value = "商业公司销区名称;用于记录商业公司销区名称", name = "商业公司销区名称;用于记录商业公司销区名称")
    private String mc04SaleAreaName;
                                /**
             * 商业公司雪茄大区代码;用于记录商业公司雪茄大区代码
             */

    @Field(value = "商业公司雪茄大区代码;用于记录商业公司雪茄大区代码", name = "商业公司雪茄大区代码;用于记录商业公司雪茄大区代码")
    private String mc04CigarSaleRegionCode;
                                /**
             * 商业公司雪茄大区名称;用于记录商业公司雪茄大区名称
             */

    @Field(value = "商业公司雪茄大区名称;用于记录商业公司雪茄大区名称", name = "商业公司雪茄大区名称;用于记录商业公司雪茄大区名称")
    private String mc04CigarSaleRegionName;
                                /**
             * 商业公司雪茄销区代码;用于记录商业公司雪茄销区代码
             */

    @Field(value = "商业公司雪茄销区代码;用于记录商业公司雪茄销区代码", name = "商业公司雪茄销区代码;用于记录商业公司雪茄销区代码")
    private String mc04CigarSaleAreaCode;
                                /**
             * 商业公司雪茄销区名称;用于记录商业公司雪茄销区名称
             */

    @Field(value = "商业公司雪茄销区名称;用于记录商业公司雪茄销区名称", name = "商业公司雪茄销区名称;用于记录商业公司雪茄销区名称")
    private String mc04CigarSaleAreaName;
                                /**
             * 卷烟代码;卷烟代码编码规则为ac+“经营类型”+“卷烟规格”作为分类属性。“经营类型”标识：国内内销代码为0、国内出口代码为1、国外进口代码为2、国外来牌内销代码为3、国外来牌出口为4；“卷烟规格”国内内销烟取13位卷烟规格条码的第8位到第12位，进出口卷烟卷烟规格采用5位流水码重新编制。
             */

    @Field(value = "卷烟代码;卷烟代码编码规则为ac+“经营类型”+“卷烟规格”作为分类属性。“经营类型”标识：国内内销代码为0、国内出口代码为1、国外进口代码为2、国外来牌内销代码为3、国外来牌出口为4；“卷烟规格”国内内销烟取13位卷烟规格条码的第8位到第12位，进出口卷烟卷烟规格采用5位流水码重新编制。", name = "卷烟代码;卷烟代码编码规则为ac+“经营类型”+“卷烟规格”作为分类属性。“经营类型”标识：国内内销代码为0、国内出口代码为1、国外进口代码为2、国外来牌内销代码为3、国外来牌出口为4；“卷烟规格”国内内销烟取13位卷烟规格条码的第8位到第12位，进出口卷烟卷烟规格采用5位流水码重新编制。")
    private String acCgtCode;
                                /**
             * 卷烟代码（条）;卷烟盒的最小包装—“条(通常为10盒/条,即200支/条)”的卷烟牌号名称和特征表述组合的标识代码
             */

    @Field(value = "卷烟代码（条）;卷烟盒的最小包装—“条(通常为10盒/条,即200支/条)”的卷烟牌号名称和特征表述组合的标识代码", name = "卷烟代码（条）;卷烟盒的最小包装—“条(通常为10盒/条,即200支/条)”的卷烟牌号名称和特征表述组合的标识代码")
    private String acCgtCartonCode;
                                /**
             * 卷烟名称;卷烟牌号名称和特征表述的组合,其表达方式为“卷烟牌号名称(特征表述)”,其中括号为半角字符。如:茶花(软)、中华(硬1i mg 5支)、中南海(25 mm特制)。“特征表述”一般为可选项,但同一卷烟牌号有两个或两个以上产品时,“特征表述”为必选项。同一卷烟产品在行业内各类信息平台﹑报表中应具有同一名称
             */

    @Field(value = "卷烟名称;卷烟牌号名称和特征表述的组合,其表达方式为“卷烟牌号名称(特征表述)”,其中括号为半角字符。如:茶花(软)、中华(硬1i mg 5支)、中南海(25 mm特制)。“特征表述”一般为可选项,但同一卷烟牌号有两个或两个以上产品时,“特征表述”为必选项。同一卷烟产品在行业内各类信息平台﹑报表中应具有同一名称", name = "卷烟名称;卷烟牌号名称和特征表述的组合,其表达方式为“卷烟牌号名称(特征表述)”,其中括号为半角字符。如:茶花(软)、中华(硬1i mg 5支)、中南海(25 mm特制)。“特征表述”一般为可选项,但同一卷烟牌号有两个或两个以上产品时,“特征表述”为必选项。同一卷烟产品在行业内各类信息平台﹑报表中应具有同一名称")
    private String acCgtName;
                                /**
             * 卷烟所属组织代码;卷烟规格对应的工业公司机构标识的代码
             */

    @Field(value = "卷烟所属组织代码;卷烟规格对应的工业公司机构标识的代码", name = "卷烟所属组织代码;卷烟规格对应的工业公司机构标识的代码")
    private String acCgtOrgCode;
                                /**
             * 工业公司名称;所属工业公司名称，中文名
             */

    @Field(value = "工业公司名称;所属工业公司名称，中文名", name = "工业公司名称;所属工业公司名称，中文名")
    private String baInduOrgName;
                                /**
             * 卷烟是否省产烟标识;用来标识卷烟在目标商业公司是否省产烟
             */

    @Field(value = "卷烟是否省产烟标识;用来标识卷烟在目标商业公司是否省产烟", name = "卷烟是否省产烟标识;用来标识卷烟在目标商业公司是否省产烟")
    private String mc04CgtProvProductFlag;
                                /**
             * 卷烟品牌代码;卷烟注册商标的中文名称的标识代码。采用顺序码，用4位数字表示。从“0001”开始计数。
             */

    @Field(value = "卷烟品牌代码;卷烟注册商标的中文名称的标识代码。采用顺序码，用4位数字表示。从“0001”开始计数。", name = "卷烟品牌代码;卷烟注册商标的中文名称的标识代码。采用顺序码，用4位数字表示。从“0001”开始计数。")
    private String acCgtBrandCode;
                                /**
             * 卷烟品牌名称;卷烟注册商标的中文名称,如:茶花、红塔山,中华、中南海等。注册商标中没有汉字的以国家烟草专卖局的中文译名为准。由国(境)外委托方提供牌号授权或主要原辅料在国内生产、但由国(境)外委托方回购的卷烟牌号名称以合同约定的中文名称为准
             */

    @Field(value = "卷烟品牌名称;卷烟注册商标的中文名称,如:茶花、红塔山,中华、中南海等。注册商标中没有汉字的以国家烟草专卖局的中文译名为准。由国(境)外委托方提供牌号授权或主要原辅料在国内生产、但由国(境)外委托方回购的卷烟牌号名称以合同约定的中文名称为准", name = "卷烟品牌名称;卷烟注册商标的中文名称,如:茶花、红塔山,中华、中南海等。注册商标中没有汉字的以国家烟草专卖局的中文译名为准。由国(境)外委托方提供牌号授权或主要原辅料在国内生产、但由国(境)外委托方回购的卷烟牌号名称以合同约定的中文名称为准")
    private String acCgtBrandName;
                                /**
             * 重点品牌标识;双十五品牌和鼓励品牌统称为重点品牌；区域重点品牌；视同重点品牌
             */

    @Field(value = "重点品牌标识;双十五品牌和鼓励品牌统称为重点品牌；区域重点品牌；视同重点品牌", name = "重点品牌标识;双十五品牌和鼓励品牌统称为重点品牌；区域重点品牌；视同重点品牌")
    private String acKeyBrandFlag;
                                /**
             * 双十五品牌标识;用来标识品牌是否是双十五品牌
             */

    @Field(value = "双十五品牌标识;用来标识品牌是否是双十五品牌", name = "双十五品牌标识;用来标识品牌是否是双十五品牌")
    private String mc04DoubleFifteenBrandFlag;
                                /**
             * 卷烟系列代码;用来记录卷烟所属的系列编码
             */

    @Field(value = "卷烟系列代码;用来记录卷烟所属的系列编码", name = "卷烟系列代码;用来记录卷烟所属的系列编码")
    private String mc04CgtSeriesCode;
                                /**
             * 卷烟系列的名称;用来记录卷烟所属的系列名称
             */

    @Field(value = "卷烟系列的名称;用来记录卷烟所属的系列名称", name = "卷烟系列的名称;用来记录卷烟所属的系列名称")
    private String mc04CgtSeriesName;
                                /**
             * 卷烟条包装不含税调拨价;用来记录卷烟条包装的不含税调拨价
             */

    @Field(value = "卷烟条包装不含税调拨价;用来记录卷烟条包装的不含税调拨价", name = "卷烟条包装不含税调拨价;用来记录卷烟条包装的不含税调拨价")
    private BigDecimal mc04CgtCartonNoTaxAllotPrice;
                                /**
             * 卷烟条包装含税调拨价;用来记录卷烟条包装的含税调拨价
             */

    @Field(value = "卷烟条包装含税调拨价;用来记录卷烟条包装的含税调拨价", name = "卷烟条包装含税调拨价;用来记录卷烟条包装的含税调拨价")
    private BigDecimal mc04CgtCartonTaxAllotPrice;
                                /**
             * 卷烟条包装批发价;用来记录卷烟条包装的批发价
             */

    @Field(value = "卷烟条包装批发价;用来记录卷烟条包装的批发价", name = "卷烟条包装批发价;用来记录卷烟条包装的批发价")
    private BigDecimal mc04CgtCartonTradePrice;
                                /**
             * 卷烟条包装零售价;用来记录卷烟条包装的零售价
             */

    @Field(value = "卷烟条包装零售价;用来记录卷烟条包装的零售价", name = "卷烟条包装零售价;用来记录卷烟条包装的零售价")
    private BigDecimal mc04CgtCartonRetailPrice;
                                /**
             * 卷烟不含税调拨价;烟草工业企业卖给商业企业(即烟草局或烟草公司)的不含税价格
             */

    @Field(value = "卷烟不含税调拨价;烟草工业企业卖给商业企业(即烟草局或烟草公司)的不含税价格", name = "卷烟不含税调拨价;烟草工业企业卖给商业企业(即烟草局或烟草公司)的不含税价格")
    private BigDecimal acCgtNoTaxAllotPrice;
                                /**
             * 卷烟含税调拨价格;烟草工业企业卖给商业企业(即烟草局或烟草公司)的含税价格
             */

    @Field(value = "卷烟含税调拨价格;烟草工业企业卖给商业企业(即烟草局或烟草公司)的含税价格", name = "卷烟含税调拨价格;烟草工业企业卖给商业企业(即烟草局或烟草公司)的含税价格")
    private BigDecimal acCgtTaxAllotPrice;
                                /**
             * 卷烟统一批发价;烟草商业企业(即烟草局或烟草公司)卖给有卷烟经营资格的零售户的价格。
             */

    @Field(value = "卷烟统一批发价;烟草商业企业(即烟草局或烟草公司)卖给有卷烟经营资格的零售户的价格。", name = "卷烟统一批发价;烟草商业企业(即烟草局或烟草公司)卖给有卷烟经营资格的零售户的价格。")
    private BigDecimal acCgtTradePrice;
                                /**
             * 卷烟指导零售价;即零售户卖给消费者的价格，是烟草商业企业制定的价格。
             */

    @Field(value = "卷烟指导零售价;即零售户卖给消费者的价格，是烟草商业企业制定的价格。", name = "卷烟指导零售价;即零售户卖给消费者的价格，是烟草商业企业制定的价格。")
    private BigDecimal acCgtRetailPrice;
                                /**
             * 卷烟价类代码;卷烟按每200支/条的工业销售价格(不含增值税)进行的分类标识代码yc/t 256.1-2008
             */

    @Field(value = "卷烟价类代码;卷烟按每200支/条的工业销售价格(不含增值税)进行的分类标识代码yc/t 256.1-2008", name = "卷烟价类代码;卷烟按每200支/条的工业销售价格(不含增值税)进行的分类标识代码yc/t 256.1-2008")
    private String acCgtPrTypeCode;
                                /**
             * 卷烟价类;卷烟按每200支/条的工业销售价格(不含增值税)进行的分类说明。
             */

    @Field(value = "卷烟价类;卷烟按每200支/条的工业销售价格(不含增值税)进行的分类说明。", name = "卷烟价类;卷烟按每200支/条的工业销售价格(不含增值税)进行的分类说明。")
    private String acCgtPrType;
                                /**
             * 卷烟行业价格档位;用来记录卷烟所属行业下发的价格档名称
             */

    @Field(value = "卷烟行业价格档位;用来记录卷烟所属行业下发的价格档名称", name = "卷烟行业价格档位;用来记录卷烟所属行业下发的价格档名称")
    private String mc04CgtPrTypeLevelName;
                                /**
             * 卷烟行业价格档位编码;用来记录卷烟所属行业下发的价格档编码
             */

    @Field(value = "卷烟行业价格档位编码;用来记录卷烟所属行业下发的价格档编码", name = "卷烟行业价格档位编码;用来记录卷烟所属行业下发的价格档编码")
    private String mc04CgtPrTypeLevelCode;
                                /**
             * 卷烟行业价格段编码;用来记录卷烟所属行业下发的价格段编码
             */

    @Field(value = "卷烟行业价格段编码;用来记录卷烟所属行业下发的价格段编码", name = "卷烟行业价格段编码;用来记录卷烟所属行业下发的价格段编码")
    private String mc04CgtPrTypeSegCode;
                                /**
             * 卷烟行业价格段;用来记录卷烟所属行业下发的价格段名称
             */

    @Field(value = "卷烟行业价格段;用来记录卷烟所属行业下发的价格段名称", name = "卷烟行业价格段;用来记录卷烟所属行业下发的价格段名称")
    private String mc04CgtPrTypeSegName;
                                /**
             * 卷烟价位;高中低端分类
             */

    @Field(value = "卷烟价位;高中低端分类", name = "卷烟价位;高中低端分类")
    private String acCgtPriceRange;
                                /**
             * 卷烟价位名称;用来记录卷烟价类对应的名称
             */

    @Field(value = "卷烟价位名称;用来记录卷烟价类对应的名称", name = "卷烟价位名称;用来记录卷烟价类对应的名称")
    private String mc04CgtPriceRangeName;
                                /**
             * 卷烟自定义价位段;用来记录卷烟自定义价位段编码
             */

    @Field(value = "卷烟自定义价位段;用来记录卷烟自定义价位段编码", name = "卷烟自定义价位段;用来记录卷烟自定义价位段编码")
    private String mc04CgtCustomPriceRangeCode;
                                /**
             * 卷烟自定义价位段名称;用来记录卷烟自定义价位段的名称
             */

    @Field(value = "卷烟自定义价位段名称;用来记录卷烟自定义价位段的名称", name = "卷烟自定义价位段名称;用来记录卷烟自定义价位段的名称")
    private String mc04CgtCustomPriceRangeName;
                                /**
             * 卷烟类型代码;按卷烟生产中烟叶组配方的类型进行的分类标识代码
             */

    @Field(value = "卷烟类型代码;按卷烟生产中烟叶组配方的类型进行的分类标识代码", name = "卷烟类型代码;按卷烟生产中烟叶组配方的类型进行的分类标识代码")
    private String acCgtTypeCode;
                                /**
             * 卷烟类型名称;按卷烟生产中烟叶组配方的类型进行的分类说明,如：烤烟型、混合型、雪茄型等
             */

    @Field(value = "卷烟类型名称;按卷烟生产中烟叶组配方的类型进行的分类说明,如：烤烟型、混合型、雪茄型等", name = "卷烟类型名称;按卷烟生产中烟叶组配方的类型进行的分类说明,如：烤烟型、混合型、雪茄型等")
    private String acCgtTypeName;
                                /**
             * 雪茄型号;雪茄的型号
             */

    @Field(value = "雪茄型号;雪茄的型号", name = "雪茄型号;雪茄的型号")
    private String adCigarType;
                                /**
             * 雪茄烟型号名称;用来记录雪茄烟类型的名称
             */

    @Field(value = "雪茄烟型号名称;用来记录雪茄烟类型的名称", name = "雪茄烟型号名称;用来记录雪茄烟类型的名称")
    private String mc04CigarTypeName;
                                /**
             * 雪茄烟加工方式代码;雪茄烟加工方式的分类标识代码。
             */

    @Field(value = "雪茄烟加工方式代码;雪茄烟加工方式的分类标识代码。", name = "雪茄烟加工方式代码;雪茄烟加工方式的分类标识代码。")
    private String adCigarModeCode;
                                /**
             * 雪茄烟加工方式名称;雪茄烟加工方式的中文名称
             */

    @Field(value = "雪茄烟加工方式名称;雪茄烟加工方式的中文名称", name = "雪茄烟加工方式名称;雪茄烟加工方式的中文名称")
    private String adCigarModeName;
                                /**
             * 卷烟焦油量;每支卷烟中的焦油量的数值
             */

    @Field(value = "卷烟焦油量;每支卷烟中的焦油量的数值", name = "卷烟焦油量;每支卷烟中的焦油量的数值")
    private BigDecimal acCgtTarVal;
                                /**
             * 卷烟低焦油标识;用来标识卷烟是否为低焦油含量卷烟
             */

    @Field(value = "卷烟低焦油标识;用来标识卷烟是否为低焦油含量卷烟", name = "卷烟低焦油标识;用来标识卷烟是否为低焦油含量卷烟")
    private String mc04CgtLowTarFlag;
                                /**
             * 卷烟新品标识;用来标识卷烟是否为新品卷烟
             */

    @Field(value = "卷烟新品标识;用来标识卷烟是否为新品卷烟", name = "卷烟新品标识;用来标识卷烟是否为新品卷烟")
    private String mc04CgtNewFlag;
                                /**
             * 卷烟短支烟标识;标识该卷烟是否是短支烟
             */

    @Field(value = "卷烟短支烟标识;标识该卷烟是否是短支烟", name = "卷烟短支烟标识;标识该卷烟是否是短支烟")
    private String acCgtSgortBranceFlag;
                                /**
             * 卷烟细支烟标识;周长标准为17mm左右，焦油含量不得高于8mg/支的卷烟
             */

    @Field(value = "卷烟细支烟标识;周长标准为17mm左右，焦油含量不得高于8mg/支的卷烟", name = "卷烟细支烟标识;周长标准为17mm左右，焦油含量不得高于8mg/支的卷烟")
    private String acCgtTinyFlag;
                                /**
             * 卷烟中支烟标识;标识该卷烟是否是中支烟
             */

    @Field(value = "卷烟中支烟标识;标识该卷烟是否是中支烟", name = "卷烟中支烟标识;标识该卷烟是否是中支烟")
    private String acCgtMediumBranceFlag;
                                /**
             * 卷烟爆珠烟标识;在卷烟的过滤嘴中使用的一种类似小珠子的香丸。
             */

    @Field(value = "卷烟爆珠烟标识;在卷烟的过滤嘴中使用的一种类似小珠子的香丸。", name = "卷烟爆珠烟标识;在卷烟的过滤嘴中使用的一种类似小珠子的香丸。")
    private String acCgtPearlFlag;
                                /**
             * 卷烟常规烟标识;用来标识卷烟是否为常规卷烟
             */

    @Field(value = "卷烟常规烟标识;用来标识卷烟是否为常规卷烟", name = "卷烟常规烟标识;用来标识卷烟是否为常规卷烟")
    private String mc04CgtCommonFlag;
                                /**
             * 卷烟罚没标识;用来标识卷烟是否为罚没烟
             */

    @Field(value = "卷烟罚没标识;用来标识卷烟是否为罚没烟", name = "卷烟罚没标识;用来标识卷烟是否为罚没烟")
    private String mc04CgtForfeitureFlag;
                                /**
             * 卷烟低价烟标识;标识该卷烟是否属于低价烟。
             */

    @Field(value = "卷烟低价烟标识;标识该卷烟是否属于低价烟。", name = "卷烟低价烟标识;标识该卷烟是否属于低价烟。")
    private String acCgtLowPriceFlag;
                                /**
             * 国产烟万支含税调拨价;国产烟的含税调拨价，单位为万支
             */

    @Field(value = "国产烟万支含税调拨价;国产烟的含税调拨价，单位为万支", name = "国产烟万支含税调拨价;国产烟的含税调拨价，单位为万支")
    private BigDecimal mc04CgtTenThousandTaxAllotPrice;
                                /**
             * 国产烟万支不含税调拨价;国产卷烟、国产雪茄烟的万支不含税调拨价
             */

    @Field(value = "国产烟万支不含税调拨价;国产卷烟、国产雪茄烟的万支不含税调拨价", name = "国产烟万支不含税调拨价;国产卷烟、国产雪茄烟的万支不含税调拨价")
    private BigDecimal acCgtTenThousandNoTaxAllotPrice;
                                /**
             * 卷烟系统外销售量（万支）;指商业公司销售给零售户的卷烟的量，包括国产卷烟、进口卷烟的系统外销量，单位万支
             */

    @Field(value = "卷烟系统外销售量（万支）;指商业公司销售给零售户的卷烟的量，包括国产卷烟、进口卷烟的系统外销量，单位万支", name = "卷烟系统外销售量（万支）;指商业公司销售给零售户的卷烟的量，包括国产卷烟、进口卷烟的系统外销量，单位万支")
    private BigDecimal md03Cgt10thComCoeSaleQty;
                                /**
             * 卷烟系统外销售额（万元）;指商业公司销售给零售户的卷烟的金额，包括国产卷烟、进口卷烟。单位万元
             */

    @Field(value = "卷烟系统外销售额（万元）;指商业公司销售给零售户的卷烟的金额，包括国产卷烟、进口卷烟。单位万元", name = "卷烟系统外销售额（万元）;指商业公司销售给零售户的卷烟的金额，包括国产卷烟、进口卷烟。单位万元")
    private BigDecimal md03Cgt10thComCoeSaleAmt;
                                /**
             * 卷烟系统外销售量同期（万支）;指销售给零售户的卷烟量的同期量，单位万支
             */

    @Field(value = "卷烟系统外销售量同期（万支）;指销售给零售户的卷烟量的同期量，单位万支", name = "卷烟系统外销售量同期（万支）;指销售给零售户的卷烟量的同期量，单位万支")
    private BigDecimal md03Cgt10thComCoeSaleQtyL;
                                /**
             * 卷烟系统外销售额同期（万元）;指卷烟销售给零售户的销售额的同期量，单位万元
             */

    @Field(value = "卷烟系统外销售额同期（万元）;指卷烟销售给零售户的销售额的同期量，单位万元", name = "卷烟系统外销售额同期（万元）;指卷烟销售给零售户的销售额的同期量，单位万元")
    private BigDecimal md03Cgt10thComCoeSaleAmtL;
                                /**
             * 卷烟系统外销售量周累计（万支）;用来记录卷烟系统外销售量周累计，单位万支
             */

    @Field(value = "卷烟系统外销售量周累计（万支）;用来记录卷烟系统外销售量周累计，单位万支", name = "卷烟系统外销售量周累计（万支）;用来记录卷烟系统外销售量周累计，单位万支")
    private BigDecimal mc04Cgt10thComCoeSaleQtyWA;
                                /**
             * 卷烟系统外销售额周累计（万元）;用来记录卷烟系统外销售额周累计，单位万元
             */

    @Field(value = "卷烟系统外销售额周累计（万元）;用来记录卷烟系统外销售额周累计，单位万元", name = "卷烟系统外销售额周累计（万元）;用来记录卷烟系统外销售额周累计，单位万元")
    private BigDecimal mc04Cgt10thComCoeSaleAmtWA;
                                /**
             * 卷烟系统外销售量同期周累计（万支）;用来记录卷烟系统外同期销售量同期周累计，单位万支
             */

    @Field(value = "卷烟系统外销售量同期周累计（万支）;用来记录卷烟系统外同期销售量同期周累计，单位万支", name = "卷烟系统外销售量同期周累计（万支）;用来记录卷烟系统外同期销售量同期周累计，单位万支")
    private BigDecimal mc04Cgt10thComCoeSaleQtyWAl;
                                /**
             * 卷烟系统外销售额同期周累计（万元）;用来记录卷烟系统外同期销售额同期周累计，单位万元
             */

    @Field(value = "卷烟系统外销售额同期周累计（万元）;用来记录卷烟系统外同期销售额同期周累计，单位万元", name = "卷烟系统外销售额同期周累计（万元）;用来记录卷烟系统外同期销售额同期周累计，单位万元")
    private BigDecimal mc04Cgt10thComCoeSaleAmtWAl;
                                /**
             * 卷烟系统外销售量月累计（万支）;指销售给零售户的卷烟量的月累计量，单位万支
             */

    @Field(value = "卷烟系统外销售量月累计（万支）;指销售给零售户的卷烟量的月累计量，单位万支", name = "卷烟系统外销售量月累计（万支）;指销售给零售户的卷烟量的月累计量，单位万支")
    private BigDecimal md03Cgt10thComCoeSaleQtyMA;
                                /**
             * 卷烟系统外销售额月累计（万元）;指卷烟销售给零售户的销售额的月累计量，单位万元
             */

    @Field(value = "卷烟系统外销售额月累计（万元）;指卷烟销售给零售户的销售额的月累计量，单位万元", name = "卷烟系统外销售额月累计（万元）;指卷烟销售给零售户的销售额的月累计量，单位万元")
    private BigDecimal md03Cgt10thComCoeSaleAmtMA;
                                /**
             * 卷烟系统外销售量同期月累计（万支）;指销售给零售户的卷烟量的同期月累计量，单位万支
             */

    @Field(value = "卷烟系统外销售量同期月累计（万支）;指销售给零售户的卷烟量的同期月累计量，单位万支", name = "卷烟系统外销售量同期月累计（万支）;指销售给零售户的卷烟量的同期月累计量，单位万支")
    private BigDecimal md03Cgt10thComCoeSaleQtyMAl;
                                /**
             * 卷烟系统外销售额同期月累计（万元）;指卷烟销售给零售户的销售额的同期月累计量
             */

    @Field(value = "卷烟系统外销售额同期月累计（万元）;指卷烟销售给零售户的销售额的同期月累计量", name = "卷烟系统外销售额同期月累计（万元）;指卷烟销售给零售户的销售额的同期月累计量")
    private BigDecimal md03Cgt10thComCoeSaleAmtMAl;
                                /**
             * 卷烟系统外销售量季累计（万支）;指销售给零售户的卷烟量的季累计量，单位万支
             */

    @Field(value = "卷烟系统外销售量季累计（万支）;指销售给零售户的卷烟量的季累计量，单位万支", name = "卷烟系统外销售量季累计（万支）;指销售给零售户的卷烟量的季累计量，单位万支")
    private BigDecimal md03Cgt10thComCoeSaleQtyQA;
                                /**
             * 卷烟系统外销售额季累计（万元）;指商业公司销售给零售户的卷烟的金额的季累计量，包括国产卷烟、进口卷烟。单位万元
             */

    @Field(value = "卷烟系统外销售额季累计（万元）;指商业公司销售给零售户的卷烟的金额的季累计量，包括国产卷烟、进口卷烟。单位万元", name = "卷烟系统外销售额季累计（万元）;指商业公司销售给零售户的卷烟的金额的季累计量，包括国产卷烟、进口卷烟。单位万元")
    private BigDecimal md03Cgt10thComCoeSaleAmtQA;
                                /**
             * 卷烟系统外销售量同期季累计（万支）;指销售给零售户的卷烟量的同期季累计量，单位万支
             */

    @Field(value = "卷烟系统外销售量同期季累计（万支）;指销售给零售户的卷烟量的同期季累计量，单位万支", name = "卷烟系统外销售量同期季累计（万支）;指销售给零售户的卷烟量的同期季累计量，单位万支")
    private BigDecimal md03Cgt10thComCoeSaleQtyQAl;
                                /**
             * 卷烟系统外销售额同期季累计（万元）;指卷烟销售给零售户的销售额的同期季累计量，单位万支
             */

    @Field(value = "卷烟系统外销售额同期季累计（万元）;指卷烟销售给零售户的销售额的同期季累计量，单位万支", name = "卷烟系统外销售额同期季累计（万元）;指卷烟销售给零售户的销售额的同期季累计量，单位万支")
    private BigDecimal md03Cgt10thComCoeSaleAmtQAl;
                                /**
             * 卷烟系统外销售量年累计（万支）;指销售给零售户的卷烟量的年累计量，单位万支
             */

    @Field(value = "卷烟系统外销售量年累计（万支）;指销售给零售户的卷烟量的年累计量，单位万支", name = "卷烟系统外销售量年累计（万支）;指销售给零售户的卷烟量的年累计量，单位万支")
    private BigDecimal md03Cgt10thComCoeSaleQtyYA;
                                /**
             * 卷烟系统外销售额年累计（万元）;指卷烟销售给零售户的销售额的年累计量，单位万元
             */

    @Field(value = "卷烟系统外销售额年累计（万元）;指卷烟销售给零售户的销售额的年累计量，单位万元", name = "卷烟系统外销售额年累计（万元）;指卷烟销售给零售户的销售额的年累计量，单位万元")
    private BigDecimal md03Cgt10thComCoeSaleAmtYA;
                                /**
             * 卷烟系统外销售量同期年累计（万支）;指销售给零售户的卷烟量的同期年累计量，单位万支
             */

    @Field(value = "卷烟系统外销售量同期年累计（万支）;指销售给零售户的卷烟量的同期年累计量，单位万支", name = "卷烟系统外销售量同期年累计（万支）;指销售给零售户的卷烟量的同期年累计量，单位万支")
    private BigDecimal md03Cgt10thComCoeSaleQtyYAl;
                                /**
             * 卷烟系统外销售额同期年累计（万元）;指商业公司销售给零售户的卷烟的金额的同期年累计量，包括国产卷烟、进口卷烟。单位万元
             */

    @Field(value = "卷烟系统外销售额同期年累计（万元）;指商业公司销售给零售户的卷烟的金额的同期年累计量，包括国产卷烟、进口卷烟。单位万元", name = "卷烟系统外销售额同期年累计（万元）;指商业公司销售给零售户的卷烟的金额的同期年累计量，包括国产卷烟、进口卷烟。单位万元")
    private BigDecimal md03Cgt10thComCoeSaleAmtYAl;
                                /**
             * 卷烟从工业购进量;指商业企业从工业企业购进的卷烟量，单位万支
             */

    @Field(value = "卷烟从工业购进量;指商业企业从工业企业购进的卷烟量，单位万支", name = "卷烟从工业购进量;指商业企业从工业企业购进的卷烟量，单位万支")
    private BigDecimal ma02CgtComFromIndBuyQty;
                                /**
             * 卷烟从工业购进额;指商业企业从工业企业购进卷烟的金额，单位万元
             */

    @Field(value = "卷烟从工业购进额;指商业企业从工业企业购进卷烟的金额，单位万元", name = "卷烟从工业购进额;指商业企业从工业企业购进卷烟的金额，单位万元")
    private BigDecimal ma02CgtComFromIndBuyAmt;
                                /**
             * 卷烟从工业购进量同期;指商业企业从工业企业购进的卷烟量的同期量，单位万支
             */

    @Field(value = "卷烟从工业购进量同期;指商业企业从工业企业购进的卷烟量的同期量，单位万支", name = "卷烟从工业购进量同期;指商业企业从工业企业购进的卷烟量的同期量，单位万支")
    private BigDecimal ma02CgtComFromIndBuyQtyL;
                                /**
             * 卷烟从工业购进额同期;指商业企业从工业企业购进卷烟的金额的同期量，单位万元
             */

    @Field(value = "卷烟从工业购进额同期;指商业企业从工业企业购进卷烟的金额的同期量，单位万元", name = "卷烟从工业购进额同期;指商业企业从工业企业购进卷烟的金额的同期量，单位万元")
    private BigDecimal ma02CgtComFromIndBuyAmtL;
                                /**
             * 卷烟从工业购进量周累计;指商业企业从工业企业购进的卷烟量的周累计量，单位万支
             */

    @Field(value = "卷烟从工业购进量周累计;指商业企业从工业企业购进的卷烟量的周累计量，单位万支", name = "卷烟从工业购进量周累计;指商业企业从工业企业购进的卷烟量的周累计量，单位万支")
    private BigDecimal mc04CgtComFromIndBuyQtyWA;
                                /**
             * 卷烟从工业购进额周累计;指商业企业从工业企业购进卷烟的金额的周累计量，单位万元
             */

    @Field(value = "卷烟从工业购进额周累计;指商业企业从工业企业购进卷烟的金额的周累计量，单位万元", name = "卷烟从工业购进额周累计;指商业企业从工业企业购进卷烟的金额的周累计量，单位万元")
    private BigDecimal mc04CgtComFromIndBuyAmtWA;
                                /**
             * 卷烟从工业购进量同期周累计;指商业企业从工业企业购进的卷烟量的同期周累计量，单位万支
             */

    @Field(value = "卷烟从工业购进量同期周累计;指商业企业从工业企业购进的卷烟量的同期周累计量，单位万支", name = "卷烟从工业购进量同期周累计;指商业企业从工业企业购进的卷烟量的同期周累计量，单位万支")
    private BigDecimal mc04CgtComFromIndBuyQtyWAl;
                                /**
             * 卷烟从工业购进额同期周累计;指商业企业从工业企业购进的卷烟量的同期周累计量，单位万支
             */

    @Field(value = "卷烟从工业购进额同期周累计;指商业企业从工业企业购进的卷烟量的同期周累计量，单位万支", name = "卷烟从工业购进额同期周累计;指商业企业从工业企业购进的卷烟量的同期周累计量，单位万支")
    private BigDecimal mc04CgtComFromIndBuyAmtWAl;
                                /**
             * 卷烟从工业购进量月累计;指商业企业从工业企业购进的卷烟量的月累计量，单位万支
             */

    @Field(value = "卷烟从工业购进量月累计;指商业企业从工业企业购进的卷烟量的月累计量，单位万支", name = "卷烟从工业购进量月累计;指商业企业从工业企业购进的卷烟量的月累计量，单位万支")
    private BigDecimal ma02CgtComFromIndBuyQtyMA;
                                /**
             * 卷烟从工业购进额月累计;指商业企业从工业企业购进卷烟的金额的月累计量，单位万元
             */

    @Field(value = "卷烟从工业购进额月累计;指商业企业从工业企业购进卷烟的金额的月累计量，单位万元", name = "卷烟从工业购进额月累计;指商业企业从工业企业购进卷烟的金额的月累计量，单位万元")
    private BigDecimal ma02CgtComFromIndBuyAmtMA;
                                /**
             * 卷烟从工业购进量同期月累计;指商业企业从工业企业购进的卷烟量的同期月累计量，单位万支
             */

    @Field(value = "卷烟从工业购进量同期月累计;指商业企业从工业企业购进的卷烟量的同期月累计量，单位万支", name = "卷烟从工业购进量同期月累计;指商业企业从工业企业购进的卷烟量的同期月累计量，单位万支")
    private BigDecimal ma02CgtComFromIndBuyQtyMAl;
                                /**
             * 卷烟从工业购进额同期月累计;指商业企业从工业企业购进卷烟的金额的同期月累计量，单位万元
             */

    @Field(value = "卷烟从工业购进额同期月累计;指商业企业从工业企业购进卷烟的金额的同期月累计量，单位万元", name = "卷烟从工业购进额同期月累计;指商业企业从工业企业购进卷烟的金额的同期月累计量，单位万元")
    private BigDecimal ma02CgtComFromIndBuyAmtMAl;
                                /**
             * 卷烟从工业购进量季累计;指商业企业从工业企业购进的卷烟量的季累计量，单位万支
             */

    @Field(value = "卷烟从工业购进量季累计;指商业企业从工业企业购进的卷烟量的季累计量，单位万支", name = "卷烟从工业购进量季累计;指商业企业从工业企业购进的卷烟量的季累计量，单位万支")
    private BigDecimal ma02CgtComFromIndBuyQtyQA;
                                /**
             * 卷烟从工业购进额季累计;指商业企业从工业企业购进卷烟的金额的季累计量，单位万元
             */

    @Field(value = "卷烟从工业购进额季累计;指商业企业从工业企业购进卷烟的金额的季累计量，单位万元", name = "卷烟从工业购进额季累计;指商业企业从工业企业购进卷烟的金额的季累计量，单位万元")
    private BigDecimal ma02CgtComFromIndBuyAmtQA;
                                /**
             * 卷烟从工业购进量同期季累计;指商业企业从工业企业购进的卷烟量的同期季累计量，单位万支
             */

    @Field(value = "卷烟从工业购进量同期季累计;指商业企业从工业企业购进的卷烟量的同期季累计量，单位万支", name = "卷烟从工业购进量同期季累计;指商业企业从工业企业购进的卷烟量的同期季累计量，单位万支")
    private BigDecimal ma02CgtComFromIndBuyQtyQAl;
                                /**
             * 卷烟从工业购进额同期季累计;指商业企业从工业企业购进卷烟的金额的同期季累计量，单位万元
             */

    @Field(value = "卷烟从工业购进额同期季累计;指商业企业从工业企业购进卷烟的金额的同期季累计量，单位万元", name = "卷烟从工业购进额同期季累计;指商业企业从工业企业购进卷烟的金额的同期季累计量，单位万元")
    private BigDecimal ma02CgtComFromIndBuyAmtQAl;
                                /**
             * 卷烟从工业购进量年累计;指商业企业从工业企业购进的卷烟量的年累计，单位万支
             */

    @Field(value = "卷烟从工业购进量年累计;指商业企业从工业企业购进的卷烟量的年累计，单位万支", name = "卷烟从工业购进量年累计;指商业企业从工业企业购进的卷烟量的年累计，单位万支")
    private BigDecimal ma02CgtComFromIndBuyQtyYA;
                                /**
             * 卷烟从工业购进额年累计;指商业企业从工业企业购进卷烟的金额的年累计量，单位万元
             */

    @Field(value = "卷烟从工业购进额年累计;指商业企业从工业企业购进卷烟的金额的年累计量，单位万元", name = "卷烟从工业购进额年累计;指商业企业从工业企业购进卷烟的金额的年累计量，单位万元")
    private BigDecimal ma02CgtComFromIndBuyAmtYA;
                                /**
             * 卷烟从工业购进量同期年累计;指商业企业从工业企业购进的卷烟量的同期年累计量，单位万支
             */

    @Field(value = "卷烟从工业购进量同期年累计;指商业企业从工业企业购进的卷烟量的同期年累计量，单位万支", name = "卷烟从工业购进量同期年累计;指商业企业从工业企业购进的卷烟量的同期年累计量，单位万支")
    private BigDecimal ma02CgtComFromIndBuyQtyYAl;
                                /**
             * 卷烟从工业购进额同期年累计;指商业企业从工业企业购进卷烟的金额的同期年累计量，单位万元
             */

    @Field(value = "卷烟从工业购进额同期年累计;指商业企业从工业企业购进卷烟的金额的同期年累计量，单位万元", name = "卷烟从工业购进额同期年累计;指商业企业从工业企业购进卷烟的金额的同期年累计量，单位万元")
    private BigDecimal ma02CgtComFromIndBuyAmtYAl;
                                /**
             * 日初卷烟库存数量（万支）;商业卷烟日初库存数量
             */

    @Field(value = "日初卷烟库存数量（万支）;商业卷烟日初库存数量", name = "日初卷烟库存数量（万支）;商业卷烟日初库存数量")
    private BigDecimal mc04MgCgt10thIodStkQty;
                                /**
             * 日初卷烟库存数量同期（万支）;记录商业卷烟日初库存数量同期
             */

    @Field(value = "日初卷烟库存数量同期（万支）;记录商业卷烟日初库存数量同期", name = "日初卷烟库存数量同期（万支）;记录商业卷烟日初库存数量同期")
    private BigDecimal mc04MgCgt10thIodStkQtyL;
                                /**
             * 日末卷烟库存数量（万支）;记录日末卷烟库存数量
             */

    @Field(value = "日末卷烟库存数量（万支）;记录日末卷烟库存数量", name = "日末卷烟库存数量（万支）;记录日末卷烟库存数量")
    private BigDecimal mc04MgCgt10thEodStkQty;
                                /**
             * 日末卷烟库存数量同期（万支）;记录商业卷烟日末库存数量同期
             */

    @Field(value = "日末卷烟库存数量同期（万支）;记录商业卷烟日末库存数量同期", name = "日末卷烟库存数量同期（万支）;记录商业卷烟日末库存数量同期")
    private BigDecimal mc04MgCgt10thEodStkQtyL;
                                /**
             * 日初卷烟库存金额（万元）;商业卷烟日初库存金额
             */

    @Field(value = "日初卷烟库存金额（万元）;商业卷烟日初库存金额", name = "日初卷烟库存金额（万元）;商业卷烟日初库存金额")
    private BigDecimal mc04MgCgt10thIodStkAmt;
                                /**
             * 日初卷烟库存金额同期（万元）;记录商业卷烟日初库存金额同期
             */

    @Field(value = "日初卷烟库存金额同期（万元）;记录商业卷烟日初库存金额同期", name = "日初卷烟库存金额同期（万元）;记录商业卷烟日初库存金额同期")
    private BigDecimal mc04MgCgt10thIodStkAmtL;
                                /**
             * 日末卷烟库存金额（万元）;记录日末卷烟库存金额
             */

    @Field(value = "日末卷烟库存金额（万元）;记录日末卷烟库存金额", name = "日末卷烟库存金额（万元）;记录日末卷烟库存金额")
    private BigDecimal mc04MgCgt10thEodStkAmt;
                                /**
             * 日末卷烟库存金额同期（万元）;记录商业卷烟日末库存金额同期
             */

    @Field(value = "日末卷烟库存金额同期（万元）;记录商业卷烟日末库存金额同期", name = "日末卷烟库存金额同期（万元）;记录商业卷烟日末库存金额同期")
    private BigDecimal mc04MgCgt10thEodStkAmtL;
                                /**
             * 周初卷烟库存数量（万支）;记录商业卷烟周初库存数量
             */

    @Field(value = "周初卷烟库存数量（万支）;记录商业卷烟周初库存数量", name = "周初卷烟库存数量（万支）;记录商业卷烟周初库存数量")
    private BigDecimal mc04MgCgt10thIowStkQty;
                                /**
             * 周初卷烟库存数量同期（万支）;记录商业卷烟周初库存数量同期
             */

    @Field(value = "周初卷烟库存数量同期（万支）;记录商业卷烟周初库存数量同期", name = "周初卷烟库存数量同期（万支）;记录商业卷烟周初库存数量同期")
    private BigDecimal mc04MgCgt10thIowStkQtyL;
                                /**
             * 周初卷烟库存金额（万元）;记录商业卷烟周初库存金额
             */

    @Field(value = "周初卷烟库存金额（万元）;记录商业卷烟周初库存金额", name = "周初卷烟库存金额（万元）;记录商业卷烟周初库存金额")
    private BigDecimal mc04MgCgt10thIowStkAmt;
                                /**
             * 周初卷烟库存金额同期（万元）;记录商业卷烟周初库存金额同期
             */

    @Field(value = "周初卷烟库存金额同期（万元）;记录商业卷烟周初库存金额同期", name = "周初卷烟库存金额同期（万元）;记录商业卷烟周初库存金额同期")
    private BigDecimal mc04MgCgt10thIowStkAmtL;
                                /**
             * 月初卷烟库存数量（万支）;记录商业卷烟月初库存数量
             */

    @Field(value = "月初卷烟库存数量（万支）;记录商业卷烟月初库存数量", name = "月初卷烟库存数量（万支）;记录商业卷烟月初库存数量")
    private BigDecimal mc04MgCgt10thIomStkQty;
                                /**
             * 月初卷烟库存数量同期（万支）;记录商业卷烟月初库存数量同期
             */

    @Field(value = "月初卷烟库存数量同期（万支）;记录商业卷烟月初库存数量同期", name = "月初卷烟库存数量同期（万支）;记录商业卷烟月初库存数量同期")
    private BigDecimal mc04MgCgt10thIomStkQtyL;
                                /**
             * 月初卷烟库存金额（万元）;记录商业卷烟月初库存金额
             */

    @Field(value = "月初卷烟库存金额（万元）;记录商业卷烟月初库存金额", name = "月初卷烟库存金额（万元）;记录商业卷烟月初库存金额")
    private BigDecimal mc04MgCgt10thIomStkAmt;
                                /**
             * 月初卷烟库存金额同期（万元）;记录商业卷烟月初库存金额同期
             */

    @Field(value = "月初卷烟库存金额同期（万元）;记录商业卷烟月初库存金额同期", name = "月初卷烟库存金额同期（万元）;记录商业卷烟月初库存金额同期")
    private BigDecimal mc04MgCgt10thIomStkAmtL;
                                /**
             * 季度初卷烟库存数量（万支）;记录商业卷烟季度初库存数量
             */

    @Field(value = "季度初卷烟库存数量（万支）;记录商业卷烟季度初库存数量", name = "季度初卷烟库存数量（万支）;记录商业卷烟季度初库存数量")
    private BigDecimal mc04MgCgt10thIoqStkQty;
                                /**
             * 季度初卷烟库存数量同期（万支）;记录商业卷烟季度初库存数量同期
             */

    @Field(value = "季度初卷烟库存数量同期（万支）;记录商业卷烟季度初库存数量同期", name = "季度初卷烟库存数量同期（万支）;记录商业卷烟季度初库存数量同期")
    private BigDecimal mc04MgCgt10thIoqStkQtyL;
                                /**
             * 季度初卷烟库存金额（万元）;记录商业卷烟季度初库存金额
             */

    @Field(value = "季度初卷烟库存金额（万元）;记录商业卷烟季度初库存金额", name = "季度初卷烟库存金额（万元）;记录商业卷烟季度初库存金额")
    private BigDecimal mc04MgCgt10thIoqStkAmt;
                                /**
             * 季度初卷烟库存金额同期（万元）;记录商业卷烟季度初库存金额同期
             */

    @Field(value = "季度初卷烟库存金额同期（万元）;记录商业卷烟季度初库存金额同期", name = "季度初卷烟库存金额同期（万元）;记录商业卷烟季度初库存金额同期")
    private BigDecimal mc04MgCgt10thIoqStkAmtL;
                                /**
             * 年初卷烟库存数量（万支）;记录商业卷烟年初库存数量
             */

    @Field(value = "年初卷烟库存数量（万支）;记录商业卷烟年初库存数量", name = "年初卷烟库存数量（万支）;记录商业卷烟年初库存数量")
    private BigDecimal mc04MgCgt10thIoyStkQty;
                                /**
             * 年初卷烟库存数量同期（万支）;记录商业卷烟年度初库存数量同期
             */

    @Field(value = "年初卷烟库存数量同期（万支）;记录商业卷烟年度初库存数量同期", name = "年初卷烟库存数量同期（万支）;记录商业卷烟年度初库存数量同期")
    private BigDecimal mc04MgCgt10thIoyStkQtyL;
                                /**
             * 年初卷烟库存金额（万元）;记录商业卷烟年度初库存金额
             */

    @Field(value = "年初卷烟库存金额（万元）;记录商业卷烟年度初库存金额", name = "年初卷烟库存金额（万元）;记录商业卷烟年度初库存金额")
    private BigDecimal mc04MgCgt10thIoyStkAmt;
                                /**
             * 年初卷烟库存金额同期（万元）;记录商业卷烟年度初库存金额同期
             */

    @Field(value = "年初卷烟库存金额同期（万元）;记录商业卷烟年度初库存金额同期", name = "年初卷烟库存金额同期（万元）;记录商业卷烟年度初库存金额同期")
    private BigDecimal mc04MgCgt10thIoyStkAmtL;
                                /**
             * 滚动前3个自然日日均销量;记录商业卷烟滚动前3个自然日内平均每天销量
             */

    @Field(value = "滚动前3个自然日日均销量;记录商业卷烟滚动前3个自然日内平均每天销量", name = "滚动前3个自然日日均销量;记录商业卷烟滚动前3个自然日内平均每天销量")
    private BigDecimal mc04CgtRoll3DaysAvgSaleQty;
                                /**
             * 滚动前7个自然日日均销量;记录商业卷烟滚动前7个自然日内平均每天销量
             */

    @Field(value = "滚动前7个自然日日均销量;记录商业卷烟滚动前7个自然日内平均每天销量", name = "滚动前7个自然日日均销量;记录商业卷烟滚动前7个自然日内平均每天销量")
    private BigDecimal mc04CgtRoll7DaysAvgSaleQty;
                                /**
             * 滚动前14个自然日日均销量;记录商业卷烟滚动前14个自然日内平均每天销量
             */

    @Field(value = "滚动前14个自然日日均销量;记录商业卷烟滚动前14个自然日内平均每天销量", name = "滚动前14个自然日日均销量;记录商业卷烟滚动前14个自然日内平均每天销量")
    private BigDecimal mc04CgtRoll14DaysAvgSaleQty;
                                /**
             * 滚动前21个自然日日均销量;记录商业卷烟滚动前21个自然日内平均每天销量
             */

    @Field(value = "滚动前21个自然日日均销量;记录商业卷烟滚动前21个自然日内平均每天销量", name = "滚动前21个自然日日均销量;记录商业卷烟滚动前21个自然日内平均每天销量")
    private BigDecimal mc04CgtRoll21DaysAvgSaleQty;
                                /**
             * 滚动前28个自然日日均销量;记录商业卷烟滚动前28个自然日内平均每天销量
             */

    @Field(value = "滚动前28个自然日日均销量;记录商业卷烟滚动前28个自然日内平均每天销量", name = "滚动前28个自然日日均销量;记录商业卷烟滚动前28个自然日内平均每天销量")
    private BigDecimal mc04CgtRoll28DaysAvgSaleQty;
                                /**
             * 滚动前30个自然日日均销量;记录商业卷烟滚动前30个自然日内平均每天销量
             */

    @Field(value = "滚动前30个自然日日均销量;记录商业卷烟滚动前30个自然日内平均每天销量", name = "滚动前30个自然日日均销量;记录商业卷烟滚动前30个自然日内平均每天销量")
    private BigDecimal mc04CgtRoll30DaysAvgSaleQty;
                                /**
             * 滚动前3个工作日日均销量;记录商业卷烟滚动前3个工作日内平均每天销量
             */

    @Field(value = "滚动前3个工作日日均销量;记录商业卷烟滚动前3个工作日内平均每天销量", name = "滚动前3个工作日日均销量;记录商业卷烟滚动前3个工作日内平均每天销量")
    private BigDecimal mc04CgtRoll3WorkdaysAvgSaleQty;
                                /**
             * 滚动前7个工作日日均销量;记录商业卷烟滚动前7个工作日内平均每天销量
             */

    @Field(value = "滚动前7个工作日日均销量;记录商业卷烟滚动前7个工作日内平均每天销量", name = "滚动前7个工作日日均销量;记录商业卷烟滚动前7个工作日内平均每天销量")
    private BigDecimal mc04CgtRoll7WorkdaysAvgSaleQty;
                                /**
             * 滚动前14个工作日日均销量;记录商业卷烟滚动前14个工作日内平均每天销量
             */

    @Field(value = "滚动前14个工作日日均销量;记录商业卷烟滚动前14个工作日内平均每天销量", name = "滚动前14个工作日日均销量;记录商业卷烟滚动前14个工作日内平均每天销量")
    private BigDecimal mc04CgtRoll14WorkdaysAvgSaleQty;
                                /**
             * 滚动前21个工作日日均销量;记录商业卷烟滚动前21个工作日内平均每天销量
             */

    @Field(value = "滚动前21个工作日日均销量;记录商业卷烟滚动前21个工作日内平均每天销量", name = "滚动前21个工作日日均销量;记录商业卷烟滚动前21个工作日内平均每天销量")
    private BigDecimal mc04CgtRoll21WorkdaysAvgSaleQty;
                                /**
             * 滚动前28个工作日日均销量;记录商业卷烟滚动前28个工作日内平均每天销量
             */

    @Field(value = "滚动前28个工作日日均销量;记录商业卷烟滚动前28个工作日内平均每天销量", name = "滚动前28个工作日日均销量;记录商业卷烟滚动前28个工作日内平均每天销量")
    private BigDecimal mc04CgtRoll28WorkdaysAvgSaleQty;
                                /**
             * 滚动前30个工作日日均销量;记录商业卷烟滚动前30个工作日内平均每天销量
             */

    @Field(value = "滚动前30个工作日日均销量;记录商业卷烟滚动前30个工作日内平均每天销量", name = "滚动前30个工作日日均销量;记录商业卷烟滚动前30个工作日内平均每天销量")
    private BigDecimal mc04CgtRoll30WorkdaysAvgSaleQty;
                                /**
             * 数据汇总日期;记录汇总结果生成日期
             */

    @Field(value = "数据汇总日期;记录汇总结果生成日期", name = "数据汇总日期;记录汇总结果生成日期")
    private String mc04DataSummaryDate;
                

}
