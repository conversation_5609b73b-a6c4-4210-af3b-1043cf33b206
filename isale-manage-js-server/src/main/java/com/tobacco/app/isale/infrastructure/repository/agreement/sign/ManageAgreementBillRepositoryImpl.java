/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.agreement.sign;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.constant.CommonConstants;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.model.agreement.sign.ManageAgreementBill;
import com.tobacco.app.isale.domain.model.agreement.sign.ManageAgreementBillItem;
import com.tobacco.app.isale.domain.model.agreement.sign.ManageIndXyOrgCgtPurchSaleStkDay;
import com.tobacco.app.isale.domain.repository.agreement.sign.ManageAgreementBillRepository;
import com.tobacco.app.isale.infrastructure.converter.agreement.sign.Mc04IslmcXyBillDOToManageAgreementBillConverter;
import com.tobacco.app.isale.infrastructure.converter.agreement.sign.Mc04IslmcXyBillItemDOToManageAgreementBillItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcXyBillDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcXyBillItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IndXyOrgCgtPurchSaleStkDayService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcXyBillItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcXyBillService;
import com.tobacco.app.isale.tools.utils.CommodityUtil;
import com.tobacco.sc.icommodity.dto.common.constant.dto.item.IccItemDetailDTO;
import com.tobacco.sc.icommodity.dto.common.constant.dto.product.IccProductDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/04/22 10:42
 * @description : 销售管理-协议管理(卷烟)-集中交易
 */
@Slf4j
@Component("ISaleManageAgreementBillRepositoryImplDORepository")
public class ManageAgreementBillRepositoryImpl implements ManageAgreementBillRepository {

    private Mc04IndXyOrgCgtPurchSaleStkDayService indXyOrgCgtPurchSaleStkDayService;

    @Autowired
    public void setIndXyOrgCgtPurchSaleStkDayService(
            Mc04IndXyOrgCgtPurchSaleStkDayService indXyOrgCgtPurchSaleStkDayService) {
        this.indXyOrgCgtPurchSaleStkDayService = indXyOrgCgtPurchSaleStkDayService;
    }

    private Mc04IslmcXyBillService mc04IslmcXyBillService;

    @Autowired
    public void setMc04IslmcXyBillService(Mc04IslmcXyBillService mc04IslmcXyBillService) {
        this.mc04IslmcXyBillService = mc04IslmcXyBillService;
    }

    private Mc04IslmcXyBillItemService mc04IslmcXyBillItemService;

    @Autowired
    public void setMc04IslmcXyBillItemService(Mc04IslmcXyBillItemService mc04IslmcXyBillItemService) {
        this.mc04IslmcXyBillItemService = mc04IslmcXyBillItemService;
    }

    /**
     * 获取协议挂单上报数据 包含从表
     *
     * @param cgtType  卷烟交易业务类型代码
     * @param comId    商家编码
     * @param dateCode 卷烟协议周期
     * @return 协议挂单上报数据
     */
    @Override
    public ManageAgreementBill getAgreementBillWithItem(String cgtType, String comId, String dateCode) {
        return getAgreementBillWithItem(cgtType, comId, dateCode, CommonConstants.YES);
    }

    /**
     * 获取协议挂单上报数据 包含从表
     *
     * @param cgtType         卷烟交易业务类型代码
     * @param comId           商家编码
     * @param dateCode        卷烟协议周期
     * @param isMergeCoreData 是否合并中心数据
     * @return 协议挂单上报数据
     */
    @Override
    public ManageAgreementBill getAgreementBillWithItem(String cgtType, String comId, String dateCode,
                                                        String isMergeCoreData) {
        //从数据库取数据
        ManageAgreementBill agreementBill = getAgreementBill(cgtType, comId, dateCode);
        if (ObjectUtil.isNull(agreementBill)) {
            return null;
        }
        if (CommonConstants.NO.equals(isMergeCoreData)) {
            return agreementBill;
        }
        Assert.notEmpty(agreementBill.getManageAgreementBillItemList(),
                () -> new CustomException("协议挂单上报数据从表数据丢失"));
        List<String> cartonCodeList = new ArrayList<>();
        List<String> acTwoLevelCigCodeList = new ArrayList<>();

        agreementBill.getManageAgreementBillItemList().forEach(item -> {
            cartonCodeList.add(item.getAcCgtCartonCode());
            acTwoLevelCigCodeList.add(item.getAcTwoLevelCigCode());
        });
        // 获取商品详细信息
        Map<String, IccProductDetailDTO> productCodeMap = CommodityUtil.getIccProductDetailDtoMap(cartonCodeList);
        // 获取二级牌号信息
        Map<String, IccItemDetailDTO> itemCodeMap = CommodityUtil.getIccItemDetailDtoMap(acTwoLevelCigCodeList);

        List<ManageAgreementBillItem> manageAgreementBillItemList =
                getManageAgreementBillItemList(agreementBill.getManageAgreementBillItemList(), productCodeMap, itemCodeMap);
        agreementBill.setManageAgreementBillItemList(manageAgreementBillItemList);
        return agreementBill;
    }

    /**
     * @param baComOrgCode 地市编码
     * @param periodCode   卷烟协议周期
     * @return List<ManageIndXyOrgCgtPurchSaleStkDay>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-13 19:05:15
     * @description : 获取前半年销售量
     */
    @Override
    public List<ManageIndXyOrgCgtPurchSaleStkDay> getFirstHalfYearSalesGroupByProduct(
            String baComOrgCode, String periodCode) {
        return indXyOrgCgtPurchSaleStkDayService.getFirstHalfYearSalesGroupByProduct(baComOrgCode, periodCode);
    }

    /**
     * @param cgtType  业务类型代码
     * @param dateCode 卷烟协议周期
     * @return List<ManageAgreementBill>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-14 16:36:27
     * @description : 获取协议挂单上报数据
     */
    @Override
    public List<ManageAgreementBill> getAgreementBillList(String cgtType, String dateCode) {

        List<Mc04IslmcXyBillDO> xyBillDOList = mc04IslmcXyBillService.lambdaQuery()
                .eq(Mc04IslmcXyBillDO::getMa02TobaProdTradeTypeCode, cgtType)
                .eq(Mc04IslmcXyBillDO::getMc04CgtXyPeriodCode, dateCode)
                .eq(Mc04IslmcXyBillDO::getIcomCode, IcomUtils.getIcomCode())
                .list();
        if (CollUtil.isEmpty(xyBillDOList)) {
            return Collections.emptyList();
        }
        return Mc04IslmcXyBillDOToManageAgreementBillConverter.INSTANCE.converterDosToModels(xyBillDOList);
    }

    /**
     * @param manageAgreementBill 协议挂单上报数据
     * @return boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-14 16:36:27
     * @description : 保存协议挂单上报数据
     */
    @Override
    public Boolean saveAgreementBill(ManageAgreementBill manageAgreementBill) {

        //保存主表
        Mc04IslmcXyBillDO mc04IslmcXyBillDO =
                Mc04IslmcXyBillDOToManageAgreementBillConverter.INSTANCE.converterModelToDo(manageAgreementBill);
        boolean flag = mc04IslmcXyBillService.saveOrUpdate(mc04IslmcXyBillDO);
        if (!flag) {
            return false;
        }
        //删除从表
        flag = mc04IslmcXyBillItemService.lambdaUpdate()
                .eq(Mc04IslmcXyBillItemDO::getMc04CgtXyId, mc04IslmcXyBillDO.getMc04CgtXyId())
                .remove();
        if (!flag) {
            return false;
        }
        //保存从表
        List<Mc04IslmcXyBillItemDO> mc04IslmcXyBillItemDos =
                Mc04IslmcXyBillItemDOToManageAgreementBillItemConverter.INSTANCE
                        .converterModelsToDos(manageAgreementBill.getManageAgreementBillItemList());
        return mc04IslmcXyBillItemService.saveBatch(mc04IslmcXyBillItemDos);
    }

    /**
     * @param mc04CgtXyId 卷烟协议编号
     * @param status      当前状态
     * @param nextStatus  下一状态
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-15 09:49:46
     * @description : 更新协议挂单上报数据状态
     */
    @Override
    public Boolean updateIndDemandFoStatus(String mc04CgtXyId, String status, String nextStatus) {

        Assert.notBlank(mc04CgtXyId, () -> new CustomException("协议挂单上报数据ID不能为空"));
        return mc04IslmcXyBillService.lambdaUpdate()
                .in(Mc04IslmcXyBillDO::getMc04CgtXyId, mc04CgtXyId)
                .eq(Mc04IslmcXyBillDO::getMc04CgtXyStatus, status)
                .set(Mc04IslmcXyBillDO::getMc04CgtXyStatus, nextStatus)
                .update();
    }

    /**
     * @param cgtType  业务类型
     * @param comId    协议单位编码
     * @param dateCode 周期
     * @return Collection<IndDemandFoDTO>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-06 15:38:08
     * @description : 从订单中心获取数据
     */

    private ManageAgreementBill getAgreementBill(String cgtType, String comId, String dateCode) {
        //查询主表
        Mc04IslmcXyBillDO xyBillDO = mc04IslmcXyBillService.lambdaQuery()
                .eq(Mc04IslmcXyBillDO::getMa02TobaProdTradeTypeCode, cgtType)
                .eq(Mc04IslmcXyBillDO::getBaComOrgCode, comId)
                .eq(Mc04IslmcXyBillDO::getMc04CgtXyPeriodCode, dateCode)
                .eq(Mc04IslmcXyBillDO::getIcomCode, IcomUtils.getIcomCode())
                .one();

        if (ObjectUtil.isNull(xyBillDO)) {
            return null;
        }

        ManageAgreementBill manageAgreementBill =
                Mc04IslmcXyBillDOToManageAgreementBillConverter.INSTANCE.converterDoToModel(xyBillDO);


        //查询明细
        List<Mc04IslmcXyBillItemDO> xyItemDOList = mc04IslmcXyBillItemService.lambdaQuery()
                .eq(Mc04IslmcXyBillItemDO::getMc04CgtXyId, xyBillDO.getMc04CgtXyId())
                .list();
        Assert.notEmpty(xyItemDOList, () -> new CustomException("协议挂单上报数据从表数据丢失"));
        manageAgreementBill.setManageAgreementBillItemList(
                Mc04IslmcXyBillItemDOToManageAgreementBillItemConverter.INSTANCE.converterDosToModels(xyItemDOList));
        return manageAgreementBill;
    }


    /**
     * @param agreementBillItemList 从表信息
     * @param productCodeMap        商品详细信息
     * @param itemCodeMap           二级牌号信息
     * @return List<IndDemandFoItem>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-06 15:19:51
     * @description : 转化意向上报数据从表信息
     */

    private static List<ManageAgreementBillItem> getManageAgreementBillItemList(
            List<ManageAgreementBillItem> agreementBillItemList, Map<String, IccProductDetailDTO> productCodeMap,
            Map<String, IccItemDetailDTO> itemCodeMap) {
        agreementBillItemList.forEach(agreementBillItem -> {
            IccProductDetailDTO product = productCodeMap.get(agreementBillItem.getAcCgtCartonCode());
            Assert.notNull(product, () -> new CustomException("商品中心未获取到" +
                    agreementBillItem.getAcCgtCartonCode() + "的商品信息"));
            agreementBillItem.setAcCgtName(product.getProductName());
            agreementBillItem.setAcCigRangeCode(product.getAcCigRangeCode());
            agreementBillItem.setAcCgtTaxAllotPrice(
                    StrUtil.isNotBlank(product.getAcMateTaxTranPr()) ?
                            new BigDecimal(product.getAcMateTaxTranPr()) : null);
            //todo 少个排序
            IccItemDetailDTO item = itemCodeMap.get(agreementBillItem.getAcTwoLevelCigCode());
            Assert.notNull(item, () -> new CustomException("商品中心未获取到" +
                    agreementBillItem.getAcTwoLevelCigCode() + "的二级牌号信息"));
            agreementBillItem.setAcTwoLevelCigName(item.getAcTwoLevelCigName());
        });
        return agreementBillItemList.stream()
                .sorted(Comparator.comparing(ManageAgreementBillItem::getSeq))
                .collect(Collectors.toList());
    }

}
