/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;



import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmPlanSubjectLineDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmPlanSubjectLineMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmPlanSubjectLineService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: qintian
 * @Since: 2025-07-18
 * @Email: <EMAIL>
 * @Create: 2025-07-18
        */
@Service
public class Mc04IslmPlanSubjectLineServiceImpl extends ServiceImpl<Mc04IslmPlanSubjectLineMapper, Mc04IslmPlanSubjectLineDO> implements Mc04IslmPlanSubjectLineService {

}