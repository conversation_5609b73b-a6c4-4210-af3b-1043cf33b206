/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.cont.whse;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomPage;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.model.cont.whse.IslmcContDelivWhse;
import com.tobacco.app.isale.domain.model.cont.whse.Mc04IslmcFactory;
import com.tobacco.app.isale.domain.repository.cont.whse.IslmcContDelivWhseRepository;
import com.tobacco.app.isale.infrastructure.converter.cont.whse.Mc04IslmcContDelivWhseDOToIslmcContDelivWhseConverter;
import com.tobacco.app.isale.infrastructure.converter.cont.whse.Mc04IslmcFactoryDOTOMc04IslmcFactoryConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcFactoryDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContDelivWhseService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcFactoryService;
import com.tobacco.app.isale.infrastructure.tunnel.database.cont.whse.IslmcContDelivWhseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
/**
 * <AUTHOR> wmd
 * @email : <EMAIL>
 * @create_time : 2025-07-30 09:32:30
 * @description : 销售管理-基础数据-合同发货仓库 infrastructureRepository
 */
@Slf4j
@Component("IslmcContDelivWhseRepositoryImplDORepository")
public class IslmcContDelivWhseRepositoryImpl implements IslmcContDelivWhseRepository {

    @Resource
    private IslmcContDelivWhseMapper islmcContDelivWhseMapper;
    @Autowired
    private Mc04IslmcContDelivWhseService mc04IslmcContDelivWhseService;
    @Autowired
    private Mc04IslmcFactoryService mc04IslmcFactoryService;

    /**
     * 查询合同仓库分页数据
     * @param islmcContDelivWhse 查询参数
     * @return Page<IslmcContDelivWhseDTO>
     * <AUTHOR> wmd
     */
    @Override
    public Page<IslmcContDelivWhse> page(IslmcContDelivWhse islmcContDelivWhse){
        CustomPage<IslmcContDelivWhse> pageQuery = new CustomPage<>(
                islmcContDelivWhse.getOffset(),
                islmcContDelivWhse.getLimit(),
                islmcContDelivWhse.getSort(),
                islmcContDelivWhse.getOrder()
        );
        Page<IslmcContDelivWhse> pageResult = islmcContDelivWhseMapper.page(pageQuery,islmcContDelivWhse,IcomUtils.getIcomCode());
        return pageResult;
    }

    /**
     * 查询详情
     * @param md02CgtOutStorehouseCode 仓库代码
     * @return IslmcContDelivWhse
     * <AUTHOR> wmd
     */
    @Override
    public IslmcContDelivWhse detail(String md02CgtOutStorehouseCode){

        QueryWrapper<Mc04IslmcContDelivWhseDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(StrUtil.isNotBlank(md02CgtOutStorehouseCode),Mc04IslmcContDelivWhseDO::getMd02CgtOutStorehouseCode ,md02CgtOutStorehouseCode);
        Mc04IslmcContDelivWhseDO delivWhse = mc04IslmcContDelivWhseService.getOne(wrapper);
        IslmcContDelivWhse  islmcContDelivWhseReturn= Mc04IslmcContDelivWhseDOToIslmcContDelivWhseConverter.INSTANCE
                .converterDoToModel(delivWhse);
        return  islmcContDelivWhseReturn;
    }

    /**
     * 新增
     * @param islmcContDelivWhse 新增参数
     * @return IslmcContDelivWhse
     * <AUTHOR> wmd
     */
    @Override
    public Boolean create(IslmcContDelivWhse islmcContDelivWhse){
        Mc04IslmcContDelivWhseDO mc04IslmcContDelivWhseDO = Mc04IslmcContDelivWhseDOToIslmcContDelivWhseConverter.INSTANCE
                .converterModelToDo(islmcContDelivWhse);
        Boolean b = mc04IslmcContDelivWhseService.save(mc04IslmcContDelivWhseDO);
        return b;
    }

    /**
     * 删除
     * @param md02CgtOutStorehouseCode 仓库代码
     * @return IslmcContDelivWhse
     * <AUTHOR> wmd
     */
    @Override
    public Boolean delete(String md02CgtOutStorehouseCode){
        QueryWrapper<Mc04IslmcContDelivWhseDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(StrUtil.isNotBlank(md02CgtOutStorehouseCode),Mc04IslmcContDelivWhseDO::getMd02CgtOutStorehouseCode ,md02CgtOutStorehouseCode);
        Boolean b = mc04IslmcContDelivWhseService.remove(wrapper);
        return b;
    }

    /**
     * 修改
     * @param islmcContDelivWhse 修改参数
     * @return IslmcContDelivWhse
     * <AUTHOR> wmd
     */
    @Override
    public Boolean update(IslmcContDelivWhse islmcContDelivWhse){
        Mc04IslmcContDelivWhseDO mc04IslmcContDelivWhseDO = Mc04IslmcContDelivWhseDOToIslmcContDelivWhseConverter.INSTANCE
                .converterModelToDo(islmcContDelivWhse);

        Boolean b =  mc04IslmcContDelivWhseService.lambdaUpdate()
                .eq(Mc04IslmcContDelivWhseDO::getMd02CgtOutStorehouseCode, mc04IslmcContDelivWhseDO.getMd02CgtOutStorehouseCode())
                .set(Mc04IslmcContDelivWhseDO::getMd02CgtOutStorehouseName, mc04IslmcContDelivWhseDO.getMd02CgtOutStorehouseName())
                .set(Mc04IslmcContDelivWhseDO::getCbCbLogtWhseNameAbbrev,mc04IslmcContDelivWhseDO.getCbCbLogtWhseNameAbbrev())
                .set(Mc04IslmcContDelivWhseDO::getMd02CgtOutStorehouseAdress,mc04IslmcContDelivWhseDO.getMd02CgtOutStorehouseAdress())
                .set(Mc04IslmcContDelivWhseDO::getCaStorehouseOutLongitude,mc04IslmcContDelivWhseDO.getCaStorehouseOutLongitude())
                .set(Mc04IslmcContDelivWhseDO::getCaStorehouseOutLalitude,mc04IslmcContDelivWhseDO.getCaStorehouseOutLalitude())
                .set(Mc04IslmcContDelivWhseDO::getMc05LogtTransportDelIndWarehouseCode,mc04IslmcContDelivWhseDO.getMc05LogtTransportDelIndWarehouseCode())
                .set(Mc04IslmcContDelivWhseDO::getMd03LogtIcOrderDeliveryWhseCode,mc04IslmcContDelivWhseDO.getMd03LogtIcOrderDeliveryWhseCode())
                .set(Mc04IslmcContDelivWhseDO::getCbLogtWhseBusiType1,mc04IslmcContDelivWhseDO.getCbLogtWhseBusiType1())
                .set(Mc04IslmcContDelivWhseDO::getBaFactOrgCode,mc04IslmcContDelivWhseDO.getBaFactOrgCode())
                .set(Mc04IslmcContDelivWhseDO::getIsUse,mc04IslmcContDelivWhseDO.getIsUse())
                .set(Mc04IslmcContDelivWhseDO::getSeq,mc04IslmcContDelivWhseDO.getSeq())
                .update();
        return b;
    }

    /**
     * 查询生产点列表
     * @param
     * @return List<Mc04IslmcFactory>
     * <AUTHOR> wmd
     */
    @Override
    public List<Mc04IslmcFactory> getFactorys(){
        String icomCode = IcomUtils.getIcomCode();
        String sort = "seq";
        String order = "ascend";
        QueryWrapper<Mc04IslmcFactoryDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(ObjectUtil.isNotNull(icomCode)&&ObjectUtil.isValidIfNumber(icomCode), Mc04IslmcFactoryDO::getIcomCode, icomCode)
                .eq(Mc04IslmcFactoryDO::getIsUse, "1");
        wrapper.orderBy(StrUtil.isNotBlank(sort) && StrUtil.isNotBlank(order), "ascend".equals(order), StrUtil.toUnderlineCase(sort));

        List<Mc04IslmcFactoryDO> list = mc04IslmcFactoryService.list(wrapper);

        return Mc04IslmcFactoryDOTOMc04IslmcFactoryConverter.INSTANCE
                .converterDosToModels(list);
    }
    /**
     * 校验是否已存在
     * @param islmcContDelivWhse 仓库代码
     * @return Boolean
     * <AUTHOR> wmd
     */
    @Override
    public Boolean checkContDelivWhseId(IslmcContDelivWhse islmcContDelivWhse){
        String md02CgtOutStorehouseCode = islmcContDelivWhse.getMd02CgtOutStorehouseCode();
        QueryWrapper<Mc04IslmcContDelivWhseDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(StrUtil.isNotBlank(md02CgtOutStorehouseCode), Mc04IslmcContDelivWhseDO::getMd02CgtOutStorehouseCode, md02CgtOutStorehouseCode);
        List<Mc04IslmcContDelivWhseDO> list = mc04IslmcContDelivWhseService.list(wrapper);
        if(CollectionUtil.isNotEmpty(list)){
            return false;
        }else {
            return true;
        }
    }

}
