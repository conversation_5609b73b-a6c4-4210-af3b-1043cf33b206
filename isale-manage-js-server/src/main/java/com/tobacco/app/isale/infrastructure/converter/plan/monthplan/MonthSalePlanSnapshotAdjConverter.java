/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.monthplan;


import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadj.MonthPlanAdj;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanAdjSnapshotDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jinfuli
 * @Date: 2025/6/10
 * @Description:
 */
@Mapper
public interface MonthSalePlanSnapshotAdjConverter extends StructureBaseConverter<Mc04IslmMonthSalePlanAdjSnapshotDO, MonthPlanAdj> {

    MonthSalePlanSnapshotAdjConverter INSTANCE = Mappers.getMapper(MonthSalePlanSnapshotAdjConverter.class);

}
