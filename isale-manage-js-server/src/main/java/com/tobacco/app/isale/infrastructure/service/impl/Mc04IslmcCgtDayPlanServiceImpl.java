/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtDayPlanDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcCgtDayPlanMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcCgtDayPlanService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: liuwancheng
 * @Since: 2025-07-29
 * @Email: <EMAIL>
 * @Create: 2025-07-29
 */
@Service
public class Mc04IslmcCgtDayPlanServiceImpl extends ServiceImpl<Mc04IslmcCgtDayPlanMapper, Mc04IslmcCgtDayPlanDO> implements Mc04IslmcCgtDayPlanService {

}