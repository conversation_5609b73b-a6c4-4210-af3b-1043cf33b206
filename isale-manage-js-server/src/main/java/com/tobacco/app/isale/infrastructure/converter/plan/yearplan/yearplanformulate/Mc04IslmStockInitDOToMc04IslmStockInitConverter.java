/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.yearplan.yearplanformulate;
import com.tobacco.app.isale.domain.model.plan.yearplan.yearplanformulate.Mc04IslmStockInit;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmStockInitDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * @Author: loongxi
 * @Email: <EMAIL>
 * @Create: 2025-08-06
 */

@Mapper

public interface Mc04IslmStockInitDOToMc04IslmStockInitConverter extends StructureBaseConverter<Mc04IslmStockInitDO, Mc04IslmStockInit> {

        Mc04IslmStockInitDOToMc04IslmStockInitConverter INSTANCE =
            Mappers.getMapper(Mc04IslmStockInitDOToMc04IslmStockInitConverter.class);

}