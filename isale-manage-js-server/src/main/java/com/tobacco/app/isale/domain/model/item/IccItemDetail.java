/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.domain.model.item;

import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.StructureObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/04/28 15:35
 * @description : 卷烟明细
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@StructureObject(
        name = "卷烟明细model",
        desc = "卷烟明细model"
)
@ApiModel(
        value = "卷烟明细model",
        description = "卷烟明细model"
)
public class IccItemDetail {
    @ApiModelProperty(
            value = "卷烟编码",
            name = "卷烟编码"
    )
    @Field(
            value = "卷烟编码",
            name = "卷烟编码"
    )
    private String productCode;
    @ApiModelProperty(
            value = "卷烟名称",
            name = "卷烟名称"
    )
    @Field(
            value = "卷烟名称",
            name = "卷烟名称"
    )
    private String productName;
    @ApiModelProperty(
            value = "卷烟行业编码",
            name = "卷烟行业编码"
    )
    @Field(
            value = "卷烟行业编码",
            name = "卷烟行业编码"
    )
    private String acCigIndustryCode;
    @ApiModelProperty(
            value = "工业公司编码",
            name = "工业公司编码"
    )
    @Field(
            value = "工业公司编码",
            name = "工业公司编码"
    )
    private String providerCustomerId;
    @ApiModelProperty(
            value = "工业公司名称",
            name = "工业公司名称"
    )
    @Field(
            value = "工业公司名称",
            name = "工业公司名称"
    )
    private String providerCustomerName;
    @ApiModelProperty(
            value = "三级牌号生产点编码",
            name = "三级牌号生产点编码"
    )
    @Field(
            value = "三级牌号生产点编码",
            name = "三级牌号生产点编码"
    )
    private String acThrLevelCigProdPointCode;
    @ApiModelProperty(
            value = "三级牌号生产点名称",
            name = "三级牌号生产点名称"
    )
    @Field(
            value = "三级牌号生产点名称",
            name = "三级牌号生产点名称"
    )
    private String acThrLevelCigProdPointName;
    @ApiModelProperty(
            value = "牌号等级",
            name = "牌号等级"
    )
    @Field(
            value = "牌号等级",
            name = "牌号等级"
    )
    private String acLevelCode;
    @ApiModelProperty(
            value = "牌号等级名称",
            name = "牌号等级名称"
    )
    @Field(
            value = "牌号等级名称",
            name = "牌号等级名称"
    )
    private String acLevelName;
    @ApiModelProperty(
            value = "一级牌号编码",
            name = "一级牌号编码"
    )
    @Field(
            value = "一级牌号编码",
            name = "一级牌号编码"
    )
    private String acOneLevelCigCode;
    @ApiModelProperty(
            value = "一级牌号名称",
            name = "一级牌号名称"
    )
    @Field(
            value = "一级牌号名称",
            name = "一级牌号名称"
    )
    private String acOneLevelCigName;
    @ApiModelProperty(
            value = "二级牌号编码",
            name = "二级牌号编码"
    )
    @Field(
            value = "二级牌号编码",
            name = "二级牌号编码"
    )
    private String acTwoLevelCigCode;
    @ApiModelProperty(
            value = "二级牌号名称",
            name = "二级牌号名称"
    )
    @Field(
            value = "二级牌号名称",
            name = "二级牌号名称"
    )
    private String acTwoLevelCigName;
    @ApiModelProperty(
            value = "三级牌号编码",
            name = "三级牌号编码"
    )
    @Field(
            value = "三级牌号编码",
            name = "三级牌号编码"
    )
    private String acThrLevelCigCode;
    @ApiModelProperty(
            value = "三级牌号名称",
            name = "三级牌号名称"
    )
    @Field(
            value = "三级牌号名称",
            name = "三级牌号名称"
    )
    private String acThrLevelCigName;
    @ApiModelProperty(
            value = "生产点编码",
            name = "生产点编码"
    )
    @Field(
            value = "生产点编码",
            name = "生产点编码"
    )
    private String acProdPointCode;
    @ApiModelProperty(
            value = "牌号包装类别",
            name = "牌号包装类别"
    )
    @Field(
            value = "牌号包装类别",
            name = "牌号包装类别"
    )
    private String acPackType;
    @ApiModelProperty(
            value = "是否托盘包装",
            name = "是否托盘包装"
    )
    @Field(
            value = "是否托盘包装",
            name = "是否托盘包装"
    )
    private String acIsTray;
    @ApiModelProperty(
            value = "上级牌号编码",
            name = "上级牌号编码"
    )
    @Field(
            value = "上级牌号编码",
            name = "上级牌号编码"
    )
    private String acParentCode;
    @ApiModelProperty(
            name = "品牌编码",
            value = "品牌编码"
    )
    @Field(
            value = "品牌编码",
            name = "品牌编码"
    )
    private String acCigBrandCode;
    @ApiModelProperty(
            name = "品牌名称",
            value = "品牌名称"
    )
    @Field(
            value = "品牌名称",
            name = "品牌名称"
    )
    private String brandName;
    @ApiModelProperty(
            name = "品牌系列",
            value = "品牌系列"
    )
    @Field(
            value = "品牌系列",
            name = "品牌系列"
    )
    private String acSeriesCode;
    @ApiModelProperty(
            name = "品牌系列名称",
            value = "品牌系列名称"
    )
    @Field(
            value = "品牌系列名称",
            name = "品牌系列名称"
    )
    private String acCgtBrandName;
    @ApiModelProperty(
            name = "卷烟盒包条码",
            value = "卷烟盒包条码"
    )
    @Field(
            value = "卷烟盒包条码",
            name = "卷烟盒包条码"
    )
    private String barCode;
    @ApiModelProperty(
            name = "卷烟条包条码",
            value = "卷烟条包条码"
    )
    @Field(
            value = "卷烟条包条码",
            name = "卷烟条包条码"
    )
    private String barCode2;
    @ApiModelProperty(
            name = "卷烟件包条码",
            value = "卷烟件包条码"
    )
    @Field(
            value = "卷烟件包条码",
            name = "卷烟件包条码"
    )
    private String barCode3;
    @ApiModelProperty("卷烟箱包装条码")
    @Field(
            value = "卷烟箱包装条码",
            name = "卷烟箱包装条码"
    )
    private String barCode4;
    @ApiModelProperty(
            name = "一级分类方式编码",
            value = "一级分类方式编码"
    )
    @Field(
            value = "一级分类方式编码",
            name = "一级分类方式编码"
    )
    private String acOneLevelClassTypeCode;
    @ApiModelProperty(
            name = "一级分类方式",
            value = "一级分类方式"
    )
    @Field(
            value = "一级分类方式",
            name = "一级分类方式"
    )
    private String acOneLevelClassTypeName;
    @ApiModelProperty(
            name = "二级分类方式编码",
            value = "二级分类方式编码"
    )
    @Field(
            value = "二级分类方式编码",
            name = "二级分类方式编码"
    )
    private String acTwoLevelClassTypeCode;
    @ApiModelProperty(
            name = "二级分类方式",
            value = "二级分类方式"
    )
    @Field(
            value = "二级分类方式",
            name = "二级分类方式"
    )
    private String acTwoLevelClassTypeName;
    @ApiModelProperty(
            name = "茄芯分类编码",
            value = "茄芯分类编码"
    )
    @Field(
            value = "茄芯分类编码",
            name = "茄芯分类编码"
    )
    private String acCigarCoreClassCode;
    @ApiModelProperty(
            name = "茄芯分类名称",
            value = "茄芯分类名称"
    )
    @Field(
            value = "茄芯分类名称",
            name = "茄芯分类名称"
    )
    private String acCigarCoreClassName;
    @ApiModelProperty(
            name = "卷烟盒装支数",
            value = "卷烟盒装支数"
    )
    @Field(
            value = "卷烟盒装支数",
            name = "卷烟盒装支数"
    )
    private Integer packageQty;
    @ApiModelProperty(
            name = "卷烟条装支数",
            value = "卷烟条装支数"
    )
    @Field(
            value = "卷烟条装支数",
            name = "卷烟条装支数"
    )
    private Integer packageQty2;
    @ApiModelProperty(
            name = "卷烟件装支数",
            value = "卷烟件装支数"
    )
    @Field(
            value = "卷烟件装支数",
            name = "卷烟件装支数"
    )
    private Integer packageQty3;
    @ApiModelProperty("卷烟箱包装支数")
    @Field(
            value = "卷烟箱包装支数",
            name = "卷烟箱包装支数"
    )
    private Integer packageQty4;
    @ApiModelProperty(
            name = "卷烟销售标识",
            value = "卷烟销售标识"
    )
    @Field(
            value = "卷烟销售标识",
            name = "卷烟销售标识"
    )
    private String acCigSalFlag;
    @ApiModelProperty(
            name = "卷烟销售标识名称",
            value = "卷烟销售标识名称"
    )
    @Field(
            value = "卷烟销售标识名称",
            name = "卷烟销售标识名称"
    )
    private String acSalTypeName;
    @ApiModelProperty("卷烟是否使用 0 不使用  1 使用")
    @Field(
            value = "0 不使用  1 使用",
            name = "卷烟是否使用"
    )
    private String isUse;
    @ApiModelProperty("卷烟上市日期")
    @Field(
            value = "卷烟上市日期",
            name = "卷烟上市日期"
    )
    private String listDate;
    @ApiModelProperty("卷烟退市日期")
    @Field(
            value = "卷烟退市日期",
            name = "卷烟退市日期"
    )
    private String delistDate;
    @ApiModelProperty(
            name = "卷烟业务类型编码",
            value = "卷烟业务类型编码"
    )
    @Field(
            value = "卷烟业务类型编码",
            name = "卷烟业务类型编码"
    )
    private String acCigBusiTypeCode;
    @ApiModelProperty(
            name = "卷烟业务类型",
            value = "卷烟业务类型"
    )
    @Field(
            value = "卷烟业务类型",
            name = "卷烟业务类型"
    )
    private String acBusiTypeName;
    @ApiModelProperty(
            name = "卷烟产品类型（香型）",
            value = "卷烟产品类型（香型）"
    )
    @Field(
            value = "卷烟产品类型（香型）",
            name = "卷烟产品类型（香型）"
    )
    private String acCigScentTypeCode;
    @ApiModelProperty(
            name = "卷烟产品类型（香型）",
            value = "卷烟产品类型（香型）"
    )
    @Field(
            value = "卷烟产品类型（香型）",
            name = "卷烟产品类型（香型）"
    )
    private String acCigScentTypeName;
    @ApiModelProperty(
            name = "卷烟滤嘴类型编码",
            value = "卷烟滤嘴类型编码"
    )
    @Field(
            value = "卷烟滤嘴类型编码",
            name = "卷烟滤嘴类型编码"
    )
    private String acCigFilTipTypeCode;
    @ApiModelProperty(
            name = "卷烟滤嘴类型",
            value = "卷烟滤嘴类型"
    )
    @Field(
            value = "卷烟滤嘴类型",
            name = "卷烟滤嘴类型"
    )
    private String acFilTipTypeName;
    @ApiModelProperty(
            name = "卷烟包装类型编码",
            value = "卷烟包装类型编码"
    )
    @Field(
            value = "卷烟包装类型编码",
            name = "卷烟包装类型编码"
    )
    private String acCigPackTypeCode;
    @ApiModelProperty(
            name = "卷烟包装",
            value = "卷烟包装"
    )
    @Field(
            value = "卷烟包装",
            name = "卷烟包装"
    )
    private String md02CgtTradePackTypeName;
    @ApiModelProperty(
            name = "卷烟焦油含量（mg）",
            value = "卷烟焦油含量（mg）"
    )
    @Field(
            value = "卷烟焦油含量（mg）",
            name = "卷烟焦油含量（mg）"
    )
    private String tarQuantity;
    @ApiModelProperty(
            name = "卷烟烟气烟碱量（mg）",
            value = "卷烟烟气烟碱量（mg）"
    )
    @Field(
            value = "卷烟烟气烟碱量（mg）",
            name = "卷烟烟气烟碱量（mg）"
    )
    private String nicotineQuantity;
    @ApiModelProperty(
            name = "卷烟基本计量单位编码",
            value = "卷烟基本计量单位编码"
    )
    @Field(
            value = "卷烟基本计量单位编码",
            name = "卷烟基本计量单位编码"
    )
    private String acCigBaseMeasUnitCode;
    @ApiModelProperty(
            name = "物料类型编码",
            value = "物料类型编码"
    )
    @Field(
            value = "物料类型编码",
            name = "物料类型编码"
    )
    private String acMateTypeCode;
    @ApiModelProperty(
            name = "物料类型",
            value = "物料类型"
    )
    @Field(
            value = "物料类型",
            name = "物料类型"
    )
    private String acMateTypeCodeName;
    @ApiModelProperty(
            name = "卷烟生产类型编码",
            value = "卷烟生产类型编码"
    )
    @Field(
            value = "卷烟生产类型编码",
            name = "卷烟生产类型编码"
    )
    private String acCigProdTypeCode;
    @ApiModelProperty(
            name = "卷烟生产类型",
            value = "卷烟生产类型"
    )
    @Field(
            value = "卷烟生产类型",
            name = "卷烟生产类型"
    )
    private String acCigProdTypeName;
    @ApiModelProperty(
            name = "卷烟特殊配方属性编码",
            value = "卷烟特殊配方属性编码"
    )
    @Field(
            value = "卷烟特殊配方属性编码",
            name = "卷烟特殊配方属性编码"
    )
    private String acCigSpecialBomPropertyCode;
    @ApiModelProperty(
            name = "卷烟特殊配方属性",
            value = "卷烟特殊配方属性"
    )
    @Field(
            value = "卷烟特殊配方属性",
            name = "卷烟特殊配方属性"
    )
    private String acCigSpecialBomPropertyName;
    @ApiModelProperty(
            name = "卷烟价类编码",
            value = "卷烟价类编码"
    )
    @Field(
            value = "卷烟价类编码",
            name = "卷烟价类编码"
    )
    private String acCigPriceClassCode;
    @ApiModelProperty(
            name = "卷烟价类名称",
            value = "卷烟价类名称"
    )
    @Field(
            value = "卷烟价类名称",
            name = "卷烟价类名称"
    )
    private String acCgtPrType;
    @ApiModelProperty(
            name = "卷烟价类细分编码",
            value = "卷烟价类细分编码"
    )
    @Field(
            value = "卷烟价类细分编码",
            name = "卷烟价类细分编码"
    )
    private String acCigPriceClassDetailCode;
    @ApiModelProperty(
            name = "卷烟价类细分",
            value = "卷烟价类细分"
    )
    @Field(
            value = "卷烟价类细分",
            name = "卷烟价类细分"
    )
    private String acCigPriceClassDetailName;
    @ApiModelProperty(
            name = "卷烟档位编码",
            value = "卷烟档位编码"
    )
    @Field(
            value = "卷烟档位编码",
            name = "卷烟档位编码"
    )
    private String acCigSegCode;
    @ApiModelProperty(
            name = "卷烟档位",
            value = "卷烟档位"
    )
    @Field(
            value = "卷烟档位",
            name = "卷烟档位"
    )
    private String acCigSegName;
    @ApiModelProperty(
            name = "卷烟价区编码",
            value = "卷烟价区编码"
    )
    @Field(
            value = "卷烟价区编码",
            name = "卷烟价区编码"
    )
    private String acCigRangeCode;
    @ApiModelProperty(
            name = "卷烟价区",
            value = "卷烟价区"
    )
    @Field(
            value = "卷烟价区",
            name = "卷烟价区"
    )
    private String acCigRangeName;
    @ApiModelProperty(
            name = "自定义价区编码",
            value = "自定义价区编码"
    )
    @Field(
            value = "自定义价区编码",
            name = "自定义价区编码"
    )
    private String acCigCustomizeRangeCode;
    @ApiModelProperty(
            name = "自定义价区",
            value = "自定义价区"
    )
    @Field(
            value = "自定义价区",
            name = "自定义价区"
    )
    private String acCigCustomizeRangeName;
    @ApiModelProperty(
            name = "卷烟烟支规格编码",
            value = "卷烟烟支规格编码"
    )
    @Field(
            value = "卷烟烟支规格编码",
            name = "卷烟烟支规格编码"
    )
    private String acCigCigSpec;
    @ApiModelProperty(
            name = "卷烟烟支规格",
            value = "卷烟烟支规格"
    )
    @Field(
            value = "卷烟烟支规格",
            name = "卷烟烟支规格"
    )
    private String acCigCigSpecName;
    @ApiModelProperty(
            name = "是否短支",
            value = "是否短支"
    )
    @Field(
            value = "是否短支",
            name = "是否短支"
    )
    private String isShortbranch;
    @ApiModelProperty(
            name = "是否中支",
            value = "是否中支"
    )
    @Field(
            value = "是否中支",
            name = "是否中支"
    )
    private String isMedium;
    @ApiModelProperty(
            name = "是否细支",
            value = "是否细支"
    )
    @Field(
            value = "是否细支",
            name = "是否细支"
    )
    private String isTiny;
    @ApiModelProperty(
            name = "是否爆珠",
            value = "是否爆珠"
    )
    @Field(
            value = "是否爆珠",
            name = "是否爆珠"
    )
    private String isExplodingBeads;
    @ApiModelProperty(
            name = "卷烟烟气一氧化碳量（mg）",
            value = "卷烟烟气一氧化碳量（mg）"
    )
    @Field(
            value = "卷烟烟气一氧化碳量（mg）",
            name = "卷烟烟气一氧化碳量（mg）"
    )
    private BigDecimal coQty;
    @ApiModelProperty(
            name = "卷烟烟支长度（mm）",
            value = "卷烟烟支长度（mm）"
    )
    @Field(
            value = "卷烟烟支长度（mm）",
            name = "卷烟烟支长度（mm）"
    )
    private BigDecimal tbcLength;
    @ApiModelProperty(
            name = "卷烟嘴棒长度（mm）",
            value = "卷烟嘴棒长度（mm）"
    )
    @Field(
            value = "卷烟嘴棒长度（mm）",
            name = "卷烟嘴棒长度（mm）"
    )
    private BigDecimal filterLength;
    @ApiModelProperty(
            name = "卷烟烟支总长度（mm）",
            value = "卷烟烟支总长度（mm）"
    )
    @Field(
            value = "卷烟烟支总长度（mm）",
            name = "卷烟烟支总长度（mm）"
    )
    private BigDecimal tobaccoTotalLength;
    @ApiModelProperty(
            name = "卷烟周长（mm）",
            value = "卷烟周长（mm）"
    )
    @Field(
            value = "卷烟周长（mm）",
            name = "卷烟周长（mm）"
    )
    private BigDecimal circumference;
    @ApiModelProperty(
            name = "含税调拨价",
            value = "含税调拨价"
    )
    @Field(
            value = "含税调拨价",
            name = "含税调拨价"
    )
    private BigDecimal acMateTaxTranPr;
    @ApiModelProperty(
            name = "不含税调拨价",
            value = "不含税调拨价"
    )
    @Field(
            value = "不含税调拨价",
            name = "不含税调拨价"
    )
    private BigDecimal acMateNonTaxTranPr;
    @ApiModelProperty(
            name = "批发价（条）",
            value = "批发价（条）"
    )
    @Field(
            value = "批发价（条）",
            name = "批发价（条）"
    )
    private BigDecimal wholeSalePrice;
    @ApiModelProperty(
            name = "零售价（条）",
            value = "零售价（条）"
    )
    @Field(
            value = "零售价（条）",
            name = "零售价（条）"
    )
    private BigDecimal retailPrice;
    @ApiModelProperty(
            name = "币种编码",
            value = "币种编码"
    )
    @Field(
            value = "币种编码",
            name = "币种编码"
    )
    private String acCurrCode;
    @ApiModelProperty(
            name = "币种名称",
            value = "币种名称"
    )
    @Field(
            value = "币种名称",
            name = "币种名称"
    )
    private String acCurrCodeName;
    @ApiModelProperty("牌号是否使用 0 不使用  1 使用")
    @Field(
            value = "0 不使用  1 使用",
            name = "牌号是否使用"
    )
    private String itemIsUse;



    @Field(
            desc = "是否新品",
            name = "是否新品"
    )
    @ApiModelProperty(value = "是否新品", name = "是否新品")
    private String isNewProduct;

    @Field(
            desc = "是否重点品规",
            name = "是否重点品规"
    )
    @ApiModelProperty(value = "是否重点品规", name = "是否重点品规")
    private String isKey;

    @Field(
            desc = "品规类型",
            name = "品规类型"
    )
    @ApiModelProperty(value = "品规类型", name = "品规类型")
    private String productlifecycleType;

    @Field(desc = "卷烟顺序", name = "卷烟顺序")
    private Integer productSeq;
}
