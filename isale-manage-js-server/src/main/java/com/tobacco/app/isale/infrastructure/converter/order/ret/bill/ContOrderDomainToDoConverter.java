/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.order.ret.bill;

import com.tobacco.app.isale.domain.model.order.ret.bill.ContOrderDomain;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: qifengyu
 * @Email: <EMAIL>
 * @Create: 2025-07-25
 */

@Mapper

public interface ContOrderDomainToDoConverter extends StructureBaseConverter<Mc04IslmContOrderDO, ContOrderDomain> {
    ContOrderDomainToDoConverter INSTANCE = Mappers.getMapper(ContOrderDomainToDoConverter.class);
}