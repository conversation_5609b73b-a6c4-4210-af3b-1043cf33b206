/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.domain.service.plan.ycplan;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.ddd.DomainService;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.enums.plan.ycplan.LatestVersionEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.OrgTypeEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.PeriodTypeEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.SalePlanStatusEnum;
import com.tobacco.app.isale.domain.enums.plan.yearplan.DemandFoItemPeriodTypeEnum;
import com.tobacco.app.isale.domain.model.plan.ycplan.*;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmDemandFo;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmDemandFoItem;
import com.tobacco.app.isale.domain.repository.plan.ycplan.YcSalePlanItemRepository;
import com.tobacco.app.isale.domain.repository.plan.ycplan.YcSalePlanRepository;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmDemandFoItemRepository;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmDemandFoRepository;
import com.tobacco.sc.icommodity.dto.common.constant.client.api.product.ProductServiceApi;
import com.tobacco.sc.icommodity.dto.common.constant.dto.common.IccMultiDataDTO;
import com.tobacco.sc.icommodity.dto.common.constant.dto.common.IccMultiResponse;
import com.tobacco.sc.icommodity.dto.common.constant.dto.product.IccProductDetailDTO;
import com.tobacco.sc.icommodity.dto.common.constant.req.product.IccGetProductListRequest;
import com.tobacco.sc.icust.client.api.com.ComServiceAPI;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import com.tobacco.sc.icust.dto.common.KCMultiResponse;
import com.tobacco.sc.icust.req.com.GetBusiComListREQ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Component("YcSalePlanAllotCenterDS")
@DomainService(name = "YcSalePlanAllotCenter", desc = "元春计划分解中心领域服务")
@RequiredArgsConstructor
public class YcSalePlanAllotCenterDomainService {

    @Resource
    private YcSalePlanRepository ycSalePlanRepository;
    @Resource
    private YcSalePlanItemRepository ycSalePlanItemRepository;
    @Resource
    private ComServiceAPI comServiceAPI;
    @Resource
    private ProductServiceApi productServiceApi;
    @Resource
    private IslmDemandFoRepository demandFoRepository;
    @Resource
    private IslmDemandFoItemRepository demandFoItemRepository;

    @ReturnValue(desc = "省外以及省外各省元春计划编制详情结果")
    @Method(name = "获取分解后省外以及省外各省元春计划编制详情结果")
    public YcSalePlanAllotCenter getYcSalePlanDetailSw(String mc04SalePlanId, String busiComCode) {
        Assert.notNull(mc04SalePlanId, () -> new CustomException("元春计划不能为空"));
        // 1.根据id获取元春计划
        Mc04IslmYcSalePlan mc04IslmYcSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById((mc04SalePlanId));

        BusiComDto provinceIn = getProvinceIn();

        YcSalePlanListWithItemList swAndProvinceYcSalePlanListWithItemList;
        List<Mc04IslmYcSalePlan> ycSalePlanSwAndProvinceList;
        // 全部省公司
        if (StringUtils.isEmpty(busiComCode)) {
            // 1.根据全国元春的行获取省外元春计划以及省(不包括本省)的元春计划
            ycSalePlanSwAndProvinceList = getPlanListSwAndProvinceByYcPlan(mc04IslmYcSalePlan, provinceIn.getBaComOrgCode());
        } else {
            // 指定省公司
            ycSalePlanSwAndProvinceList = getPlanListSwAndThisProvinceByYcPlan(mc04IslmYcSalePlan, busiComCode);
        }

        // 2.根据id列表获取元春计划详情
        List<String> planIdList = ycSalePlanSwAndProvinceList.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
        List<Mc04IslmYcSalePlanItem> ycSalePlanSwAndProvinceItemList = ycSalePlanItemRepository.getListByYcPlanIdList(planIdList);
        // 构建元春计划集合和计划详情集合对象
        swAndProvinceYcSalePlanListWithItemList = new YcSalePlanListWithItemList(ycSalePlanSwAndProvinceList, ycSalePlanSwAndProvinceItemList);
        // 构建省外1,2月计划详情映射
        swAndProvinceYcSalePlanListWithItemList.buildSwJanAndFebYcPlanItemProductMap();

        // 获取产品列表
        Collection<IccProductDetailDTO> productList = getProductList();
        // 获取省外列表
        List<BusiComDto> provinceOutList = getProvinceOut();

        // 计算每个规格每个省的的元春、1月、2月的占比
        List<String> busiComCodeList = provinceOutList.stream().map(BusiComDto::getBaComOrgCode).collect(Collectors.toList());
        List<String> productCodeList = productList.stream().map(IccProductDetailDTO::getProductCode).collect(Collectors.toList());
        Map<String, Map<String, Map<String, BigDecimal>>> productProvinceRate = computeProductProvinceRate(productCodeList, busiComCodeList, mc04IslmYcSalePlan.getZaOccurrenceYear());
        // 获取每个省的每个卷烟的N-1期初库存，当前年上一年销售、调拨和期末库存
        ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear = ycSalePlanRepository.getProvinceEndStkSaleAllotInfoForYear(mc04IslmYcSalePlan.getZaOccurrenceYear());
        // 获取省外每个卷烟 N-1 期初库存 当前年销售，当前年调拨 当前年期末库存
        SnSwEndStkSaleAllotInfoForYear snSwEndStkSaleAllotInfoForYear = ycSalePlanRepository.getSnSwEndStkSaleAllotInfoForYear(mc04IslmYcSalePlan.getZaOccurrenceYear());
        List<Map<String, Object>> ycSaleAllotCenterItemList;

        // 根据是否有省的计划来决定处理方式
        ycSaleAllotCenterItemList = processYcSalePlanDetails(
                swAndProvinceYcSalePlanListWithItemList,
                productList,
                provinceOutList,
                productProvinceRate,
                provinceEndStkSaleAllotInfoForYear,
                snSwEndStkSaleAllotInfoForYear
        );

        YcSalePlanAllotCenter ycSalePlanAllotCenter = new YcSalePlanAllotCenter();
        BeanUtils.copyProperties(mc04IslmYcSalePlan, ycSalePlanAllotCenter);
        ycSalePlanAllotCenter.setYcSalePlanDetailList(ycSaleAllotCenterItemList);
        return ycSalePlanAllotCenter;
    }

    /**
     * 处理元春计划分解中心界面展示信息
     * @param swAndProvinceYcSalePlanListWithItemList 省外以及省外各省的元春计划详情
     * @param productList 卷烟列表
     * @param provinceOutList 省外列表
     * @param productProvinceRate 每个规格每个省的根据需求预测提报计算出来的1月、2月的占比
     * @param provinceEndStkSaleAllotInfoForYear 省每个卷烟 N-1 期初库存 当前年销售，当前年调拨 当前年期末库存
     * @param snSwEndStkSaleAllotInfoForYear 省外每个卷烟 N-1 期初库存 当前年销售，当前年调拨 当前年期末库存
     */
    private List<Map<String, Object>> processYcSalePlanDetails(
            YcSalePlanListWithItemList swAndProvinceYcSalePlanListWithItemList,
            Collection<IccProductDetailDTO> productList,
            List<BusiComDto> provinceOutList,
            Map<String, Map<String, Map<String, BigDecimal>>> productProvinceRate,
            ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear,
            SnSwEndStkSaleAllotInfoForYear snSwEndStkSaleAllotInfoForYear) {

        // 获取省计划列表
        List<Mc04IslmYcSalePlan> provinceSalePlanList = swAndProvinceYcSalePlanListWithItemList.getProvinceSalePlanList();
        // 如果不存在省的计划，那需要全部进行初始化
        if (CollectionUtils.isEmpty(provinceSalePlanList)) {
            return processEmptyProvinceSalePlan(
                    swAndProvinceYcSalePlanListWithItemList,
                    productList,
                    provinceOutList,
                    productProvinceRate,
                    provinceEndStkSaleAllotInfoForYear,
                    snSwEndStkSaleAllotInfoForYear
            );
        } else {
            // 如果存在省计划，就需要对每个省的每个卷烟进行处理
            return processNonEmptyProvinceSalePlan(
                    swAndProvinceYcSalePlanListWithItemList,
                    productList,
                    provinceOutList,
                    productProvinceRate,
                    provinceEndStkSaleAllotInfoForYear,
                    snSwEndStkSaleAllotInfoForYear,
                    provinceSalePlanList
            );
        }
    }

    /**
     * 处理没有省计划
     * @param swAndProvinceYcSalePlanListWithItemList 省外元春计划详情
     * @param productList 卷烟列表
     * @param provinceOutList 省外列表
     * @param productProvinceRate 每个规格每个省的根据需求预测提报计算出来的1月、2月的占比
     * @param provinceEndStkSaleAllotInfoForYear 省每个卷烟 N-1 期初库存 当前年销售，当前年调拨 当前年期末库存
     * @param snSwEndStkSaleAllotInfoForYear 省外每个卷烟 N-1 期初库存 当前年销售，当前年调拨 当前年期末库存
     */
    private List<Map<String, Object>> processEmptyProvinceSalePlan(
            YcSalePlanListWithItemList swAndProvinceYcSalePlanListWithItemList,
            Collection<IccProductDetailDTO> productList,
            List<BusiComDto> provinceOutList,
            Map<String, Map<String, Map<String, BigDecimal>>> productProvinceRate,
            ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear,
            SnSwEndStkSaleAllotInfoForYear snSwEndStkSaleAllotInfoForYear) {

        // 根据之前的计划获取产品列表
        List<String> planProductCodeList = swAndProvinceYcSalePlanListWithItemList.getProductCodeList();
        // 1.先构建卷烟信息，如果没有省类型的数据，就证明第一次进入页面，需要最基础的卷烟数据
        List<Map<String, Object>> ycSaleAllotCenterItemList = new ArrayList<>(initYcSalePlanSwProvinceItemListProductInfo(planProductCodeList, productList));
        for (Map<String, Object> ycSaleAllotCenterItem : ycSaleAllotCenterItemList) {
            // 2.为每一类烟卷的 省外数据初始化，这时计划和计划详情都只有省外的总体数据
            initYcSalePlanSwProvinceItemListQtyInfo(ycSaleAllotCenterItem, swAndProvinceYcSalePlanListWithItemList, snSwEndStkSaleAllotInfoForYear);
            String productCode = (String) ycSaleAllotCenterItem.get("acCgtCartonCode");
            // 3.对每一个卷烟构建省外信息，根据计算好的各个省外省的占比以及上一步初始化好的省外总数
            for (BusiComDto busiComDto : provinceOutList) {
                // 传入该卷烟的占比数据和总量数据
                initProvinceQtyInfo(ycSaleAllotCenterItem, busiComDto, productProvinceRate.get(productCode), provinceEndStkSaleAllotInfoForYear);
            }
        }

        return ycSaleAllotCenterItemList;
    }

    /**
     *
     * @param swAndProvinceYcSalePlanListWithItemList 省外元春计划详情
     * @param productList 卷烟列表
     * @param provinceOutList 省外列表
     * @param productProvinceRate 每个规格每个省的根据需求预测提报计算出来的1月、2月的占比
     * @param provinceEndStkSaleAllotInfoForYear 省每个卷烟 N-1 期初库存 当前年销售，当前年调拨 当前年期末库存
     * @param snSwEndStkSaleAllotInfoForYear 省外每个卷烟 N-1 期初库存 当前年销售，当前年调拨 当前年期末库存
     * @param provinceSalePlanList 省计划
     */
    private List<Map<String, Object>> processNonEmptyProvinceSalePlan(
            YcSalePlanListWithItemList swAndProvinceYcSalePlanListWithItemList,
            Collection<IccProductDetailDTO> productList,
            List<BusiComDto> provinceOutList,
            Map<String, Map<String, Map<String, BigDecimal>>> productProvinceRate,
            ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear,
            SnSwEndStkSaleAllotInfoForYear snSwEndStkSaleAllotInfoForYear,
            List<Mc04IslmYcSalePlan> provinceSalePlanList) {

        // 构建卷烟基础信息
        List<String> planProductCodeList = swAndProvinceYcSalePlanListWithItemList.getProductCodeList();
        List<Map<String, Object>> ycSaleAllotCenterItemList = new ArrayList<>(initYcSalePlanSwProvinceItemListProductInfo(planProductCodeList, productList));

        // 2. 初始化省外数据
        for (Map<String, Object> productItem : ycSaleAllotCenterItemList) {
            initYcSalePlanSwProvinceItemListQtyInfo(productItem, swAndProvinceYcSalePlanListWithItemList, snSwEndStkSaleAllotInfoForYear);
        }

        // 3. 处理各省数据
        processProvinceDataForEachProvince(
                ycSaleAllotCenterItemList,
                provinceOutList,
                provinceSalePlanList,
                productProvinceRate,
                provinceEndStkSaleAllotInfoForYear
        );

        // 4. 将处理好的数据放入结果列表
        return ycSaleAllotCenterItemList;
    }

    /**
     * 处理每个省的数据
     */
    private void processProvinceDataForEachProvince(
            List<Map<String, Object>> ycSaleAllotCenterItemList,
            List<BusiComDto> provinceOutList,
            List<Mc04IslmYcSalePlan> provinceSalePlanList,
            Map<String, Map<String, Map<String, BigDecimal>>> productProvinceRate,
            ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear) {

        // 获取所有省公司的计划详情
        List<String> provincePlanIds = provinceSalePlanList.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
        List<Mc04IslmYcSalePlanItem> provinceItems = ycSalePlanItemRepository.getListByYcPlanIdList(provincePlanIds);

        for (BusiComDto province : provinceOutList) {
            // 获取该省的计划
            List<Mc04IslmYcSalePlan> thisProvincePlans = provinceSalePlanList.stream()
                    .filter(p -> p.getMc04OrgTypeCode().equals(province.getBaComOrgCode()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(thisProvincePlans)) {
                // 如果没有该省的计划，则按照比例初始化
                processProvinceWithoutPlan(ycSaleAllotCenterItemList, province, productProvinceRate, provinceEndStkSaleAllotInfoForYear);
            } else {
                List<String> thisProvincePlansIdList = thisProvincePlans.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
                // 如果有该省的计划，则从数据库获取数据
                // 获取该省的计划详情
                List<Mc04IslmYcSalePlanItem> thisProvinceItems = provinceItems.stream()
                        .filter(p -> thisProvincePlansIdList.contains(p.getMc04SalePlanId()))
                        .collect(Collectors.toList());
                processProvinceWithPlan(ycSaleAllotCenterItemList, province, productProvinceRate, thisProvincePlans, thisProvinceItems, provinceEndStkSaleAllotInfoForYear);
            }
        }
    }

    /**
     * 处理没有计划的省份
     */
    private void processProvinceWithoutPlan(
            List<Map<String, Object>> ycSaleAllotCenterItemList,
            BusiComDto province,
            Map<String, Map<String, Map<String, BigDecimal>>> productProvinceRate,
            ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear) {

        for (Map<String, Object> ycSaleAllotCenterItem : ycSaleAllotCenterItemList) {
            String productCode = (String) ycSaleAllotCenterItem.get("acCgtCartonCode");
            initProvinceQtyInfo(ycSaleAllotCenterItem, province, productProvinceRate.get(productCode), provinceEndStkSaleAllotInfoForYear);
        }
    }

    /**
     * 处理有计划的省份
     */
    private void processProvinceWithPlan(
            List<Map<String, Object>> ycSaleAllotCenterItemList,
            BusiComDto province,
            Map<String, Map<String, Map<String, BigDecimal>>> productProvinceRate,
            List<Mc04IslmYcSalePlan> thisProvincePlans,
            List<Mc04IslmYcSalePlanItem> thisProvinceItems,
            ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear) {

        for (Map<String, Object> ycSaleAllotCenterItem : ycSaleAllotCenterItemList) {

            String productCode = (String) ycSaleAllotCenterItem.get("acCgtCartonCode");

            List<Mc04IslmYcSalePlanItem> thisProductItems = thisProvinceItems.stream()
                    .filter(item -> item.getAcCgtCartonCode().equals(productCode)).collect(Collectors.toList());

            if (CollectionUtil.isEmpty(thisProductItems)) {
                initProvinceQtyInfo(ycSaleAllotCenterItem, province, productProvinceRate.get(productCode), provinceEndStkSaleAllotInfoForYear);
            } else {
                YcSalePlanListWithItemList oneProvinceYcSalePlanListWithItemList = new YcSalePlanListWithItemList(thisProvincePlans, thisProvinceItems);
                // 构建该省公司1月2月的数据
                oneProvinceYcSalePlanListWithItemList.buildOneProvinceJanAndFebYcPlanItemProductMap();
                String provinceCode = oneProvinceYcSalePlanListWithItemList.getProvinceCode();

                // N-1年末商业库存
                ycSaleAllotCenterItem.put("lastYearStkQtyOut" + provinceCode, provinceEndStkSaleAllotInfoForYear.getLastYearEndStkQty(provinceCode, productCode));
                // N年预计销售
                ycSaleAllotCenterItem.put("currentYearSalesOut" + provinceCode, provinceEndStkSaleAllotInfoForYear.getCurrentYearPlanSaleQty(provinceCode, productCode));
                // N年预计调拨
                ycSaleAllotCenterItem.put("currentYearAllocationOut" + provinceCode, provinceEndStkSaleAllotInfoForYear.getCurrentYearPlanAllotQty(provinceCode, productCode));
                // N年末预计库存
                ycSaleAllotCenterItem.put("currentYearEndInventoryOut" + provinceCode, provinceEndStkSaleAllotInfoForYear.getCurrentYearEndStkQty(provinceCode, productCode));

                // 1月销售计划 用户手动填写 mc04_cgt_sale_plan_origin_qty
                Mc04IslmYcSalePlanItem janProductItem = oneProvinceYcSalePlanListWithItemList.getOneProvinceJanYcPlanItem(productCode);
                Mc04IslmYcSalePlanItem febProductItem = oneProvinceYcSalePlanListWithItemList.getOneProvinceFebYcPlanItem(productCode);

                ycSaleAllotCenterItem.put("nextYearFirstMonthSalesPlanOut" + provinceCode, janProductItem.getMc04CgtSalePlanOriginQty());
                ycSaleAllotCenterItem.put("nextYearFirstMonthAllocationPlanOut" + provinceCode, janProductItem.getMa02CgtPlResolOrigplanQty());
                ycSaleAllotCenterItem.put("nextYearFirstMonthEndingStockOut" + provinceCode, janProductItem.getMd03Cgt10thComEndStkQty());
                ycSaleAllotCenterItem.put("nextYearSecondMonthSalesPlanOut" + provinceCode, febProductItem.getMc04CgtSalePlanOriginQty());
                ycSaleAllotCenterItem.put("nextYearSecondMonthAllocationPlanOut" + provinceCode, febProductItem.getMa02CgtPlResolOrigplanQty());
                ycSaleAllotCenterItem.put("nextYearSecondMonthEndingStockOut" + provinceCode, febProductItem.getMd03Cgt10thComEndStkQty());
            }
        }
    }

    @ReturnValue(desc = "全国、省内、省外以及各省公司元春计划编制详情结果")
    @Method(name = "获取分解后全国、省内、省外以及各省公司元春计划编制详情结果")
    public YcSalePlanAllotCenter getYcSalePlanDetailQg(String mc04SalePlanId) {
        // 获取省外以及省外商业公司的信息
        YcSalePlanAllotCenter ycSalePlanAllotCenter = this.getYcSalePlanDetailSw(mc04SalePlanId, "");

        // 获取省内和省内商业公司计划和计划详情
        BusiComDto provinceIn = getProvinceIn();
        Mc04IslmYcSalePlan ycSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(mc04SalePlanId);
        List<Mc04IslmYcSalePlan> snYcSalePlan = getPlanListSnAndProvinceByYcPlan(ycSalePlan, provinceIn.getBaComOrgCode());

        List<String> planId = snYcSalePlan.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
        List<Mc04IslmYcSalePlanItem> snYcSalePlanItem = ycSalePlanItemRepository.getListByYcPlanIdList(planId);

        // 构建元春计划集合和计划详情集合对象
        YcSalePlanListWithItemList snAndProvinceYcSalePlanListWithItemList = new YcSalePlanListWithItemList(snYcSalePlan, snYcSalePlanItem);
        // 构建省外1,2月计划详情映射
        snAndProvinceYcSalePlanListWithItemList.buildSnJanAndFebYcPlanItemProductMap();
        // 获取省内的 N-1年末商业库存 N年预计销售 N年预计调拨 N年末预计库存
        SnSwEndStkSaleAllotInfoForYear snSwEndStkSaleAllotInfoForYear = ycSalePlanRepository.getSnSwEndStkSaleAllotInfoForYear(ycSalePlan.getZaOccurrenceYear());

        // 构建省内1,2月数据
        buildSnQtyInfo(ycSalePlanAllotCenter, snAndProvinceYcSalePlanListWithItemList, snSwEndStkSaleAllotInfoForYear);

        // 构建省内省公司1,2月数据
        snAndProvinceYcSalePlanListWithItemList.buildOneProvinceJanAndFebYcPlanItemProductMap();

        // 获取省内省公司 N-1年末商业库存 N年预计销售 N年预计调拨 N年末预计库存
        ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear = ycSalePlanRepository.getProvinceEndStkSaleAllotInfoForYear(ycSalePlan.getZaOccurrenceYear());

        buildProvinceInQtyInfo(ycSalePlanAllotCenter, provinceIn, snAndProvinceYcSalePlanListWithItemList, provinceEndStkSaleAllotInfoForYear);

        return ycSalePlanAllotCenter;
    }

    private void buildSnQtyInfo(YcSalePlanAllotCenter ycSalePlanAllotCenter,
                                YcSalePlanListWithItemList swAndProvinceYcSalePlanListWithItemList,
                                SnSwEndStkSaleAllotInfoForYear snEndStkSaleAllotInfoForYear) {

        List<Map<String, Object>> ycSalePlanDetailList = ycSalePlanAllotCenter.getYcSalePlanDetailList();
        for (Map<String, Object> ycSaleAllotCenterItem : ycSalePlanDetailList) {
            String productCode = (String) ycSaleAllotCenterItem.get("acCgtCartonCode");
            // N-1年末商业库存
            ycSaleAllotCenterItem.put("lastYearStkQtyIn", snEndStkSaleAllotInfoForYear.getLastYearEndStkQtyProvinceIn(productCode));
            // N年预计销售
            ycSaleAllotCenterItem.put("currentYearSalesIn", snEndStkSaleAllotInfoForYear.getCurrentYearPlanSaleQtyProvinceIn(productCode));
            // N年预计调拨
            ycSaleAllotCenterItem.put("currentYearAllocationIn", snEndStkSaleAllotInfoForYear.getCurrentYearPlanAllotQtyProvinceIn(productCode));
            // N年末预计库存
            ycSaleAllotCenterItem.put("currentYearEndInventoryIn", snEndStkSaleAllotInfoForYear.getCurrentYearEndStkQtyProvinceIn(productCode));

            // 省内1月2月销售调拨
            ycSaleAllotCenterItem.put("nextYearFirstMonthSalesPlanIn", swAndProvinceYcSalePlanListWithItemList.getSnJanYcPlanItem(productCode).getMc04CgtSalePlanOriginQty());
            ycSaleAllotCenterItem.put("nextYearFirstMonthAllocationPlanIn", swAndProvinceYcSalePlanListWithItemList.getSnJanYcPlanItem(productCode).getMa02CgtPlResolOrigplanQty());

            ycSaleAllotCenterItem.put("nextYearSecondMonthSalesPlanIn", swAndProvinceYcSalePlanListWithItemList.getSnFebYcPlanItem(productCode).getMd03Cgt10thComEndStkQty());
            ycSaleAllotCenterItem.put("nextYearSecondMonthAllocationPlanIn", swAndProvinceYcSalePlanListWithItemList.getSnFebYcPlanItem(productCode).getMa02CgtPlResolOrigplanQty());
        }
    }

    private void buildProvinceInQtyInfo(YcSalePlanAllotCenter ycSalePlanAllotCenter, BusiComDto provinceIn, YcSalePlanListWithItemList snAndProvinceYcSalePlanListWithItemList, ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear) {
        List<Map<String, Object>> ycSalePlanDetailList = ycSalePlanAllotCenter.getYcSalePlanDetailList();
        for (Map<String, Object> ycSaleAllotCenterItem : ycSalePlanDetailList) {
            String productCode = (String) ycSaleAllotCenterItem.get("acCgtCartonCode");
            String provinceCode = provinceIn.getBaComOrgCode();

            // N-1年末商业库存
            ycSaleAllotCenterItem.put("lastYearStkQtyOut" + provinceCode, provinceEndStkSaleAllotInfoForYear.getLastYearEndStkQty(provinceCode, productCode));
            // N年预计销售
            ycSaleAllotCenterItem.put("currentYearSalesOut" + provinceCode, provinceEndStkSaleAllotInfoForYear.getCurrentYearPlanSaleQty(provinceCode, productCode));
            // N年预计调拨
            ycSaleAllotCenterItem.put("currentYearAllocationOut" + provinceCode, provinceEndStkSaleAllotInfoForYear.getCurrentYearPlanAllotQty(provinceCode, productCode));
            // N年末预计库存
            ycSaleAllotCenterItem.put("currentYearEndInventoryOut" + provinceCode, provinceEndStkSaleAllotInfoForYear.getCurrentYearEndStkQty(provinceCode, productCode));

            // 省内1月2月销售调拨
            ycSaleAllotCenterItem.put("nextYearFirstMonthSalesPlanOut" + provinceCode, snAndProvinceYcSalePlanListWithItemList.getOneProvinceJanYcPlanItem(productCode).getMc04CgtSalePlanOriginQty());
            ycSaleAllotCenterItem.put("nextYearFirstMonthAllocationPlanOut" + provinceCode, snAndProvinceYcSalePlanListWithItemList.getOneProvinceJanYcPlanItem(productCode).getMa02CgtPlResolOrigplanQty());
            ycSaleAllotCenterItem.put("nextYearFirstMonthEndingStockOut" + provinceCode, snAndProvinceYcSalePlanListWithItemList.getOneProvinceJanYcPlanItem(productCode).getMd03Cgt10thComEndStkQty());
            ycSaleAllotCenterItem.put("nextYearSecondMonthSalesPlanOut" + provinceCode, snAndProvinceYcSalePlanListWithItemList.getOneProvinceFebYcPlanItem(productCode).getMc04CgtSalePlanOriginQty());
            ycSaleAllotCenterItem.put("nextYearSecondMonthAllocationPlanOut" + provinceCode, snAndProvinceYcSalePlanListWithItemList.getOneProvinceFebYcPlanItem(productCode).getMa02CgtPlResolOrigplanQty());
            ycSaleAllotCenterItem.put("nextYearSecondMonthEndingStockOut" + provinceCode, snAndProvinceYcSalePlanListWithItemList.getOneProvinceFebYcPlanItem(productCode).getMd03Cgt10thComEndStkQty());
        }
    }

    private void initProvinceQtyInfo(Map<String, Object> ycSaleAllotCenterItem, BusiComDto province, Map<String, Map<String, BigDecimal>> provinceRate, ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear) {
        // 需要获取初始话数据
        // 对每一个省外数据进行初始化 ，初始化公式为通过需求预测获取每个卷烟规格外省在省外数量的占比，在用占比*省外元春计划
        // 根据省的编码以及卷烟号获取需求预测数据
        BigDecimal swJanSalesTotalNum = BigDecimal.ZERO;
        BigDecimal swFebSalesTotalNum = BigDecimal.ZERO;
        BigDecimal swJanAllocateTotalNum = BigDecimal.ZERO;
        BigDecimal swFebAllocateTotalNum = BigDecimal.ZERO;


        // 使用plan表里的省外的数据
        if (ycSaleAllotCenterItem != null) {
            swJanSalesTotalNum = (BigDecimal) ycSaleAllotCenterItem.get("nextYearFirstMonthSalesPlanOut");
            swFebSalesTotalNum = (BigDecimal) ycSaleAllotCenterItem.get("nextYearSecondMonthSalesPlanOut");
            swJanAllocateTotalNum = (BigDecimal) ycSaleAllotCenterItem.get("nextYearFirstMonthAllocationPlanOut");
            swFebAllocateTotalNum = (BigDecimal) ycSaleAllotCenterItem.get("nextYearSecondMonthAllocationPlanOut");
        }

        String productCode = (String) ycSaleAllotCenterItem.get("acCgtCartonCode");
        String provinceCode = province.getBaComOrgCode();
        // N-1年末商业库存
        ycSaleAllotCenterItem.put("lastYearStkQtyOut" + provinceCode, provinceEndStkSaleAllotInfoForYear.getLastYearEndStkQty(provinceCode, productCode));
        // N年预计销售
        ycSaleAllotCenterItem.put("currentYearSalesOut" + provinceCode, provinceEndStkSaleAllotInfoForYear.getCurrentYearPlanSaleQty(provinceCode, productCode));
        // N年预计调拨
        ycSaleAllotCenterItem.put("currentYearAllocationOut" + provinceCode, provinceEndStkSaleAllotInfoForYear.getCurrentYearPlanAllotQty(provinceCode, productCode));
        // N年末预计库存
        ycSaleAllotCenterItem.put("currentYearEndInventoryOut" + provinceCode, provinceEndStkSaleAllotInfoForYear.getCurrentYearEndStkQty(provinceCode, productCode));

        Map<String, BigDecimal> thisProvinceRate = provinceRate.get(provinceCode);
        if (thisProvinceRate == null) {
            ycSaleAllotCenterItem.put("nextYearFirstMonthSalesPlanOut" + provinceCode, BigDecimal.ZERO);
            ycSaleAllotCenterItem.put("nextYearFirstMonthAllocationPlanOut" + provinceCode, BigDecimal.ZERO);
            ycSaleAllotCenterItem.put("nextYearSecondMonthSalesPlanOut" + provinceCode, BigDecimal.ZERO);
            ycSaleAllotCenterItem.put("nextYearSecondMonthAllocationPlanOut" + provinceCode, BigDecimal.ZERO);
        } else {
            ycSaleAllotCenterItem.put("nextYearFirstMonthSalesPlanOut" + provinceCode,
                    swJanSalesTotalNum.multiply(thisProvinceRate.get("januaryRate")));
            // N+1年1月调拨计划
            ycSaleAllotCenterItem.put("nextYearFirstMonthAllocationPlanOut" + provinceCode,
                    swJanAllocateTotalNum.multiply(thisProvinceRate.get("januaryRate")));
            // N+1年1月末商业库存
            // N+1年2月销售计划
            ycSaleAllotCenterItem.put("nextYearSecondMonthSalesPlanOut" + provinceCode,
                    swFebSalesTotalNum.multiply(thisProvinceRate.get("februaryRate")));
            // N+1年2月调拨计划
            ycSaleAllotCenterItem.put("nextYearSecondMonthAllocationPlanOut" + provinceCode,
                    swFebAllocateTotalNum.multiply(thisProvinceRate.get("februaryRate")));
        }


        // N+1年1-2月销售计划
        BigDecimal nextYearFirstTwoMonthSalesPlanOut = (BigDecimal) ycSaleAllotCenterItem.get("nextYearFirstMonthSalesPlanOut" + provinceCode);
        nextYearFirstTwoMonthSalesPlanOut = nextYearFirstTwoMonthSalesPlanOut.add((BigDecimal) ycSaleAllotCenterItem.get("nextYearSecondMonthSalesPlanOut" + provinceCode));
        ycSaleAllotCenterItem.put("nextYearFirstTwoMonthSalesPlanOut" + provinceCode, nextYearFirstTwoMonthSalesPlanOut);
        // N+1年1-2月调拨计划
        BigDecimal nextYearFirstTwoMonthAllocationPlanOut = (BigDecimal) ycSaleAllotCenterItem.get("nextYearFirstMonthAllocationPlanOut" + provinceCode);
        nextYearFirstTwoMonthAllocationPlanOut = nextYearFirstTwoMonthAllocationPlanOut.add((BigDecimal) ycSaleAllotCenterItem.get("nextYearSecondMonthAllocationPlanOut" + provinceCode));
        ycSaleAllotCenterItem.put("nextYearFirstTwoMonthAllocationPlanOut" + provinceCode, nextYearFirstTwoMonthAllocationPlanOut);
    }

    private List<Map<String, Object>> initYcSalePlanSwProvinceItemListProductInfo(List<String> productCodeList, Collection<IccProductDetailDTO> productList) {
        List<Map<String, Object>> ycSaleAllotCenterItemList = new ArrayList<>();
        for (String code : productCodeList) {
            Map<String, Object> ycSaleAllotCenterItem = new HashMap<>();
            // 通过名字找到对应的产品详情
            Optional<IccProductDetailDTO> productOptional = productList.stream().filter(item -> item.getProductCode().equals(code)).findFirst();
            IccProductDetailDTO product;
            if (productOptional.isPresent()) {
                product = productOptional.get();
            } else {
                throw new CustomException("没有找到对应的卷烟信息");
            }
            ycSaleAllotCenterItem.put("acCgtName", product.getProductName());
            ycSaleAllotCenterItem.put("acCgtCartonCode", product.getProductCode());
            // 做烟卷合并需要的信息
            ycSaleAllotCenterItem.put("acCigBrandCode", product.getAcCigBrandCode());
            ycSaleAllotCenterItem.put("acCgtPriceSegmentCode", product.getAcCigRangeCode());
            ycSaleAllotCenterItem.put("acCgtTinyFlag", product.getIsTiny());
            ycSaleAllotCenterItem.put("acCgtMediumBranceFlag", product.getIsMedium());
            Integer packageQty2 = Integer.parseInt(product.getPackageQty2());
            // 卷烟含税调拨价格
            ycSaleAllotCenterItem.put("acCgtTaxAllotPrice", getAcCgtTaxAllotPrice(packageQty2, new BigDecimal(product.getAcMateTaxTranPr())));
            // 卷烟统一批发价
            ycSaleAllotCenterItem.put("acCgtTradePrice", getAcCgtTradePrice(packageQty2, new BigDecimal(product.getWholeSalePrice())));
            ycSaleAllotCenterItemList.add(ycSaleAllotCenterItem);
        }
        return ycSaleAllotCenterItemList;
    }

    /**
     * 初始化省外数据
     * @param ycSaleAllotCenterItem 初始化的分解中心行
     * @param swAndProvinceYcSalePlanListWithItemList 省外计划省外计划详情
     * @param snSwEndStkSaleAllotInfoForYear 省外的其他统计信息
     */
    private void initYcSalePlanSwProvinceItemListQtyInfo(Map<String, Object> ycSaleAllotCenterItem, YcSalePlanListWithItemList swAndProvinceYcSalePlanListWithItemList, SnSwEndStkSaleAllotInfoForYear snSwEndStkSaleAllotInfoForYear) {

        // 如果没写入过省外其他信息
        String productCode = (String) ycSaleAllotCenterItem.get("acCgtCartonCode");
        // 获取上一年末商业库存
        ycSaleAllotCenterItem.put("lastYearStkQtyOut", snSwEndStkSaleAllotInfoForYear.getLastYearEndStkQtyProvinceOut(productCode));
        // 获取当前年预计销售
        ycSaleAllotCenterItem.put("currentYearSalesOut", snSwEndStkSaleAllotInfoForYear.getCurrentYearPlanSaleQtyProvinceOut(productCode));
        // 获取当前年预计调拨
        ycSaleAllotCenterItem.put("currentYearAllocationOut", snSwEndStkSaleAllotInfoForYear.getCurrentYearPlanAllotQtyProvinceOut(productCode));
        // 获取当前年末商业库存
        ycSaleAllotCenterItem.put("currentYearEndInventoryOut", snSwEndStkSaleAllotInfoForYear.getCurrentYearEndStkQtyProvinceOut(productCode));

        ycSaleAllotCenterItem.put("nextYearFirstMonthSalesPlanOut", swAndProvinceYcSalePlanListWithItemList.getSwJanYcPlanItem(productCode).getMc04CgtSalePlanOriginQty());
        ycSaleAllotCenterItem.put("nextYearFirstMonthAllocationPlanOut", swAndProvinceYcSalePlanListWithItemList.getSwJanYcPlanItem(productCode).getMa02CgtPlResolOrigplanQty());
        ycSaleAllotCenterItem.put("nextYearFirstMonthEndingStockOut", swAndProvinceYcSalePlanListWithItemList.getSwJanYcPlanItem(productCode).getMd03Cgt10thComEndStkQty());
        ycSaleAllotCenterItem.put("nextYearSecondMonthSalesPlanOut", swAndProvinceYcSalePlanListWithItemList.getSwFebYcPlanItem(productCode).getMc04CgtSalePlanOriginQty());
        ycSaleAllotCenterItem.put("nextYearSecondMonthAllocationPlanOut", swAndProvinceYcSalePlanListWithItemList.getSwFebYcPlanItem(productCode).getMa02CgtPlResolOrigplanQty());
        ycSaleAllotCenterItem.put("nextYearSecondMonthEndingStockOut", swAndProvinceYcSalePlanListWithItemList.getSwFebYcPlanItem(productCode).getMd03Cgt10thComEndStkQty());
    }

    /**
     * 获取当前业务年份当前使用版本的省外和省外各个省的元春计划
     * @param ycSalePlan 全国元春
     * @param snProvinceCode 省内code，排除code
     */
    private List<Mc04IslmYcSalePlan> getPlanListSwAndProvinceByYcPlan(Mc04IslmYcSalePlan ycSalePlan, String snProvinceCode) {
        // 构建查询参数
        Mc04IslmYcSalePlan plan = new Mc04IslmYcSalePlan();
        plan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        plan.setZaOccurrenceYear(ycSalePlan.getZaOccurrenceYear());
        plan.setMc04CgtSalePlanVersion(ycSalePlan.getMc04CgtSalePlanVersion());
        plan.setMc04OrgTypeKind(OrgTypeEnum.SW.getKind());
        List<Mc04IslmYcSalePlan> ycSalePlanList = ycSalePlanRepository.getYcPlanList(plan);
        // 在获取省一级的
        plan.setMc04OrgTypeKind(OrgTypeEnum.PROVINCE.getKind());
        ycSalePlanList.addAll(ycSalePlanRepository.getYcPlanList(plan));
        // 排除本省
        ycSalePlanList.removeIf(item -> item.getMc04OrgTypeCode().equals(snProvinceCode));

        return ycSalePlanList;
    }

    /**
     * 获取当前业务年份当前使用版本的省外和省外特定省公司的元春计划
     * @param ycSalePlan 全国元春
     * @param provinceCode 省内code，排除code
     */
    private List<Mc04IslmYcSalePlan> getPlanListSwAndThisProvinceByYcPlan(Mc04IslmYcSalePlan ycSalePlan, String provinceCode) {
        // 构建查询参数
        Mc04IslmYcSalePlan plan = new Mc04IslmYcSalePlan();
        plan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        plan.setZaOccurrenceYear(ycSalePlan.getZaOccurrenceYear());
        plan.setMc04CgtSalePlanVersion(ycSalePlan.getMc04CgtSalePlanVersion());
        plan.setMc04OrgTypeKind(OrgTypeEnum.SW.getKind());
        List<Mc04IslmYcSalePlan> ycSalePlanList = ycSalePlanRepository.getYcPlanList(plan);
        // 在获取省一级的
        plan.setMc04OrgTypeKind(OrgTypeEnum.PROVINCE.getKind());
        plan.setMc04OrgTypeCode(provinceCode);
        ycSalePlanList.addAll(ycSalePlanRepository.getYcPlanList(plan));
        return ycSalePlanList;
    }

    /**
     * 获取当前业务年份当前使用版本的省内和省内公司的元春计划
     * @param ycSalePlan 全国元春
     * @param snProvinceCode 省内code
     */
    private List<Mc04IslmYcSalePlan> getPlanListSnAndProvinceByYcPlan(Mc04IslmYcSalePlan ycSalePlan, String snProvinceCode) {
        // 构建查询参数
        Mc04IslmYcSalePlan plan = new Mc04IslmYcSalePlan();
        plan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        plan.setZaOccurrenceYear(ycSalePlan.getZaOccurrenceYear());
        plan.setMc04CgtSalePlanVersion(ycSalePlan.getMc04CgtSalePlanVersion());
        plan.setMc04OrgTypeKind(OrgTypeEnum.SN.getKind());
        List<Mc04IslmYcSalePlan> ycSalePlanList = ycSalePlanRepository.getYcPlanList(plan);
        // 在获取省一级的
        plan.setMc04OrgTypeKind(OrgTypeEnum.PROVINCE.getKind());
        plan.setMc04OrgTypeCode(snProvinceCode);
        ycSalePlanList.addAll(ycSalePlanRepository.getYcPlanList(plan));
        return ycSalePlanList;
    }

    public BusiComDto getProvinceIn() {
        GetBusiComListREQ q = new GetBusiComListREQ();
        q.setMc04ComOrgLevel("01");
        q.setMc04ComOrgIsImported("0");
        q.setIcomCode(IcomUtils.getIcomCode());
        KCMultiResponse<BusiComDto> comList = comServiceAPI.getBusiComList(q);
        Collection<BusiComDto> items = comList.getData().getItems();
        List<BusiComDto> itemList = new ArrayList<>(items);
        Assert.notEmpty(itemList, () -> new CustomException("没有获取到省数据！请联系管理员"));
        return itemList.get(0);
    }

    public List<BusiComDto> getProvinceOut() {
        GetBusiComListREQ q = new GetBusiComListREQ();
        q.setMc04ComOrgLevel("01");
        q.setMc04ComOrgIsImported("1");
        q.setIcomCode(IcomUtils.getIcomCode());
        KCMultiResponse<BusiComDto> comList = comServiceAPI.getBusiComList(q);
        Collection<BusiComDto> items = comList.getData().getItems();
        List<BusiComDto> itemList = new ArrayList<>(items);
        Assert.notEmpty(itemList, () -> new CustomException("没有获取到省数据！请联系管理员"));
        return itemList;
    }

    private BigDecimal getAcCgtTaxAllotPrice(Integer packageQty2, BigDecimal acCgtTaxAllotPrice) {
        if (packageQty2 <= 0) {
            return null;
        }
        return acCgtTaxAllotPrice
                .divide(new BigDecimal(packageQty2), 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(5000));

    }

    private BigDecimal getAcCgtTradePrice(Integer packageQty2, BigDecimal acCgtTradePrice) {
        if (packageQty2 <= 0) {
            return null;
        }
        return acCgtTradePrice
                .divide(new BigDecimal(packageQty2), 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(5000));
    }

    private Collection<IccProductDetailDTO> getProductList() {
        IccGetProductListRequest iccGetProductListRequest = new IccGetProductListRequest();
        String icomCode = IcomUtils.getIcomCode();
        iccGetProductListRequest.setProviderCustomerId(Collections.singletonList(icomCode));
        iccGetProductListRequest.setAcOneLevelClassTypeCode(Collections.singletonList("01"));
        iccGetProductListRequest.setAcCigSalFlag(Collections.singletonList("1"));
        iccGetProductListRequest.setAcCigBusiTypeCode("A");
        IccMultiResponse<IccProductDetailDTO> productListResponse =
                productServiceApi.getProductList(iccGetProductListRequest);
        Assert.notNull(productListResponse, () -> new CustomException("商品中心获取商品信息失败"));
        Assert.isTrue(productListResponse.isSuccess(), () -> {
            log.error("商品中心获取商品信息失败, 商品中心返回失败信息:{}", productListResponse.getMessage());
            return new CustomException("商品中心获取商品信息失败");
        });
        IccMultiDataDTO<IccProductDetailDTO> data = productListResponse.getData();
        Assert.notNull(data, () -> new CustomException("商品中心获取商品信息失败"));
        return data.getItems();
    }

    /**
     * 根据提供的卷烟和省商业公司信息获取当前业务年份的 每个卷烟的每个省份，1,2月的占比
     */
    private Map<String, Map<String, Map<String, BigDecimal>>> computeProductProvinceRate(List<String> productCodeList, List<String> busiComCodeList, String zaOccurrenceYear) {
        List<Mc04IslmDemandFo> demandFoList = demandFoRepository.getYcDemandFoListByBusiComCode(busiComCodeList, zaOccurrenceYear);
        Assert.notEmpty(demandFoList, () -> new CustomException("没有查询到省外需求预测数据"));
        List<String> demandFoIdList = demandFoList.stream().map(Mc04IslmDemandFo::getMc04DemandFoId).collect(Collectors.toList());
        // 根据总的需求预测数据获取详细信息
        List<Mc04IslmDemandFoItem> demandFoItemList = demandFoItemRepository.getYcItemListByDemandFoIdList(demandFoIdList);
        Assert.notEmpty(demandFoItemList, () -> new CustomException("没有查询到省外需求预测数据详情"));

        Map<String, Map<String, Map<String, BigDecimal>>> productProvinceRate = new HashMap<>();
        for (String productCode : productCodeList) {
            Map<String, Map<String, BigDecimal>> provinceRateMap = new HashMap<>();
            productProvinceRate.put(productCode, provinceRateMap);
            // 计算该卷烟1月和2月的总数及明细列表
            MonthDataInfo monthDataInfo = computeMonthDataInfo(demandFoItemList, productCode);

            // 获取所有的省份区域
            List<String> orgCodeList = demandFoList.stream().map(Mc04IslmDemandFo::getBaComOrgCode).distinct().collect(Collectors.toList());
            processProvinceRateData(provinceRateMap, orgCodeList, demandFoList, monthDataInfo);
        }
        return productProvinceRate;
    }

    /**
     * 月份数据信息类，用于封装1月和2月的总数量及明细列表
     */
    private static class MonthDataInfo {
        BigDecimal januaryTotalNum;
        BigDecimal februaryTotalNum;
        List<Mc04IslmDemandFoItem> januaryList;
        List<Mc04IslmDemandFoItem> februaryList;

        MonthDataInfo(BigDecimal januaryTotalNum, BigDecimal februaryTotalNum,
                      List<Mc04IslmDemandFoItem> januaryList, List<Mc04IslmDemandFoItem> februaryList) {
            this.januaryTotalNum = januaryTotalNum;
            this.februaryTotalNum = februaryTotalNum;
            this.januaryList = januaryList;
            this.februaryList = februaryList;
        }
    }

    /**
     * 计算月份数据信息
     * @param demandFoItemList 需求预测项列表
     * @param productCode 产品编码
     * @return 月份数据信息
     */
    private MonthDataInfo computeMonthDataInfo(List<Mc04IslmDemandFoItem> demandFoItemList, String productCode) {
        // 该卷烟下先获取该卷烟 1月 2月的总数
        BigDecimal januaryTotalNum = BigDecimal.ZERO;
        BigDecimal februaryTotalNum = BigDecimal.ZERO;
        // 该卷烟下所有省的1,2月明细
        List<Mc04IslmDemandFoItem> januaryList = new ArrayList<>();
        List<Mc04IslmDemandFoItem> februaryList = new ArrayList<>();

        for (Mc04IslmDemandFoItem mc04IslmDemandFoItem : demandFoItemList) {
            // 如果不是这个卷烟就跳过
            if (!mc04IslmDemandFoItem.getAcCgtCartonCode().equals(productCode)) {
                continue;
            }
            String periodType = mc04IslmDemandFoItem.getMc04CgtSaleFoPeriodType();
            String periodCode = mc04IslmDemandFoItem.getMc04CgtSaleFoPeriodCode();

            if (periodType.equals(DemandFoItemPeriodTypeEnum.DATE_PERIOD_TYPE_CODE_T04.getCode()) && periodCode.equals(DemandFoItemPeriodTypeEnum.DATE_PERIOD_CODE_01Month.getCode())) {
                januaryTotalNum = januaryTotalNum.add(mc04IslmDemandFoItem.getMc04DemandFoConfirmQty());
                januaryList.add(mc04IslmDemandFoItem);
            } else if (periodType.equals(DemandFoItemPeriodTypeEnum.DATE_PERIOD_TYPE_CODE_T04.getCode()) && periodCode.equals(DemandFoItemPeriodTypeEnum.DATE_PERIOD_CODE_02Month.getCode())) {
                februaryTotalNum = februaryTotalNum.add(mc04IslmDemandFoItem.getMc04DemandFoConfirmQty());
                februaryList.add(mc04IslmDemandFoItem);
            }
        }

        return new MonthDataInfo(januaryTotalNum, februaryTotalNum, januaryList, februaryList);
    }

    /**
     * 处理省份占比数据
     * @param provinceRateMap 省份占比映射
     * @param orgCodeList 组织编码列表
     * @param demandFoList 需求预测列表
     * @param monthDataInfo 月份数据信息
     */
    private void processProvinceRateData(Map<String, Map<String, BigDecimal>> provinceRateMap,
                                         List<String> orgCodeList,
                                         List<Mc04IslmDemandFo> demandFoList,
                                         MonthDataInfo monthDataInfo) {
        for (String orgCode : orgCodeList) {
            Map<String, BigDecimal> rateMap = new HashMap<>();
            provinceRateMap.put(orgCode, rateMap);
            // 先根据orgCode获取该省的计划
            Mc04IslmDemandFo demandFo = demandFoList.stream().filter(item -> item.getBaComOrgCode().equals(orgCode)).findFirst().orElse(null);
            // 计算该省的月份占比
            computeProvinceMonthRate(rateMap, demandFo, monthDataInfo);
        }
    }

    /**
     * 计算省份月份占比
     * @param rateMap 比率映射
     * @param demandFo 需求预测
     * @param monthDataInfo 月份数据信息
     */
    private void computeProvinceMonthRate(Map<String, BigDecimal> rateMap,
                                          Mc04IslmDemandFo demandFo,
                                          MonthDataInfo monthDataInfo) {
        // 如果不存在该省的预测计划信息就直接设置成0
        if (demandFo == null) {
            rateMap.put("januaryRate", BigDecimal.ZERO);
            rateMap.put("februaryRate", BigDecimal.ZERO);
            return;
        }

        // 计算一月占比
        computeJanuaryRate(rateMap, demandFo, monthDataInfo);
        // 计算二月占比
        computeFebruaryRate(rateMap, demandFo, monthDataInfo);
    }

    /**
     * 计算一月占比
     * @param rateMap 比率映射
     * @param demandFo 需求预测
     * @param monthDataInfo 月份数据信息
     */
    private void computeJanuaryRate(Map<String, BigDecimal> rateMap,
                                    Mc04IslmDemandFo demandFo,
                                    MonthDataInfo monthDataInfo) {
        Mc04IslmDemandFoItem januaryItem = monthDataInfo.januaryList.stream()
                .filter(item -> item.getMc04DemandFoId().equals(demandFo.getMc04DemandFoId()))
                .findFirst().orElse(null);

        if (januaryItem == null) {
            rateMap.put("januaryRate", BigDecimal.ZERO);
        } else {
            BigDecimal januaryData = januaryItem.getMc04DemandFoConfirmQty();
            // 获取一月的占比
            if (monthDataInfo.januaryTotalNum.compareTo(BigDecimal.ZERO) == 0) {
                rateMap.put("januaryRate", BigDecimal.ZERO);
            } else {
                if (januaryData.compareTo(BigDecimal.ZERO) == 0) {
                    rateMap.put("januaryRate", BigDecimal.ZERO);
                } else {
                    rateMap.put("januaryRate", januaryData.divide(monthDataInfo.januaryTotalNum, 4, RoundingMode.HALF_UP));
                }
            }
        }
    }

    /**
     * 计算二月占比
     * @param rateMap 比率映射
     * @param demandFo 需求预测
     * @param monthDataInfo 月份数据信息
     */
    private void computeFebruaryRate(Map<String, BigDecimal> rateMap,
                                     Mc04IslmDemandFo demandFo,
                                     MonthDataInfo monthDataInfo) {
        Mc04IslmDemandFoItem febProvinceItem = monthDataInfo.februaryList.stream()
                .filter(item -> item.getMc04DemandFoId().equals(demandFo.getMc04DemandFoId()))
                .findFirst().orElse(null);

        if (febProvinceItem == null) {
            rateMap.put("februaryRate", BigDecimal.ZERO);
        } else {
            BigDecimal februaryData = febProvinceItem.getMc04DemandFoConfirmQty();

            if (monthDataInfo.februaryTotalNum.compareTo(BigDecimal.ZERO) == 0) {
                rateMap.put("februaryRate", BigDecimal.ZERO);
            } else {
                if (februaryData.compareTo(BigDecimal.ZERO) == 0) {
                    rateMap.put("februaryRate", BigDecimal.ZERO);
                } else {
                    // 获取二月的占比
                    rateMap.put("februaryRate", februaryData.divide(monthDataInfo.februaryTotalNum, 4, RoundingMode.HALF_UP));
                }
            }
        }
    }

    @ReturnValue(desc = "是否成功")
    @Method(name = "保存分解后省外各省元春计划和计划详情")
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveWithItem(YcSalePlanAllotCenter ycSalePlanAllotCenter) {

        Assert.notNull(ycSalePlanAllotCenter.getMc04SalePlanId(), () -> new CustomException("元春计划不能为空"));
        Assert.notNull(ycSalePlanAllotCenter.getYcSalePlanDetailList(), () -> new CustomException("元春计划详情不能为空"));
        // 1.首先根据id获取元春计划，如果只获取到一条数据那说明是第一次保存，需要创建好其他的元春计划的信息，如果是多条则不需要在保存元春计划，只处理元春计划详情
        Mc04IslmYcSalePlan ycSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(ycSalePlanAllotCenter.getMc04SalePlanId());
        Assert.isTrue(ycSalePlan.getMc04SalePlanStatus().equals(SalePlanStatusEnum.PROVINCIAL_PENDING.getCode()) ||
                        ycSalePlan.getMc04SalePlanStatus().equals(SalePlanStatusEnum.SALES_CONSULT.getCode())
                , () -> new CustomException("元春计划状态不是待分解到省级区域，无法保存！"));

        // 获取省内
        BusiComDto provinceIn = getProvinceIn();
        // 根据全国元春的行获取省外元春计划以及省(不包括本省)的元春计划
        List<Mc04IslmYcSalePlan> ycSalePlanSwAndProvinceList = getPlanListSwAndProvinceByYcPlan(ycSalePlan, provinceIn.getBaComOrgCode());

        // 查看计划中是否只有类型为省外的
        boolean isHaveProvince = ycSalePlanSwAndProvinceList.stream().anyMatch(plan -> plan.getMc04OrgTypeKind().equals(OrgTypeEnum.PROVINCE.getKind()));
        // 根据之前的计划获取产品列表
        Collection<IccProductDetailDTO> productList = getProductList();

        if (!isHaveProvince) {
            // 第一次保存需要保存省一级元春计划信息
            List<BusiComDto> provinceOutList = getProvinceOut();
            saveProvinceYcPlan(ycSalePlan, provinceOutList);
            // 在获取所有省一级的元春计划，除了本省以外
            List<Mc04IslmYcSalePlan> ycSalePlanProvinceList = getProvinceYcPlanListExceptCode(ycSalePlan, provinceIn.getBaComOrgCode());
            // 保存详情信息
            // 根据产品信息，元春计划，以及前端填写的详细信息构建元春计划详情
            saveSalePlanItemList(productList, ycSalePlanProvinceList, ycSalePlanAllotCenter.getYcSalePlanDetailList());
        } else {
            // 获取详细信息列表，更新详情信息，无法做到识别更改项，采用先删后插入
            List<Mc04IslmYcSalePlan> ycSalePlanProvinceList = getProvinceYcPlanListExceptCode(ycSalePlan, provinceIn.getBaComOrgCode());
            List<String> provincePlanIdList = ycSalePlanProvinceList.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
            ycSalePlanItemRepository.deleteByYcPlanIdList(provincePlanIdList);
            saveSalePlanItemList(productList, ycSalePlanProvinceList, ycSalePlanAllotCenter.getYcSalePlanDetailList());
        }
        return true;
    }

    private List<Mc04IslmYcSalePlan> getProvinceYcPlanListExceptCode(Mc04IslmYcSalePlan ycSalePlan, String baComOrgCode) {
        // 根据版本以及业务年份获取最新的元春计划列表
        // 在获取省一级的
        Mc04IslmYcSalePlan plan = new Mc04IslmYcSalePlan();
        plan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        plan.setZaOccurrenceYear(ycSalePlan.getZaOccurrenceYear());
        plan.setMc04CgtSalePlanVersion(ycSalePlan.getMc04CgtSalePlanVersion());
        plan.setMc04OrgTypeKind(OrgTypeEnum.PROVINCE.getKind());
        List<Mc04IslmYcSalePlan> ycSalePlanList = new ArrayList<>(ycSalePlanRepository.getYcPlanList(plan));
        // 排除本省
        ycSalePlanList.removeIf(item -> item.getMc04OrgTypeCode().equals(baComOrgCode));
        return ycSalePlanList;
    }

    private void saveProvinceYcPlan(Mc04IslmYcSalePlan ycSalePlan, List<BusiComDto> provinceOutList) {
        List<Mc04IslmYcSalePlan> ycSalePlanList = new ArrayList<>();
        provinceOutList.forEach(provinceOut -> {
            String code = provinceOut.getBaComOrgCode();
            String name = provinceOut.getMc04ComOrgShortName();
            String zaOccurrenceYear = ycSalePlan.getZaOccurrenceYear();
            ycSalePlanList.add(buildYcPlan(ycSalePlan, PeriodTypeEnum.MONTH.getCode(), zaOccurrenceYear + "01", code, name));
            ycSalePlanList.add(buildYcPlan(ycSalePlan, PeriodTypeEnum.MONTH.getCode(), zaOccurrenceYear + "02", code, name));
            ycSalePlanList.add(buildYcPlan(ycSalePlan, PeriodTypeEnum.YC.getCode(), zaOccurrenceYear, code, name));
        });
        ycSalePlanRepository.saveBatch(ycSalePlanList);
    }


    private Mc04IslmYcSalePlan buildYcPlan(Mc04IslmYcSalePlan mc04IslmYcSalePlan, String periodType, String periodCode, String typeCode, String typeName) {
        Mc04IslmYcSalePlan ycSalePlan = new Mc04IslmYcSalePlan();
        ycSalePlan.setMa02TobaProdTradeTypeCode(mc04IslmYcSalePlan.getMa02TobaProdTradeTypeCode());
        ycSalePlan.setMc04CgtSalePlanVersion(mc04IslmYcSalePlan.getMc04CgtSalePlanVersion());
        ycSalePlan.setMc04IsLastestVersion(mc04IslmYcSalePlan.getMc04IsLastestVersion());
        ycSalePlan.setZaOccurrenceYear(mc04IslmYcSalePlan.getZaOccurrenceYear());
        ycSalePlan.setMc04PlanSubjectType(mc04IslmYcSalePlan.getMc04PlanSubjectType());
        ycSalePlan.setMc04PlanSubjectName(mc04IslmYcSalePlan.getMc04PlanSubjectName());
        ycSalePlan.setMc04PlanSubjectBeginDate(mc04IslmYcSalePlan.getMc04PlanSubjectBeginDate());
        ycSalePlan.setMc04PlanSubjectEndDate(mc04IslmYcSalePlan.getMc04PlanSubjectEndDate());
        ycSalePlan.setMc04SalePlanStatus(mc04IslmYcSalePlan.getMc04SalePlanStatus());
        ycSalePlan.setMc04CgtSaleFoPeriodType(periodType);
        ycSalePlan.setMc04CgtSaleFoPeriodCode(periodCode);
        ycSalePlan.setMc04OrgTypeKind(OrgTypeEnum.PROVINCE.getKind());
        ycSalePlan.setMc04OrgTypeCode(typeCode);
        ycSalePlan.setMc04OrgTypeName(typeName);
        ycSalePlan.setIcomCode(mc04IslmYcSalePlan.getIcomCode());
        return ycSalePlan;
    }


    private void saveSalePlanItemList(Collection<IccProductDetailDTO> productList, List<Mc04IslmYcSalePlan> ycSalePlanProvinceList, List<Map<String, Object>> ycSalePlanDetailList) {
        List<Mc04IslmYcSalePlanItem> itemList = new ArrayList<>();
        // 这是规格列表，每个规格行需要根据9个计划进行拆解
        for (Map<String, Object> item : ycSalePlanDetailList) {
            // 通过产品列表获取产品详情
            String productCode = (String) item.get("acCgtCartonCode");
            IccProductDetailDTO productDetail = getProductDetail(productList, productCode);
            for (Mc04IslmYcSalePlan salePlan : ycSalePlanProvinceList) {
                Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem = new Mc04IslmYcSalePlanItem();
                mc04IslmYcSalePlanItem.setMc04SalePlanId(salePlan.getMc04SalePlanId());
                mc04IslmYcSalePlanItem.setAcCgtCartonCode(productDetail.getProductCode());
                mc04IslmYcSalePlanItem.setAcCgtName(productDetail.getProductName());
                mc04IslmYcSalePlanItem.setAcCgtPriceSegmentCode(productDetail.getAcCigRangeCode());
                mc04IslmYcSalePlanItem.setAcCgtPriceSegmentName(productDetail.getAcCigRangeName());
                mc04IslmYcSalePlanItem.setAcCgtTaxAllotPrice(new BigDecimal(productDetail.getAcMateTaxTranPr()));
                mc04IslmYcSalePlanItem.setAcCgtTradePrice(new BigDecimal(productDetail.getWholeSalePrice()));
                mc04IslmYcSalePlanItem.setAcCgtMediumBranceFlag(productDetail.getIsMedium());
                mc04IslmYcSalePlanItem.setAcCgtTinyFlag(productDetail.getIsTiny());
                mc04IslmYcSalePlanItem.setAcCgtTarVal(new BigDecimal(productDetail.getTarQuantity()));
                // 数量值根据不同的计划存的也不同
                buildSalePlanItemQtyData(item, mc04IslmYcSalePlanItem, salePlan);
                mc04IslmYcSalePlanItem.setIcomCode(salePlan.getIcomCode());
                itemList.add(mc04IslmYcSalePlanItem);
            }
        }
        ycSalePlanItemRepository.saveBatch(itemList);
    }

    private void buildSalePlanItemQtyData(Map<String, Object> ycSalePlanDetail, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem, Mc04IslmYcSalePlan salePlan) {
        String periodType = salePlan.getMc04CgtSaleFoPeriodType();
        String periodCode = salePlan.getMc04CgtSaleFoPeriodCode();
        String zaOccurrenceYear = salePlan.getZaOccurrenceYear();
        // N库存 + N1调拨 - N1销售 = N1库存
        if (periodType.equals(PeriodTypeEnum.YC.getCode())) {
            buildYcPeriodData(ycSalePlanDetail, mc04IslmYcSalePlanItem, salePlan);
        } else if (isMonthPeriodAndJanuary(periodType, periodCode, zaOccurrenceYear)) {
            buildJanuaryPeriodData(ycSalePlanDetail, mc04IslmYcSalePlanItem, salePlan);
        } else if (isMonthPeriodAndFebruary(periodType, periodCode, zaOccurrenceYear)) {
            buildFebruaryPeriodData(ycSalePlanDetail, mc04IslmYcSalePlanItem, salePlan);
        } else {
            throw new CustomException("计划周期异常！请联系管理员");
        }
    }

    private boolean isMonthPeriodAndJanuary(String periodType, String periodCode, String zaOccurrenceYear) {
        return periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "01");
    }

    private boolean isMonthPeriodAndFebruary(String periodType, String periodCode, String zaOccurrenceYear) {
        return periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "02");
    }

    private void buildYcPeriodData(Map<String, Object> ycSalePlanDetail, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem, Mc04IslmYcSalePlan salePlan) {
        // 外省元春
        // N年末预计库存 N年末预计库存：(N-1年末商业库存)+(N年预计调拨)-(N年预计销售)
        Object value = ycSalePlanDetail.get("currentYearEndInventoryOut" + salePlan.getMc04OrgTypeCode());
        mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO);
        // 1+2月销量
        value = ycSalePlanDetail.get("nextYearFirstTwoMonthSalesPlanOut" + salePlan.getMc04OrgTypeCode());
        mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO);
        // 1+2月调拨
        value = ycSalePlanDetail.get("nextYearFirstTwoMonthAllocationPlanOut" + salePlan.getMc04OrgTypeCode());
        mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO);
        // 2月末商业库存
        value = ycSalePlanDetail.get("nextYearSecondMonthEndingStockOut" + salePlan.getMc04OrgTypeCode());
        mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO);
    }

    private void buildJanuaryPeriodData(Map<String, Object> ycSalePlanDetail, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem, Mc04IslmYcSalePlan salePlan) {
        // 外省1月
        // 1月期初商业库存
        Object value = ycSalePlanDetail.get("currentYearEndInventoryOut" + salePlan.getMc04OrgTypeCode());
        mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO);
        // 1月期初调拨
        value = ycSalePlanDetail.get("nextYearFirstMonthSalesPlanOut" + salePlan.getMc04OrgTypeCode());
        mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO);
        // 1月期初销售
        value = ycSalePlanDetail.get("nextYearFirstMonthAllocationPlanOut" + salePlan.getMc04OrgTypeCode());
        mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO);
        // 1月期末商业库存
        value = ycSalePlanDetail.get("nextYearFirstMonthEndingStockOut" + salePlan.getMc04OrgTypeCode());
        mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO);
    }

    private void buildFebruaryPeriodData(Map<String, Object> ycSalePlanDetail, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem, Mc04IslmYcSalePlan salePlan) {
        // 外省2月
        Object value = ycSalePlanDetail.get("nextYearFirstMonthEndingStockOut" + salePlan.getMc04OrgTypeCode());
        mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO);

        value = ycSalePlanDetail.get("nextYearSecondMonthSalesPlanOut" + salePlan.getMc04OrgTypeCode());
        mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO);

        value = ycSalePlanDetail.get("nextYearSecondMonthAllocationPlanOut" + salePlan.getMc04OrgTypeCode());
        mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO);

        value = ycSalePlanDetail.get("nextYearSecondMonthEndingStockOut" + salePlan.getMc04OrgTypeCode());
        mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO);
    }


    private IccProductDetailDTO getProductDetail(Collection<IccProductDetailDTO> productList, String productCode) {
        for (IccProductDetailDTO product : productList) {
            if (product.getProductCode().equals(productCode)) {
                return product;
            }
        }
        throw new CustomException("产品详情获取失败！不存在的产品Code" + productCode);
    }

    @Method(name = "保存分解后省外各省元春计划和计划详情")
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitWithItem(YcSalePlanAllotCenter ycSalePlanAllotCenter) {
        canYcSalePlanSubmit(ycSalePlanAllotCenter);
        Mc04IslmYcSalePlan ycSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(ycSalePlanAllotCenter.getMc04SalePlanId());
        saveWithItem(ycSalePlanAllotCenter);
        List<Mc04IslmYcSalePlan> ycSalePlanList = getAllPlanListByYcPlan(ycSalePlan);

        // 全国计划和省级计划的状态，如果全国计划状态是（20:待分解到省级区域）,提交后改成（21:待审核省级区域分解结果）；如果全国计划状态是（22:待征求销区意见），提交后改成（30:待分解到地市区域）
        handlePlanStatusTransition(ycSalePlan, ycSalePlanList);

        return true;
    }

    /**
     * 处理计划状态转换
     * @param ycSalePlan 元春计划
     * @param ycSalePlanList 计划列表
     */
    private void handlePlanStatusTransition(Mc04IslmYcSalePlan ycSalePlan, List<Mc04IslmYcSalePlan> ycSalePlanList) {
        String planStatus = ycSalePlan.getMc04SalePlanStatus();

        if (SalePlanStatusEnum.PROVINCIAL_PENDING.getCode().equals(planStatus)) {
            handleProvincialPendingStatus(ycSalePlanList);
        } else if (SalePlanStatusEnum.SALES_CONSULT.getCode().equals(planStatus)) {
            handleSalesConsultStatus(ycSalePlanList);
        } else {
            throw new CustomException("元春计划状态异常！无法提交！请联系管理员");
        }
    }

    /**
     * 处理待分解到省级区域状态
     * @param ycSalePlanList 计划列表
     */
    private void handleProvincialPendingStatus(List<Mc04IslmYcSalePlan> ycSalePlanList) {
        for (Mc04IslmYcSalePlan salePlan : ycSalePlanList) {
            salePlan.setMc04SalePlanStatus(SalePlanStatusEnum.PROVINCIAL_REVIEW.getCode());
        }
        ycSalePlanRepository.updateBatch(ycSalePlanList);
    }

    /**
     * 处理待征求销区意见状态
     * @param ycSalePlanList 计划列表
     */
    private void handleSalesConsultStatus(List<Mc04IslmYcSalePlan> ycSalePlanList) {
        // 处理一些特殊省份
        List<String> specificProvinceList = getSpecificProvinceList();

        // 1)如果全国计划状态是（22:待征求销区意见），省公司编码是上表的11个时，提交后省级计划的状态是（90:已发布）;
        // 获取特殊省份的计划列表
        updatePlanStatusForSpecificProvinces(ycSalePlanList, specificProvinceList);

        // 2)如果全国计划状态是（22:待征求销区意见），省公司编码
        // 是11110000,11210000,11500000这三个时，复制一份省级计划数据到对应的地市公司，地市计划的状态是（90:已发布）。
        handleCityPlanReplication(ycSalePlanList);
    }

    /**
     * 更新特殊省份的计划状态
     * @param ycSalePlanList 计划列表
     * @param specificProvinceList 特殊省份列表
     */
    private void updatePlanStatusForSpecificProvinces(List<Mc04IslmYcSalePlan> ycSalePlanList, List<String> specificProvinceList) {
        for (Mc04IslmYcSalePlan salePlan : ycSalePlanList) {
            if (specificProvinceList.contains(salePlan.getMc04OrgTypeCode())) {
                salePlan.setMc04SalePlanStatus(SalePlanStatusEnum.PUBLISHED.getCode());
            } else {
                salePlan.setMc04SalePlanStatus(SalePlanStatusEnum.CITY_PENDING.getCode());
            }
        }
        ycSalePlanRepository.updateBatch(ycSalePlanList);
    }

    /**
     * 处理地市计划复制
     * @param ycSalePlanList 计划列表
     */
    private void handleCityPlanReplication(List<Mc04IslmYcSalePlan> ycSalePlanList) {
        List<String> specificProvinceNeedSaveCityList = Arrays.asList("11110000", "11210000", "11500000");
        List<Mc04IslmYcSalePlan> specificProvinceNeedSaveCityPlanList = ycSalePlanList.stream()
                .filter(item -> specificProvinceNeedSaveCityList.contains(item.getMc04OrgTypeCode()))
                .collect(Collectors.toList());

        // 根据省的计划复制生成一份地市的计划
        List<Mc04IslmYcSalePlan> cityYcSalePlanList = createCityPlans(specificProvinceNeedSaveCityPlanList);
        ycSalePlanRepository.saveBatch(cityYcSalePlanList);
        saveCityYcSalePlanItem(specificProvinceNeedSaveCityPlanList);
    }

    /**
     * 根据省计划创建地市计划
     * @param specificProvinceNeedSaveCityPlanList 省计划列表
     * @return 地市计划列表
     */
    private List<Mc04IslmYcSalePlan> createCityPlans(List<Mc04IslmYcSalePlan> specificProvinceNeedSaveCityPlanList) {
        List<Mc04IslmYcSalePlan> cityYcSalePlanList = new ArrayList<>();

        for (Mc04IslmYcSalePlan salePlan : specificProvinceNeedSaveCityPlanList) {
            Mc04IslmYcSalePlan cityPlan = buildCityPlanFromProvincePlan(salePlan);
            cityYcSalePlanList.add(cityPlan);
        }

        return cityYcSalePlanList;
    }

    /**
     * 根据省计划构建地市计划
     * @param provincePlan 省计划
     * @return 地市计划
     */
    private Mc04IslmYcSalePlan buildCityPlanFromProvincePlan(Mc04IslmYcSalePlan provincePlan) {
        Mc04IslmYcSalePlan cityPlan = new Mc04IslmYcSalePlan();
        cityPlan.setMa02TobaProdTradeTypeCode(provincePlan.getMa02TobaProdTradeTypeCode());
        cityPlan.setMc04CgtSalePlanVersion(provincePlan.getMc04CgtSalePlanVersion());
        cityPlan.setMc04IsLastestVersion(provincePlan.getMc04IsLastestVersion());
        cityPlan.setZaOccurrenceYear(provincePlan.getZaOccurrenceYear());
        cityPlan.setMc04PlanSubjectType(provincePlan.getMc04PlanSubjectType());
        cityPlan.setMc04PlanSubjectName(provincePlan.getMc04PlanSubjectName());
        cityPlan.setMc04PlanSubjectBeginDate(provincePlan.getMc04PlanSubjectBeginDate());
        cityPlan.setMc04PlanSubjectEndDate(provincePlan.getMc04PlanSubjectEndDate());
        cityPlan.setMc04SalePlanStatus(SalePlanStatusEnum.PUBLISHED.getCode());
        cityPlan.setMc04CgtSaleFoPeriodType(provincePlan.getMc04CgtSaleFoPeriodType());
        cityPlan.setMc04CgtSaleFoPeriodCode(provincePlan.getMc04CgtSaleFoPeriodCode());
        cityPlan.setMc04OrgTypeKind(OrgTypeEnum.CITY.getKind());

        setCityOrgInfo(cityPlan, provincePlan.getMc04OrgTypeCode());
        cityPlan.setIcomCode(provincePlan.getIcomCode());

        return cityPlan;
    }

    /**
     * 设置地市组织信息
     * @param cityPlan 地市计划
     * @param provinceCode 省编码
     */
    private void setCityOrgInfo(Mc04IslmYcSalePlan cityPlan, String provinceCode) {
        switch (provinceCode) {
            case "11110000":
                cityPlan.setMc04OrgTypeCode("11110001");
                cityPlan.setMc04OrgTypeName("北京");
                break;
            case "11210000":
                cityPlan.setMc04OrgTypeCode("11210201");
                cityPlan.setMc04OrgTypeName("大连");
                break;
            case "11500000":
                cityPlan.setMc04OrgTypeCode("11500001");
                cityPlan.setMc04OrgTypeName("重庆");
                break;
            default:
                throw new CustomException("获取地市计划异常！请联系管理员");
        }
    }

    private void saveCityYcSalePlanItem(List<Mc04IslmYcSalePlan> specificProvinceNeedSaveCityPlanList) {
        List<Mc04IslmYcSalePlanItem> cityYcSalePlanItemList = new ArrayList<>();
        // 获取所有城市的元春计划
        Mc04IslmYcSalePlan salePlan = new Mc04IslmYcSalePlan();
        salePlan.setMc04OrgTypeKind(OrgTypeEnum.CITY.getKind());
        salePlan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        List<Mc04IslmYcSalePlan> cityYcPanList = ycSalePlanRepository.getYcPlanList(salePlan);
        // 获取特殊省份的计划详情
        List<Mc04IslmYcSalePlanItem> provinceYcPlanItemList = ycSalePlanItemRepository.getListByYcPlanIdList
                (specificProvinceNeedSaveCityPlanList.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList()));
        // 将特殊省份的计划详情拷贝到对应的地市中
        for (Mc04IslmYcSalePlan provincePlan : specificProvinceNeedSaveCityPlanList) {
            // 获取和省内周期类型相同的省级计划
            String cityCode;
            if ("11110000".equals(provincePlan.getMc04OrgTypeCode())) {
                cityCode = "11110001";
            } else if ("11210000".equals(provincePlan.getMc04OrgTypeCode())) {
                cityCode = "11210201";
            } else if ("11500000".equals(provincePlan.getMc04OrgTypeCode())) {
                cityCode = "11500001";
            } else {
                throw new CustomException("获取地市计划异常！请联系管理员");
            }
            Mc04IslmYcSalePlan newCityPlan = cityYcPanList.stream()
                    .filter(item -> item.getMc04CgtSaleFoPeriodType().equals(provincePlan.getMc04CgtSaleFoPeriodType()) &&
                            item.getMc04CgtSaleFoPeriodCode().equals(provincePlan.getMc04CgtSaleFoPeriodCode()) &&
                            item.getMc04OrgTypeCode().equals(cityCode)).findFirst().get();

            for (Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem : provinceYcPlanItemList) {
                if (!provincePlan.getMc04SalePlanId().equals(mc04IslmYcSalePlanItem.getMc04SalePlanId())) {
                    continue;
                }
                Mc04IslmYcSalePlanItem newItem = new Mc04IslmYcSalePlanItem();
                newItem.setMc04SalePlanId(newCityPlan.getMc04SalePlanId());
                newItem.setAcCgtCartonCode(mc04IslmYcSalePlanItem.getAcCgtCartonCode());
                newItem.setAcCgtName(mc04IslmYcSalePlanItem.getAcCgtName());
                newItem.setAcCgtPriceSegmentCode(mc04IslmYcSalePlanItem.getAcCgtPriceSegmentCode());
                newItem.setAcCgtPriceSegmentName(mc04IslmYcSalePlanItem.getAcCgtPriceSegmentName());
                newItem.setAcCgtTaxAllotPrice(mc04IslmYcSalePlanItem.getAcCgtTaxAllotPrice());
                newItem.setAcCgtTradePrice(mc04IslmYcSalePlanItem.getAcCgtTradePrice());
                newItem.setAcCgtMediumBranceFlag(mc04IslmYcSalePlanItem.getAcCgtMediumBranceFlag());
                newItem.setAcCgtTinyFlag(mc04IslmYcSalePlanItem.getAcCgtTinyFlag());
                newItem.setAcCgtTarVal(mc04IslmYcSalePlanItem.getAcCgtTarVal());
                newItem.setMd03Cgt10thComInitStkQty(mc04IslmYcSalePlanItem.getMd03Cgt10thComInitStkQty());
                newItem.setMc04CgtSalePlanOriginQty(mc04IslmYcSalePlanItem.getMc04CgtSalePlanOriginQty());
                newItem.setMa02CgtPlResolOrigplanQty(mc04IslmYcSalePlanItem.getMa02CgtPlResolOrigplanQty());
                newItem.setMd03Cgt10thComEndStkQty(mc04IslmYcSalePlanItem.getMd03Cgt10thComEndStkQty());
                newItem.setIcomCode(mc04IslmYcSalePlanItem.getIcomCode());
                cityYcSalePlanItemList.add(newItem);
            }
        }
        ycSalePlanItemRepository.saveBatch(cityYcSalePlanItemList);
    }

    private static List<String> getSpecificProvinceList() {
        // 序号	省份编码	省份名称	地市编码	地市名称
        // 1	11110000	北京	11110001	北京
        // 2	11210000	大连	11210201	大连
        // 3	11500000	重庆	11500001	重庆
        // 4	11120001	天津
        // 5	11440301	深圳
        // 6	11460001	海南
        // 7	11540001	西藏
        // 8	11630001	青海
        // 9	11640001	宁夏
        // 10	11650001	新疆
        // 11	99310101	上海
        List<String> specificProvinceList = new ArrayList<>();
        specificProvinceList.add("11110000");
        specificProvinceList.add("11210000");
        specificProvinceList.add("11500000");
        specificProvinceList.add("11120001");
        specificProvinceList.add("11440301");
        specificProvinceList.add("11460001");
        specificProvinceList.add("11540001");
        specificProvinceList.add("11630001");
        specificProvinceList.add("11640001");
        specificProvinceList.add("11650001");
        specificProvinceList.add("99310101");
        return specificProvinceList;
    }

    private List<Mc04IslmYcSalePlan> getAllPlanListByYcPlan(Mc04IslmYcSalePlan ycSalePlan) {
        // 根据版本以及业务年份获取最新的元春计划列表
        Mc04IslmYcSalePlan plan = new Mc04IslmYcSalePlan();
        plan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        plan.setZaOccurrenceYear(ycSalePlan.getZaOccurrenceYear());
        plan.setMc04CgtSalePlanVersion(ycSalePlan.getMc04CgtSalePlanVersion());
        return ycSalePlanRepository.getYcPlanList(plan);
    }

    private void canYcSalePlanSubmit(YcSalePlanAllotCenter ycSalePlanAllotCenter) {
        List<BusiComDto> provinceOutList = getProvinceOut();
        for (Map<String, Object> ycSalePlanDetail : ycSalePlanAllotCenter.getYcSalePlanDetailList()) {
            validatePlanDetail(ycSalePlanDetail, provinceOutList);
        }
    }

    /**
     * 验证计划详情
     * @param ycSalePlanDetail 计划详情
     * @param provinceOutList 省外列表
     */
    private void validatePlanDetail(Map<String, Object> ycSalePlanDetail, List<BusiComDto> provinceOutList) {
        // 获取省外计划总量
        PlanSummary planSummary = extractPlanSummary(ycSalePlanDetail);

        // 验证各省库存并计算分解总量
        PlanSummary provincePlanSummary = validateProvincesAndCalculateTotals(ycSalePlanDetail, provinceOutList);

        // 校验各规格的各省级区域的销售计划和调拨计划分解量合计需等于省外计划量
        validatePlanSums(planSummary, provincePlanSummary);
    }

    /**
     * 计划汇总数据类
     */
    private static class PlanSummary {
        BigDecimal firstMonthSaleSum;
        BigDecimal firstMonthAllocationSum;
        BigDecimal secondMonthSaleSum;
        BigDecimal secondMonthAllocationSum;

        PlanSummary(BigDecimal firstMonthSaleSum, BigDecimal firstMonthAllocationSum,
                    BigDecimal secondMonthSaleSum, BigDecimal secondMonthAllocationSum) {
            this.firstMonthSaleSum = firstMonthSaleSum;
            this.firstMonthAllocationSum = firstMonthAllocationSum;
            this.secondMonthSaleSum = secondMonthSaleSum;
            this.secondMonthAllocationSum = secondMonthAllocationSum;
        }
    }

    /**
     * 提取计划汇总数据
     * @param ycSalePlanDetail 计划详情
     * @return 计划汇总数据
     */
    private PlanSummary extractPlanSummary(Map<String, Object> ycSalePlanDetail) {
        BigDecimal firstMonthSaleSum = convertToBigDecimal(ycSalePlanDetail.get("nextYearFirstMonthSalesPlanOut"));
        BigDecimal firstMonthAllocationSum = convertToBigDecimal(ycSalePlanDetail.get("nextYearFirstMonthAllocationPlanOut"));
        BigDecimal secondMonthSaleSum = convertToBigDecimal(ycSalePlanDetail.get("nextYearSecondMonthSalesPlanOut"));
        BigDecimal secondMonthAllocationSum = convertToBigDecimal(ycSalePlanDetail.get("nextYearSecondMonthAllocationPlanOut"));

        return new PlanSummary(firstMonthSaleSum, firstMonthAllocationSum,
                secondMonthSaleSum, secondMonthAllocationSum);
    }

    /**
     * 验证各省库存并计算分解总量
     * @param ycSalePlanDetail 计划详情
     * @param provinceOutList 省外列表
     * @return 各省计划汇总数据
     */
    private PlanSummary validateProvincesAndCalculateTotals(Map<String, Object> ycSalePlanDetail, List<BusiComDto> provinceOutList) {
        BigDecimal provinceFirstMonthSaleSum = BigDecimal.ZERO;
        BigDecimal provinceFirstMonthAllocationSum = BigDecimal.ZERO;
        BigDecimal provinceSecondMonthSaleSum = BigDecimal.ZERO;
        BigDecimal provinceSecondMonthAllocationSum = BigDecimal.ZERO;

        // 对于每个省级区域，进行如下校验：
        for (BusiComDto provinceOut : provinceOutList) {
            String comOrgCode = provinceOut.getBaComOrgCode();
            // N+1年1月末商业库存>=0；
            BigDecimal nextYearFirstMonthEndingStockOut = convertToBigDecimal(ycSalePlanDetail.get("nextYearFirstMonthEndingStockOut" + comOrgCode));
            if (nextYearFirstMonthEndingStockOut.compareTo(BigDecimal.ZERO) < 0) {
                throw new CustomException("N+1年1月末商业库存小于0");
            }
            // N+1年2月末商业库存>=0；
            BigDecimal nextYearSecondMonthEndingStock = convertToBigDecimal(ycSalePlanDetail.get("nextYearSecondMonthEndingStock" + provinceOut.getBaComOrgCode()));
            if (nextYearSecondMonthEndingStock.compareTo(BigDecimal.ZERO) < 0) {
                throw new CustomException("N+1年2月末商业库存小于0");
            }

            provinceFirstMonthSaleSum = provinceFirstMonthSaleSum.add(
                    convertToBigDecimal(ycSalePlanDetail.get("nextYearFirstMonthSalesPlanOut" + comOrgCode)));
            provinceFirstMonthAllocationSum = provinceFirstMonthAllocationSum.add(
                    convertToBigDecimal(ycSalePlanDetail.get("nextYearFirstMonthAllocationPlanOut" + comOrgCode)));
            provinceSecondMonthSaleSum = provinceSecondMonthSaleSum.add(
                    convertToBigDecimal(ycSalePlanDetail.get("nextYearSecondMonthSalesPlanOut" + comOrgCode)));
            provinceSecondMonthAllocationSum = provinceSecondMonthAllocationSum.add(
                    convertToBigDecimal(ycSalePlanDetail.get("nextYearSecondMonthAllocationPlanOut" + comOrgCode)));
        }

        return new PlanSummary(provinceFirstMonthSaleSum, provinceFirstMonthAllocationSum,
                provinceSecondMonthSaleSum, provinceSecondMonthAllocationSum);
    }

    /**
     * 校验计划总和
     * @param planSummary 省外计划汇总
     * @param provincePlanSummary 省计划汇总
     */
    private void validatePlanSums(PlanSummary planSummary, PlanSummary provincePlanSummary) {
        // 校验各规格的各省级区域的1月销售计划和调拨计划分解量合计需等于省外1月计划量；
        if (planSummary.firstMonthSaleSum.compareTo(provincePlanSummary.firstMonthSaleSum) != 0) {
            throw new CustomException("各规格的各省级区域的1月销售计划不等于省外1月计划量");
        }
        if (planSummary.firstMonthAllocationSum.compareTo(provincePlanSummary.firstMonthAllocationSum) != 0) {
            throw new CustomException("各规格的各省级区域的1月调拨计划不等于省外1月计划量");
        }
        // 2月销售计划和调拨计划分解量合计需等于省外2月计划量；
        if (planSummary.secondMonthSaleSum.compareTo(provincePlanSummary.secondMonthSaleSum) != 0) {
            throw new CustomException("各规格的各省级区域的2月销售计划不等于省外2月计划量");
        }
        if (planSummary.secondMonthAllocationSum.compareTo(provincePlanSummary.secondMonthAllocationSum) != 0) {
            throw new CustomException("各规格的各省级区域的2月调拨计划不等于省外2月计划量");
        }
    }

    @ReturnValue(desc = "是否成功")
    @Method(name = "保存审核界面分解后省外各省元春计划和计划详情")
    public Boolean saveReviewWithItem(YcSalePlanAllotCenter ycSalePlanAllotCenter) {
        String ycSalePlanId = ycSalePlanAllotCenter.getMc04SalePlanId();
        Assert.notNull(ycSalePlanId, () -> new CustomException("元春计划不能为空"));
        Assert.notNull(ycSalePlanAllotCenter.getYcSalePlanDetailList(), () -> new CustomException("元春计划详情不能为空"));

        Mc04IslmYcSalePlan ycSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(ycSalePlanId);
        Assert.isTrue(ycSalePlan.getMc04SalePlanStatus().equals(SalePlanStatusEnum.PROVINCIAL_REVIEW.getCode()), () -> new CustomException("元春计划状态不是待审核省级区域分解结果，无法保存！"));
        // 获取所有省一级的元春计划
        List<Mc04IslmYcSalePlan> ycSalePlanProvinceList = getProvinceYcPlanList(ycSalePlan);
        Assert.notEmpty(ycSalePlanProvinceList, () -> new CustomException("没有找到省一级的元春计划"));
        List<String> ycSalePlanIdList = ycSalePlanProvinceList.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
        // 采用先删后插入，删除省一级元春计划详情，再插入
        ycSalePlanItemRepository.deleteByYcPlanIdList(ycSalePlanIdList);
        Collection<IccProductDetailDTO> productList = getProductList();
        saveSalePlanItemList(productList, ycSalePlanProvinceList, ycSalePlanAllotCenter.getYcSalePlanDetailList());
        return true;
    }

    @ReturnValue(desc = "是否成功")
    @Method(name = "保存审核界面分解后省外各省元春计划和计划详情")
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitReviewWithItem(YcSalePlanAllotCenter ycSalePlanAllotCenter) {
        canYcSalePlanSubmit(ycSalePlanAllotCenter);
        Mc04IslmYcSalePlan ycSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(ycSalePlanAllotCenter.getMc04SalePlanId());
        saveReviewWithItem(ycSalePlanAllotCenter);
        List<Mc04IslmYcSalePlan> ycSalePlanList = getAllPlanListByYcPlan(ycSalePlan);
        for (Mc04IslmYcSalePlan salePlan : ycSalePlanList) {
            salePlan.setMc04SalePlanStatus(SalePlanStatusEnum.SALES_CONSULT.getCode());
        }
        ycSalePlanRepository.updateBatch(ycSalePlanList);
        return true;
    }

    @ReturnValue(desc = "是否成功")
    @Method(name = "驳回分解后省外各省元春计划和计划详情")
    public Boolean reject(String mc04SalePlanId) {
        Mc04IslmYcSalePlan ycSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(mc04SalePlanId);
        Assert.isTrue(ycSalePlan.getMc04SalePlanStatus().equals(SalePlanStatusEnum.PROVINCIAL_REVIEW.getCode()), () -> new CustomException("元春计划状态不是待审核省级区域分解结果，无法驳回！"));
        List<Mc04IslmYcSalePlan> ycSalePlanList = getAllPlanListByYcPlan(ycSalePlan);
        for (Mc04IslmYcSalePlan salePlan : ycSalePlanList) {
            salePlan.setMc04SalePlanStatus(SalePlanStatusEnum.PROVINCIAL_PENDING.getCode());
        }
        ycSalePlanRepository.updateBatch(ycSalePlanList);
        return true;
    }

    private List<Mc04IslmYcSalePlan> getProvinceYcPlanList(Mc04IslmYcSalePlan ycSalePlan) {
        Mc04IslmYcSalePlan plan = new Mc04IslmYcSalePlan();
        plan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        plan.setZaOccurrenceYear(ycSalePlan.getZaOccurrenceYear());
        plan.setMc04CgtSalePlanVersion(ycSalePlan.getMc04CgtSalePlanVersion());
        plan.setMc04OrgTypeKind(OrgTypeEnum.PROVINCE.getCode());
        return ycSalePlanRepository.getYcPlanList(plan);
    }

    private List<Mc04IslmYcSalePlan> getProvinceYcPlanList(Mc04IslmYcSalePlan ycSalePlan, String provinceCode) {
        Mc04IslmYcSalePlan plan = new Mc04IslmYcSalePlan();
        plan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        plan.setZaOccurrenceYear(ycSalePlan.getZaOccurrenceYear());
        plan.setMc04CgtSalePlanVersion(ycSalePlan.getMc04CgtSalePlanVersion());
        plan.setMc04OrgTypeKind(OrgTypeEnum.PROVINCE.getCode());
        plan.setMc04OrgTypeCode(provinceCode);
        return ycSalePlanRepository.getYcPlanList(plan);
    }

    private List<Mc04IslmYcSalePlan> getSwYcPlanExceptYcList(Mc04IslmYcSalePlan ycSalePlan) {
        Mc04IslmYcSalePlan plan = new Mc04IslmYcSalePlan();
        plan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        plan.setZaOccurrenceYear(ycSalePlan.getZaOccurrenceYear());
        plan.setMc04OrgTypeKind(OrgTypeEnum.SW.getKind());
        return ycSalePlanRepository.getYcPlanList(plan).stream().filter(item -> !item.getMc04CgtSaleFoPeriodType().equals(PeriodTypeEnum.YC.getCode())).collect(Collectors.toList());
    }

    @ReturnValue(desc = "某规格分解数据")
    @Method(name = "获取某规格下的数据")
    public List<YcSalePlanAllotCenterProductView> getProductViewData(String mc04SalePlanId, String productCode) {
        List<YcSalePlanAllotCenterProductView> ycSalePlanAllotCenterProductViewList = new ArrayList<>();
        // 根据id获取元春计划
        Mc04IslmYcSalePlan mc04IslmYcSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById((mc04SalePlanId));

        // 1.先添加省外数据
        buildSwProvinceViewData(ycSalePlanAllotCenterProductViewList, mc04IslmYcSalePlan, productCode);

        // 2.获取所有省外各省的计划与计划详情
        // 根据获取的元春计划获取所有省一级的元春计划
        List<Mc04IslmYcSalePlan> ycSaleProvincePlanList = getProvinceYcPlanList(mc04IslmYcSalePlan);
        // 根据省一级的元春计划和产品code获取所有省一级元春计划详情
        List<String> planIdList = ycSaleProvincePlanList.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
        // 获取所有该卷烟规格的计划详情
        List<Mc04IslmYcSalePlanItem> ycSaleProvincePlanItemList = ycSalePlanItemRepository.getListByYcPlanIdListAndProductCode(planIdList, productCode);

        // 需要获取所有省外的省列表
        List<BusiComDto> provinceOutList = getProvinceOut();
        // 对于需要初始化的省计划详情数据需要按照以下三步进行
        // 3.在获取该卷烟下各个省份在需求预测中计算的占比，根据占比对各个省的销售和调拨初始化
        List<String> busiComCodeList = provinceOutList.stream().map(BusiComDto::getBaComOrgCode).collect(Collectors.toList());
        Map<String, Map<String, Map<String, BigDecimal>>> productProvinceRate = computeProductProvinceRate(Collections.singletonList(productCode), busiComCodeList, mc04IslmYcSalePlan.getZaOccurrenceYear());

        // 获取每个省的每个卷烟的N-1期初库存，当前年上一年销售、调拨和期末库存
        ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear = ycSalePlanRepository.getProvinceEndStkSaleAllotInfoForYear(mc04IslmYcSalePlan.getZaOccurrenceYear());

        // 循环各省查看是否有该省的元春计划
        processProvincePlanData(ycSalePlanAllotCenterProductViewList, provinceOutList, ycSaleProvincePlanList,
                ycSaleProvincePlanItemList, productProvinceRate, provinceEndStkSaleAllotInfoForYear, productCode);

        return ycSalePlanAllotCenterProductViewList;
    }

    /**
     * 添加省外视图数据
     * @param viewList 视图列表
     * @param ycSalePlan 元春计划
     * @param productCode 产品编码
     */
    private void buildSwProvinceViewData(List<YcSalePlanAllotCenterProductView> viewList, Mc04IslmYcSalePlan ycSalePlan, String productCode) {
        List<Mc04IslmYcSalePlan> swPlans = getSwYcPlanExceptYcList(ycSalePlan);
        List<String> swPlanIds = swPlans.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
        List<Mc04IslmYcSalePlanItem> swItems = ycSalePlanItemRepository.getListByYcPlanIdListAndProductCode(swPlanIds, productCode);

        YcSalePlanListWithItemList swYcPlanListWithItemList = new YcSalePlanListWithItemList(swPlans, swItems);
        swYcPlanListWithItemList.buildSwJanAndFebYcPlanItemProductMap();

        // 构建该卷烟产品的省外数据
        YcSalePlanAllotCenterProductView swView = new YcSalePlanAllotCenterProductView();
        swView.setProvinceName("省外计划");
        swView.setProvinceCode("SW");
        swView.setIsProvince(false);

        // 设置 N-1年商业库存 N年预计销售 N年预计调拨 N年末预计库存
        SnSwEndStkSaleAllotInfoForYear swEndStkSaleAllotInfoForYear = ycSalePlanRepository.getSnSwEndStkSaleAllotInfoForYear(ycSalePlan.getZaOccurrenceYear());
        swView.setLastYearStkQty(swEndStkSaleAllotInfoForYear.getLastYearEndStkQtyProvinceOut(productCode));
        swView.setCurrentYearSales(swEndStkSaleAllotInfoForYear.getCurrentYearPlanSaleQtyProvinceOut(productCode));
        swView.setCurrentYearAllocation(swEndStkSaleAllotInfoForYear.getCurrentYearPlanAllotQtyProvinceOut(productCode));
        swView.setCurrentYearEndStock(swEndStkSaleAllotInfoForYear.getCurrentYearEndStkQtyProvinceOut(productCode));

        // 设置省外1,2月销售和调拨的数据
        swView.setNextYearFirstMonthSalesPlan(swYcPlanListWithItemList.getSwJanYcPlanItem(productCode).getMc04CgtSalePlanOriginQty());
        swView.setNextYearFirstMonthAllocationPlan(swYcPlanListWithItemList.getSwJanYcPlanItem(productCode).getMa02CgtPlResolOrigplanQty());
        swView.setNextYearSecondMonthSalesPlan(swYcPlanListWithItemList.getSwFebYcPlanItem(productCode).getMc04CgtSalePlanOriginQty());
        swView.setNextYearSecondMonthAllocationPlan(swYcPlanListWithItemList.getSwFebYcPlanItem(productCode).getMa02CgtPlResolOrigplanQty());
        viewList.add(swView);
    }

    /**
     * 处理各省计划数据
     * @param viewList 视图列表
     * @param provinceOutList 省外列表
     * @param ycSaleProvincePlanList 省计划列表
     * @param ycSaleProvincePlanItemList 省计划项列表
     * @param productProvinceRate 产品省占比
     * @param provinceEndStkSaleAllotInfoForYear 省库存销售调拨信息
     * @param productCode 产品编码
     */
    private void processProvincePlanData(List<YcSalePlanAllotCenterProductView> viewList,
                                         List<BusiComDto> provinceOutList,
                                         List<Mc04IslmYcSalePlan> ycSaleProvincePlanList,
                                         List<Mc04IslmYcSalePlanItem> ycSaleProvincePlanItemList,
                                         Map<String, Map<String, Map<String, BigDecimal>>> productProvinceRate,
                                         ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear,
                                         String productCode) {
        for (BusiComDto provinceOut : provinceOutList) {
            // 过滤省一级的元春计划列表，查看是否存在该省计划
            List<Mc04IslmYcSalePlan> provincePlanList = ycSaleProvincePlanList.stream()
                    .filter(ycSalePlan -> ycSalePlan.getMc04OrgTypeCode().equals(provinceOut.getBaComOrgCode()))
                    .collect(Collectors.toList());

            // 处理该省数据
            processSingleProvinceData(viewList, provinceOut, provincePlanList, ycSaleProvincePlanItemList,
                    productProvinceRate, provinceEndStkSaleAllotInfoForYear, productCode);
        }
    }

    /**
     * 处理单个省份数据
     * @param viewList 视图列表
     * @param provinceOut 省外信息
     * @param provincePlanList 省计划列表
     * @param provinceProductPlanItemList 省计划项列表
     * @param productProvinceRate 产品省占比
     * @param provinceEndStkSaleAllotInfoForYear 省库存销售调拨信息
     * @param productCode 产品编码
     */
    private void processSingleProvinceData(List<YcSalePlanAllotCenterProductView> viewList,
                                           BusiComDto provinceOut,
                                           List<Mc04IslmYcSalePlan> provincePlanList,
                                           List<Mc04IslmYcSalePlanItem> provinceProductPlanItemList,
                                           Map<String, Map<String, Map<String, BigDecimal>>> productProvinceRate,
                                           ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear,
                                           String productCode) {
        // 获取省外计划数据
        YcSalePlanAllotCenterProductView swProductView = viewList.get(0);
        // 如果没有该省的计划，则需要构建该省的计划详情
        if (CollectionUtils.isEmpty(provincePlanList)) {
            // 对该省下 元春、1月、2月的计划详情全部进行初始化
            viewList.add(initProductView(swProductView, provinceOut, productProvinceRate.get(productCode), provinceEndStkSaleAllotInfoForYear, productCode));
        } else {
            List<String> provincePlanIdList = provincePlanList.stream()
                    .map(Mc04IslmYcSalePlan::getMc04SalePlanId)
                    .collect(Collectors.toList());

            // 如果有该省计划，则需要获取该省计划详情，需要获取该省是否有该规格的计划详情
            List<Mc04IslmYcSalePlanItem> provinceProductItems = provinceProductPlanItemList.stream()
                    .filter(item -> item.getAcCgtCartonCode().equals(productCode) && provincePlanIdList.contains(item.getMc04SalePlanId()))
                    .collect(Collectors.toList());

            // 如果该省计划中不存在该规格的计划详情，则需要构建该省该卷烟规格的详情
            if (CollectionUtils.isEmpty(provinceProductItems)) {
                viewList.add(initProductView(swProductView, provinceOut, productProvinceRate.get(productCode), provinceEndStkSaleAllotInfoForYear, productCode));
            } else {
                // 用表里的值进行初始化
                viewList.add(buildProductView(provincePlanList, provinceProductItems, provinceEndStkSaleAllotInfoForYear));
            }
        }
    }

    /**
     * 通过省计划和省下某卷烟规格的计划详情，构建卷烟规格视图
     *
     * @param provincePlanList 省计划列表
     * @param provinceProductPlanItemList 省计划详情列表
     */
    private YcSalePlanAllotCenterProductView buildProductView(List<Mc04IslmYcSalePlan> provincePlanList,
                                                              List<Mc04IslmYcSalePlanItem> provinceProductPlanItemList,
                                                              ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear) {


        YcSalePlanListWithItemList oneProvinceYcSalePlanListWithItemList = new YcSalePlanListWithItemList(provincePlanList, provinceProductPlanItemList);
        // 构建该省公司1月2月的数据
        oneProvinceYcSalePlanListWithItemList.buildOneProvinceJanAndFebYcPlanItemProductMap();
        String provinceCode = oneProvinceYcSalePlanListWithItemList.getProvinceCode();
        String provinceName = oneProvinceYcSalePlanListWithItemList.getProvinceName();

        YcSalePlanAllotCenterProductView productView = new YcSalePlanAllotCenterProductView();

        String productCode = provinceProductPlanItemList.get(0).getAcCgtCartonCode();

        // N-1年末商业库存 mc04_islm_sale_plan_item表md03_cgt_10th_com_init_stk_qty字段
        productView.setLastYearStkQty(provinceEndStkSaleAllotInfoForYear.getLastYearEndStkQty(provinceCode, productCode));
        // N年预计销售 mc04_islm_sale_plan_item表mc04_cgt_sale_plan_adjusted_qty字段
        productView.setCurrentYearSales(provinceEndStkSaleAllotInfoForYear.getCurrentYearPlanSaleQty(provinceCode, productCode));
        // N年预计调拨 mc04_islm_sale_plan_item表ma02_cgt_pl_adjusted_qty字段
        productView.setCurrentYearAllocation(provinceEndStkSaleAllotInfoForYear.getCurrentYearPlanAllotQty(provinceCode, productCode));
        // N年末预计库存 mc04_islm_sale_plan_item表md03_cgt_10th_com_end_stk_qty字段
        productView.setCurrentYearEndStock(provinceEndStkSaleAllotInfoForYear.getCurrentYearEndStkQty(provinceCode, productCode));

        productView.setProvinceName(provinceName);
        productView.setProvinceCode(provinceCode);
        productView.setIsProvince(true);

        // N+1年1月销售计划：各省数据可编辑
        productView.setNextYearFirstMonthSalesPlan(oneProvinceYcSalePlanListWithItemList.getOneProvinceJanYcPlanItem(productCode).getMc04CgtSalePlanOriginQty());
        // N+1年1月调拨计划：各省数据可编辑
        productView.setNextYearFirstMonthAllocationPlan(oneProvinceYcSalePlanListWithItemList.getOneProvinceJanYcPlanItem(productCode).getMa02CgtPlResolOrigplanQty());
        // N+1年1月末商业库存
        productView.setNextYearFirstMonthEndingStock(oneProvinceYcSalePlanListWithItemList.getOneProvinceJanYcPlanItem(productCode).getMd03Cgt10thComEndStkQty());
        // N+1年2月销售计划：各省数据可编辑
        productView.setNextYearSecondMonthSalesPlan(oneProvinceYcSalePlanListWithItemList.getOneProvinceFebYcPlanItem(productCode).getMc04CgtSalePlanOriginQty());
        // N+1年2月调拨计划：各省数据可编辑
        productView.setNextYearSecondMonthAllocationPlan(oneProvinceYcSalePlanListWithItemList.getOneProvinceFebYcPlanItem(productCode).getMa02CgtPlResolOrigplanQty());
        // N+1年2月末商业库存
        productView.setNextYearSecondMonthEndingStock(oneProvinceYcSalePlanListWithItemList.getOneProvinceFebYcPlanItem(productCode).getMd03Cgt10thComEndStkQty());

        return productView;
    }

    private YcSalePlanAllotCenterProductView initProductView(YcSalePlanAllotCenterProductView swProductView,
                                                             BusiComDto provinceOut, Map<String, Map<String, BigDecimal>> provinceRate,
                                                             ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear,
                                                             String productCode) {

        String provinceCode = provinceOut.getBaComOrgCode();
        YcSalePlanAllotCenterProductView productView = new YcSalePlanAllotCenterProductView();
        productView.setProvinceName(provinceOut.getMc04ComOrgShortName());
        productView.setProvinceCode(provinceCode);
        productView.setIsProvince(true);

        // N-1年末商业库存 mc04_islm_sale_plan_item表md03_cgt_10th_com_init_stk_qty字段
        productView.setLastYearStkQty(provinceEndStkSaleAllotInfoForYear.getLastYearEndStkQty(provinceCode, productCode));
        // N年预计销售 mc04_islm_sale_plan_item表mc04_cgt_sale_plan_adjusted_qty字段
        productView.setCurrentYearSales(provinceEndStkSaleAllotInfoForYear.getCurrentYearPlanSaleQty(provinceCode, productCode));
        // N年预计调拨 mc04_islm_sale_plan_item表ma02_cgt_pl_adjusted_qty字段
        productView.setCurrentYearAllocation(provinceEndStkSaleAllotInfoForYear.getCurrentYearPlanAllotQty(provinceCode, productCode));
        // N年末预计库存 mc04_islm_sale_plan_item表md03_cgt_10th_com_end_stk_qty字段
        productView.setCurrentYearEndStock(provinceEndStkSaleAllotInfoForYear.getCurrentYearEndStkQty(provinceCode, productCode));

        BigDecimal swJanSalesTotalNum = swProductView.getNextYearFirstMonthSalesPlan();
        BigDecimal swFebSalesTotalNum = swProductView.getNextYearSecondMonthSalesPlan();
        BigDecimal swJanAllocateTotalNum = swProductView.getNextYearFirstMonthAllocationPlan();
        BigDecimal swFebAllocateTotalNum = swProductView.getNextYearSecondMonthAllocationPlan();

        Map<String, BigDecimal> zeroMap = new HashMap<>();
        zeroMap.put("januaryRate", BigDecimal.ZERO);
        zeroMap.put("februaryRate", BigDecimal.ZERO);

        // N+1年1月销售计划：各省数据可编辑
        productView.setNextYearFirstMonthSalesPlan(swJanSalesTotalNum.multiply(provinceRate.getOrDefault(provinceOut.getBaComOrgCode(), zeroMap).get("januaryRate")));
        // N+1年1月调拨计划：各省数据可编辑
        productView.setNextYearFirstMonthAllocationPlan(swJanAllocateTotalNum.multiply(provinceRate.getOrDefault(provinceOut.getBaComOrgCode(), zeroMap).get("januaryRate")));

        // N+1年2月销售计划：各省数据可编辑
        productView.setNextYearSecondMonthSalesPlan(swFebSalesTotalNum.multiply(provinceRate.getOrDefault(provinceOut.getBaComOrgCode(), zeroMap).get("februaryRate")));
        // N+1年2月调拨计划：各省数据可编辑
        productView.setNextYearSecondMonthAllocationPlan(swFebAllocateTotalNum.multiply(provinceRate.getOrDefault(provinceOut.getBaComOrgCode(), zeroMap).get("februaryRate")));

        return productView;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean saveProductViewData(String mc04SalePlanId, String productCode, List<YcSalePlanAllotCenterProductView> productViewList) {
        // 先校验 id 和 code 是否为空
        Assert.notNull(mc04SalePlanId, () -> new CustomException("元春计划不能为空"));
        Assert.notNull(productCode, () -> new CustomException("选择商品不能为空"));
        // 先根据id获取元春计划
        Mc04IslmYcSalePlan ycSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById((mc04SalePlanId));
        // 根据元春计划获取所有省外的元春计划
        List<Mc04IslmYcSalePlan> mc04IslmYcSalePlanList = getProvinceYcPlanList(ycSalePlan);
        List<String> salePlanIdList = mc04IslmYcSalePlanList.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
        List<Mc04IslmYcSalePlanItem> productSalePlanItemList = ycSalePlanItemRepository.getListByYcPlanIdListAndProductCode(salePlanIdList, productCode);
        // 获取外省信息
        List<BusiComDto> provinceOutList = getProvinceOut();
        Collection<IccProductDetailDTO> productList = getProductList();
        IccProductDetailDTO product = productList.stream().filter(item -> item.getProductCode().equals(productCode)).findFirst().orElse(null);
        // 需要和传过来的数据进行比对，查看是否存在该省的计划
        for (YcSalePlanAllotCenterProductView ycSalePlanAllotCenterProductView : productViewList) {
            String provinceCode = ycSalePlanAllotCenterProductView.getProvinceCode();
            List<Mc04IslmYcSalePlan> provinceYcPlanList = mc04IslmYcSalePlanList.stream()
                    .filter(item -> item.getMc04OrgTypeCode().equals(provinceCode)).collect(Collectors.toList());
            // 如果不存在就需要新增该省的计划
            if (CollectionUtils.isEmpty(provinceYcPlanList)) {
                // 保存省一级元春计划信息
                List<BusiComDto> province = provinceOutList.stream().filter(item -> item.getBaComOrgCode().equals(provinceCode)).collect(Collectors.toList());
                saveProvinceYcPlan(ycSalePlan, province);
                // 获取刚才保存的省信息
                provinceYcPlanList = getProvinceYcPlanList(ycSalePlan, provinceCode);
                Assert.notNull(product, () -> new CustomException("没有找到对应的卷烟信息"));
                saveSalePlanItemListForProductView(product, provinceYcPlanList, ycSalePlanAllotCenterProductView);
            } else {
                List<String> provinceYcPlanIdList = provinceYcPlanList.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
                // 如果存在就需要确认，就需要确认该省计划下是否存在该卷烟规格的计划详情
                List<Mc04IslmYcSalePlanItem> mc04IslmYcSalePlanItemList = productSalePlanItemList.stream()
                        .filter(item -> provinceYcPlanIdList.contains(item.getMc04SalePlanId()) && item.getAcCgtCartonCode().equals(productCode))
                        .collect(Collectors.toList());
                // 如果只是有计划没有详情，就需要新增，如果没有就先删除后插入
                if (!CollectionUtils.isEmpty(mc04IslmYcSalePlanItemList)) {
                    ycSalePlanItemRepository.deleteByYcPlanIdListAndProductCode(provinceYcPlanIdList, productCode);
                }
                saveSalePlanItemListForProductView(product, provinceYcPlanList, ycSalePlanAllotCenterProductView);
            }
        }
        return true;
    }

    private void saveSalePlanItemListForProductView(IccProductDetailDTO product, List<Mc04IslmYcSalePlan> provinceYcPlanList, YcSalePlanAllotCenterProductView productView) {
        List<Mc04IslmYcSalePlanItem> itemList = new ArrayList<>();
        for (Mc04IslmYcSalePlan salePlan : provinceYcPlanList) {
            Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem = new Mc04IslmYcSalePlanItem();
            mc04IslmYcSalePlanItem.setMc04SalePlanId(salePlan.getMc04SalePlanId());
            mc04IslmYcSalePlanItem.setAcCgtCartonCode(product.getProductCode());
            mc04IslmYcSalePlanItem.setAcCgtName(product.getProductName());
            mc04IslmYcSalePlanItem.setAcCgtPriceSegmentCode(product.getAcCigRangeCode());
            mc04IslmYcSalePlanItem.setAcCgtPriceSegmentName(product.getAcCigRangeName());
            mc04IslmYcSalePlanItem.setAcCgtTaxAllotPrice(new BigDecimal(product.getAcMateTaxTranPr()));
            mc04IslmYcSalePlanItem.setAcCgtTradePrice(new BigDecimal(product.getWholeSalePrice()));
            mc04IslmYcSalePlanItem.setAcCgtMediumBranceFlag(product.getIsMedium());
            mc04IslmYcSalePlanItem.setAcCgtTinyFlag(product.getIsTiny());
            mc04IslmYcSalePlanItem.setAcCgtTarVal(new BigDecimal(product.getTarQuantity()));

            // 数量值根据不同的计划存的也不同
            String periodType = salePlan.getMc04CgtSaleFoPeriodType();
            String periodCode = salePlan.getMc04CgtSaleFoPeriodCode();
            String zaOccurrenceYear = salePlan.getZaOccurrenceYear();
            // N库存 + N1调拨 - N1销售 = N1库存
            if (periodType.equals(PeriodTypeEnum.YC.getCode())) {
                // 外省元春
                // N年末预计库存 N年末预计库存：(N-1年末商业库存)+(N年预计调拨)-(N年预计销售)
                mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(productView.getCurrentYearEndStock());
                // 1+2月销量
                mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(productView.getNextYearFirstTwoMonthSalesPlan());
                // 1+2月调拨
                mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(productView.getNextYearFirstTwoMonthAllocationPlan());
                // 2月末商业库存
                 mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(productView.getNextYearSecondMonthEndingStock());
            } else if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "01")) {
                // 外省1月
                // 1月期初商业库存
                mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(productView.getCurrentYearEndStock());
                // 1月期初销售
                mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(productView.getNextYearFirstMonthSalesPlan());
                // 1月期初调拨
                mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(productView.getNextYearFirstMonthAllocationPlan());
                // 1月期末商业库存
                mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(productView.getNextYearFirstMonthEndingStock());
            } else if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "02")) {
                // 外省2月
                mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(productView.getNextYearFirstMonthEndingStock());
                mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(productView.getNextYearSecondMonthSalesPlan());
                mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(productView.getNextYearSecondMonthAllocationPlan());
                mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(productView.getNextYearSecondMonthEndingStock());
            } else {
                throw new CustomException("计划周期异常！请联系管理员");
            }

            mc04IslmYcSalePlanItem.setIcomCode(salePlan.getIcomCode());
            itemList.add(mc04IslmYcSalePlanItem);
        }
        ycSalePlanItemRepository.saveBatch(itemList);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean saveProvinceViewData(String mc04SalePlanId, String provinceCode, List<YcSalePlanAllotCenterProvinceView> productViewList) {
        // 根据id获取元春计划
        Mc04IslmYcSalePlan mc04IslmYcSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById((mc04SalePlanId));
        // 根据获取的元春计划获取所有省一级的元春计划
        List<Mc04IslmYcSalePlan> ycSaleProvincePlanList = getProvinceYcPlanList(mc04IslmYcSalePlan, provinceCode);
        // 根据省一级的计划id和省code获取该省所有的计划详情
        List<BusiComDto> provinceOutList = getProvinceOut();
        Collection<IccProductDetailDTO> productList = getProductList();
        // 如果不存在该省的元春计划，就需要保存计划和详情
        if (CollectionUtils.isEmpty(ycSaleProvincePlanList)) {
            // 第一次保存需要保存省一级元春计划信息
            List<BusiComDto> province = provinceOutList.stream().filter(busiComDto -> busiComDto.getBaComOrgCode().equals(provinceCode)).collect(Collectors.toList());
            saveProvinceYcPlan(mc04IslmYcSalePlan, province);
            // 在获取所有省一级的元春计划，除了本省以外
            ycSaleProvincePlanList = getProvinceYcPlanList(mc04IslmYcSalePlan, provinceCode);
            // 保存详情信息
            // 根据产品信息，元春计划，以及前端填写的详细信息构建元春计划详情
            saveSalePlanItemListForProvinceView(productList, ycSaleProvincePlanList, productViewList);
        } else {
            // 获取详细信息列表，更新详情信息，无法做到识别更改项，采用先删后插入
            List<String> provincePlanIdList = ycSaleProvincePlanList.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
            ycSalePlanItemRepository.deleteByYcPlanIdList(provincePlanIdList);
            saveSalePlanItemListForProvinceView(productList, ycSaleProvincePlanList, productViewList);
        }
        return true;


    }

    private void saveSalePlanItemListForProvinceView(Collection<IccProductDetailDTO> productList, List<Mc04IslmYcSalePlan> ycSaleProvincePlanList, List<YcSalePlanAllotCenterProvinceView> productViewList) {
        List<Mc04IslmYcSalePlanItem> itemList = new ArrayList<>();
        // 这是规格列表，每个规格行需要根据9个计划进行拆解
        for (YcSalePlanAllotCenterProvinceView item : productViewList) {
            // 通过产品列表获取产品详情
            String productCode = item.getAcCgtCartonCode();
            IccProductDetailDTO productDetail = getProductDetail(productList, productCode);
            for (Mc04IslmYcSalePlan salePlan : ycSaleProvincePlanList) {
                Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem = new Mc04IslmYcSalePlanItem();
                populateSalePlanItemBasicInfo(mc04IslmYcSalePlanItem, salePlan, productDetail);

                // 数量值根据不同的计划存的也不同
                populateSalePlanItemQuantities(mc04IslmYcSalePlanItem, salePlan, item);

                mc04IslmYcSalePlanItem.setIcomCode(salePlan.getIcomCode());
                itemList.add(mc04IslmYcSalePlanItem);
            }
        }
        ycSalePlanItemRepository.saveBatch(itemList);
    }

    /**
     * 填充销售计划项的基本信息
     * @param planItem 销售计划项
     * @param salePlan 销售计划
     * @param productDetail 产品详情
     */
    private void populateSalePlanItemBasicInfo(Mc04IslmYcSalePlanItem planItem, Mc04IslmYcSalePlan salePlan, IccProductDetailDTO productDetail) {
        planItem.setMc04SalePlanId(salePlan.getMc04SalePlanId());
        planItem.setAcCgtCartonCode(productDetail.getProductCode());
        planItem.setAcCgtName(productDetail.getProductName());
        planItem.setAcCgtPriceSegmentCode(productDetail.getAcCigRangeCode());
        planItem.setAcCgtPriceSegmentName(productDetail.getAcCigRangeName());
        planItem.setAcCgtTaxAllotPrice(new BigDecimal(productDetail.getAcMateTaxTranPr()));
        planItem.setAcCgtTradePrice(new BigDecimal(productDetail.getWholeSalePrice()));
        planItem.setAcCgtMediumBranceFlag(productDetail.getIsMedium());
        planItem.setAcCgtTinyFlag(productDetail.getIsTiny());
        planItem.setAcCgtTarVal(new BigDecimal(productDetail.getTarQuantity()));
    }

    /**
     * 填充销售计划项的数量信息
     * @param planItem 销售计划项
     * @param salePlan 销售计划
     * @param itemView 视图项
     */
    private void populateSalePlanItemQuantities(Mc04IslmYcSalePlanItem planItem, Mc04IslmYcSalePlan salePlan, YcSalePlanAllotCenterProvinceView itemView) {
        String periodType = salePlan.getMc04CgtSaleFoPeriodType();
        String periodCode = salePlan.getMc04CgtSaleFoPeriodCode();
        String zaOccurrenceYear = salePlan.getZaOccurrenceYear();

        // N库存 + N1调拨 - N1销售 = N1库存
        if (periodType.equals(PeriodTypeEnum.YC.getCode())) {
            populateYearPlanQuantities(planItem, itemView);
        } else if (isJanuaryPlan(periodType, periodCode, zaOccurrenceYear)) {
            populateJanuaryPlanQuantities(planItem, itemView);
        } else if (isFebruaryPlan(periodType, periodCode, zaOccurrenceYear)) {
            populateFebruaryPlanQuantities(planItem, itemView);
        } else {
            throw new CustomException("计划周期异常！请联系管理员");
        }
    }

    /**
     * 判断是否为1月计划
     * @param periodType 周期类型
     * @param periodCode 周期代码
     * @param zaOccurrenceYear 业务年份
     * @return 是否为1月计划
     */
    private boolean isJanuaryPlan(String periodType, String periodCode, String zaOccurrenceYear) {
        return periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "01");
    }

    /**
     * 判断是否为2月计划
     * @param periodType 周期类型
     * @param periodCode 周期代码
     * @param zaOccurrenceYear 业务年份
     * @return 是否为2月计划
     */
    private boolean isFebruaryPlan(String periodType, String periodCode, String zaOccurrenceYear) {
        return periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "02");
    }

    /**
     * 填充年度计划数量信息
     * @param planItem 销售计划项
     * @param itemView 视图项
     */
    private void populateYearPlanQuantities(Mc04IslmYcSalePlanItem planItem, YcSalePlanAllotCenterProvinceView itemView) {
        // 外省元春
        // N年末预计库存 N年末预计库存：(N-1年末商业库存)+(N年预计调拨)-(N年预计销售)
        planItem.setMd03Cgt10thComInitStkQty(itemView.getCurrentYearEndInventory());
        // 1+2月销量
        planItem.setMc04CgtSalePlanOriginQty(itemView.getNextYearFirstTwoMonthSalesPlan());
        // 1+2月调拨
        planItem.setMa02CgtPlResolOrigplanQty(itemView.getNextYearFirstTwoMonthAllocationPlan());
        // 2月末商业库存
         planItem.setMd03Cgt10thComEndStkQty(itemView.getNextYearSecondMonthEndingStock());
    }

    /**
     * 填充1月计划数量信息
     * @param planItem 销售计划项
     * @param itemView 视图项
     */
    private void populateJanuaryPlanQuantities(Mc04IslmYcSalePlanItem planItem, YcSalePlanAllotCenterProvinceView itemView) {
        // 外省1月
        // 1月期初商业库存
         planItem.setMd03Cgt10thComInitStkQty(itemView.getCurrentYearEndInventory());
        // 1月期初调拨
        planItem.setMc04CgtSalePlanOriginQty(itemView.getNextYearFirstMonthSalesPlan());
        // 1月期初销售
        planItem.setMa02CgtPlResolOrigplanQty(itemView.getNextYearFirstMonthAllocationPlan());
        // 1月期末商业库存
        planItem.setMd03Cgt10thComEndStkQty(itemView.getNextYearFirstMonthEndingStock());
    }

    /**
     * 填充2月计划数量信息
     * @param planItem 销售计划项
     * @param itemView 视图项
     */
    private void populateFebruaryPlanQuantities(Mc04IslmYcSalePlanItem planItem, YcSalePlanAllotCenterProvinceView itemView) {
        // 外省2月
         planItem.setMd03Cgt10thComInitStkQty(itemView.getNextYearFirstMonthEndingStock());
        planItem.setMc04CgtSalePlanOriginQty(itemView.getNextYearSecondMonthSalesPlan());
        planItem.setMa02CgtPlResolOrigplanQty(itemView.getNextYearSecondMonthAllocationPlan());
        planItem.setMd03Cgt10thComEndStkQty(itemView.getNextYearSecondMonthEndingStock());
    }


    private BigDecimal convertToBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

}
