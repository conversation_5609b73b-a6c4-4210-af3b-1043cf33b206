/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcComDemandFoDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcComDemandFoMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcComDemandFoService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: hujiarong
 * @Since: 2025-09-05
 * @Email: <EMAIL>
 * @Create: 2025-09-05
        */
@Service
public class Mc04IslmcComDemandFoServiceImpl extends ServiceImpl<Mc04IslmcComDemandFoMapper, Mc04IslmcComDemandFoDO> implements Mc04IslmcComDemandFoService {

}