/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.monthplan;


import com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlanSnapshot;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmitItem;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jinfuli
 * @Date: 2025/6/13
 * @Description:
 */
@Mapper
public interface Mc04IslmMonthSalePlanSnapshotToMonthPlanSubmitItemConverter extends StructureBaseConverter<Mc04IslmMonthSalePlanSnapshot, MonthPlanSubmitItem> {

    Mc04IslmMonthSalePlanSnapshotToMonthPlanSubmitItemConverter INSTANCE =
            Mappers.getMapper(Mc04IslmMonthSalePlanSnapshotToMonthPlanSubmitItemConverter.class);
}
