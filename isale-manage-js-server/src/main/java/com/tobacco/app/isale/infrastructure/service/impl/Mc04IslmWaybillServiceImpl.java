/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmWaybillDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmWaybillMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmWaybillService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: wang<PERSON><PERSON>
 * @Since: 2025-08-26
 * @Email: <EMAIL>
 * @Create: 2025-08-26
 */
@Service
public class Mc04IslmWaybillServiceImpl extends ServiceImpl<Mc04IslmWaybillMapper, Mc04IslmWaybillDO> implements Mc04IslmWaybillService {

}