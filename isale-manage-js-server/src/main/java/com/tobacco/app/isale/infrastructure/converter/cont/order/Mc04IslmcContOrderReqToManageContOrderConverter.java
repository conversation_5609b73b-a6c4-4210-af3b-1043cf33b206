/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.cont.order;

import com.tobacco.app.isale.domain.model.cont.order.ManageContOrder;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isalecenter.client.req.cont.order.Mc04IslmcContOrderREQ;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/08/05 19:25
 * @description : Mc04IslmcContOrderREQToMc04IslmContOrderDO
 */
public interface Mc04IslmcContOrderReqToManageContOrderConverter
        extends StructureBaseConverter<Mc04IslmcContOrderREQ, ManageContOrder> {

    Mc04IslmcContOrderReqToManageContOrderConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcContOrderReqToManageContOrderConverter.class);

    /**
     * 领域对象转为数据对象
     * @param model 领域对象
     * @return 数据对象
     */
    @Override
    @Mappings({@Mapping(source = "contOrderItemList", target = "contOrderItems")})
    Mc04IslmcContOrderREQ converterModelToDo(ManageContOrder model);


    /**
     * 领域对象转为数据对象
     * @param structureDo 领域对象
     * @return 数据对象
     */
    @Override
    @Mappings({@Mapping(source = "contOrderItems", target = "contOrderItemList")})
    ManageContOrder converterDoToModel(Mc04IslmcContOrderREQ structureDo);

}
