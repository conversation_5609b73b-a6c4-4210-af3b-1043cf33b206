/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.domain.service.basic;


import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.tobacco.app.isale.domain.enums.basic.ZbSettlementModeTypeEnum;
import com.tobacco.app.isale.domain.model.basic.ManageIslmcComRecType;
import com.tobacco.app.isale.domain.model.basic.ManageZbSettlementMode;
import com.tobacco.app.isale.domain.repository.basic.ManageIslmcComRecTypeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR> zhangshch01
 * @email : <EMAIL>
 * @create_time : 2025/08/05 15:30
 * @description : 销售管理-基础数据-客户结算方式 domainService
 */

@Component("ISaleManageIslmcComRecTypeDS")
@Slf4j
public class ManageIslmcComRecTypeDomainService {

    @Autowired
    private ManageIslmcComRecTypeRepository manageIslmcComRecTypeRepository;


    /**
     * @param baComOrgCode 商业公司编码
     * @param zbSettlementModeCode 结算方式代码
     * @return List<ManageIslmcComRecType> 客户结算方式列表
     */
    @ReturnValue(value = "返回的客户结算方式列表", name = "返回的客户结算方式列表")
    @Method(value = "查询客户结算方式列表", name = "查询客户结算方式列表")
    public List<ManageIslmcComRecType> list(@Parameter(name = "商业公司编码", value = "商业公司编码", required = true) String baComOrgCode,
                                            @Parameter(name = "结算方式代码", value = "结算方式代码", required = true) String zbSettlementModeCode) {
        return manageIslmcComRecTypeRepository.list(baComOrgCode, zbSettlementModeCode);
    }


    /**
     * @param manageIslmcComRecTypes 保存客户结算方式数据
     * @return Boolean 是否保存成功
     */
    @ReturnValue(value = "是否保存成功", name = "是否保存成功")
    @Method(value = "保存客户结算方式数据", name = "保存客户结算方式数据")
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(@Parameter(name = "保存客户结算方式数据", value = "保存客户结算方式数据", required = true) List<ManageIslmcComRecType> manageIslmcComRecTypes) {

        return manageIslmcComRecTypeRepository.save(manageIslmcComRecTypes);
    }

    /**
     * @return List<ManageZbSettlementMode> 结算方式枚举列表
     */
    @ReturnValue(value = "返回结算方式枚举列表", name = "返回结算方式枚举列表")
    @Method(value = "查询结算方式枚举列表", name = "查询结算方式枚举列表")
    public List<ManageZbSettlementMode> getZbSettlementModeList() {
        return Stream.of(ZbSettlementModeTypeEnum.values())
                .map(item -> new ManageZbSettlementMode(item.getType(), item.getDesc()))
                .collect(Collectors.toList());
    }
}
