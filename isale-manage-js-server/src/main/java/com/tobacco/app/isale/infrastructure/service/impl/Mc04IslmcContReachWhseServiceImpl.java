/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContReachWhseDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcContReachWhseMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContReachWhseService;
import org.springframework.stereotype.Service;


/**
 * @description : 服务实现类
 *
 * <AUTHOR> wangluhao01
 * @since : 2025-05-26
 * @email : <EMAIL>
 * @create_time : 2025-05-26
 */
@Service
public class Mc04IslmcContReachWhseServiceImpl extends ServiceImpl<Mc04IslmcContReachWhseMapper, Mc04IslmcContReachWhseDO> implements Mc04IslmcContReachWhseService {

}