/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.yearplan;


import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSaleFoItem;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSaleFoItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: jinfuli
 * @Date: 2025/6/13
 * @Description:
 */
@Mapper
public interface Mc04IslmSaleFoItemConverter extends StructureBaseConverter<Mc04IslmSaleFoItemDO, Mc04IslmSaleFoItem> {

    Mc04IslmSaleFoItemConverter INSTANCE = Mappers.getMapper(Mc04IslmSaleFoItemConverter.class);
    @Mappings({})
    @Override
    Mc04IslmSaleFoItemDO converterModelToDo(Mc04IslmSaleFoItem mc04IslmMonthSalePlan);
    @Mappings({})
    @Override
    Mc04IslmSaleFoItem converterDoToModel(Mc04IslmSaleFoItemDO DO);
    @Mappings({})
    @Override
    List<Mc04IslmSaleFoItemDO> converterModelsToDos(List<Mc04IslmSaleFoItem> mc04IslmMonthSalePlans);
    @Mappings({})
    @Override
    List<Mc04IslmSaleFoItem> converterDosToModels(List<Mc04IslmSaleFoItemDO> DOList);

}
