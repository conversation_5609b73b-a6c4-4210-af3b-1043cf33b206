/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.order;

import com.tobacco.app.isale.domain.model.basic.ManageContDelivWhse;
import com.tobacco.app.isale.domain.model.basic.ContDelivWhse;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: jinfuli
 * @Date: 2025/8/6
 * @Description:
 */
@Mapper
public interface ContDelivWhseConverter extends StructureBaseConverter<Mc04IslmcContDelivWhseDO,ContDelivWhse> {
    ContDelivWhseConverter INSTANCE = Mappers.getMapper(ContDelivWhseConverter.class);

    @Mappings({})
    List<ManageContDelivWhse> convert(List<Mc04IslmcContDelivWhseDO> mc04IslmcContDelivWhseDOList);


}
