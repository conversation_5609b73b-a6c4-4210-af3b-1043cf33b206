/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.yearplan;

import com.tobacco.app.isale.domain.model.plan.yearplan.IslmSaleInitItemModel;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSaleInitItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 销量初始化明细 数据转换器
 * <p>
 * IslmSaleInitItemConverter
 *
 * @Author: longxi
 * @Since: 2025-08-11
 */

@Mapper
public interface Mc04IslmSaleInitItemDoConverter extends StructureBaseConverter<Mc04IslmSaleInitItemDO, IslmSaleInitItemModel> {

    Mc04IslmSaleInitItemDoConverter INSTANCE = Mappers.getMapper(Mc04IslmSaleInitItemDoConverter.class);

}