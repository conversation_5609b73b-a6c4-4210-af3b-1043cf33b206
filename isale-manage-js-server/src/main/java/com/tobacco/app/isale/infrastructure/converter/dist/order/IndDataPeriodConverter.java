/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.order;

import com.tobacco.app.isale.domain.model.date.Mc04IndDataPeriod;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IndDataPeriodDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jinfuli
 * @Date: 2025/8/6
 * @Description:
 */
@Mapper
public interface IndDataPeriodConverter extends StructureBaseConverter<Mc04IndDataPeriodDO, Mc04IndDataPeriod> {
    IndDataPeriodConverter INSTANCE = Mappers.getMapper(IndDataPeriodConverter.class);
}
