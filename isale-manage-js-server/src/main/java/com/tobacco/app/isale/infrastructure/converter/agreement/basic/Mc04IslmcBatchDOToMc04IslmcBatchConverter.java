/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.agreement.basic;

import com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcBatch;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcBatchDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/04/22 15:31
 * @description : 数据转化
 */
@Mapper
public interface Mc04IslmcBatchDOToMc04IslmcBatchConverter
        extends StructureBaseConverter<Mc04IslmcBatchDO, Mc04IslmcBatch> {
    Mc04IslmcBatchDOToMc04IslmcBatchConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcBatchDOToMc04IslmcBatchConverter.class);


}
