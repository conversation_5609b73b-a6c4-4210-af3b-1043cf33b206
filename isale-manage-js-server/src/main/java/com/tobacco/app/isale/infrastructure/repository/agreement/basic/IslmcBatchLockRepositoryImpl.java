/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.agreement.basic;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcBatch;
import com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcBatchLine;
import com.tobacco.app.isale.domain.repository.agreement.basic.IslmcBatchLockRepository;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcBatchDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcBatchLineDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcBatchLineService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcBatchService;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> jinfuli
 * @Email : @inspur.com
 * @Create : 2025/04/27 17:25
 * @Description : TODO
 */
@Component
public class IslmcBatchLockRepositoryImpl implements IslmcBatchLockRepository {

    private final Mc04IslmcBatchLineService mc04IslmcBatchLineService;
    private final Mc04IslmcBatchService mc04IslmcBatchService;

    public IslmcBatchLockRepositoryImpl(Mc04IslmcBatchLineService mc04IslmcBatchLineService,
                                        Mc04IslmcBatchService mc04IslmcBatchService) {
        this.mc04IslmcBatchLineService = mc04IslmcBatchLineService;
        this.mc04IslmcBatchService = mc04IslmcBatchService;
    }

    /**
     * 更新批次状态
     *
     * @param mc04IslmcBatchLines
     * @return
     */
    @Override
    public Boolean updateComBatchStatus(List<Mc04IslmcBatchLine> mc04IslmcBatchLines) {
        List<Mc04IslmcBatchLineDO> mc04IslmcBatchLineDOS = BeanUtil.copyToList(mc04IslmcBatchLines, Mc04IslmcBatchLineDO.class);
        return mc04IslmcBatchLineService.updateBatchById(mc04IslmcBatchLineDOS);
    }

    /**
     * 查询批次列表
     *
     * @param mc04IslmcBatch
     * @return
     */
    @Override
    public List<Mc04IslmcBatch> queryComBatchList(Mc04IslmcBatch mc04IslmcBatch) {
        QueryWrapper<Mc04IslmcBatchDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Mc04IslmcBatchDO::getMa02TobaProdTradeTypeCode, mc04IslmcBatch.getMa02TobaProdTradeTypeCode())
                .eq(Mc04IslmcBatchDO::getMc04BatchType, mc04IslmcBatch.getMc04BatchType())
                .eq(Mc04IslmcBatchDO::getMc04DatePeriodType, mc04IslmcBatch.getMc04DatePeriodType())
                .eq(Mc04IslmcBatchDO::getMc04DatePeriodCode, mc04IslmcBatch.getMc04DatePeriodCode());
        List<Mc04IslmcBatchDO> mc04IslmcBatchDOS = mc04IslmcBatchService.list(queryWrapper);
        return BeanUtil.copyToList(mc04IslmcBatchDOS, Mc04IslmcBatch.class);
    }
}