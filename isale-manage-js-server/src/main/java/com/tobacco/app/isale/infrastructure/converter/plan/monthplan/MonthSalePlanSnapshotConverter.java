/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.monthplan;


import com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlanSnapshot;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanSnapshotDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jinfuli
 * @Date: 2025/6/10
 * @Description:
 */
@Mapper
public interface MonthSalePlanSnapshotConverter extends StructureBaseConverter<Mc04IslmMonthSalePlanSnapshotDO, Mc04IslmMonthSalePlanSnapshot> {
    MonthSalePlanSnapshotConverter INSTANCE = Mappers.getMapper(MonthSalePlanSnapshotConverter.class);
}
