/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanAdjSnapshotDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmMonthSalePlanAdjSnapshotMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanAdjSnapshotService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: hzs
 * @Since: 2025-08-18
 * @Email: <EMAIL>
 * @Create: 2025-08-18
 */
@Service
public class Mc04IslmMonthSalePlanAdjSnapshotServiceImpl extends ServiceImpl<Mc04IslmMonthSalePlanAdjSnapshotMapper, Mc04IslmMonthSalePlanAdjSnapshotDO> implements Mc04IslmMonthSalePlanAdjSnapshotService {

}
