/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcComRecTypeDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcComRecTypeMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcComRecTypeService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: zhangshch01
 * @Since: 2025-08-05
 * @Email: <EMAIL>
 * @Create: 2025-08-05
        */
@Service
public class Mc04IslmcComRecTypeServiceImpl extends ServiceImpl<Mc04IslmcComRecTypeMapper, Mc04IslmcComRecTypeDO> implements Mc04IslmcComRecTypeService {

}