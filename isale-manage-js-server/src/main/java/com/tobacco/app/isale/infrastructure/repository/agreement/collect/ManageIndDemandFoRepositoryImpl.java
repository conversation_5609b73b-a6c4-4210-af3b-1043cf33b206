/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.agreement.collect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.constant.CommonConstants;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcBatch;
import com.tobacco.app.isale.domain.model.agreement.collect.ManageDemandFoIndex;
import com.tobacco.app.isale.domain.model.agreement.collect.ManageIndDemandFo;
import com.tobacco.app.isale.domain.model.agreement.collect.ManageIndDemandFoItem;
import com.tobacco.app.isale.domain.repository.agreement.collect.ManageIndDemandFoRepository;
import com.tobacco.app.isale.infrastructure.converter.agreement.basic.Mc04IslmcBatchDOToMc04IslmcBatchConverter;
import com.tobacco.app.isale.infrastructure.converter.agreement.collect.Mc04IslmcIndDemandFoDoToManageIndDemandFoConverter;
import com.tobacco.app.isale.infrastructure.converter.agreement.collect.Mc04IslmcIndDemandFoItemDOToManageIndDemandFoItemConverter;
import com.tobacco.app.isale.infrastructure.entity.*;
import com.tobacco.app.isale.infrastructure.service.api.*;
import com.tobacco.app.isale.tools.utils.CommodityUtil;
import com.tobacco.app.isale.tools.utils.CustUtil;
import com.tobacco.app.isalecenter.common.constants.SaleCenterConstants;
import com.tobacco.sc.icommodity.dto.common.constant.dto.item.IccItemDetailDTO;
import com.tobacco.sc.icommodity.dto.common.constant.dto.product.IccProductDetailDTO;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/04/22 10:42
 * @description : 销售管理-协议管理(卷烟)-意向采集-意向上报(市场填报) infrastructureRepository
 */
@Slf4j
@Component("ISaleIndDemandFoRepositoryImplDORepository")
public class ManageIndDemandFoRepositoryImpl implements ManageIndDemandFoRepository {

    private Mc04IslmcIndDemandFoService mc04IslmcIndDemandFoService;

    @Autowired
    public void setMc04IslmcIndDemandFoService(Mc04IslmcIndDemandFoService mc04IslmcIndDemandFoService) {
        this.mc04IslmcIndDemandFoService = mc04IslmcIndDemandFoService;
    }

    private Mc04IslmcIndDemandFoItemService mc04IslmcIndDemandFoItemService;

    @Autowired
    public void setMc04IslmcIndDemandFoItemService(Mc04IslmcIndDemandFoItemService mc04IslmcIndDemandFoItemService) {
        this.mc04IslmcIndDemandFoItemService = mc04IslmcIndDemandFoItemService;
    }

    private Mc04IslmcBatchIndexService mc04IslmcBatchIndexService;

    @Autowired
    public void setMc04IslmcBatchIndexService(Mc04IslmcBatchIndexService mc04IslmcBatchIndexService) {
        this.mc04IslmcBatchIndexService = mc04IslmcBatchIndexService;
    }

    private Mc04IslmcDemandFoIndexService mc04IslmcDemandFoIndexService;

    @Autowired
    public void setMc04IslmcDemandFoIndexService(Mc04IslmcDemandFoIndexService mc04IslmcDemandFoIndexService) {
        this.mc04IslmcDemandFoIndexService = mc04IslmcDemandFoIndexService;
    }

    private Mc04IslmcBatchService mc04IslmcBatchService;

    @Autowired
    public void setMc04IslmcBatchService(Mc04IslmcBatchService mc04IslmcBatchService) {
        this.mc04IslmcBatchService = mc04IslmcBatchService;
    }

    private Mc04IslmcBatchLineService mc04IslmcBatchLineService;

    @Autowired
    public void setMc04IslmcBatchLineService(Mc04IslmcBatchLineService mc04IslmcBatchLineService) {
        this.mc04IslmcBatchLineService = mc04IslmcBatchLineService;
    }

    /**
     * 意向上报数据查询已经保存的数据
     *
     * @param cgtType   业务类型
     * @param comIdList 协议单位编码
     * @param dateCode  周期
     * @param batchNo   批次
     * @return List<Map < String, Object>>
     */
    @Override
    public List<ManageIndDemandFo> queryIndDemandFoWithItem(String cgtType, List<String> comIdList,
                                                            String dateCode, String batchNo) {
        return queryIndDemandFoWithItem(cgtType, comIdList, dateCode, batchNo, CommonConstants.YES);
    }

    /**
     * @param cgtType         业务类型
     * @param comIdList       协议单位编码
     * @param dateCode        周期
     * @param batchNo         批次
     * @param isMergeCoreData 是否拼接商品中心与客户中心的数据
     * @return List<IndDemandFo>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-22 16:32:21
     * @description : 获取已保存数据 (包含商品与客户中心信息)
     */
    @Override
    public List<ManageIndDemandFo> queryIndDemandFoWithItem(String cgtType, List<String> comIdList, String dateCode,
                                                            String batchNo, String isMergeCoreData) {
        //从中心取数据 改为从数据库获取数据
        List<ManageIndDemandFo> demandFoList = getManageIndDemandFos(cgtType, comIdList, dateCode, batchNo);
        if (CollUtil.isEmpty(demandFoList)) {
            return Collections.emptyList();
        }
        if (!CommonConstants.YES.equals(isMergeCoreData)) {
            return demandFoList;
        }
        List<String> comCodeList = new ArrayList<>();
        List<String> cartonCodeList = new ArrayList<>();
        List<String> acTwoLevelCigCodeList = new ArrayList<>();
        demandFoList.forEach(demandFo -> {
            comCodeList.add(demandFo.getBaComOrgCode());
            demandFo.getManageIndDemandFoItemList().forEach(item -> {
                cartonCodeList.add(item.getAcCgtCartonCode());
                acTwoLevelCigCodeList.add(item.getAcTwoLevelCigCode());
            });
        });
        // 获取商品详细信息
        Map<String, IccProductDetailDTO> productCodeMap = CommodityUtil.getIccProductDetailDtoMap(cartonCodeList);
        // 获取二级牌号信息
        Map<String, IccItemDetailDTO> itemCodeMap = CommodityUtil.getIccItemDetailDtoMap(acTwoLevelCigCodeList);

        //获取地市信息
        Map<String, BusiComDto> comCodeMap = CustUtil.getBusiComDtoMap(comCodeList);

        demandFoList.forEach(demandFoDTO -> {
            Assert.notEmpty(demandFoDTO.getManageIndDemandFoItemList(),
                    () -> new CustomException("意向上报数据从表数据丢失"));
            List<ManageIndDemandFoItem> manageIndDemandFoItemList =
                    getIndDemandFoItemList(demandFoDTO.getManageIndDemandFoItemList(), productCodeMap, itemCodeMap);
            demandFoDTO.setManageIndDemandFoItemList(manageIndDemandFoItemList);
            BusiComDto busiCom = comCodeMap.get(demandFoDTO.getBaComOrgCode());
            Assert.notNull(busiCom, () -> new CustomException("中心未获取到" +
                    demandFoDTO.getBaComOrgCode() + "的协议单位信息"));
            demandFoDTO.setBaComOrgName(busiCom.getMc04ComOrgShortName());
            demandFoDTO.setSeq(busiCom.getSeq());
        });
        return demandFoList.stream()
                .sorted(Comparator.comparing(ManageIndDemandFo::getSeq))
                .collect(Collectors.toList());
    }

    /**
     * @param cgtType   业务类型
     * @param comIdList 协议单位编码
     * @param dateCode  周期
     * @param batchNo   批次
     * @return Collection<IndDemandFoDTO>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-06 15:38:08
     * @description : 从订单中心获取数据
     */

    private List<ManageIndDemandFo> getManageIndDemandFos(String cgtType, List<String> comIdList,
                                                          String dateCode, String batchNo) {
        return queryIndDemandFoList(cgtType, comIdList, dateCode, batchNo, SaleCenterConstants.YES);
    }


    private List<ManageIndDemandFo> queryIndDemandFoList(String cgtType, List<String> comIdList,
                                                         String dateCode, String batchNo,
                                                         String isContainItems) {
        //查询工业预测主表
        List<Mc04IslmcIndDemandFoDO> demandFoList = mc04IslmcIndDemandFoService.lambdaQuery()
                .eq(Mc04IslmcIndDemandFoDO::getMa02TobaProdTradeTypeCode, cgtType)
                .eq(StrUtil.isNotBlank(batchNo),
                        Mc04IslmcIndDemandFoDO::getMc04BatchNo, batchNo)
                .eq(Mc04IslmcIndDemandFoDO::getMc04DatePeriodCode, dateCode)
                .eq(Mc04IslmcIndDemandFoDO::getIcomCode, IcomUtils.getIcomCode())
                .in(CollUtil.isNotEmpty(comIdList), Mc04IslmcIndDemandFoDO::getBaComOrgCode, comIdList)
                .list();
        if (CollUtil.isEmpty(demandFoList)) {
            return Collections.emptyList();
        }
        if (SaleCenterConstants.NO.equals(isContainItems)) {
            return Mc04IslmcIndDemandFoDoToManageIndDemandFoConverter.INSTANCE
                    .converterDosToModels(demandFoList);
        }
        //主表有值，获取从表数据
        List<String> demandFoIdSet = demandFoList.stream()
                .map(Mc04IslmcIndDemandFoDO::getMc04DemandFoId)
                .collect(Collectors.toList());
        List<Mc04IslmcIndDemandFoItemDO> foItemList = mc04IslmcIndDemandFoItemService.lambdaQuery()
                .in(Mc04IslmcIndDemandFoItemDO::getMc04DemandFoId, demandFoIdSet)
                .list();
        Assert.notEmpty(foItemList, () -> new CustomException("存在异常数据," +
                CollectionUtil.join(comIdList, ",") + "有意向数据,但无明细数据"));
        List<ManageIndDemandFoItem> indDemandFoItems =
                Mc04IslmcIndDemandFoItemDOToManageIndDemandFoItemConverter.INSTANCE
                        .converterDosToModels(foItemList);
        Map<String, List<ManageIndDemandFoItem>> foItemMap = indDemandFoItems.stream()
                .collect(Collectors.groupingBy(ManageIndDemandFoItem::getMc04DemandFoId));
        return demandFoList.stream()
                .map(demandFo -> {
                    ManageIndDemandFo indDemandFo = Mc04IslmcIndDemandFoDoToManageIndDemandFoConverter.INSTANCE
                            .converterDoToModel(demandFo);
                    Assert.isTrue(foItemMap.containsKey(demandFo.getMc04DemandFoId()),
                            () -> new CustomException("存在异常数据," + demandFo.getBaComOrgCode() + "有意向数据,但无明细数据"));
                    indDemandFo.setManageIndDemandFoItemList(
                            foItemMap.get(demandFo.getMc04DemandFoId()));
                    return indDemandFo;
                })
                .sorted(Comparator.comparing(ManageIndDemandFo::getSeq))
                .collect(Collectors.toList());
    }


    /**
     * @param demandFoItemDtoList 从表信息
     * @param productCodeMap      商品详细信息
     * @param itemCodeMap         二级牌号信息
     * @return List<IndDemandFoItem>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-06 15:19:51
     * @description : 转化意向上报数据从表信息
     */

    private static List<ManageIndDemandFoItem> getIndDemandFoItemList(
            List<ManageIndDemandFoItem> demandFoItemDtoList, Map<String, IccProductDetailDTO> productCodeMap,
            Map<String, IccItemDetailDTO> itemCodeMap) {
        demandFoItemDtoList.forEach(indDemandFoItemDTO -> {
            IccProductDetailDTO product = productCodeMap.get(indDemandFoItemDTO.getAcCgtCartonCode());
            Assert.notNull(product, () -> new CustomException("商品中心未获取到" +
                    indDemandFoItemDTO.getAcCgtCartonCode() + "的商品信息"));
            indDemandFoItemDTO.setAcCgtName(product.getProductName());
            indDemandFoItemDTO.setAcCigRangeCode(product.getAcCigRangeCode());
            if (ObjectUtil.isNull(indDemandFoItemDTO.getAcCgtTradePrice())) {
                indDemandFoItemDTO.setAcCgtTradePrice(
                        StrUtil.isNotBlank(product.getWholeSalePrice()) ?
                                new BigDecimal(product.getWholeSalePrice()) : null);
            }
            if (ObjectUtil.isNull(indDemandFoItemDTO.getAcCgtTaxAllotPrice())) {
                indDemandFoItemDTO.setAcCgtTaxAllotPrice(
                        StrUtil.isNotBlank(product.getAcMateTaxTranPr()) ?
                                new BigDecimal(product.getAcMateTaxTranPr()) : null);
            }
            IccItemDetailDTO item = itemCodeMap.get(indDemandFoItemDTO.getAcTwoLevelCigCode());
            Assert.notNull(item, () -> new CustomException("商品中心未获取到" +
                    indDemandFoItemDTO.getAcTwoLevelCigCode() + "的二级牌号信息"));
            indDemandFoItemDTO.setAcTwoLevelCigName(item.getAcTwoLevelCigName());
            indDemandFoItemDTO.setSeq(item.getProductSeq());
        });
        return demandFoItemDtoList.stream()
                .sorted(Comparator.comparing(ManageIndDemandFoItem::getSeq))
                .collect(Collectors.toList());
    }

    /**
     * @param batchNo 批次号
     * @return List<DemandFoIndex>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-22 16:32:21
     * @description : 获取需求预测指标
     */
    @Override
    public List<ManageDemandFoIndex> getDemandFoIndexList(String batchNo, String batchEnable, String indexEnable) {
        Assert.notBlank(batchNo, () -> new CustomException("批次号不能为空"));
        // 先查批次获取有效批次
        Long count = mc04IslmcBatchService.lambdaQuery()
                .eq(Mc04IslmcBatchDO::getMc04BatchNo, batchNo)
                .eq(StrUtil.isNotBlank(batchEnable), Mc04IslmcBatchDO::getZaEnableStatus, batchEnable)
                .count();
        Assert.isTrue(count > 0, () -> new CustomException("批次号不存在或已失效"));
        // 查询批次与指标的关系表
        List<Mc04IslmcBatchIndexDO> batchIndexList = mc04IslmcBatchIndexService.lambdaQuery()
                .eq(Mc04IslmcBatchIndexDO::getMc04BatchNo, batchNo)
                .orderByAsc(Mc04IslmcBatchIndexDO::getSeq)
                .list();

        if (CollUtil.isEmpty(batchIndexList)) {
            return Collections.emptyList();
        }

        // 获取关联的指标ID集合
        Set<String> indexCodeSet = batchIndexList.stream()
                .map(Mc04IslmcBatchIndexDO::getMz10IndexCode)
                .collect(Collectors.toSet());

        // 查询指标详情
        List<Mc04IslmcDemandFoIndexDO> indexList = mc04IslmcDemandFoIndexService.lambdaQuery()
                .in(Mc04IslmcDemandFoIndexDO::getMz10IndexCode, indexCodeSet)
                .eq(StrUtil.isNotBlank(batchEnable), Mc04IslmcDemandFoIndexDO::getZaEnableStatus, batchEnable)
                .list();

        // 将 indexList 转换为 Map，方便根据 indexCode 快速查找
        Map<String, Mc04IslmcDemandFoIndexDO> indexMap = indexList.stream()
                .collect(Collectors.toMap(Mc04IslmcDemandFoIndexDO::getMz10IndexCode, Function.identity()));

        // 按照 batchIndexList 的 seq 顺序排序并转换为领域模型
        List<ManageDemandFoIndex> result = new ArrayList<>();
        for (Mc04IslmcBatchIndexDO mc04IslmcBatchIndex : batchIndexList) {
            Mc04IslmcDemandFoIndexDO index = indexMap.get(mc04IslmcBatchIndex.getMz10IndexCode());
            Assert.notNull(index, () -> new CustomException("批次号不能为空"));
            if (index != null && SaleCenterConstants.YES.equals(index.getZaEnableStatus())) {
                ManageDemandFoIndex batchIndex = new ManageDemandFoIndex();
                batchIndex.setMz10IndexCode(mc04IslmcBatchIndex.getMz10IndexCode());
                batchIndex.setMz10IndexName(index.getMz10IndexName());
                result.add(batchIndex);
            }
        }
        return result;
    }



    /**
     * @param manageIndDemandFoList 意向上报数据
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-22 16:32:21
     * @description : 保存意向上报数据
     */
    @Override
    public Boolean saveDemandFoList(List<ManageIndDemandFo> manageIndDemandFoList) {
        List<Mc04IslmcIndDemandFoItemDO> mc04IslmcIndDemandFoItemDO = new ArrayList<>();
        manageIndDemandFoList.forEach(manageIndDemandFo ->
                mc04IslmcIndDemandFoItemDO.addAll(Mc04IslmcIndDemandFoItemDOToManageIndDemandFoItemConverter.INSTANCE
                        .converterModelsToDos(manageIndDemandFo.getManageIndDemandFoItemList())));
        List<Mc04IslmcIndDemandFoDO> mc04IslmcIndDemandFoDOList =
                Mc04IslmcIndDemandFoDoToManageIndDemandFoConverter.INSTANCE.converterModelsToDos(manageIndDemandFoList);
        boolean flag = mc04IslmcIndDemandFoService.saveOrUpdateBatch(mc04IslmcIndDemandFoDOList);
        if (!flag) {
            return flag;
        }
        //删除从表
        List<String> demandFoIdList = manageIndDemandFoList.stream()
                .map(ManageIndDemandFo::getMc04DemandFoId).collect(Collectors.toList());
        mc04IslmcIndDemandFoItemService.lambdaUpdate()
                .in(Mc04IslmcIndDemandFoItemDO::getMc04DemandFoId, demandFoIdList)
                .remove();
        flag = mc04IslmcIndDemandFoItemService.saveBatch(mc04IslmcIndDemandFoItemDO);
        return flag;
    }

    /**
     * @param demandFoIdList 意向上报数据主键
     * @param status         状态
     * @param nextStatus     下一个状态
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-22 16:32:21
     * @description : 意向上报数据状态修改
     */
    @Override
    public Boolean batchUpdateIndDemandFoStatus(List<String> demandFoIdList, String status, String nextStatus) {
        Assert.notEmpty(demandFoIdList, () -> new CustomException("意向上报数据ID不能为空"));
        return mc04IslmcIndDemandFoService.lambdaUpdate()
                .in(Mc04IslmcIndDemandFoDO::getMc04DemandFoId, demandFoIdList)
                .eq(Mc04IslmcIndDemandFoDO::getMc04DemandFoStatus, status)
                .set(Mc04IslmcIndDemandFoDO::getMc04DemandFoStatus, nextStatus)
                .update();
    }

    /**
     * @param cgtType   业务类型
     * @param dateCode  周期
     * @param batchNo   批次
     * @param comIdList 协议单位编码
     * @return List<IndDemandFo>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-22 16:32:21
     * @description : 意向上报数据查询
     */
    @Override
    public List<ManageIndDemandFo> queryIndDemandFoList(String cgtType, String dateCode,
                                                        String batchNo, List<String> comIdList) {
        //查询工业预测主表
        List<ManageIndDemandFo> indDemandFoDtos = queryIndDemandFoList(cgtType, comIdList, dateCode,
                batchNo, SaleCenterConstants.NO);
        if (CollUtil.isEmpty(indDemandFoDtos)) {
            return Collections.emptyList();
        }
        return indDemandFoDtos;
    }

    /**
     * @param batchNo 批次号
     * @param comId   协议单位编码
     * @return String
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-22 16:32:21
     * @description : 获取批次对应的协议单位状态
     */
    @Override
    public String getBatchComLockStatus(String batchNo, String comId) {
        Mc04IslmcBatchLineDO batchLineDO = mc04IslmcBatchLineService.lambdaQuery()
                .eq(Mc04IslmcBatchLineDO::getMc04BatchNo, batchNo)
                .eq(Mc04IslmcBatchLineDO::getBaComOrgCode, comId)
                .select(Mc04IslmcBatchLineDO::getMa02AsLockState)
                .one();
        Assert.notNull(batchLineDO, () -> new CustomException("批次明细不存在"));
        return batchLineDO.getMa02AsLockState();

    }

    /**
     * @param batchType 批次类型
     * @param cgtType   业务类型
     * @param dateCode  周期
     * @return List<IslmcBatch>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-22 16:32:21
     * @description : 批次列表查询
     */
    @Override
    public List<Mc04IslmcBatch> queryBatchList(String batchType, String cgtType, String dateCode) {
        List<Mc04IslmcBatchDO> batchDOList = mc04IslmcBatchService.lambdaQuery()
                .eq(Mc04IslmcBatchDO::getMa02TobaProdTradeTypeCode, cgtType)
                .eq(Mc04IslmcBatchDO::getMc04DatePeriodCode, dateCode)
                .eq(Mc04IslmcBatchDO::getMc04BatchType, batchType)
                .eq(Mc04IslmcBatchDO::getZaEnableStatus, CommonConstants.YES)
                .select(Mc04IslmcBatchDO::getMc04BatchNo, Mc04IslmcBatchDO::getMc04BatchName)
                .orderByAsc(Mc04IslmcBatchDO::getCreateTime)
                .list();

        return Mc04IslmcBatchDOToMc04IslmcBatchConverter.INSTANCE.converterDosToModels(batchDOList);
    }

}
