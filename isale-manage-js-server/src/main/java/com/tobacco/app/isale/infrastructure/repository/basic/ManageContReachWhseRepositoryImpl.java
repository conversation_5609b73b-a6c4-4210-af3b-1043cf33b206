package com.tobacco.app.isale.infrastructure.repository.basic;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.model.basic.ManageContReachWhse;
import com.tobacco.app.isale.domain.repository.basic.ManageContReachWhseRepository;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContReachWhseDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContReachWhseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: wangluhao01
 * @Since: 2025-05-27
 */
@Slf4j
@Component("ISaleManageManageContReachWhseDORepository")
public class ManageContReachWhseRepositoryImpl implements ManageContReachWhseRepository {

    private Mc04IslmcContReachWhseService mc04IslmcContReachWhseService;

    @Autowired
    public void setContReachWhseApi(Mc04IslmcContReachWhseService mc04IslmcContReachWhseService) {
        this.mc04IslmcContReachWhseService = mc04IslmcContReachWhseService;
    }
    /**
     * @param comCode    公司编码
     * @return 公司（商业公司、工业公司）合同到货仓库列表
     */
    @Override
    public List<ManageContReachWhse> queryComContReachWhseList(String comCode) {
        QueryWrapper<Mc04IslmcContReachWhseDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Mc04IslmcContReachWhseDO::getIcomCode, IcomUtils.getIcomCode())
                .eq(Mc04IslmcContReachWhseDO::getBaComOrgCode, comCode)
                .eq(Mc04IslmcContReachWhseDO::getIsUse, "1")
                .orderByAsc(Mc04IslmcContReachWhseDO::getSeq);
        List<Mc04IslmcContReachWhseDO> list = mc04IslmcContReachWhseService.list(queryWrapper);
        List<ManageContReachWhse> whseDTOS = list.stream()
                .map(whseDO ->{
                    ManageContReachWhse whseDTO = new ManageContReachWhse();
                    whseDTO.setMd02CgtInStorehouseCode(whseDO.getMd02CgtInStorehouseCode());
                    whseDTO.setBaComOrgCode(whseDO.getBaComOrgCode());
                    whseDTO.setMd03LogtIcOrderDeliveryWhseCode(whseDO.getMd03LogtIcOrderDeliveryWhseCode());
                    whseDTO.setMc05LogtTransportDelIndWarehouseCode(whseDO.getMc05LogtTransportDelIndWarehouseCode());
                    whseDTO.setMd02CgtInStorehouseName(whseDO.getMd02CgtInStorehouseName());
                    whseDTO.setMd02CgtInStorehouseAdress(whseDO.getMd02CgtInStorehouseAdress());
                    whseDTO.setZaDivCode(whseDO.getZaDivCode());
                    whseDTO.setCaDefaultStoreouseFlag(whseDO.getCaDefaultStoreouseFlag());
                    return whseDTO;
                } )
                .collect(Collectors.toList());
        return whseDTOS;
    }

    @Override
    public List<ManageContReachWhse> queryAllComContReachWhseList() {
        QueryWrapper<Mc04IslmcContReachWhseDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Mc04IslmcContReachWhseDO::getIcomCode, IcomUtils.getIcomCode())
                .eq(Mc04IslmcContReachWhseDO::getIsUse, "1")
                .orderByAsc(Mc04IslmcContReachWhseDO::getSeq);
        List<Mc04IslmcContReachWhseDO> list = mc04IslmcContReachWhseService.list(queryWrapper);
        List<ManageContReachWhse> whseDTOS = list.stream()
                .map(whseDO ->{
                    ManageContReachWhse whseDTO = new ManageContReachWhse();
                    whseDTO.setMd02CgtInStorehouseCode(whseDO.getMd02CgtInStorehouseCode());
                    whseDTO.setBaComOrgCode(whseDO.getBaComOrgCode());
                    whseDTO.setMd03LogtIcOrderDeliveryWhseCode(whseDO.getMd03LogtIcOrderDeliveryWhseCode());
                    whseDTO.setMc05LogtTransportDelIndWarehouseCode(whseDO.getMc05LogtTransportDelIndWarehouseCode());
                    whseDTO.setMd02CgtInStorehouseName(whseDO.getMd02CgtInStorehouseName());
                    whseDTO.setMd02CgtInStorehouseAdress(whseDO.getMd02CgtInStorehouseAdress());
                    whseDTO.setZaDivCode(whseDO.getZaDivCode());
                    whseDTO.setCaDefaultStoreouseFlag(whseDO.getCaDefaultStoreouseFlag());
                    return whseDTO;
                } )
                .collect(Collectors.toList());
        return whseDTOS;
    }
}