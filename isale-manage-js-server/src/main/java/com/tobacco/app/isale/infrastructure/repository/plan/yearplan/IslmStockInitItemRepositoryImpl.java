/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.yearplan;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.inspur.ind.util.IDUtils;
import com.tobacco.app.isale.domain.model.plan.yearplan.IslmStockInitItemModel;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmStockInitItemRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmStockInitItemDoConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmStockInitItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmStockInitItemService;
import com.tobacco.app.isale.tools.utils.OrderedIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 库存初始化 领域仓库实现
 *
 * @Author: longxi
 * @Since: 2025-08-07
 */
@Component("IslmStockInitItemRepository")
public class IslmStockInitItemRepositoryImpl implements IslmStockInitItemRepository {

    @Autowired
    private Mc04IslmStockInitItemService islmStockInitItemService;

    @Override
    public List<IslmStockInitItemModel> listByStockInitId(String mc04StockInitId) {
        LambdaQueryWrapper<Mc04IslmStockInitItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmStockInitItemDO::getMc04StockInitId, mc04StockInitId);
        List<Mc04IslmStockInitItemDO> stockInitItemDoS = islmStockInitItemService.list(queryWrapper);
        return Mc04IslmStockInitItemDoConverter.INSTANCE.converterDosToModels(stockInitItemDoS);
    }

    @Override
    public boolean saveBatch(List<IslmStockInitItemModel> stockInitItemModelList) {
        if (CollectionUtil.isNotEmpty(stockInitItemModelList)) {
            stockInitItemModelList.forEach(item -> item.setMc04StockInitItemId(OrderedIdGenerator.generateOrderedId32()));
        }
        List<Mc04IslmStockInitItemDO> mc04IslmStockInitItemDoS = Mc04IslmStockInitItemDoConverter.INSTANCE.converterModelsToDos(stockInitItemModelList);
        return islmStockInitItemService.saveBatch(mc04IslmStockInitItemDoS);
    }

    @Override
    public boolean updateBatch(List<IslmStockInitItemModel> stockInitItemModelList) {
        List<Mc04IslmStockInitItemDO> mc04IslmStockInitItemDoS = Mc04IslmStockInitItemDoConverter.INSTANCE.converterModelsToDos(stockInitItemModelList);
        return islmStockInitItemService.updateBatchById(mc04IslmStockInitItemDoS);
    }
}
