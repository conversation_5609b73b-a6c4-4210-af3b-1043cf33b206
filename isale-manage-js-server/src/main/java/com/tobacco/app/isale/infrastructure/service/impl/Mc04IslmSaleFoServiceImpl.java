/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSaleFoDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmSaleFoMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmSaleFoService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: qintian
 * @Since: 2025-07-22
 * @Email: <EMAIL>
 * @Create: 2025-07-22
        */
@Service
public class Mc04IslmSaleFoServiceImpl extends ServiceImpl<Mc04IslmSaleFoMapper, Mc04IslmSaleFoDO> implements Mc04IslmSaleFoService {

}