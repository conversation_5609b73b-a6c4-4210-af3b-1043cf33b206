/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.order.ret.bill;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.constants.DistConstants;
import com.tobacco.app.isale.domain.enums.order.ret.bill.ReturnBillStatusEnum;
import com.tobacco.app.isale.domain.model.order.ret.bill.ReturnBillDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.apply.ApplyDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.apply.ApplyItemDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.contstart.ContStartDomain;
import com.tobacco.app.isale.domain.repository.order.ret.bill.ReturnBillRepository;
import com.tobacco.app.isale.infrastructure.converter.order.ret.bill.ReturnBillDomainToDoConverter;
import com.tobacco.app.isale.infrastructure.converter.order.ret.bill.apply.ReturnBillDOToModelConverter;
import com.tobacco.app.isale.infrastructure.converter.order.ret.bill.apply.ReturnBillItemDOToModelConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtReturnBillDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtReturnBillItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcCgtReturnBillMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmContOrderService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcCgtReturnBillItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcCgtReturnBillService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.tobacco.app.isale.domain.enums.order.ret.bill.ReturnBillStatusEnum.RETURN_BILL_STATUS_50;
import static com.tobacco.app.isale.domain.enums.order.ret.bill.TobaProdTradeReturnTypeCpdeEnum.TOBA_PROD_TRADE_RETURN_0;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: qifengyu
 * @Since: 2025-07-25
 */
@Component("ISaleMc04IslmcCgtReturnBillDORepository")
public class Mc04IslmcCgtReturnBillRepositoryImpl implements ReturnBillRepository {

    private Mc04IslmcCgtReturnBillMapper returnBillMapper;

    private Mc04IslmcCgtReturnBillService billService;

    private Mc04IslmcCgtReturnBillItemService billItemService;

    private Mc04IslmContOrderService contOrderService;

    @Autowired
    public void setContOrderService(Mc04IslmContOrderService contOrderService) {
        this.contOrderService = contOrderService;
    }

    @Autowired
    public void setBillItemService(Mc04IslmcCgtReturnBillItemService billItemService) {
        this.billItemService = billItemService;
    }

    @Autowired
    public void setBillService(Mc04IslmcCgtReturnBillService billService) {
        this.billService = billService;
    }

    @Autowired
    public void setReturnBillMapper(Mc04IslmcCgtReturnBillMapper returnBillMapper) {
        this.returnBillMapper = returnBillMapper;
    }

    /**
     * 根据商业公司code获取申请单信息
     *
     * @param baComOrgCode 查询参数
     * @return ApplyDomain
     * @create_time : 2025-07-23 09:14:49
     */
    @Override
    public ApplyDomain getApplyDomainByCode(String baComOrgCode) {
        List<Mc04IslmcCgtReturnBillDO> list = billService.lambdaQuery()
                .eq(Mc04IslmcCgtReturnBillDO::getBaComOrgCode, baComOrgCode)
                .list();
        if (list.isEmpty()) {
            return null;
        }
        Mc04IslmcCgtReturnBillDO mc04IslmcCgtReturnBillDO = list.get(0);
        ReturnBillDomain returnBillDomain = ReturnBillDOToModelConverter.INSTANCE.converterDoToModel(mc04IslmcCgtReturnBillDO);
        ApplyDomain applyDomain = new ApplyDomain();
        BeanUtils.copyProperties(returnBillDomain, applyDomain);
        return applyDomain;
    }

    /**
     * 批量校验当前选中退货订单是否存在状态不合规
     *
     * @param mc04CgtReturnBillIdList 查询参数
     * @return Boolean
     * @create_time : 2025-05-23 09:14:49
     * @description : 批量校验当前选中退货订单是否存在状态不合规
     */
    @Override
    public Boolean batchVerificationStatus(List<String> mc04CgtReturnBillIdList) {
        List<Mc04IslmcCgtReturnBillDO> mc04IslmcCgtReturnBillDOS = returnBillMapper.selectBatchIds(mc04CgtReturnBillIdList);
        //判断
        return mc04IslmcCgtReturnBillDOS.stream()
                .allMatch(mc04IslmcCgtReturnBillDO -> RETURN_BILL_STATUS_50.getStatus().equals(mc04IslmcCgtReturnBillDO.getMc04CgtReturnBillStatus()));
    }


    /**
     * 根据制定参数获取查询详情
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param baComOrgCode 商业公司code
     * @param returnBillStatus 状态
     * @return List<ReturnBillDomain>
     * @create_time : 2025-07-23 09:14:49
     */
    @Override
    public List<ReturnBillDomain> queryReturnBillList(String startTime, String endTime, String baComOrgCode, String returnBillStatus) {
        if (StrUtil.isBlank(startTime) || StrUtil.isBlank(endTime)) {
            return new ArrayList<>();
        }
        LambdaQueryChainWrapper<Mc04IslmcCgtReturnBillDO> lambdaQueryChainWrapper = billService.lambdaQuery();
        if (StringUtils.isNotBlank(returnBillStatus)) {
            lambdaQueryChainWrapper.eq(Mc04IslmcCgtReturnBillDO::getMc04CgtReturnBillStatus, returnBillStatus);
        }
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            lambdaQueryChainWrapper.ge(Mc04IslmcCgtReturnBillDO::getMc04CgtReturnBillDate, startTime)
                    .le(Mc04IslmcCgtReturnBillDO::getMc04CgtReturnBillDate, endTime);
        }
        if (StrUtil.isNotBlank(baComOrgCode)) {
            String[] baComOrgCodeList = baComOrgCode.split(",");
            lambdaQueryChainWrapper.in(Mc04IslmcCgtReturnBillDO::getBaComOrgCode, baComOrgCodeList);
        }
        List<Mc04IslmcCgtReturnBillDO> returnBillDOList = lambdaQueryChainWrapper.list();
        return ReturnBillDomainToDoConverter.INSTANCE.converterDosToModels(returnBillDOList);
    }

    /**
     * 检验是否是指定状态
     *
     * @param ReturnBillIdList 申请ID
     * @param returnBillStatus 状态
     * @return Boolean
     * @create_time : 2025-07-23 09:14:49
     */
    @Override
    public Boolean verifyReturnBillStatus(List<String> ReturnBillIdList, String returnBillStatus) {
        List<Mc04IslmcCgtReturnBillDO> list = billService.lambdaQuery().in(Mc04IslmcCgtReturnBillDO::getMc04CgtReturnBillId, ReturnBillIdList).list();
        return list.stream().anyMatch(mc04IslmcCgtReturnBillDO -> mc04IslmcCgtReturnBillDO.getMc04CgtReturnBillStatus().equals(returnBillStatus));
    }

    /**
     * 保存退货信息以及状态
     *
     * @param applyDomain 退货单信息
     * @param billStatus 状态
     * @return Boolean
     * @create_time : 2025-07-23 09:14:49
     */
    @Override
    public Boolean saveBillAndItem(ApplyDomain applyDomain, String billStatus) {
        //如果id为空 说明是初始化内容 则创建
        //可以直接保存 直接提交
        Mc04IslmcCgtReturnBillDO mc04IslmcCgtReturnBillDO = new Mc04IslmcCgtReturnBillDO();
        mc04IslmcCgtReturnBillDO.setBaComOrgCode(applyDomain.getBaComOrgCode());
        mc04IslmcCgtReturnBillDO.setMd02TobaProdTradeReturnTypeCpde(TOBA_PROD_TRADE_RETURN_0.getStatus());
        mc04IslmcCgtReturnBillDO.setMc04CgtReturnBillDate(applyDomain.getMc04CgtReturnBillDate());
        mc04IslmcCgtReturnBillDO.setMc04CgtReturnBillStatus(billStatus);
        mc04IslmcCgtReturnBillDO.setZaRemark(applyDomain.getZaRemark());

        //流水号
        String id = IcomUtils.getNextId(DistConstants.DIST_PARM_APPLY_KEY);
        mc04IslmcCgtReturnBillDO.setMc04CgtReturnBillId(id);
        //根据code 查找当前退货仓库和发货仓库
        //收货为发货仓库  发货为收货
        mc04IslmcCgtReturnBillDO.setMd02CgtInStorehouseCode(applyDomain.getMd02CgtInStorehouseCode());
        mc04IslmcCgtReturnBillDO.setMd02CgtOutStorehouseCode(applyDomain.getMd02CgtOutStorehouseCode());
        //存储退货订单
        billService.saveOrUpdate(mc04IslmcCgtReturnBillDO);

        List<ApplyItemDomain> applyItemDomainList = applyDomain.getApplyItemDomainList();
        List<Mc04IslmcCgtReturnBillItemDO> billItemDOList = new ArrayList<>(applyItemDomainList.size());
        applyItemDomainList.forEach(item -> {
            Mc04IslmcCgtReturnBillItemDO billItemDO = new Mc04IslmcCgtReturnBillItemDO();
            BeanUtils.copyProperties(item, billItemDO);
            billItemDO.setMc04CgtReturnBillId(id);
            billItemDOList.add(billItemDO);
        });
        return billItemService.saveBatch(billItemDOList);
    }

    /**
     * 批量更新状态
     *
     * @param mc04CgtReturnBillIds 退货单信息
     * @param status 状态
     * @return Boolean
     * @create_time : 2025-07-23 09:14:49
     */
    @Override
    public Boolean batchSetStatus(List<String> mc04CgtReturnBillIds, String status) {
        LambdaUpdateChainWrapper<Mc04IslmcCgtReturnBillDO> lambdaUpdateChainWrapper = billService.lambdaUpdate();
        return lambdaUpdateChainWrapper.in(Mc04IslmcCgtReturnBillDO::getMc04CgtReturnBillId, mc04CgtReturnBillIds)
                .set(Mc04IslmcCgtReturnBillDO::getMc04CgtReturnBillStatus, status)
                .update();
    }

    /**
     * 查询合同拆分表获取信息
     *
     * @param contStartDomain 合同开始信息
     * @return  List<ReturnBillDomain>
     * @create_time : 2025-07-23 09:14:49
     */
    @Override
    public List<ReturnBillDomain> getContStartList(ContStartDomain contStartDomain) {
        LambdaQueryChainWrapper<Mc04IslmcCgtReturnBillDO> query = billService.lambdaQuery();
        if (StrUtil.isAllNotBlank(contStartDomain.getMc04CgtReturnBillDateStartTime(), contStartDomain.getMc04CgtReturnBillDateEndTime())) {
            query.ge(Mc04IslmcCgtReturnBillDO::getMc04CgtReturnBillDate, contStartDomain.getMc04CgtReturnBillDateStartTime())
                    .le(Mc04IslmcCgtReturnBillDO::getMc04CgtReturnBillDate, contStartDomain.getMc04CgtReturnBillDateEndTime());
        }
        if (StrUtil.isNotBlank(contStartDomain.getBaComOrgCode())) {
            query.eq(Mc04IslmcCgtReturnBillDO::getBaComOrgCode, contStartDomain.getBaComOrgCode());
        }
        List<Mc04IslmContOrderDO> orderDOList = contOrderService.lambdaQuery().isNotNull(Mc04IslmContOrderDO::getMc04CgtDayPlanCode).list();
        List<String> orderCodeList = orderDOList.stream().map(Mc04IslmContOrderDO::getMc04CgtDistOrderCode).collect(Collectors.toList());
        String status = ReturnBillStatusEnum.RETURN_BILL_STATUS_40.getStatus();
        query.eq(Mc04IslmcCgtReturnBillDO::getMc04CgtReturnBillStatus, status);
        query.notIn(!orderCodeList.isEmpty(),Mc04IslmcCgtReturnBillDO::getMc04CgtReturnBillId, orderCodeList);
        return ReturnBillDomainToDoConverter.INSTANCE.converterDosToModels(query.list());
    }

    /**
     * 获取退货单列表
     *
     * @param baComOrgCodeList 商业公司code列表
     * @param statusList 状态列表
     * @return  Boolean
     * @create_time : 2025-07-23 09:14:49
     */
    @Override
    public Boolean getReturnBillList(List<String> baComOrgCodeList, List<String> statusList) {
        List<Mc04IslmcCgtReturnBillDO> list = billService
                .lambdaQuery()
                .in(Mc04IslmcCgtReturnBillDO::getBaComOrgCode, baComOrgCodeList)
                .in(Mc04IslmcCgtReturnBillDO::getMc04CgtReturnBillStatus, statusList)
                .list();
        return list.isEmpty();
    }


    /**
     * 获取退货单列表
     *
     * @param baComOrgCodeArray 商业公司code列表
     * @return   List<ApplyItemDomain>
     * @create_time : 2025-07-23 09:14:49
     */
    @Override
    public List<ApplyItemDomain> queryBillsByParams(List<String> baComOrgCodeArray) {
        //退货日期查询范围，用英逗分割起始和结束
        QueryWrapper<Mc04IslmcCgtReturnBillDO> queryWrapper = new QueryWrapper<>();

        if (!baComOrgCodeArray.isEmpty()) {
            queryWrapper.lambda().in(Mc04IslmcCgtReturnBillDO::getBaComOrgCode, baComOrgCodeArray);
        }
        List<Mc04IslmcCgtReturnBillDO> returnBillDOList = returnBillMapper.selectList(queryWrapper);
        List<String> returnBillIdList = returnBillDOList.stream().map(Mc04IslmcCgtReturnBillDO::getMc04CgtReturnBillId).collect(Collectors.toList());

        List<Mc04IslmcCgtReturnBillItemDO> billItemList = billItemService.lambdaQuery().in(Mc04IslmcCgtReturnBillItemDO::getMc04CgtReturnBillId, returnBillIdList).list();

        return ReturnBillItemDOToModelConverter.INSTANCE.converterDosToModels(billItemList);
    }

    @Override
    public ReturnBillDomain getReturnBillById(String billId) {
        Mc04IslmcCgtReturnBillDO returnBillDo = billService.lambdaQuery().eq(Mc04IslmcCgtReturnBillDO::getMc04CgtReturnBillId, billId).one();
        return ReturnBillDOToModelConverter.INSTANCE.converterDoToModel(returnBillDo);
    }
}