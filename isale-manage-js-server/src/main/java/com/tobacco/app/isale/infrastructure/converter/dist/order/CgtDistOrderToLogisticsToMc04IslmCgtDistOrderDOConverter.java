/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.order;

import com.tobacco.app.isale.domain.model.order.dist.order.CgtDistOrderToLogistics;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtDistOrderDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/09/08 10:20
 * @description : 传输物流对象和数据库对象转换
 */
@Mapper
public interface CgtDistOrderToLogisticsToMc04IslmCgtDistOrderDOConverter extends StructureBaseConverter<Mc04IslmCgtDistOrderDO, CgtDistOrderToLogistics> {
    CgtDistOrderToLogisticsToMc04IslmCgtDistOrderDOConverter SELF = Mappers.getMapper(CgtDistOrderToLogisticsToMc04IslmCgtDistOrderDOConverter.class);
}