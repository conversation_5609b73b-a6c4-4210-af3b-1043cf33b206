package com.tobacco.app.isale.infrastructure.converter.plan.yearplan;

import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanItem;
import com.tobacco.app.isale.dto.plan.yearplan.Mc04SalePlanItemDto;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSalePlanItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version v1.0.0
 * 创建时间：2025/8/12 15:08
 */
@Mapper
public interface Mc04IslmSalePlanItemDOToMc04IslmSalePlanItemConverter extends StructureBaseConverter<Mc04IslmSalePlanItemDO, Mc04IslmSalePlanItem> {

    Mc04IslmSalePlanItemDOToMc04IslmSalePlanItemConverter INSTANCE =
            Mappers.getMapper(Mc04IslmSalePlanItemDOToMc04IslmSalePlanItemConverter.class);
}
