/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.agreement.sign;

import com.tobacco.app.isale.domain.model.agreement.sign.ManageAgreementBill;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcXyBillDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/05/13 15:25
 * @description :
 */
@Mapper
public interface Mc04IslmcXyBillDOToManageAgreementBillConverter
        extends StructureBaseConverter<Mc04IslmcXyBillDO,  ManageAgreementBill> {

    Mc04IslmcXyBillDOToManageAgreementBillConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcXyBillDOToManageAgreementBillConverter.class);
}
