/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.order;

import com.tobacco.app.isale.domain.model.order.dist.order.NationDistPreview;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.NationDistPreviewDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jinfuli
 * @Date: 2025/8/11
 * @Description:
 */
@Mapper
public interface NationDistPreviewConverter extends StructureBaseConverter<NationDistPreviewDO, NationDistPreview> {
    NationDistPreviewConverter INSTANCE = Mappers.getMapper(NationDistPreviewConverter.class);
}
