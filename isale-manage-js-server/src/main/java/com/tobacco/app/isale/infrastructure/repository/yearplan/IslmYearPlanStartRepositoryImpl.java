/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.yearplan;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.icom.IcomUtils;
import com.inspur.ind.util.IDUtils;
import com.tobacco.app.isale.domain.enums.common.ProdTradeTypeCodeEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.LatestVersionEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.OrgTypeEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.PeriodTypeEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.SalePlanStatusEnum;
import com.tobacco.app.isale.domain.repository.plan.IslmYearPlanStartRepository;
import com.tobacco.app.isale.dto.plan.yearplan.Mc04IslmSalePlanDTO;
import com.tobacco.app.isale.infrastructure.entity.*;
import com.tobacco.app.isale.infrastructure.service.api.*;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmYearPlanMapper;
import com.tobacco.sc.icust.client.api.com.ComServiceAPI;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import com.tobacco.sc.icust.dto.common.KCMultiResponse;
import com.tobacco.sc.icust.req.com.GetBusiComListREQ;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * @Author: jinfuli
 * @Date: 2025/6/13
 * @Description:
 */
@Component("IslmYearPlanStartRepository")
public class IslmYearPlanStartRepositoryImpl implements IslmYearPlanStartRepository {
    private static final Logger log = LoggerFactory.getLogger(IslmYearPlanStartRepositoryImpl.class);
    @Autowired
    private IslmYearPlanMapper islmYearPlanMapper;
    @Autowired
    private Mc04IslmBrandPlanService mc04IslmBrandPlanService;
    @Autowired
    private Mc04IslmSaleFoService mc04IslmSaleFoService;
    @Autowired
    private Mc04IslmDemandFoService mc04IslmDemandFoService;
    @Autowired
    private Mc04IslmYcSalePlanService mc04IslmYcSalePlanService;
    @Autowired
    private Mc04IslmPlanSubjectService mc04IslmPlanSubjectService;
    @Autowired
    private Mc04IslmPlanSubjectLineService mc04IslmPlanSubjectLineService;
    @Autowired
    private Mc04IslmSalePlanService mc04IslmSalePlanService;

    private final ComServiceAPI comServiceAPI;

    public IslmYearPlanStartRepositoryImpl(ComServiceAPI comServiceAPI) {
        this.comServiceAPI = comServiceAPI;
    }

    @Override
    public boolean createMc04IslmSalePlan(
            String planId,
            String year,
            String mc04PlanSubjectType,
            String mc04PlanSubjectName,
            String startDate,
            String endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddmmhhssSSS");
        String version = "PV" + LocalDateTime.now().format(formatter);

        Mc04IslmSalePlanDO mc04IslmSalePlanDO = new Mc04IslmSalePlanDO();
        mc04IslmSalePlanDO.setMc04SalePlanId(planId);  // 设置计划 ID
        mc04IslmSalePlanDO.setMa02TobaProdTradeTypeCode("0");  // 设置产品贸易类型代码
        mc04IslmSalePlanDO.setMc04CgtSalePlanVersion(version);  // 设置销售计划版本
        mc04IslmSalePlanDO.setMc04IsLastestVersion("1");  // 设置是否是最新版本
        mc04IslmSalePlanDO.setZaOccurrenceYear(year);  // 设置发生年份
        mc04IslmSalePlanDO.setMc04PlanSubjectType(mc04PlanSubjectType);  // 设置计划主题类型
        mc04IslmSalePlanDO.setMc04PlanSubjectName(mc04PlanSubjectName);  // 设置计划主题名称
        mc04IslmSalePlanDO.setMc04PlanSubjectBeginDate(startDate);  // 设置计划主题开始日期
        mc04IslmSalePlanDO.setMc04PlanSubjectEndDate(endDate);  // 设置计划主题结束日期
        mc04IslmSalePlanDO.setMc04SalePlanStatus("10");  // 设置销售计划状态
        mc04IslmSalePlanDO.setMc04CgtSaleFoPeriodType("T01");  // 设置销售 FO 期类型
        mc04IslmSalePlanDO.setMc04CgtSaleFoPeriodCode(year);  // 设置销售 FO 期代码
        mc04IslmSalePlanDO.setMc04OrgTypeKind("QG");  // 设置组织类型
        mc04IslmSalePlanDO.setMc04OrgTypeCode("1");  // 设置组织类型代码
        return mc04IslmSalePlanService.save(mc04IslmSalePlanDO);
    }

    @Override
    public boolean createIslmBrandPlan(String planId, String year, String mc04PlanSubjectType, String mc04PlanSubjectName, String startDate, String endDate) {
        Mc04IslmBrandPlanDO mc04IslmBrandPlan = new Mc04IslmBrandPlanDO();
        mc04IslmBrandPlan.setMc04BrandPlanId(planId);
        mc04IslmBrandPlan.setMa02TobaProdTradeTypeCode("0");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddmmhhssSSS");
        String version = "PV" + LocalDateTime.now().format(formatter);
        mc04IslmBrandPlan.setMc04BrandPlanVersion(version);
        mc04IslmBrandPlan.setMc04IsLastestVersion("1");
        mc04IslmBrandPlan.setZaOccurrenceYear(year);
        mc04IslmBrandPlan.setMc04PlanSubjectType(mc04PlanSubjectType);
        mc04IslmBrandPlan.setMc04PlanSubjectName(mc04PlanSubjectName);
        mc04IslmBrandPlan.setMc04PlanSubjectBeginDate(startDate);
        mc04IslmBrandPlan.setMc04PlanSubjectEndDate(endDate);
        mc04IslmBrandPlan.setMc04PlanSubjectStatus("0");
        mc04IslmBrandPlan.setMc04CgtSaleFoPeriodType("T01");
        mc04IslmBrandPlan.setMc04CgtSaleFoPeriodCode(year);
        mc04IslmBrandPlan.setMc04OrgTypeKind("QG");
        mc04IslmBrandPlan.setMc04OrgTypeCode("1");
        mc04IslmBrandPlan.setCreateId(null);
        mc04IslmBrandPlan.setCreateName(null);
        mc04IslmBrandPlan.setCreateTime(null);
        return mc04IslmBrandPlanService.save(mc04IslmBrandPlan);
    }

    @Override
    public boolean createMc04IslmSaleFoDO(String planId, String year, String mc04PlanSubjectType, String mc04PlanSubjectName, String startDate, String endDate) {
        Mc04IslmSaleFoDO mc04IslmSaleFoDO = new Mc04IslmSaleFoDO();
        mc04IslmSaleFoDO.setMc04CgtSaleFoId(planId);
        mc04IslmSaleFoDO.setMa02TobaProdTradeTypeCode("0");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddmmhhssSSS");
        String version = "PV" + LocalDateTime.now().format(formatter);
        mc04IslmSaleFoDO.setMc04CgtSaleFoVersion(version);
        mc04IslmSaleFoDO.setMc04IsLastestVersion("1");
        mc04IslmSaleFoDO.setZaOccurrenceYear(year);
        mc04IslmSaleFoDO.setMc04PlanSubjectType(mc04PlanSubjectType);
        mc04IslmSaleFoDO.setMc04PlanSubjectName(mc04PlanSubjectName);
        mc04IslmSaleFoDO.setMc04PlanSubjectBeginDate(startDate);
        mc04IslmSaleFoDO.setMc04PlanSubjectEndDate(endDate);
        mc04IslmSaleFoDO.setMc04PlanSubjectStatus("0");
        mc04IslmSaleFoDO.setCreateId(null);
        mc04IslmSaleFoDO.setCreateName(null);
        mc04IslmSaleFoDO.setCreateTime(null);
        return mc04IslmSaleFoService.save(mc04IslmSaleFoDO);
    }

    @Override
    public boolean createMc04IslmDemandFoDO(String planId, String year, String mc04PlanSubjectType, String mc04PlanSubjectName, String startDate, String endDate) {
        try {
            GetBusiComListREQ q = new GetBusiComListREQ();
            q.setMc04ComOrgIsXy("1");
            q.setIcomCode(IcomUtils.getIcomCode());
            KCMultiResponse<BusiComDto> busiComList = comServiceAPI.getBusiComList(q);
            Collection<BusiComDto> items = busiComList.getData().getItems();
            List<Mc04IslmDemandFoDO> list = new ArrayList<>();
            for (BusiComDto busiComDto : items) {
                Mc04IslmDemandFoDO mc04IslmDemandFoDO = new Mc04IslmDemandFoDO();
                mc04IslmDemandFoDO.setMc04DemandFoId(IDUtils.randomUUID(32));
                mc04IslmDemandFoDO.setMa02TobaProdTradeTypeCode("0");
                mc04IslmDemandFoDO.setBaComOrgCode(busiComDto.getBaComOrgCode());
                mc04IslmDemandFoDO.setZaOccurrenceYear(year);
                mc04IslmDemandFoDO.setMc04ComOrgLevel(busiComDto.getMc04ComOrgLevel());
                mc04IslmDemandFoDO.setMc04ComOrgIsImported(busiComDto.getMc04ComOrgIsImported());
                mc04IslmDemandFoDO.setMc04PlanSubjectType(mc04PlanSubjectType);
                mc04IslmDemandFoDO.setMc04PlanSubjectName(mc04PlanSubjectName);
                mc04IslmDemandFoDO.setMd02CgtFoMakeBeginDate(startDate);
                mc04IslmDemandFoDO.setMd02CgtFoMakeEndDate(endDate);
                mc04IslmDemandFoDO.setMc04DemandFoStatus("0");
                mc04IslmDemandFoDO.setCreateId(null);
                mc04IslmDemandFoDO.setCreateName(null);
                mc04IslmDemandFoDO.setCreateTime(null);
                list.add(mc04IslmDemandFoDO);
            }
            mc04IslmDemandFoService.saveBatch(list);
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.error("调用获取协议单位接口异常{}", e);
            }
        }
        return true;
    }

    @Override
    public boolean createMc04IslmYcSalePlanDO(String planId, String year, String mc04PlanSubjectType, String mc04PlanSubjectName, String startDate, String endDate) {
        Mc04IslmYcSalePlanDO mc04IslmYcSalePlanDO = new Mc04IslmYcSalePlanDO();
        mc04IslmYcSalePlanDO.setMc04SalePlanId(planId);
        mc04IslmYcSalePlanDO.setMa02TobaProdTradeTypeCode(ProdTradeTypeCodeEnum.PROD_TRADE_TYPE_CODE_0.getCode());
        mc04IslmYcSalePlanDO.setMc04CgtSalePlanVersion("1");
        mc04IslmYcSalePlanDO.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        mc04IslmYcSalePlanDO.setZaOccurrenceYear(year);
        mc04IslmYcSalePlanDO.setMc04PlanSubjectType(mc04PlanSubjectType);
        mc04IslmYcSalePlanDO.setMc04PlanSubjectName(mc04PlanSubjectName);
        mc04IslmYcSalePlanDO.setMc04PlanSubjectBeginDate(startDate);
        mc04IslmYcSalePlanDO.setMc04PlanSubjectEndDate(endDate);
        mc04IslmYcSalePlanDO.setMc04OrgTypeKind(OrgTypeEnum.QG.getKind());
        mc04IslmYcSalePlanDO.setMc04OrgTypeCode(OrgTypeEnum.QG.getCode());
        mc04IslmYcSalePlanDO.setMc04OrgTypeName(OrgTypeEnum.QG.getName());
        mc04IslmYcSalePlanDO.setMc04SalePlanStatus(SalePlanStatusEnum.DRAFT.getCode());
        mc04IslmYcSalePlanDO.setMc04CgtSaleFoPeriodType(PeriodTypeEnum.YC.getCode());
        mc04IslmYcSalePlanDO.setMc04CgtSaleFoPeriodCode(year);
        mc04IslmYcSalePlanDO.setCreateId(null);
        mc04IslmYcSalePlanDO.setCreateName(null);
        mc04IslmYcSalePlanDO.setCreateTime(null);
        return mc04IslmYcSalePlanService.save(mc04IslmYcSalePlanDO);
    }

    @Override
    public Page<Mc04IslmSalePlanDTO> getyearplanlist(Page page, String startYear, String endYear, String type, String status, String ma02TobaProdTradeTypeCode, String foCount) {
        Page<Mc04IslmSalePlanDTO> getyearplanlist =
                islmYearPlanMapper.getyearplanlist(
                        page, startYear, endYear, type, status, ma02TobaProdTradeTypeCode, foCount
                );
        return getyearplanlist;
    }

    @Override
    public boolean getMc04IslmPlanSubjectDOList(String zaOccurrenceYear, String mc04PlanSubjectType) {
        //判断新增的年度主题类型是否已经存在
        QueryWrapper<Mc04IslmPlanSubjectDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmPlanSubjectDO::getZaOccurrenceYear, zaOccurrenceYear)
                .eq(Mc04IslmPlanSubjectDO::getMc04PlanSubjectType, mc04PlanSubjectType);

        List<Mc04IslmPlanSubjectDO> list = mc04IslmPlanSubjectService.list(queryWrapper);
        if (!list.isEmpty()) {
            return false;
        }
        return true;
    }

    @Override
    public void saveMc04IslmPlanSubject(Mc04IslmPlanSubjectDO mc04IslmPlanSubject) {
        mc04IslmPlanSubjectService.save(mc04IslmPlanSubject);
    }

    @Override
    public void removeMc04IslmPlanSubjectById(String mc04SalePlanId) {
        mc04IslmPlanSubjectService.removeById(mc04SalePlanId);
    }

    @Override
    public boolean removeMc04IslmPlanSubjectLine(String mc04SalePlanId) {
        QueryWrapper<Mc04IslmPlanSubjectLineDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(
                Mc04IslmPlanSubjectLineDO::getMc04PlanSubjectId, mc04SalePlanId
        );
        return mc04IslmPlanSubjectLineService.remove(queryWrapper);
    }

    @Override
    public void saveOrUpdateMc04IslmPlanSubject(Mc04IslmPlanSubjectDO mc04IslmPlanSubject) {
        mc04IslmPlanSubjectService.saveOrUpdate(mc04IslmPlanSubject);
    }

    @Override
    public void batchSaveMc04IslmPlanSubjectLine(List<Mc04IslmPlanSubjectLineDO> mc04IslmPlanSubjectLineDOS) {
        mc04IslmPlanSubjectLineService.saveBatch(mc04IslmPlanSubjectLineDOS);
    }

    @Override
    public void batchRemoveMc04IslmPlanSubjectLine(String mc04PlanSubjectId) {
        QueryWrapper<Mc04IslmPlanSubjectLineDO> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.lambda().eq(
                Mc04IslmPlanSubjectLineDO::getMc04PlanSubjectId, mc04PlanSubjectId);
        //先删后插
        mc04IslmPlanSubjectLineService.remove(queryWrapper1);
    }
}
