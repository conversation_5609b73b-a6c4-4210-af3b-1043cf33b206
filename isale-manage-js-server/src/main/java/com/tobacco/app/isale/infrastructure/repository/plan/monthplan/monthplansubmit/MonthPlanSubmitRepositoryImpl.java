/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.monthplan.monthplansubmit;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ind.util.IDUtils;
import com.tobacco.app.isale.domain.constants.MonthPlanConstants;
import com.tobacco.app.isale.domain.model.item.IccItemDetail;
import com.tobacco.app.isale.domain.model.monthplan.Mc04IslmMonthSalePlanLock;
import com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan;
import com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlanSnapshot;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanQuarterItem;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmit;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmitAdd;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmitBasicItem;
import com.tobacco.app.isale.domain.repository.plan.monthplan.monthplansubmit.MonthPlanSubmitRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.MonthPlanLockConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.MonthPlanSubmitConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.MonthSalePlanSnapshotConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.monthplansubmit.MonthPlanSubmitAddConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.monthplansubmit.MonthPlanSubmitAddSnapshotConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanLockDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanSnapshotDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcForwardWhseComDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmMonthSalePlanMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanLockService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanSnapshotService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcForwardWhseComService;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan.monthplansubmit.MonthPlanSubmitMapper;
import com.tobacco.app.isale.tools.utils.CommodityUtil;
import com.tobacco.app.isale.tools.utils.CustUtil;
import com.tobacco.app.isalecenter.client.api.plan.monthplan.MonthSalePlanServiceAPI;
import com.tobacco.app.isalecenter.client.api.xy.XyAPI;
import com.tobacco.app.isalecenter.client.dto.common.SaleSingleResponse;
import com.tobacco.app.isalecenter.client.dto.plan.monthplan.MonthSalePlanDTO;
import com.tobacco.app.isalecenter.client.dto.xy.Mc04IslmcXyBatchDTO;
import com.tobacco.app.isalecenter.client.req.plan.monthplan.MonthSalePlanQueryBatchREQ;
import com.tobacco.app.isalecenter.client.req.xy.Mc04IslmcXyQueryBatchREQ;
import com.tobacco.sc.icommodity.dto.common.constant.dto.product.IccProductDetailDTO;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: jinfuli
 * @Date: 2025/6/13
 * @Description:
 */
@Repository
public class MonthPlanSubmitRepositoryImpl implements MonthPlanSubmitRepository {


    private final MonthPlanSubmitMapper monthPlanSubmitMapper;
    private final Mc04IslmMonthSalePlanService mc04IslmMonthSalePlanService;


    @Resource
    private XyAPI xyApi;

    @Autowired
    private MonthSalePlanServiceAPI monthSalePlanServiceAPI;

    @Autowired
    private Mc04IslmMonthSalePlanSnapshotService mc04IslmMonthSalePlanSnapshotService;

    @Autowired
    private Mc04IslmMonthSalePlanLockService mc04IslmMonthSalePlanLockService;

    @Autowired
    Mc04IslmMonthSalePlanMapper insurancePlanMapper;

    @Autowired
    private Mc04IslmcForwardWhseComService mc04IslmcForwardWhseComService;

    public MonthPlanSubmitRepositoryImpl(MonthPlanSubmitMapper monthPlanSubmitMapper, Mc04IslmMonthSalePlanService mc04IslmMonthSalePlanService) {
        this.monthPlanSubmitMapper = monthPlanSubmitMapper;
        this.mc04IslmMonthSalePlanService = mc04IslmMonthSalePlanService;
    }


    @Override
    public String getPlanSubjectType(String year) {
        return monthPlanSubmitMapper.getPlanSubjectType(year);
    }

    @Override
    public List<MonthPlanSubmitBasicItem> getBacicItem(String year, String currentMonth, List<String> comIds, String subjectType, String quarter, String halfYear, String isCigar, String nextQuarter, String lastHalfYear) {
        return monthPlanSubmitMapper.getBacicItem(year, currentMonth, comIds, subjectType, quarter, halfYear, isCigar, nextQuarter, lastHalfYear, null);
    }

    @Override
    public List<MonthPlanSubmit> getPurchSaleStk(String currentMonth, String nextMonth3, String monthMinus1, String monthMinus2, String monthMinus3, String sameMonth1, String sameMonth2, String sameMonth3, List<String> comIds, List<String> monthList) {
        return monthPlanSubmitMapper.getPurchSaleStk(currentMonth, nextMonth3, monthMinus1, monthMinus2, monthMinus3, sameMonth1, sameMonth2, sameMonth3, comIds, monthList);
    }

    @Override
    public List<MonthPlanSubmit> getLastThreeMonthQty(String monthMinus1, String monthMinus2, String monthMinus3, String icomCode, String isCigar, List<String> comIds, List<String> monthList) {
        return monthPlanSubmitMapper.getLastThreeMonthQty(monthMinus1, monthMinus2, monthMinus3, icomCode, isCigar, comIds, monthList);
    }

    @Override
    public List<MonthPlanSubmitBasicItem> getSelfYearPlan(List<String> comIds, String halfYear, String isCigar, String lastHalfYear) {
        return monthPlanSubmitMapper.getSelfYearPlan(comIds, halfYear, isCigar, lastHalfYear);
    }

    @Override
    public List<Map> getComItem(String comId) {
        return monthPlanSubmitMapper.getComItem(comId);
    }

    @Override
    public List<Map> getParamConfig(String year, String month, String comId) {
        return monthPlanSubmitMapper.getParamConfig(year, month, comId);
    }

    @Override
    public List<Map> getPurchSaleStkDay(String lastDay, String comId) {
        return monthPlanSubmitMapper.getPurchSaleStkDay(lastDay, comId);
    }

    @Override
    public List<Mc04IslmcXyBatchDTO> getMc04IslmcXyBatch(Mc04IslmcXyQueryBatchREQ request) {
        SaleSingleResponse<List<Mc04IslmcXyBatchDTO>> listSaleSingleResponse = xyApi.listBatchXy(request);
        if (!listSaleSingleResponse.isSuccess()){
            return Collections.emptyList();
        }
        return listSaleSingleResponse.getData();
    }

    @Override
    public List<Mc04IslmMonthSalePlan> getIslmMonthSalePlan(String zaOccurrenceMonth, List<String> baComOrgCodeList, String status) {
        QueryWrapper<Mc04IslmMonthSalePlanDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmMonthSalePlanDO::getZaOccurrenceMonth, zaOccurrenceMonth)
                .in(Mc04IslmMonthSalePlanDO::getBaComOrgCode, baComOrgCodeList)
                .eq(MonthPlanConstants.MONTH_SALE_PLAN_STATUS_40.equals(status), Mc04IslmMonthSalePlanDO::getMc04CgtSalePlanVersion, MonthPlanConstants.MC04_CGT_SALE_PLAN_VERSION_1);
        List<Mc04IslmMonthSalePlanDO> monthSalePlanDOS = mc04IslmMonthSalePlanService.list(queryWrapper);
        return MonthPlanSubmitConverter.INSTANCE.converterDosToModels(monthSalePlanDOS);
    }

    @Override
    public Mc04IslmMonthSalePlanLock getIslmMonthSalePlanLock(String baComOrgCode, String mz04BusinessTypeCode, String ma02TobaProdTradeTypeCode) {
        QueryWrapper<Mc04IslmMonthSalePlanLockDO> salePlanLockDOQueryWrapper = new QueryWrapper<>();
        salePlanLockDOQueryWrapper.lambda().eq(Mc04IslmMonthSalePlanLockDO::getBaComOrgCode, baComOrgCode)
                .eq(Mc04IslmMonthSalePlanLockDO::getMz04BusinessTypeCode, mz04BusinessTypeCode)
                .eq(Mc04IslmMonthSalePlanLockDO::getMa02TobaProdTradeTypeCode, ma02TobaProdTradeTypeCode);
        Mc04IslmMonthSalePlanLockDO planLockDO = mc04IslmMonthSalePlanLockService.getOne(salePlanLockDOQueryWrapper);
        return MonthPlanLockConverter.INSTANCE.converterDoToModel(planLockDO);
    }

    @Override
    public Map<String, IccItemDetail> getIccItemDetailMap(List<String> acTwoLevelCigCodeList) {
        return CommodityUtil.getIccItemDetailMap(acTwoLevelCigCodeList);
    }

    @Override
    public Map<String, IccProductDetailDTO> getIccProductDetailDtoMap(List<String> acCgtCartonCodeList) {
        return CommodityUtil.getIccProductDetailDtoMap(acCgtCartonCodeList);
    }

    @Override
    public List<MonthSalePlanDTO> getSaleListBatch(MonthSalePlanQueryBatchREQ req) {
        SaleSingleResponse<List<MonthSalePlanDTO>> list = monthSalePlanServiceAPI.listBatch(req);
        if (!list.isSuccess()){
            return Collections.emptyList();
        }
        return list.getData();
    }

    @Override
    public List<Mc04IslmMonthSalePlanSnapshot> getIslmMonthSalePlanSnapshotList(String zaOccurrenceMonth, List<String> baComOrgCodeList) {
        QueryWrapper<Mc04IslmMonthSalePlanSnapshotDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmMonthSalePlanSnapshotDO::getZaOccurrenceMonth, zaOccurrenceMonth)
                .in(Mc04IslmMonthSalePlanSnapshotDO::getBaComOrgCode, baComOrgCodeList)
                .orderBy(true,true, Mc04IslmMonthSalePlanSnapshotDO::getBaComOrgCode);
        List<Mc04IslmMonthSalePlanSnapshotDO> monthSalePlanList = mc04IslmMonthSalePlanSnapshotService.list(queryWrapper);
        return MonthSalePlanSnapshotConverter.INSTANCE.converterDosToModels(monthSalePlanList);
    }

    @Override
    public Collection<IccProductDetailDTO> getIccProductDetailDtos(List<String> acCgtCartonCodeList) {
        return CommodityUtil.getIccProductDetailDtos(acCgtCartonCodeList);
    }

    @Override
    public BusiComDto getBusiComDto(String baComOrgCode) {
        return CustUtil.getBusiComDto(baComOrgCode);
    }

    @Override
    public Collection<BusiComDto> getBusiComDtos(List<String> baComOrgCodeList) {
        return CustUtil.getBusiComDtos(baComOrgCodeList);
    }

    /**
     * 查询城市状态
     *
     * @param ma02PlanMonth
     * @param icomCode
     * @param mc04MonthSalePlanStatus
     * @return
     */
    @Override
    public List<Map<String, Object>> queryOrgStatus(String ma02PlanMonth, String icomCode, String mc04MonthSalePlanStatus) {
        return insurancePlanMapper.queryOrgStatus(ma02PlanMonth, icomCode, mc04MonthSalePlanStatus);
    }

    @Override
    public List<Mc04IslmMonthSalePlanLock> getIslmMonthSalePlanLockList(List<String> baComOrgCodeList, String mz04BusinessTypeCode, String ma02TobaProdTradeTypeCode) {
        QueryWrapper<Mc04IslmMonthSalePlanLockDO> salePlanLockDOQueryWrapper = new QueryWrapper<>();
        salePlanLockDOQueryWrapper.lambda().in(Mc04IslmMonthSalePlanLockDO::getBaComOrgCode, baComOrgCodeList)
                .eq(Mc04IslmMonthSalePlanLockDO::getMz04BusinessTypeCode, mz04BusinessTypeCode)
                .eq(Mc04IslmMonthSalePlanLockDO::getMa02TobaProdTradeTypeCode, ma02TobaProdTradeTypeCode);
        List<Mc04IslmMonthSalePlanLockDO> list = mc04IslmMonthSalePlanLockService.list(salePlanLockDOQueryWrapper);
        return MonthPlanLockConverter.INSTANCE.converterDosToModels(list);
    }

    @Override
    public List<MonthPlanQuarterItem> getQuarterItemList(String year, List<String> comIds, String subjectType, List<String> quarters) {
        return monthPlanSubmitMapper.getQuarterItemList(year, comIds, subjectType, quarters);
    }

    @Override
    public List<MonthPlanQuarterItem> getLastMonthQty(List<String> comIds, String month, String icomCode) {
        return monthPlanSubmitMapper.getLastMonthQty(comIds, month, icomCode);
    }

    @Override
    public Boolean saveBatch(List<MonthPlanSubmitAdd> monthPlanSubmitAddList) {
        List<Mc04IslmMonthSalePlanDO> doList = MonthPlanSubmitAddConverter.INSTANCE.converterModelsToDos(monthPlanSubmitAddList);
        return mc04IslmMonthSalePlanService.saveBatch(doList);
    }

    @Override
    public Boolean updateBatchById(List<MonthPlanSubmitAdd> monthPlanSubmitAddList) {
        List<Mc04IslmMonthSalePlanDO> doList = MonthPlanSubmitAddConverter.INSTANCE.converterModelsToDos(monthPlanSubmitAddList);
        return mc04IslmMonthSalePlanService.updateBatchById(doList);
    }

    @Override
    public Boolean saveBatchSnapshot(List<MonthPlanSubmitAdd> monthPlanSubmitAddList) {
        List<Mc04IslmMonthSalePlanSnapshotDO> doList = MonthPlanSubmitAddSnapshotConverter.INSTANCE.converterModelsToDos(monthPlanSubmitAddList);
        doList.forEach(e -> e.setMc04MonthSalePlanSnapshotId(IDUtils.randomUUID()));
        return mc04IslmMonthSalePlanSnapshotService.saveBatch(doList);
    }

    @Override
    public List<String> getForwardWhseCom() {

        List<Mc04IslmcForwardWhseComDO> list = mc04IslmcForwardWhseComService.list();
        return list.stream().map(Mc04IslmcForwardWhseComDO::getBaComOrgCode).collect(Collectors.toList());
    }

    /**
     * 根据业务月份查询快照
     *
     * @param zaOccurrenceMonths
     * @param baComOrgCodeList
     * @param ma02TobaProdTradeTypeCode
     * @return
     */
    @Override
    public List<Mc04IslmMonthSalePlanSnapshot> seleteBatchSnapshotList(List<String> zaOccurrenceMonths, List<String> baComOrgCodeList, String ma02TobaProdTradeTypeCode) {
        QueryWrapper<Mc04IslmMonthSalePlanSnapshotDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmMonthSalePlanSnapshotDO::getMa02TobaProdTradeTypeCode, ma02TobaProdTradeTypeCode)
                .in(Mc04IslmMonthSalePlanSnapshotDO::getZaOccurrenceMonth, zaOccurrenceMonths)
                .in(Mc04IslmMonthSalePlanSnapshotDO::getBaComOrgCode, baComOrgCodeList);
        return MonthSalePlanSnapshotConverter.INSTANCE.converterDosToModels(mc04IslmMonthSalePlanSnapshotService.list(queryWrapper));
    }

    /**
     * 删除快照
     *
     * @param idList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatchById(List<String> idList) {
        return mc04IslmMonthSalePlanSnapshotService.removeBatchByIds(idList);
    }

    @Override
    public List<Mc04IslmMonthSalePlan> getBatchByPlanMonth(String planMonth, List<String> comIds) {

        QueryWrapper<Mc04IslmMonthSalePlanDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmMonthSalePlanDO::getMa02PlanMonth, planMonth)
                .eq(Mc04IslmMonthSalePlanDO::getMc04MonthSalePlanStatus, MonthPlanConstants.MONTH_SALE_PLAN_STATUS_90)
                .eq(Mc04IslmMonthSalePlanDO::getMc04CgtSalePlanVersion, MonthPlanConstants.MC04_CGT_SALE_PLAN_VERSION_1);
//                .in(Mc04IslmMonthSalePlanDO::getBaComOrgCode, comIds);
        List<Mc04IslmMonthSalePlanDO> monthSalePlanDOS = mc04IslmMonthSalePlanService.list(queryWrapper);
        return MonthPlanSubmitConverter.INSTANCE.converterDosToModels(monthSalePlanDOS);
    }

    /**
     * 根据条件批量删除月计划表
     *
     * @param comOrgCode
     * @param zaOccurrenceMonth
     * @param acCgtCartonCodes
     * @return
     */
    @Override
    public Boolean deleteBatchByCondition(String comOrgCode, String zaOccurrenceMonth, List<String> acCgtCartonCodes) {
        QueryWrapper<Mc04IslmMonthSalePlanDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmMonthSalePlanDO::getBaComOrgCode, comOrgCode)
                .eq(Mc04IslmMonthSalePlanDO::getZaOccurrenceMonth, zaOccurrenceMonth)
                .in(Mc04IslmMonthSalePlanDO::getAcCgtCartonCode, acCgtCartonCodes);
        return mc04IslmMonthSalePlanService.remove(queryWrapper);
    }

    @Override
    public List<Mc04IslmMonthSalePlan> getPlanMonthByIdList(List<String> idList) {
        return MonthPlanSubmitConverter.INSTANCE.converterDosToModels(mc04IslmMonthSalePlanService.listByIds(idList));
    }
}
