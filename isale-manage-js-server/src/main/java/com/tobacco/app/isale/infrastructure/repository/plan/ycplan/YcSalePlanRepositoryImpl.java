/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.ycplan;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.base.CustomPage;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.enums.plan.ycplan.LatestVersionEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.OrgTypeEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.PeriodTypeEnum;
import com.tobacco.app.isale.domain.enums.plan.yearplan.YearPlanSubjectTypeEnum;
import com.tobacco.app.isale.domain.model.plan.PlanQty;
import com.tobacco.app.isale.domain.enums.plan.ycplan.SalePlanStatusEnum;
import com.tobacco.app.isale.domain.enums.plan.yearplan.YearPlanOrgTypeEnum;
import com.tobacco.app.isale.domain.enums.plan.yearplan.YearPlanPeriodTypeEnum;
import com.tobacco.app.isale.domain.model.plan.ycplan.*;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanItem;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanItemModel;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanModel;
import com.tobacco.app.isale.domain.repository.plan.ycplan.YcSalePlanRepository;
import com.tobacco.app.isale.dto.common.TreeHeader;
import com.tobacco.app.isale.infrastructure.converter.plan.ycplan.Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmSalePlanDoConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmSalePlanItemDoConverter;
import com.tobacco.app.isale.infrastructure.entity.*;
import com.tobacco.app.isale.infrastructure.service.api.*;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.ycplan.IslmYcSalePlanMapper;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmSalePlanMapper;
import com.tobacco.sc.icust.client.api.com.ComServiceAPI;
import com.tobacco.sc.icust.client.api.com.ComTreeServiceAPI;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import com.tobacco.sc.icust.dto.com.ComTree;
import com.tobacco.sc.icust.dto.common.KCMultiResponse;
import com.tobacco.sc.icust.req.com.ComTreeREQ;
import com.tobacco.sc.icust.req.com.GetBusiComListREQ;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Year;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: loongxi
 * @Since: 2025-08-07
 */
@Component("ISaleMc04IslmYcSalePlanDORepository")
public class YcSalePlanRepositoryImpl implements YcSalePlanRepository {

    @Resource
    private Mc04IslmYcSalePlanService mc04IslmYcSalePlanService;
    @Resource
    private Mc04IslmSalePlanService mc04IslmSalePlanService;
    @Resource
    private Mc04IslmYcSalePlanItemService mc04IslmYcSalePlanItemService;
    @Resource
    private Mc04IslmSalePlanItemService mc04IslmSalePlanItemService;
    @Resource
    private Mc04IslmDemandFoService mc04IslmDemandFoService;
    @Resource
    private Mc04IslmDemandFoItemService mc04IslmDemandFoItemService;
    @Resource
    private ComTreeServiceAPI comTreeServiceAPI;
    @Resource
    private IslmYcSalePlanMapper islmYcSalePlanMapper;
    @Resource
    private IslmSalePlanMapper islmSalePlanMapper;
    @Resource
    private ComServiceAPI comServiceAPI;
    @Override
    public Page<Mc04IslmYcSalePlan>  queryYcPlanPage(YcSalePlanPage ycSalePlanPage) {
        //查主表
        CustomPage<Mc04IslmYcSalePlanDO> page = new CustomPage<>(
                ycSalePlanPage.getOffset(),
                ycSalePlanPage.getLimit(),
                ycSalePlanPage.getSort(),
                ycSalePlanPage.getOrder()
        );
        Page<Mc04IslmYcSalePlan> pageResult = new Page<>();

        LambdaQueryWrapper<Mc04IslmYcSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();

        // 处理年份范围 - 当zaOccurrenceYear为空时不添加年份条件
        if (StringUtils.isNotBlank(ycSalePlanPage.getZaOccurrenceYear())) {
            String[] years = ycSalePlanPage.getZaOccurrenceYear().split(",");
            String startYear = years[0];
            String endYear = years.length > 1 ? years[1] : years[0];
            queryWrapper.between(Mc04IslmYcSalePlanDO::getZaOccurrenceYear, startYear, endYear);
        }

        List<String> statusList = new ArrayList<>();
        if (StringUtils.isNotBlank(ycSalePlanPage.getMc04SalePlanStatus())) {
            statusList = Arrays.asList(ycSalePlanPage.getMc04SalePlanStatus().split(","));
        }
        queryWrapper.in(CollectionUtil.isNotEmpty(statusList),
                Mc04IslmYcSalePlanDO::getMc04SalePlanStatus, statusList);
        // 这里需要注意，一个元春年度编制包含9行数据，这里分页查询只展示 组织分类类别为：全国，周期类型为：年度的那一行数据，而且最新版本标志为：1，表示为当前版本
        queryWrapper.eq(Mc04IslmYcSalePlanDO::getMc04IsLastestVersion, LatestVersionEnum.CURRENT.getCode())
                .eq(Mc04IslmYcSalePlanDO::getMc04CgtSaleFoPeriodType, PeriodTypeEnum.YC)
                .eq(StrUtil.isNotBlank(ycSalePlanPage.getMc04OrgTypeKind()),
                        Mc04IslmYcSalePlanDO::getMc04OrgTypeKind,
                        ycSalePlanPage.getMc04OrgTypeKind())
                .eq(StrUtil.isBlank(ycSalePlanPage.getMc04OrgTypeKind()),
                        Mc04IslmYcSalePlanDO::getMc04OrgTypeKind,
                        OrgTypeEnum.QG.getKind());
        // 按照创建倒序
        queryWrapper.orderByDesc(Mc04IslmYcSalePlanDO::getCreateTime);

        Page<Mc04IslmYcSalePlanDO> pageDoResult = mc04IslmYcSalePlanService.page(page, queryWrapper);

        if (ObjectUtil.isNull(pageDoResult) || pageDoResult.getTotal() == 0) {
            return pageResult;
        }

        pageResult.setRecords(pageDoResult.getRecords().stream().map(Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter.INSTANCE::converterDoToModel).collect(Collectors.toList()));
        pageResult.setTotal(pageDoResult.getTotal());
        return pageResult;
    }

    @Override
    public Mc04IslmYcSalePlan getLatestVersionYcSalePlanById(String mc04SalePlanId) {
        Mc04IslmYcSalePlanDO mc04IslmYcSalePlanDO = mc04IslmYcSalePlanService.getById(mc04SalePlanId);
        Assert.notNull(mc04IslmYcSalePlanDO, () -> new CustomException("元春计划不存在"));
        // 检查这个元春计划是否是当前版本，避免获取历史版本
        Assert.isTrue(mc04IslmYcSalePlanDO.getMc04IsLastestVersion().equals(LatestVersionEnum.CURRENT.getCode()), () -> new CustomException("元春计划不是最新版本！请联系管理员"));
        // 因为元春计划是由 plan 中的多行组成的，所以主要是通过这一行获取业务年份
        String zaOccurrenceYear = mc04IslmYcSalePlanDO.getZaOccurrenceYear();
        Assert.notNull(zaOccurrenceYear, () -> new CustomException("元春计划业务年份异常！请联系管理员"));
        // 获取当前销售调拨计划版本
        String mc04CgtSalePlanVersion = mc04IslmYcSalePlanDO.getMc04CgtSalePlanVersion();
        Assert.notNull(mc04CgtSalePlanVersion, () -> new CustomException("元春计划版本异常！请联系管理员"));
        return Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter.INSTANCE.converterDoToModel(mc04IslmYcSalePlanDO);
    }

    @Override
    public List<Mc04IslmYcSalePlan> getYcPlanList(Mc04IslmYcSalePlan mc04IslmYcSalePlan) {
        LambdaQueryWrapper<Mc04IslmYcSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotBlank(mc04IslmYcSalePlan.getZaOccurrenceYear())) {
            queryWrapper.eq(Mc04IslmYcSalePlanDO::getZaOccurrenceYear, mc04IslmYcSalePlan.getZaOccurrenceYear());
        }
        if(StringUtils.isNotBlank(mc04IslmYcSalePlan.getMc04CgtSalePlanVersion())) {
            queryWrapper.eq(Mc04IslmYcSalePlanDO::getMc04CgtSalePlanVersion, mc04IslmYcSalePlan.getMc04CgtSalePlanVersion());
        }
        if (StringUtils.isNotBlank(mc04IslmYcSalePlan.getMc04OrgTypeKind())) {
            queryWrapper.eq(Mc04IslmYcSalePlanDO::getMc04OrgTypeKind, mc04IslmYcSalePlan.getMc04OrgTypeKind());
        }
        if (StringUtils.isNotBlank(mc04IslmYcSalePlan.getMc04OrgTypeCode())) {
            queryWrapper.eq(Mc04IslmYcSalePlanDO::getMc04OrgTypeCode, mc04IslmYcSalePlan.getMc04OrgTypeCode());
        }
        if (StringUtils.isNotBlank(mc04IslmYcSalePlan.getMc04SalePlanStatus())) {
            queryWrapper.eq(Mc04IslmYcSalePlanDO::getMc04SalePlanStatus, mc04IslmYcSalePlan.getMc04SalePlanStatus());
        }
        if (StringUtils.isNotBlank(mc04IslmYcSalePlan.getMc04CgtSaleFoPeriodType())){
            queryWrapper.eq(Mc04IslmYcSalePlanDO::getMc04CgtSaleFoPeriodType, mc04IslmYcSalePlan.getMc04CgtSaleFoPeriodType());
        }
        if (StringUtils.isNotBlank(mc04IslmYcSalePlan.getMc04CgtSaleFoPeriodCode())){
            queryWrapper.eq(Mc04IslmYcSalePlanDO::getMc04CgtSaleFoPeriodCode, mc04IslmYcSalePlan.getMc04CgtSaleFoPeriodCode());
        }
        if (StringUtils.isNotBlank(mc04IslmYcSalePlan.getMc04IsLastestVersion())){
            queryWrapper.eq(Mc04IslmYcSalePlanDO::getMc04IsLastestVersion, mc04IslmYcSalePlan.getMc04IsLastestVersion());
        }

        List<Mc04IslmYcSalePlanDO> mc04IslmYcSalePlanDOList = mc04IslmYcSalePlanService.list(queryWrapper);
        return mc04IslmYcSalePlanDOList.stream().map(ycSalePlan -> Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter.INSTANCE.converterDoToModel(ycSalePlan)).collect(Collectors.toList());
    }

    @Override
    public void saveBatch(List<Mc04IslmYcSalePlan> ycSalePlanList) {
        List<Mc04IslmYcSalePlanDO> ycSalePlanDOList = Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter.INSTANCE.converterModelsToDos(ycSalePlanList);
        mc04IslmYcSalePlanService.saveBatch(ycSalePlanDOList);
    }

    @Override
    public void updateBatch(List<Mc04IslmYcSalePlan> ycSalePlanList) {
        List<Mc04IslmYcSalePlanDO> ycSalePlanDOList = Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter.INSTANCE.converterModelsToDos(ycSalePlanList);
        mc04IslmYcSalePlanService.updateBatchById(ycSalePlanDOList);
    }

    @Override
    public void deleteBatchByIds(List<String> existingPlanIds) {
        mc04IslmYcSalePlanService.removeBatchByIds(existingPlanIds);
    }

    @Override
    public List<BusiComDto> getValidCities(Mc04IslmYcSalePlan provincePlan) {
        // 获取原始城市列表
        List<BusiComDto> rawCities = getRawCityList(provincePlan.getMc04OrgTypeCode());

        // 计算并过滤有效城市
        Map<String, Map<String, BigDecimal>> cityRatios = getCityProvinceRatios(
                provincePlan.getMc04OrgTypeCode(),
                provincePlan.getZaOccurrenceYear(),
                rawCities
        );

        return rawCities.stream()
                .filter(city -> hasValidRatio(cityRatios.get(city.getBaComOrgCode())))
                .collect(Collectors.toList());
    }

    @Override
    public Mc04IslmSalePlanModel getYearPlan(String zaOccurrenceYear, String mc04OrgTypeCode) {
        LambdaQueryWrapper<Mc04IslmSalePlanDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Mc04IslmSalePlanDO::getZaOccurrenceYear, zaOccurrenceYear)
                .eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodType, "T01")
                .eq(Mc04IslmSalePlanDO::getMc04OrgTypeCode, mc04OrgTypeCode);
        Mc04IslmSalePlanDO mc04IslmSalePlanDO = mc04IslmSalePlanService.getOne(lambdaQueryWrapper);

        return Mc04IslmSalePlanDoConverter.INSTANCE.converterDoToModel(mc04IslmSalePlanDO);
    }

    @Override
    public List<Mc04IslmSalePlanItemModel> getYearPlanItems(String mc04SalePlanId) {

        List<Mc04IslmSalePlanItemDO> mc04IslmSalePlanItemDOList = mc04IslmSalePlanItemService.list(new LambdaQueryWrapper<Mc04IslmSalePlanItemDO>()
                .eq(Mc04IslmSalePlanItemDO::getMc04SalePlanId, mc04SalePlanId));
        return Mc04IslmSalePlanItemDoConverter.INSTANCE.converterDosToModels(mc04IslmSalePlanItemDOList);
    }

    @Override
    public void save(Mc04IslmYcSalePlan plan) {
        Mc04IslmYcSalePlanDO mc04IslmYcSalePlanDO = Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter.INSTANCE.converterModelToDo(plan);
        mc04IslmYcSalePlanService.save(mc04IslmYcSalePlanDO);
    }

    @Override
    public Mc04IslmYcSalePlan latestByYear4City(String zaOccurrenceYear, String mc04OrgTypeCode) {
        LambdaQueryWrapper<Mc04IslmYcSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmYcSalePlanDO::getZaOccurrenceYear, zaOccurrenceYear)
                .eq(Mc04IslmYcSalePlanDO::getMc04OrgTypeKind, YearPlanOrgTypeEnum.CITY.getKind())
                .eq(Mc04IslmYcSalePlanDO::getMc04OrgTypeCode, mc04OrgTypeCode)
                .eq(Mc04IslmYcSalePlanDO::getMc04CgtSaleFoPeriodType, PeriodTypeEnum.YC.getCode())
                .eq(Mc04IslmYcSalePlanDO::getMc04CgtSaleFoPeriodCode, zaOccurrenceYear)
                //todo 状态临时测试变更为待分解到地市区域
                .eq(Mc04IslmYcSalePlanDO::getMc04SalePlanStatus, SalePlanStatusEnum.CITY_PENDING.getCode())
                .eq(Mc04IslmYcSalePlanDO::getMc04IsLastestVersion, LatestVersionEnum.CURRENT.getCode());
        return Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter.INSTANCE
                .converterDoToModel(mc04IslmYcSalePlanService.getOne(queryWrapper));
    }

    @Override
    public List<Mc04IslmYcSalePlan> listYcByVersion(String zaOccurrenceYear, String mc04CgtSalePlanVersion, String mc04OrgTypeCode) {
        LambdaQueryWrapper<Mc04IslmYcSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmYcSalePlanDO::getZaOccurrenceYear, zaOccurrenceYear)
                .eq(Mc04IslmYcSalePlanDO::getMc04CgtSalePlanVersion, mc04CgtSalePlanVersion)
                .eq(Mc04IslmYcSalePlanDO::getMc04OrgTypeCode, mc04OrgTypeCode);
        return Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter.INSTANCE.converterDosToModels(mc04IslmYcSalePlanService.list(queryWrapper));
    }

    @Override
    public Mc04IslmYcSalePlan getCityMonthPlanByVersion(String zaOccurrenceYear, String mc04CgtSalePlanVersion, String mc04OrgTypeCode, String periodCode) {
        LambdaQueryWrapper<Mc04IslmYcSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmYcSalePlanDO::getZaOccurrenceYear, zaOccurrenceYear)
                .eq(Mc04IslmYcSalePlanDO::getMc04CgtSalePlanVersion, mc04CgtSalePlanVersion)
                .eq(Mc04IslmYcSalePlanDO::getMc04OrgTypeCode, mc04OrgTypeCode)
                .eq(Mc04IslmYcSalePlanDO::getMc04CgtSaleFoPeriodType, YearPlanPeriodTypeEnum.MONTH.getCode())
                .eq(Mc04IslmYcSalePlanDO::getMc04CgtSaleFoPeriodCode, periodCode);
        return Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter.INSTANCE.converterDoToModel(mc04IslmYcSalePlanService.getOne(queryWrapper));
    }

    @Override
    public SnSwEndStkSaleAllotInfoForYear getSnSwEndStkSaleAllotInfoForYear(String zaOccurrenceYear) {
        // 获取所有卷烟规格省内省外上一年年末商业库存
        // 获取上一年，业务年份(zaOccurrenceYear)-2
        String zaLastYear = String.valueOf(Integer.valueOf(zaOccurrenceYear) - 2);
        List<PlanQty> lastYearEndStkQtySnSw =  islmYcSalePlanMapper.getYearEndStkQtySnSw(zaLastYear);
        // 获取当前年所有卷烟规格的省内销售计划和调拨计划
        String zaCurrentYear = String.valueOf(Integer.valueOf(zaOccurrenceYear) - 1);
        // 当前年省内省外所有卷烟规格的销售计划和调拨计划
        List<PlanQty> currentYearSaleAndAllotQtySnSwList = islmSalePlanMapper.getAllProductSaleAndAllotQtyListForYearSnSw(zaCurrentYear);
        // 获取当前年所有卷烟规格的省外销售计划和调拨计划
        SnSwEndStkSaleAllotInfoForYear snSwEndStkSaleAllotInfoForYear = new SnSwEndStkSaleAllotInfoForYear();
        snSwEndStkSaleAllotInfoForYear.buildProductCodeMap(lastYearEndStkQtySnSw, currentYearSaleAndAllotQtySnSwList);
        return snSwEndStkSaleAllotInfoForYear;
    }

    @Override
    public ProvinceEndStkSaleAllotInfoForYear getProvinceEndStkSaleAllotInfoForYear(String zaOccurrenceYear) {
        String zaCurrentYear = String.valueOf(Integer.valueOf(zaOccurrenceYear) - 1);
        // 获取当前年所有卷烟规格的省内销售计划和调拨计划
        List<PlanQty> currentYearEndStkSaleAndAllotQtyProvinceList = islmSalePlanMapper.getAllProductEndStkSaleAndAllotQtyListForYearProvince(zaCurrentYear);

        ProvinceEndStkSaleAllotInfoForYear provinceEndStkSaleAllotInfoForYear = new ProvinceEndStkSaleAllotInfoForYear(currentYearEndStkSaleAndAllotQtyProvinceList);
        return provinceEndStkSaleAllotInfoForYear;
    }

    @Override
    public SnSwLastThreeYearMaxEndStkJanAndFeb getLastThreeYearSnSwJanAndFebMaxEndStkQtyMap(String zaOccurrenceYear) {
        Integer zaCurrentYear = Integer.valueOf(zaOccurrenceYear) - 1;
        // 获取近三年省内省外 每个卷烟 1,2月商业末库存数据
        List<PlanQty> currentYearPlanQtyList = islmYcSalePlanMapper.getSnSwJanAndFebMaxEndStkQty(zaCurrentYear.toString());
        List<PlanQty> lastYearPlanQtyList = islmYcSalePlanMapper.getSnSwJanAndFebMaxEndStkQty(String.valueOf(zaCurrentYear - 1));
        List<PlanQty> lastLastYearPlanQtyList = islmYcSalePlanMapper.getSnSwJanAndFebMaxEndStkQty(String.valueOf(zaCurrentYear - 2));
        // 从近三年的数据中获取每个卷烟省内省外1,2月商业末库存数据的最大值
        // 合并所有年份的数据
        List<PlanQty> allPlanQtyList = new ArrayList<>();
        allPlanQtyList.addAll(currentYearPlanQtyList);
        allPlanQtyList.addAll(lastYearPlanQtyList);
        allPlanQtyList.addAll(lastLastYearPlanQtyList);

        // 按产品代码分组，并获取每个卷烟的库存最大值
        Map<String, List<PlanQty>> groupedByProduct = allPlanQtyList.stream()
                .filter(Objects::nonNull)
                .filter(p -> p.getProductCode() != null)
                .collect(Collectors.groupingBy(PlanQty::getProductCode));

        // 为每个产品代码获取库存最大值
        List<PlanQty> result = new ArrayList<>();
        for (Map.Entry<String, List<PlanQty>> entry : groupedByProduct.entrySet()) {
            List<PlanQty> planQtyList = entry.getValue();
            if (planQtyList.isEmpty()) {
                continue;
            }

            // 创建一个新的planQty对象存储最大值
            PlanQty maxPlanQty = new PlanQty();
            maxPlanQty.setProductCode(entry.getKey());

            // 计算各个库存字段的最大值
            maxPlanQty.setFebEndStockSn(getMaxValue(planQtyList, PlanQty::getFebEndStockSn));
            maxPlanQty.setFebEndStockSw(getMaxValue(planQtyList, PlanQty::getFebEndStockSw));
            maxPlanQty.setJanEndStockSn(getMaxValue(planQtyList, PlanQty::getJanEndStockSn));
            maxPlanQty.setJanEndStockSw(getMaxValue(planQtyList, PlanQty::getJanEndStockSw));

            result.add(maxPlanQty);
        }
        SnSwLastThreeYearMaxEndStkJanAndFeb snSwLastThreeYearMaxEndStkJanAndFeb = new SnSwLastThreeYearMaxEndStkJanAndFeb(result);
        return snSwLastThreeYearMaxEndStkJanAndFeb;
    }


    @Override
    public Map<String, Object> getMonthSaleData(String baComOrgCode, String acCgtCartonCode, String yearMonth) {
        return islmYcSalePlanMapper.getMonthSaleData(baComOrgCode, acCgtCartonCode, yearMonth);
    }

    @Override
    public Map<String, Object> getSpecialProvinceMonthSaleData(String baComOrgCode, String acCgtCartonCode, String yearMonth) {
        return islmYcSalePlanMapper.getSpecialProvinceMonthSaleData(baComOrgCode, acCgtCartonCode, yearMonth);
    }

    @Override
    public Map<String, Object> getNormalProvinceMonthSaleData(String baComOrgCode, String acCgtCartonCode, String yearMonth) {
        return islmYcSalePlanMapper.getNormalProvinceMonthSaleData(baComOrgCode, acCgtCartonCode, yearMonth);
    }

    /**
     * 从planQty列表中获取指定字段的最大值
     * @param planQtyList planQty列表
     * @param getter 字段getter方法
     * @return 最大值
     */
    private BigDecimal getMaxValue(List<PlanQty> planQtyList, Function<PlanQty, BigDecimal> getter) {
        return planQtyList.stream()
                .map(getter)
                .filter(Objects::nonNull)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
    }

    @Override
    public List<BusiComDto> getRawCityList(String provinceCode) {
        GetBusiComListREQ q = new GetBusiComListREQ();
        q.setPcomCodeArr(Collections.singletonList(provinceCode));
        q.setMc04ComOrgLevel("02");
        q.setIcomCode(IcomUtils.getIcomCode());

        KCMultiResponse<BusiComDto> comList = comServiceAPI.getBusiComList(q);
        if (!comList.isSuccess()) {
            throw new RuntimeException("获取城市列表失败");
        }

        return new ArrayList<>(comList.getData().getItems());
    }

    @Override
    public List<Mc04IslmYcSalePlan> queryRelatedPlans(Mc04IslmYcSalePlan provincePlan, List<BusiComDto> cities) {
        // 构建查询条件
        LambdaQueryWrapper<Mc04IslmYcSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmYcSalePlanDO::getMc04PlanSubjectName, provincePlan.getMc04PlanSubjectName())
                .eq(Mc04IslmYcSalePlanDO::getMc04SalePlanStatus, SalePlanStatusEnum.CITY_PENDING.getCode())
                .in(Mc04IslmYcSalePlanDO::getMc04OrgTypeCode,
                        Stream.concat(
                                Stream.of(provincePlan.getMc04OrgTypeCode()),
                                cities.stream().map(BusiComDto::getBaComOrgCode)
                        ).collect(Collectors.toList())
                );

        List<Mc04IslmYcSalePlanDO> list = mc04IslmYcSalePlanService.list(queryWrapper);

        return Mc04IslmYcSalePlanDOToMc04IslmYcSalePlanConverter.INSTANCE.converterDosToModels(list);
    }



    private boolean hasValidRatio(Map<String, BigDecimal> ratios) {
        return ratios != null && ratios.values().stream()
                .anyMatch(ratio -> ratio.compareTo(BigDecimal.ZERO) > 0);
    }
    @Override
    public List<Mc04IslmSalePlanModel> getYearPlans(List<Mc04IslmYcSalePlan> ycPlans, int currentYear, int prevYear) {
        if (CollectionUtil.isEmpty(ycPlans)) {
            return Collections.emptyList();
        }

        Mc04IslmYcSalePlan firstPlan = ycPlans.get(0);
        LambdaQueryWrapper<Mc04IslmSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmSalePlanDO::getMc04PlanSubjectName, firstPlan.getMc04PlanSubjectName())
                .in(Mc04IslmSalePlanDO::getZaOccurrenceYear, String.valueOf(currentYear), String.valueOf(prevYear))
                .in(Mc04IslmSalePlanDO::getMc04OrgTypeCode,
                        ycPlans.stream().map(Mc04IslmYcSalePlan::getMc04OrgTypeCode).collect(Collectors.toList()));
        List<Mc04IslmSalePlanDO> list = mc04IslmSalePlanService.list(queryWrapper);

        return Mc04IslmSalePlanDoConverter.INSTANCE.converterDosToModels(list);
    }

    @Override
    public void updateProvinceAndCityPlansStatus(String provinceCode, String zaOccurrenceYear, String version, String status, List<String> cityCodes) {
        // 构建更新条件
        LambdaQueryWrapper<Mc04IslmYcSalePlanDO> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(Mc04IslmYcSalePlanDO::getZaOccurrenceYear, zaOccurrenceYear)
                .eq(Mc04IslmYcSalePlanDO::getMc04CgtSalePlanVersion, version)
                .eq(Mc04IslmYcSalePlanDO::getMc04IsLastestVersion, LatestVersionEnum.CURRENT.getCode())
                .and(wrapper -> wrapper
                        .eq(Mc04IslmYcSalePlanDO::getMc04OrgTypeCode, provinceCode)
                        .or()
                        .in(CollectionUtil.isNotEmpty(cityCodes), Mc04IslmYcSalePlanDO::getMc04OrgTypeCode, cityCodes));

        // 构建更新实体
        Mc04IslmYcSalePlanDO updateEntity = new Mc04IslmYcSalePlanDO();
        updateEntity.setMc04SalePlanStatus(status);

        // 执行批量更新
        mc04IslmYcSalePlanService.update(updateEntity, updateWrapper);
    }

    @Override
    public List<Mc04IslmSalePlanItemModel> getYearPlanItems(List<Mc04IslmSalePlanModel> yearPlans) {
        if (CollectionUtil.isEmpty(yearPlans)) {
            return Collections.emptyList();
        }

        List<String> planIds = yearPlans.stream()
                .map(Mc04IslmSalePlanModel::getMc04SalePlanId)
                .collect(Collectors.toList());


        List<Mc04IslmSalePlanItemDO> list = mc04IslmSalePlanItemService.list(
                new LambdaQueryWrapper<Mc04IslmSalePlanItemDO>()
                        .in(Mc04IslmSalePlanItemDO::getMc04SalePlanId, planIds)
        );
        return Mc04IslmSalePlanItemDoConverter.INSTANCE.converterDosToModels(list);
    }


    // 比值查询方法
    @Override
    public Map<String, Map<String, BigDecimal>> getCityProvinceRatios(String provinceCode, String year, List<BusiComDto> cityList) {
        Map<String, Map<String, BigDecimal>> ratios = new HashMap<>();

        // 如果城市列表为空，直接返回空结果
        if (CollectionUtil.isEmpty(cityList)) {
            return ratios;
        }

        // 1. 批量获取所有城市的需求预测主表数据
        List<String> cityCodes = cityList.stream().map(BusiComDto::getBaComOrgCode).collect(Collectors.toList());
        Map<String, Mc04IslmDemandFoDO> cityDemandMap = mc04IslmDemandFoService.lambdaQuery()
                .eq(Mc04IslmDemandFoDO::getZaOccurrenceYear, year)
                .eq(Mc04IslmDemandFoDO::getMc04PlanSubjectType, "2")
                .in(Mc04IslmDemandFoDO::getBaComOrgCode, cityCodes)
                .list()
                .stream()
                .collect(Collectors.toMap(Mc04IslmDemandFoDO::getBaComOrgCode, Function.identity()));

        // 2. 获取省的需求预测主表数据
        Mc04IslmDemandFoDO provinceDemand = mc04IslmDemandFoService.lambdaQuery()
                .eq(Mc04IslmDemandFoDO::getBaComOrgCode, provinceCode)
                .eq(Mc04IslmDemandFoDO::getZaOccurrenceYear, year)
                .eq(Mc04IslmDemandFoDO::getMc04PlanSubjectType, YearPlanSubjectTypeEnum.SUBJECT_TYPE_REVISE.getCode())
                .one();

        if (provinceDemand == null) {
            return ratios;
        }

        // 3. 批量获取所有城市和省的明细数据
        List<String> allDemandFoIds = new ArrayList<>();
        allDemandFoIds.add(provinceDemand.getMc04DemandFoId());
        allDemandFoIds.addAll(cityDemandMap.values().stream()
                .map(Mc04IslmDemandFoDO::getMc04DemandFoId)
                .collect(Collectors.toList()));

        List<Mc04IslmDemandFoItemDO> allItems = mc04IslmDemandFoItemService.lambdaQuery()
                .in(Mc04IslmDemandFoItemDO::getMc04DemandFoId, allDemandFoIds)
                .eq(Mc04IslmDemandFoItemDO::getMc04CgtSaleFoPeriodType, "T04")
                .in(Mc04IslmDemandFoItemDO::getMc04CgtSaleFoPeriodCode, "01", "02")
                .list();

        // 4. 按需求预测ID分组
        Map<String, List<Mc04IslmDemandFoItemDO>> itemsByDemandId = allItems.stream()
                .collect(Collectors.groupingBy(Mc04IslmDemandFoItemDO::getMc04DemandFoId));

        // 5. 处理省的数据
        List<Mc04IslmDemandFoItemDO> provinceItems = itemsByDemandId.getOrDefault(provinceDemand.getMc04DemandFoId(), Collections.emptyList());
        Map<String, Map<String, BigDecimal>> provinceDemandByMonthAndCgt = provinceItems.stream()
                .collect(Collectors.groupingBy(
                        Mc04IslmDemandFoItemDO::getMc04CgtSaleFoPeriodCode,
                        Collectors.groupingBy(
                                Mc04IslmDemandFoItemDO::getAcCgtCartonCode,
                                Collectors.reducing(
                                        BigDecimal.ZERO,
                                        Mc04IslmDemandFoItemDO::getMc04DemandFoConfirmQty,
                                        BigDecimal::add
                                )
                        )
                ));

        // 6. 处理城市数据
        for (BusiComDto city : cityList) {
            String cityCode = city.getBaComOrgCode();
            Mc04IslmDemandFoDO cityDemand = cityDemandMap.get(cityCode);
            if (cityDemand == null) {
                continue;
            }

            List<Mc04IslmDemandFoItemDO> cityItems = itemsByDemandId.getOrDefault(cityDemand.getMc04DemandFoId(), Collections.emptyList());
            Map<String, BigDecimal> cityRatios = new HashMap<>();
            ratios.put(cityCode, cityRatios);

            Map<String, Map<String, BigDecimal>> cityDemandByMonthAndCgt = cityItems.stream()
                    .collect(Collectors.groupingBy(
                            Mc04IslmDemandFoItemDO::getMc04CgtSaleFoPeriodCode,
                            Collectors.groupingBy(
                                    Mc04IslmDemandFoItemDO::getAcCgtCartonCode,
                                    Collectors.reducing(
                                            BigDecimal.ZERO,
                                            Mc04IslmDemandFoItemDO::getMc04DemandFoConfirmQty,
                                            BigDecimal::add
                                    )
                            )
                    ));

            cityDemandByMonthAndCgt.forEach((month, cgtMap) -> {
                cgtMap.forEach((cgtCode, cityTotal) -> {
                    BigDecimal provinceTotal = provinceDemandByMonthAndCgt.getOrDefault(month, Collections.emptyMap())
                            .getOrDefault(cgtCode, BigDecimal.ZERO);
                    if (provinceTotal.compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal ratio = cityTotal.divide(provinceTotal, 4, BigDecimal.ROUND_HALF_UP);
                        if (ratio.compareTo(BigDecimal.ZERO) != 0) {
                            cityRatios.put(month + "_" + cgtCode, ratio);
                        }
                    }
                });
            });
        }

        return ratios;
    }
}

