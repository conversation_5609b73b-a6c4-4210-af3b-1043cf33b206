/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.common;

import com.tobacco.app.isale.domain.model.com.BusiCom;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/07/30 17:54
 * @description : 转换
 */
@Mapper
public interface BusiComDtoToBusiComConverter extends StructureBaseConverter<BusiComDto, BusiCom> {
    BusiComDtoToBusiComConverter INSTANCE =
            Mappers.getMapper(BusiComDtoToBusiComConverter.class);
}

