/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.parm;

import com.tobacco.app.isale.domain.model.order.dist.parm.DistParmStatus;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isalecenter.client.dto.order.dist.parm.DistParmStatusDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create_time : 2025/06/03 19:18
 * @description : 配货参数状态数据转化
 */
@Mapper
public interface DistParmStatusToDistParmStatusDTOConverter extends StructureBaseConverter<DistParmStatusDTO, DistParmStatus> {

    DistParmStatusToDistParmStatusDTOConverter INSTANCE =
            Mappers.getMapper(DistParmStatusToDistParmStatusDTOConverter.class);

}
