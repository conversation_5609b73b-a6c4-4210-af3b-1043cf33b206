package com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan;

import org.apache.ibatis.annotations.MapKey;
import com.tobacco.app.isale.domain.model.plan.PlanQty;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface IslmDemandFoMapper {
    /**
     * 获取每个卷烟规格的1,2月全国实际销量
     * @param bamCode 业务组织代码
     * @param month 1月 01  2月 02
     */
    @MapKey("cartonCode")
    List<Map<String, Object>> allSalesByMonthAndBamCode(@Param("bamCode") String bamCode,
                                                          @Param("month") String month);
    /**
     * 获取每个卷烟规格省内或省外的1,2月区域确认需求预测量
     * @param year 业务年份，今年是2025，业务年份就是2026
     * @param month 1月 01  2月 02
     * @param orgIsImported 省内 0 省外 1
     */
    List<PlanQty> getDemandFoConfirmQty(String year, String month, String orgIsImported);

    /**
     * 获取每个卷烟规格的年度计划销售量
     * @param zaOccurrenceYear 业务年份
     * @param planSubjectType 主题类型
     */
    @MapKey("cartonCode")
    List<Map<String, Object>> selectCartonCodeAndAdjustedQtyByYearAndSubjectType(
            @Param("zaOccurrenceYear") String zaOccurrenceYear,
            @Param("planSubjectType") String planSubjectType
    );
}
