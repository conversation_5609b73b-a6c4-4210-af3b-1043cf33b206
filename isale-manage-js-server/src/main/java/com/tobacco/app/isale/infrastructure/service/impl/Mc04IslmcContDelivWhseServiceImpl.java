/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcContDelivWhseMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContDelivWhseService;
import org.springframework.stereotype.Service;


/**
 * @description : 服务实现类
 *
 * <AUTHOR> wangluhao01
 * @since : 2025-05-23
 * @email : <EMAIL>
 * @create_time : 2025-05-23
 */
@Service
public class Mc04IslmcContDelivWhseServiceImpl extends ServiceImpl<Mc04IslmcContDelivWhseMapper, Mc04IslmcContDelivWhseDO> implements Mc04IslmcContDelivWhseService {

}