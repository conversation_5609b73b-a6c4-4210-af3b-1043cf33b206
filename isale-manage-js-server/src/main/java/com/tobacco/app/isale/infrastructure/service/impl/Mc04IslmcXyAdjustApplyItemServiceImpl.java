/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcXyAdjustApplyItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcXyAdjustApplyItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcXyAdjustApplyItemService;
import org.springframework.stereotype.Service;

/**
 * @Description: 服务实现类
 *
 * @Author: jinfuli
 * @Since: 2025-05-12
 * @Email: <EMAIL>
 * @Create: 2025-05-12
 */
@Service
public class Mc04IslmcXyAdjustApplyItemServiceImpl extends ServiceImpl<Mc04IslmcXyAdjustApplyItemMapper, Mc04IslmcXyAdjustApplyItemDO> implements Mc04IslmcXyAdjustApplyItemService {

}