package com.tobacco.app.isale.infrastructure.repository.basic;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.constant.CommonConstants;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.enums.common.AcOneLevelClassTypeCodeEnum;
import com.tobacco.app.isale.domain.enums.common.ProdTradeTypeCodeEnum;
import com.tobacco.app.isale.domain.model.basic.IslmcComItem;
import com.tobacco.app.isale.domain.model.basic.ManageComItem;
import com.tobacco.app.isale.domain.repository.basic.ManageComItemRepository;
import com.tobacco.app.isale.infrastructure.converter.basic.Mc04IslmcComItemDOToIslmcComItemConverter;
import com.tobacco.app.isale.infrastructure.converter.basic.Mc04IslmcComItemDOToManageComItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcComItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcComItemService;
import com.tobacco.app.isale.tools.utils.CommodityUtil;
import com.tobacco.app.isale.tools.utils.CustUtil;
import com.tobacco.app.isalecenter.common.constants.SaleCenterConstants;
import com.tobacco.sc.icommodity.dto.common.constant.dto.item.IccItemDetailDTO;
import com.tobacco.sc.icommodity.dto.common.constant.dto.product.IccProductDetailDTO;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: wangluhao01
 * @Since: 2025-05-29
 */
@Slf4j
@Component("ISaleManageManageComItemDORepository")
public class ManageComItemRepositoryImpl implements ManageComItemRepository {

    private Mc04IslmcComItemService comItemService;


    @Autowired
    public void setComItemService(Mc04IslmcComItemService comItemService) {
        this.comItemService = comItemService;
    }


    /**
     * @param cgtType   业务类型
     * @param comIdList 协议单位编码
     * @return List<IndDemandFoComItem>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-22 16:32:21
     * @description : 获取地市卷烟
     */
    @Override
    public List<ManageComItem> getComItemList(String cgtType, List<String> comIdList, String isMergeCoreData) {
        String acOneLevelClassTypeCode = getAcOneLevelClassTypeCode(cgtType);

        // 根据公司ID列表查询有效的公司商品项
        List<Mc04IslmcComItemDO> comItemList = comItemService.lambdaQuery()
                .in(CollUtil.isNotEmpty(comIdList), Mc04IslmcComItemDO::getBaComOrgCode, comIdList)
                .eq(Mc04IslmcComItemDO::getIsUse, SaleCenterConstants.YES)
                .eq(Mc04IslmcComItemDO::getAcOneLevelClassTypeCode, acOneLevelClassTypeCode)
                .list();
        // 如果未找到公司商品项，则返回空列表
        if (CollUtil.isEmpty(comItemList)) {
            return Collections.emptyList();
        }
        List<ManageComItem> manageComItems =
                Mc04IslmcComItemDOToManageComItemConverter.INSTANCE.converterDosToModels(comItemList);
        if (CommonConstants.YES.equals(isMergeCoreData)) {
            //获取地市信息
            Map<String, BusiComDto> comCodeMap = CustUtil.getBusiComDtoMap(comIdList);

            List<String> cartonCodeList = new ArrayList<>();
            List<String> acTwoLevelCigCodeList = new ArrayList<>();
            manageComItems.forEach(manageComItem -> {
                cartonCodeList.add(manageComItem.getAcCgtCartonCode());
                acTwoLevelCigCodeList.add(manageComItem.getAcTwoLevelCigCode());
            });
            // 获取商品详细信息
            Map<String, IccProductDetailDTO> productCodeMap = CommodityUtil.getIccProductDetailDtoMap(cartonCodeList);
            // 获取二级牌号信息
            Map<String, IccItemDetailDTO> itemCodeMap = CommodityUtil.getIccItemDetailDtoMap(acTwoLevelCigCodeList);

            manageComItems.forEach(manageComItem -> {
                BusiComDto busiComDto = comCodeMap.get(manageComItem.getBaComOrgCode());
                Assert.notNull(busiComDto, () -> new CustomException("中心未获取到" +
                        manageComItem.getBaComOrgCode() + "的协议单位信息"));
                manageComItem.setBaComOrgName(busiComDto.getBaComOrgName());
                manageComItem.setMc04ComOrgShortName(busiComDto.getMc04ComOrgShortName());
                IccProductDetailDTO productDetailDTO = productCodeMap.get(manageComItem.getAcCgtCartonCode());
                Assert.notNull(productDetailDTO, () -> new CustomException("中心未获取到" +
                        manageComItem.getAcCgtCartonCode() + "的卷烟信息"));
                manageComItem.setAcCgtName(productDetailDTO.getProductName());
                IccItemDetailDTO itemDetailDTO = itemCodeMap.get(manageComItem.getAcTwoLevelCigCode());
                Assert.notNull(itemDetailDTO, () -> new CustomException("中心未获取到" +
                        manageComItem.getAcTwoLevelCigCode() + "的二级牌号信息"));
                manageComItem.setAcTwoLevelCigName(itemDetailDTO.getAcTwoLevelCigName());
            });
        }
        return manageComItems;
    }


    private static String getAcOneLevelClassTypeCode(String cgtType) {
        String acOneLevelClassTypeCode;
        if (cgtType.equals(ProdTradeTypeCodeEnum.PROD_TRADE_TYPE_CODE_0.getCode())) {
            acOneLevelClassTypeCode = AcOneLevelClassTypeCodeEnum.AC_ONE_LEVEL_CLASS_TYPE_CODE_01.getCode();
        } else if (cgtType.equals(ProdTradeTypeCodeEnum.PROD_TRADE_TYPE_CODE_1.getCode())) {
            acOneLevelClassTypeCode = AcOneLevelClassTypeCodeEnum.AC_ONE_LEVEL_CLASS_TYPE_CODE_02.getCode();
        } else {
            throw new CustomException("业务类型错误");
        }
        return acOneLevelClassTypeCode;
    }

    @Override
    public List<IslmcComItem> list(String baComOrgCode, String acOneLevelClassTypeCode) {
        QueryWrapper<Mc04IslmcComItemDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(StrUtil.isNotBlank(baComOrgCode), Mc04IslmcComItemDO::getBaComOrgCode, baComOrgCode)
                .eq(StrUtil.isNotBlank(acOneLevelClassTypeCode), Mc04IslmcComItemDO::getAcOneLevelClassTypeCode, acOneLevelClassTypeCode)
                .eq(Mc04IslmcComItemDO::getIsUse, SaleCenterConstants.YES)
                .eq(Mc04IslmcComItemDO::getIcomCode, IcomUtils.getIcomCode());
        List<Mc04IslmcComItemDO> list = comItemService.list(wrapper);
        return Mc04IslmcComItemDOToIslmcComItemConverter.INSTANCE.converterDosToModels(list);
    }


    @Override
    public List<IslmcComItem> byCodeListGetItemList(List<String> baComOrgCodeList,String typeCode) {
        QueryWrapper<Mc04IslmcComItemDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .in(CollectionUtil.isEmpty(baComOrgCodeList), Mc04IslmcComItemDO::getBaComOrgCode, baComOrgCodeList)
                .eq(Mc04IslmcComItemDO::getAcOneLevelClassTypeCode, typeCode)
                .eq(Mc04IslmcComItemDO::getIcomCode, IcomUtils.getIcomCode());
        List<Mc04IslmcComItemDO> list = comItemService.list(wrapper);
        return Mc04IslmcComItemDOToIslmcComItemConverter.INSTANCE.converterDosToModels(list);
    }



    @Override
    public Boolean save(List<IslmcComItem> islmcComItemList) {
        if (islmcComItemList == null || islmcComItemList.isEmpty()) {
            return true;
        }
        // 获取商业公司编码
        String baComOrgCode = islmcComItemList.get(0).getBaComOrgCode();
        if (StrUtil.isBlank(baComOrgCode)) {
            return false;
        }
        List<Mc04IslmcComItemDO> mc04IslmcComItemDOList = Mc04IslmcComItemDOToIslmcComItemConverter.INSTANCE
                .converterModelsToDos(islmcComItemList);
        List<String> acTwoLevelCigCodeList = mc04IslmcComItemDOList.stream().map(Mc04IslmcComItemDO::getAcTwoLevelCigCode).collect(Collectors.toList());
        QueryWrapper<Mc04IslmcComItemDO> listWrapper = new QueryWrapper<>();
        listWrapper.lambda()
                .eq(Mc04IslmcComItemDO::getBaComOrgCode, baComOrgCode)
                .in(Mc04IslmcComItemDO::getAcTwoLevelCigCode, acTwoLevelCigCodeList)
                .eq(Mc04IslmcComItemDO::getIcomCode, IcomUtils.getIcomCode());
        List<Mc04IslmcComItemDO> islmcComItemExitList = comItemService.list(listWrapper);
        // 获取表中已存在的单地市二级牌号编码
        Set<String> acTwoLevelCigCodeExitSet = islmcComItemExitList.stream().map(Mc04IslmcComItemDO::getAcTwoLevelCigCode).collect(Collectors.toSet());
        boolean isUpdate;
        if (!acTwoLevelCigCodeExitSet.isEmpty()) {
            QueryWrapper<Mc04IslmcComItemDO> updateWrapper = new QueryWrapper<>();
            updateWrapper.lambda()
                    .eq(Mc04IslmcComItemDO::getBaComOrgCode, baComOrgCode)
                    .in(Mc04IslmcComItemDO::getAcTwoLevelCigCode, acTwoLevelCigCodeExitSet)
                    .eq(Mc04IslmcComItemDO::getIcomCode, IcomUtils.getIcomCode());
            Mc04IslmcComItemDO mc04IslmcComItemDO = new Mc04IslmcComItemDO();
            mc04IslmcComItemDO.setIsUse(SaleCenterConstants.YES);
            // 批量更新
            isUpdate = comItemService.update(mc04IslmcComItemDO, updateWrapper);
        } else {
            isUpdate = true;
        }
        boolean isSave = false;
        if (isUpdate) {
            // 分离出需要新增的数据
            List<Mc04IslmcComItemDO> saveIslmcComItemList = mc04IslmcComItemDOList.stream()
                    .filter(item -> !acTwoLevelCigCodeExitSet.contains(item.getAcTwoLevelCigCode()))
                    .collect(Collectors.toList());
            if (!saveIslmcComItemList.isEmpty()) {
                // 批量新增
                isSave = comItemService.saveBatch(saveIslmcComItemList);
            } else {
                isSave = true;
            }
        }
        return isUpdate && isSave;
    }

    @Override
    public Boolean remove(String baComOrgCode, String acTwoLevelCigCode) {
        QueryWrapper<Mc04IslmcComItemDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(StrUtil.isNotBlank(baComOrgCode), Mc04IslmcComItemDO::getBaComOrgCode, baComOrgCode)
                .eq(StrUtil.isNotBlank(acTwoLevelCigCode), Mc04IslmcComItemDO::getAcTwoLevelCigCode, acTwoLevelCigCode)
                .eq(Mc04IslmcComItemDO::getIcomCode, IcomUtils.getIcomCode());
        Mc04IslmcComItemDO mc04IslmcComItemDO = new Mc04IslmcComItemDO();
        mc04IslmcComItemDO.setIsUse(SaleCenterConstants.NO);
        return comItemService.update(mc04IslmcComItemDO, wrapper);
    }
}