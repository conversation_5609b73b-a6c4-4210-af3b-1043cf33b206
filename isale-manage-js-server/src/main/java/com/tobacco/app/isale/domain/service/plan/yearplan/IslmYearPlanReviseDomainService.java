/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.domain.service.plan.yearplan;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.inspur.ind.base.BaseFactor;
import com.inspur.ind.base.CustomException;
import com.tobacco.app.isale.app.converter.plan.yearplan.formulate.*;
import com.tobacco.app.isale.domain.enums.plan.ycplan.LatestVersionEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.SalePlanStatusEnum;
import com.tobacco.app.isale.domain.enums.plan.yearplan.*;
import com.tobacco.app.isale.domain.model.plan.yearplan.*;
import com.tobacco.app.isale.domain.model.plan.yearplan.history.IslmHistoryFormulateData;
import com.tobacco.app.isale.domain.model.plan.yearplan.history.IslmHistoryFormulateTypeData;
import com.tobacco.app.isale.domain.repository.plan.IslmSaleFoRepository;
import com.tobacco.app.isale.domain.repository.plan.yearplan.*;
import com.tobacco.app.isale.dto.plan.yearplan.formulate.*;
import com.tobacco.app.isale.req.plan.yearplan.IslmSalePlan4Revise4SnwReq;
import com.tobacco.app.isale.req.plan.yearplan.IslmSalePlan4ReviseReq;
import com.tobacco.app.isale.req.plan.yearplan.IslmSalePlanItem4Revise4SnwReq;
import com.tobacco.app.isale.tools.utils.BigDecimalUtil;
import com.tobacco.sc.icommodity.dto.common.constant.client.api.product.ProductServiceApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 年度计划编制领域服务
 *
 * @Author: longxi
 * @Since: 2025-08-17
 */
@Component("ISaleManageIslmYearPlanReviseDS")
public class IslmYearPlanReviseDomainService {

    private ProductServiceApi productServiceApi;
    private IslmSalePlanRepository salePlanRepository;
    private IslmSalePlanItemRepository salePlanItemRepository;
    private IslmSaleInitRepository saleInitRepository;
    private IslmSaleInitItemRepository saleInitItemRepository;
    private IslmStockInitRepository stockInitRepository;
    private IslmStockInitItemRepository stockInitItemRepository;
    private IslmBrandPlanRepository brandPlanRepository;
    private IslmBrandPlanItemRepository brandPlanItemRepository;
    private IslmSaleFoRepository saleFoRepository;
    private IslmSaleFoItemRepository saleFoItemRepository;
    private IslmDemandFoRepository demandFoRepository;
    private IslmDemandFoItemRepository demandFoItemRepository;
    private IslmYearPlanGeneralDomainService yearPlanGeneralDomainService;
    private IslmYearPlanGeneralTransactionService yearPlanGeneralTransactionService;

    @Autowired
    public void setAllDependencies(
            ProductServiceApi productServiceApi,
            IslmSalePlanRepository salePlanRepository,
            IslmSalePlanItemRepository salePlanItemRepository,
            IslmSaleInitRepository saleInitRepository,
            IslmSaleInitItemRepository saleInitItemRepository,
            IslmStockInitRepository stockInitRepository,
            IslmStockInitItemRepository stockInitItemRepository,
            IslmBrandPlanRepository brandPlanRepository,
            IslmBrandPlanItemRepository brandPlanItemRepository,
            IslmSaleFoRepository saleFoRepository,
            IslmDemandFoRepository demandFoRepository,
            IslmDemandFoItemRepository demandFoItemRepository,
            IslmSaleFoItemRepository saleFoItemRepository,
            IslmYearPlanGeneralDomainService yearPlanGeneralDomainService,
            IslmYearPlanGeneralTransactionService yearPlanGeneralTransactionService) {
        this.productServiceApi = productServiceApi;
        this.salePlanRepository = salePlanRepository;
        this.salePlanItemRepository = salePlanItemRepository;
        this.saleInitRepository = saleInitRepository;
        this.saleInitItemRepository = saleInitItemRepository;
        this.stockInitRepository = stockInitRepository;
        this.stockInitItemRepository = stockInitItemRepository;
        this.brandPlanRepository = brandPlanRepository;
        this.brandPlanItemRepository = brandPlanItemRepository;
        this.saleFoRepository = saleFoRepository;
        this.saleFoItemRepository = saleFoItemRepository;
        this.demandFoRepository = demandFoRepository;
        this.demandFoItemRepository = demandFoItemRepository;
        this.yearPlanGeneralDomainService = yearPlanGeneralDomainService;
        this.yearPlanGeneralTransactionService = yearPlanGeneralTransactionService;
    }

    @ReturnValue(desc = "修正明细查询结果")
    @Method(name = "修正明细查询")
    public IslmSalePlan4FormulateDto detailRevise(@Parameter(name = "编制计划ID", required = true) String mc04SalePlanId) {
        return yearPlanGeneralDomainService.detailFormulate(mc04SalePlanId);
    }

    private void initAllot(@Parameter(name = "编制计划ID", required = true) String mc04SalePlanId,
                             @Parameter(name = "组织分类类别", required = true) String mc04OrgTypeKind) {
        Mc04IslmSalePlanModel gnSalePlanModel = salePlanRepository.getById(mc04SalePlanId);
        String zaOccurrenceYear = gnSalePlanModel.getZaOccurrenceYear();
        String salePlanVersion = gnSalePlanModel.getMc04CgtSalePlanVersion();
        Mc04IslmSalePlanModel snwSalePlanModel;
        YearPlanOrgTypeEnum orgTypeEnum;
        if (YearPlanOrgTypeEnum.SN.getKind().equals(mc04OrgTypeKind)) {
            snwSalePlanModel = yearPlanGeneralDomainService.getSnSalePlanModel(gnSalePlanModel);
            orgTypeEnum = YearPlanOrgTypeEnum.SN;
        } else if (YearPlanOrgTypeEnum.SW.getKind().equals(mc04OrgTypeKind)) {
            snwSalePlanModel = yearPlanGeneralDomainService.getSwSalePlanModel(gnSalePlanModel);
            orgTypeEnum = YearPlanOrgTypeEnum.SW;
        } else {
            throw new CustomException("计划组织分类类型异常！请联系管理员");
        }

        // 统一构建数据
        SnwAllotData allotData = buildSnwAllotData(snwSalePlanModel, zaOccurrenceYear, salePlanVersion, mc04OrgTypeKind);

        // 执行计算
        performAllotCalculation(allotData, orgTypeEnum, zaOccurrenceYear);

        // 保存新创建的数据
        List<Mc04IslmSalePlanItemModel> batchItemModelList = allotData.getAllPeriodItemModelList();
        yearPlanGeneralTransactionService.saveBatchSalePlanItem(batchItemModelList);
    }

    @ReturnValue(desc = "省内外计划分解计算明细结果")
    @Method(name = "省内外计划分解计算明细")
    public Boolean computeResetAllot(@Parameter(name = "编制计划ID", required = true) String mc04SalePlanId,
                                     @Parameter(name = "组织分类类别", required = true) String mc04OrgTypeKind) {
        Mc04IslmSalePlanModel gnSalePlanModel = salePlanRepository.getById(mc04SalePlanId);
        String zaOccurrenceYear = gnSalePlanModel.getZaOccurrenceYear();
        String salePlanVersion = gnSalePlanModel.getMc04CgtSalePlanVersion();
        String salePlanStatus = gnSalePlanModel.getMc04SalePlanStatus();
        Mc04IslmSalePlanModel snwSalePlanModel;
        YearPlanOrgTypeEnum orgTypeEnum = null;
        if (YearPlanOrgTypeEnum.SN.getKind().equals(mc04OrgTypeKind)) {
            snwSalePlanModel = yearPlanGeneralDomainService.getSnSalePlanModel(gnSalePlanModel);
            orgTypeEnum = YearPlanOrgTypeEnum.SN;
        } else if (YearPlanOrgTypeEnum.SW.getKind().equals(mc04OrgTypeKind)) {
            snwSalePlanModel = yearPlanGeneralDomainService.getSwSalePlanModel(gnSalePlanModel);
            orgTypeEnum = YearPlanOrgTypeEnum.SW;
        } else {
            throw new CustomException("计划组织分类类型异常！请联系管理员");
        }

        // 统一构建数据（用于计算）
        SnwAllotData allotData = buildSnwAllotDataForCompute(snwSalePlanModel, zaOccurrenceYear,
                salePlanVersion, salePlanStatus, mc04OrgTypeKind);

        // 执行计算
        performAllotCalculation(allotData, orgTypeEnum, zaOccurrenceYear);

        // 更新现有数据
        return salePlanItemRepository.updateBatchById(allotData.getAllPeriodItemModelList());
    }

    // 提取公共计算方法
    private void performAllotCalculation(SnwAllotData allotData, YearPlanOrgTypeEnum orgTypeEnum,
                                         String zaOccurrenceYear) {
        // 计算分配数据（公共逻辑）
        for (Mc04IslmSalePlanItemModel salePlanItemModel : allotData.snwItemModelList) {
            initAllotData(salePlanItemModel, orgTypeEnum, zaOccurrenceYear, allotData.stockHistoryData,
                    allotData.h1SalePlanItemMap, allotData.h2SalePlanItemMap,
                    allotData.q1SalePlanItemMap, allotData.q2SalePlanItemMap,
                    allotData.q3SalePlanItemMap, allotData.q4SalePlanItemMap);
        }
    }

    /**
     * 省内外计划分解数据封装类
     */
    private static class SnwAllotData {
        // 计划模型
        Mc04IslmSalePlanModel h1SalePlanModel;
        Mc04IslmSalePlanModel h2SalePlanModel;
        Mc04IslmSalePlanModel q1SalePlanModel;
        Mc04IslmSalePlanModel q2SalePlanModel;
        Mc04IslmSalePlanModel q3SalePlanModel;
        Mc04IslmSalePlanModel q4SalePlanModel;

        // 计划项列表
        List<Mc04IslmSalePlanItemModel> snwItemModelList;
        List<Mc04IslmSalePlanItemModel> h1ItemModelList;
        List<Mc04IslmSalePlanItemModel> h2ItemModelList;
        List<Mc04IslmSalePlanItemModel> q1ItemModelList;
        List<Mc04IslmSalePlanItemModel> q2ItemModelList;
        List<Mc04IslmSalePlanItemModel> q3ItemModelList;
        List<Mc04IslmSalePlanItemModel> q4ItemModelList;

        // 计划项映射
        Map<String, Mc04IslmSalePlanItemModel> h1SalePlanItemMap;
        Map<String, Mc04IslmSalePlanItemModel> h2SalePlanItemMap;
        Map<String, Mc04IslmSalePlanItemModel> q1SalePlanItemMap;
        Map<String, Mc04IslmSalePlanItemModel> q2SalePlanItemMap;
        Map<String, Mc04IslmSalePlanItemModel> q3SalePlanItemMap;
        Map<String, Mc04IslmSalePlanItemModel> q4SalePlanItemMap;

        // 历史数据
        IslmHistoryFormulateTypeData stockHistoryData;

        /**
         * 构建计划项映射
         */
        public void buildItemMaps() {
            h1SalePlanItemMap = h1ItemModelList.stream()
                    .collect(Collectors.toMap(Mc04IslmSalePlanItemModel::getAcCgtCartonCode, item -> item));
            h2SalePlanItemMap = h2ItemModelList.stream()
                    .collect(Collectors.toMap(Mc04IslmSalePlanItemModel::getAcCgtCartonCode, item -> item));
            q1SalePlanItemMap = q1ItemModelList.stream()
                    .collect(Collectors.toMap(Mc04IslmSalePlanItemModel::getAcCgtCartonCode, item -> item));
            q2SalePlanItemMap = q2ItemModelList.stream()
                    .collect(Collectors.toMap(Mc04IslmSalePlanItemModel::getAcCgtCartonCode, item -> item));
            q3SalePlanItemMap = q3ItemModelList.stream()
                    .collect(Collectors.toMap(Mc04IslmSalePlanItemModel::getAcCgtCartonCode, item -> item));
            q4SalePlanItemMap = q4ItemModelList.stream()
                    .collect(Collectors.toMap(Mc04IslmSalePlanItemModel::getAcCgtCartonCode, item -> item));
        }

        /**
         * 获取所有计划项列表（除snwItemModelList外）
         * @return 所有计划项列表
         */
        public List<Mc04IslmSalePlanItemModel> getAllPeriodItemModelList() {
            List<Mc04IslmSalePlanItemModel> allItemModelList = new ArrayList<>();
            allItemModelList.addAll(h1ItemModelList);
            allItemModelList.addAll(h2ItemModelList);
            allItemModelList.addAll(q1ItemModelList);
            allItemModelList.addAll(q2ItemModelList);
            allItemModelList.addAll(q3ItemModelList);
            allItemModelList.addAll(q4ItemModelList);
            return allItemModelList;
        }
    }

    /**
     * 统一构建省内外计划分解数据
     *
     * @param snwSalePlanModel 省内外销售计划模型
     * @param zaOccurrenceYear 编制年份
     * @param salePlanVersion 计划版本
     * @param mc04OrgTypeKind 组织类型
     * @return 省内外计划分解数据
     */
    private SnwAllotData buildSnwAllotData(Mc04IslmSalePlanModel snwSalePlanModel, String zaOccurrenceYear,
                                           String salePlanVersion, String mc04OrgTypeKind) {
        SnwAllotData allotData = new SnwAllotData();

        // 获取计划模型映射
        Map<String, Mc04IslmSalePlanModel> salePlanMap = getSalePlanMap(zaOccurrenceYear, salePlanVersion, mc04OrgTypeKind);

        // 设置各期间计划模型
        allotData.h1SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H1.getCode());
        allotData.h2SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H2.getCode());
        allotData.q1SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q1.getCode());
        allotData.q2SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q2.getCode());
        allotData.q3SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q3.getCode());
        allotData.q4SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q4.getCode());

        // 初始化各期间计划项列表
        allotData.snwItemModelList = salePlanItemRepository.listByPlanId(snwSalePlanModel.getMc04SalePlanId());
        allotData.h1ItemModelList = yearPlanGeneralDomainService.initSalePlanItems(allotData.h1SalePlanModel);
        allotData.h2ItemModelList = yearPlanGeneralDomainService.initSalePlanItems(allotData.h2SalePlanModel);
        allotData.q1ItemModelList = yearPlanGeneralDomainService.initSalePlanItems(allotData.q1SalePlanModel);
        allotData.q2ItemModelList = yearPlanGeneralDomainService.initSalePlanItems(allotData.q2SalePlanModel);
        allotData.q3ItemModelList = yearPlanGeneralDomainService.initSalePlanItems(allotData.q3SalePlanModel);
        allotData.q4ItemModelList = yearPlanGeneralDomainService.initSalePlanItems(allotData.q4SalePlanModel);

        // 构建计划项映射
        allotData.buildItemMaps();

        // 获取历史数据
        int lastYear = Integer.parseInt(zaOccurrenceYear) - 1;
        IslmHistoryFormulateData historyFormulateData = yearPlanGeneralDomainService.buildHistoryFormulateDataMap(
                Collections.singletonList(lastYear), YearPlanHistoryCategoryEnum.STOCK);
        allotData.stockHistoryData = historyFormulateData.getStock();

        return allotData;
    }

    /**
     * 统一构建省内外计划分解数据（用于计算）
     *
     * @param snwSalePlanModel 省内外销售计划模型
     * @param zaOccurrenceYear 编制年份
     * @param salePlanVersion 计划版本
     * @param salePlanStatus 计划状态
     * @param mc04OrgTypeKind 组织类型
     * @return 省内外计划分解数据
     */
    private SnwAllotData buildSnwAllotDataForCompute(Mc04IslmSalePlanModel snwSalePlanModel, String zaOccurrenceYear,
                                                     String salePlanVersion, String salePlanStatus, String mc04OrgTypeKind) {
        SnwAllotData allotData = new SnwAllotData();

        // 获取计划项映射
        HashMap<String, List<Mc04IslmSalePlanItemModel>> salePlanItemMap = getSalePlanItemMap(snwSalePlanModel, zaOccurrenceYear,
                salePlanVersion, salePlanStatus, mc04OrgTypeKind);

        // 设置计划项列表
        allotData.snwItemModelList = salePlanItemMap.get("all");
        allotData.h1ItemModelList = salePlanItemMap.get("h1");
        allotData.h2ItemModelList = salePlanItemMap.get("h2");
        allotData.q1ItemModelList = salePlanItemMap.get("q1");
        allotData.q2ItemModelList = salePlanItemMap.get("q2");
        allotData.q3ItemModelList = salePlanItemMap.get("q3");
        allotData.q4ItemModelList = salePlanItemMap.get("q4");

        // 构建计划项映射
        allotData.buildItemMaps();

        // 获取历史数据
        int lastYear = Integer.parseInt(zaOccurrenceYear) - 1;
        IslmHistoryFormulateData historyFormulateData = yearPlanGeneralDomainService.buildHistoryFormulateDataMap(
                Collections.singletonList(lastYear), YearPlanHistoryCategoryEnum.STOCK);
        allotData.stockHistoryData = historyFormulateData.getStock();

        return allotData;
    }

    @ReturnValue(desc = "省内外计划分解明细")
    @Method(name = "查询省内外计划明细")
    public IslmSalePlan4Revise4SnwDto detailAllot(@Parameter(name = "编制计划ID", required = true) String mc04SalePlanId,
                                                  @Parameter(name = "组织分类类别", required = true) String mc04OrgTypeKind) {
        Mc04IslmSalePlanModel gnSalePlanModel = salePlanRepository.getById(mc04SalePlanId);
        String zaOccurrenceYear = gnSalePlanModel.getZaOccurrenceYear();
        String salePlanVersion = gnSalePlanModel.getMc04CgtSalePlanVersion();
        String salePlanStatus = gnSalePlanModel.getMc04SalePlanStatus();
        Mc04IslmSalePlanModel snwSalePlanModel;
        YearPlanOrgTypeEnum orgTypeEnum;
        if (YearPlanOrgTypeEnum.SN.getKind().equals(mc04OrgTypeKind)) {
            snwSalePlanModel = yearPlanGeneralDomainService.getSnSalePlanModel(gnSalePlanModel);
            orgTypeEnum = YearPlanOrgTypeEnum.SN;
        } else if (YearPlanOrgTypeEnum.SW.getKind().equals(mc04OrgTypeKind)) {
            snwSalePlanModel = yearPlanGeneralDomainService.getSwSalePlanModel(gnSalePlanModel);
            orgTypeEnum = YearPlanOrgTypeEnum.SW;
        } else {
            throw new CustomException("计划组织分类类型异常！请联系管理员");
        }

        if (!allotIsInit(snwSalePlanModel, zaOccurrenceYear, salePlanVersion)) {
            initAllot(mc04SalePlanId, mc04OrgTypeKind);
        }

        int lastYear = Integer.parseInt(zaOccurrenceYear) - 1;
        IslmHistoryFormulateData historyFormulateData = yearPlanGeneralDomainService.buildHistoryFormulateDataMap(
                Collections.singletonList(lastYear), YearPlanHistoryCategoryEnum.STOCK);
        IslmHistoryFormulateTypeData stockHistoryData = historyFormulateData.getStock();
        HashMap<String, List<Mc04IslmSalePlanItemModel>> salePlanItemMap = getSalePlanItemMap(snwSalePlanModel, zaOccurrenceYear, salePlanVersion, salePlanStatus, mc04OrgTypeKind);
        List<Mc04IslmSalePlanItemModel> salePlanItemModelList = salePlanItemMap.get("all");
        List<Mc04IslmSalePlanItemModel> h1SalePlanItemModelList = salePlanItemMap.get("h1");
        List<Mc04IslmSalePlanItemModel> h2SalePlanItemModelList = salePlanItemMap.get("h2");
        List<Mc04IslmSalePlanItemModel> q1SalePlanItemModelList = salePlanItemMap.get("q1");
        List<Mc04IslmSalePlanItemModel> q2SalePlanItemModelList = salePlanItemMap.get("q2");
        List<Mc04IslmSalePlanItemModel> q3SalePlanItemModelList = salePlanItemMap.get("q3");
        List<Mc04IslmSalePlanItemModel> q4SalePlanItemModelList = salePlanItemMap.get("q4");
        Map<String, Mc04IslmSalePlanItemModel> h1SalePlanItemMap = h1SalePlanItemModelList.stream()
                .collect(Collectors.toMap(Mc04IslmSalePlanItemModel::getAcCgtCartonCode, item -> item));
        Map<String, Mc04IslmSalePlanItemModel> h2SalePlanItemMap = h2SalePlanItemModelList.stream()
                .collect(Collectors.toMap(Mc04IslmSalePlanItemModel::getAcCgtCartonCode, item -> item));
        Map<String, Mc04IslmSalePlanItemModel> q1SalePlanItemMap = q1SalePlanItemModelList.stream()
                .collect(Collectors.toMap(Mc04IslmSalePlanItemModel::getAcCgtCartonCode, item -> item));
        Map<String, Mc04IslmSalePlanItemModel> q2SalePlanItemMap = q2SalePlanItemModelList.stream()
                .collect(Collectors.toMap(Mc04IslmSalePlanItemModel::getAcCgtCartonCode, item -> item));
        Map<String, Mc04IslmSalePlanItemModel> q3SalePlanItemMap = q3SalePlanItemModelList.stream()
                .collect(Collectors.toMap(Mc04IslmSalePlanItemModel::getAcCgtCartonCode, item -> item));
        Map<String, Mc04IslmSalePlanItemModel> q4SalePlanItemMap = q4SalePlanItemModelList.stream()
                .collect(Collectors.toMap(Mc04IslmSalePlanItemModel::getAcCgtCartonCode, item -> item));
        IslmSalePlan4Revise4SnwDto salePlan4ReviseDto = IslmSalePlan4Revise4SnwConverter.INSTANCE.converterModelToDto(snwSalePlanModel);
        List<IslmSalePlanItem4Revise4SnwDto> salePlanItem4ReviseDtoList = IslmSalePlanItem4Revise4SnwConverter.INSTANCE.converterModelsToDtos(salePlanItemModelList);
        salePlan4ReviseDto.setSalePlanItemList(salePlanItem4ReviseDtoList);
        for (IslmSalePlanItem4Revise4SnwDto salePlanItem : salePlanItem4ReviseDtoList) {
            String cartonCode = salePlanItem.getAcCgtCartonCode();
            setSalePlanItemDetails(salePlanItem, cartonCode, orgTypeEnum, zaOccurrenceYear, stockHistoryData,
                    h1SalePlanItemMap, h2SalePlanItemMap,
                    q1SalePlanItemMap, q2SalePlanItemMap, q3SalePlanItemMap, q4SalePlanItemMap);
        }
        return salePlan4ReviseDto;
    }

    @ReturnValue(desc = "省内外分解结果更新")
    @Method(name = "省内外分解结果更新")
    public Boolean saveAllot(@Parameter(name = "分解明细列表", required = true) List<Mc04IslmSalePlanItemModel> salePlanItemList) {
        return salePlanItemRepository.updateBatchById(salePlanItemList);
    }

    @ReturnValue(desc = "启动分解结果")
    @Method(name = "启动分解")
    public Boolean startAllot(@Parameter(name = "编制计划ID", required = true) IslmSalePlan4ReviseReq req) {
        Mc04IslmSalePlanModel salePlanModel = IslmSalePlan4ReviseConverter.INSTANCE.converterReqToModel(req);
        salePlanModel.setMc04SalePlanStatus(SalePlanStatusEnum.PROVINCIAL_PENDING.getCode());
        return salePlanRepository.updateById(salePlanModel);
    }

    @ReturnValue(desc = "发布操作结果")
    @Method(name = "发布操作")
    public Boolean publishRevise(@Parameter(name = "编制计划ID", required = true) IslmSalePlan4ReviseReq req) {
        Mc04IslmSalePlanModel salePlanModel = IslmSalePlan4ReviseConverter.INSTANCE.converterReqToModel(req);
        salePlanModel.setMc04SalePlanStatus(SalePlanStatusEnum.PUBLISHED.getCode());
        return salePlanRepository.updateById(salePlanModel);
    }

    @ReturnValue(desc = "更新操作结果")
    @Method(name = "更新操作")
    public Boolean updateRevise(@Parameter(name = "编制计划ID", required = true) String mc04SalePlanId) {
        //todo 业务未知
        return false;
    }

    @ReturnValue(desc = "重新编制初始化结果")
    @Method(name = "重新编制初始化")
    public IslmSalePlanDto reReviseInit(@Parameter(name = "编制计划ID", required = true) String mc04SalePlanId) {
        // 获取基础模型数据
        ReReviseInitData initData = getReReviseInitData(mc04SalePlanId);
        // 处理历史版本并创建新版本
        processVersionHistory(initData);
        // 保存新版本数据
        return saveNewVersionData(initData);
    }

    @ReturnValue(desc = "修正明细查询结果带省内外")
    @Method(name = "修正明细查询带省内外")
    public IslmSalePlan4ReviseDto detailMoreRevise(@Parameter(name = "编制计划ID", required = true) String mc04SalePlanId) {
        Mc04IslmSalePlanModel qgModel = salePlanRepository.getById(mc04SalePlanId);
        String zaOccurrenceYear = qgModel.getZaOccurrenceYear();
        HashMap<String, HashMap<String, Mc04IslmSalePlanItemModel>> allItemModelMap = queryAllItemModelMap(qgModel);
        String qgCode = YearPlanOrgTypeEnum.QG.getKind();
        String snCode = YearPlanOrgTypeEnum.SN.getKind();
        String swCode = YearPlanOrgTypeEnum.SW.getKind();
        List<Mc04IslmSalePlanItemModel> qgItemModelList = new ArrayList<>(allItemModelMap.get(qgCode).values());

        int lastYear = Integer.parseInt(zaOccurrenceYear) - 1;
        IslmHistoryFormulateData historyFormulateData = yearPlanGeneralDomainService.buildHistoryFormulateDataMap(
                Collections.singletonList(lastYear), YearPlanHistoryCategoryEnum.STOCK);
        IslmHistoryFormulateTypeData stockHistoryData = historyFormulateData.getStock();
        List<IslmSalePlanItem4ReviseDto> itemDtoList = new ArrayList<>();
        for (Mc04IslmSalePlanItemModel itemModel : qgItemModelList) {
            String cartonCode = itemModel.getAcCgtCartonCode();
            Mc04IslmSalePlanItemModel snItem = allItemModelMap.get(snCode).get(cartonCode);
            Mc04IslmSalePlanItemModel swItem = allItemModelMap.get(swCode).get(cartonCode);
            Mc04IslmSalePlanItemModel h1SnItem = allItemModelMap.get(snCode + YearPlanPeriodHalfYearEnum.HALF_YEAR_H1.getCode()).get(cartonCode);
            Mc04IslmSalePlanItemModel h2SnItem = allItemModelMap.get(snCode + YearPlanPeriodHalfYearEnum.HALF_YEAR_H2.getCode()).get(cartonCode);
            Mc04IslmSalePlanItemModel q1SnItem = allItemModelMap.get(snCode + YearPlanPeriodQuarterEnum.Quarter_Q1.getCode()).get(cartonCode);
            Mc04IslmSalePlanItemModel q2SnItem = allItemModelMap.get(snCode + YearPlanPeriodQuarterEnum.Quarter_Q2.getCode()).get(cartonCode);
            Mc04IslmSalePlanItemModel q3SnItem = allItemModelMap.get(snCode + YearPlanPeriodQuarterEnum.Quarter_Q3.getCode()).get(cartonCode);
            Mc04IslmSalePlanItemModel q4SnItem = allItemModelMap.get(snCode + YearPlanPeriodQuarterEnum.Quarter_Q4.getCode()).get(cartonCode);
            Mc04IslmSalePlanItemModel h1SwItem = allItemModelMap.get(swCode + YearPlanPeriodHalfYearEnum.HALF_YEAR_H1.getCode()).get(cartonCode);
            Mc04IslmSalePlanItemModel h2SwItem = allItemModelMap.get(swCode + YearPlanPeriodHalfYearEnum.HALF_YEAR_H2.getCode()).get(cartonCode);
            Mc04IslmSalePlanItemModel q1SwItem = allItemModelMap.get(swCode + YearPlanPeriodQuarterEnum.Quarter_Q1.getCode()).get(cartonCode);
            Mc04IslmSalePlanItemModel q2SwItem = allItemModelMap.get(swCode + YearPlanPeriodQuarterEnum.Quarter_Q2.getCode()).get(cartonCode);
            Mc04IslmSalePlanItemModel q3SwItem = allItemModelMap.get(swCode + YearPlanPeriodQuarterEnum.Quarter_Q3.getCode()).get(cartonCode);
            Mc04IslmSalePlanItemModel q4SwItem = allItemModelMap.get(swCode + YearPlanPeriodQuarterEnum.Quarter_Q4.getCode()).get(cartonCode);
            BigDecimal lastStockQty = BigDecimalUtil.safeSetBigDecimal(stockHistoryData.getValue(cartonCode, YearPlanOrgTypeEnum.QG, zaOccurrenceYear));
            BigDecimal lastInStockQty = BigDecimalUtil.safeSetBigDecimal(stockHistoryData.getValue(cartonCode, YearPlanOrgTypeEnum.SN, zaOccurrenceYear));
            BigDecimal lastOutStockQty = BigDecimalUtil.safeSetBigDecimal(stockHistoryData.getValue(cartonCode, YearPlanOrgTypeEnum.SW, zaOccurrenceYear));

            IslmSalePlanItem4ReviseDto itemDto = buildReviseDetailItem(itemModel, snItem, swItem,
                    h1SnItem, h2SnItem, q1SnItem, q2SnItem, q3SnItem, q4SnItem,
                    h1SwItem, h2SwItem, q1SwItem, q2SwItem, q3SwItem, q4SwItem,
                    BigDecimalUtil.safeSetBigDecimal(lastStockQty),
                    BigDecimalUtil.safeSetBigDecimal(lastInStockQty),
                    BigDecimalUtil.safeSetBigDecimal(lastOutStockQty));
            itemDtoList.add(itemDto);
        }
        IslmSalePlan4ReviseDto islmSalePlan4ReviseDto = IslmSalePlan4ReviseConverter.INSTANCE.converterModelToDto(qgModel);
        islmSalePlan4ReviseDto.setSalePlanItemList(itemDtoList);
        return islmSalePlan4ReviseDto;
    }

    @ReturnValue(desc = "提交结果")
    @Method(name = "保存并提交")
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitFormulate(@Parameter(name = "编制计划ID", required = true) String parentMc04SalePlanId,
                                   @Parameter(name = "分解明细列表", required = true) List<Mc04IslmSalePlanItemModel> salePlanItemList) {
        saveAllot(salePlanItemList);
        Mc04IslmSalePlanModel qgSalePlanModel = salePlanRepository.getById(parentMc04SalePlanId);
        List<Mc04IslmSalePlanModel> snSalePlanModelList = salePlanRepository.listByVersion(qgSalePlanModel.getZaOccurrenceYear(),
                qgSalePlanModel.getMc04CgtSalePlanVersion(), null, YearPlanOrgTypeEnum.SN.getKind(),
                "", "", "");
        List<Mc04IslmSalePlanModel> swSalePlanModelList = salePlanRepository.listByVersion(qgSalePlanModel.getZaOccurrenceYear(),
                qgSalePlanModel.getMc04CgtSalePlanVersion(), null, YearPlanOrgTypeEnum.SW.getKind(),
                "", "", "");
        qgSalePlanModel.setMc04SalePlanStatus(SalePlanStatusEnum.PREPARED.getCode());
        snSalePlanModelList.forEach(subModel -> subModel.setMc04SalePlanStatus(SalePlanStatusEnum.PREPARED.getCode()));
        swSalePlanModelList.forEach(subModel -> subModel.setMc04SalePlanStatus(SalePlanStatusEnum.PREPARED.getCode()));
        List<Mc04IslmSalePlanModel> updateBatch = new ArrayList<>();
        updateBatch.add(qgSalePlanModel);
        updateBatch.addAll(snSalePlanModelList);
        updateBatch.addAll(swSalePlanModelList);
        return salePlanRepository.updateBatchById(updateBatch);
    }



    private ReReviseInitData getReReviseInitData(String mc04SalePlanId) {
        ReReviseInitData initData = new ReReviseInitData();

        initData.qgModel = salePlanRepository.getById(mc04SalePlanId);
        initData.snModel = yearPlanGeneralDomainService.getSnSalePlanModel(initData.qgModel);
        initData.swModel = yearPlanGeneralDomainService.getSwSalePlanModel(initData.qgModel);
        initData.zaOccurrenceYear = initData.qgModel.getZaOccurrenceYear();
        initData.salePlanVersion = initData.qgModel.getMc04CgtSalePlanVersion();
        initData.saleInitModel = saleInitRepository.getById(initData.qgModel.getMc04SaleInitId());
        initData.stockInitModel = stockInitRepository.getById(initData.qgModel.getMc04StockInitId());

        List<Mc04IslmSalePlanModel> snSalePlanModelList = salePlanRepository.listByVersion(
                initData.zaOccurrenceYear, initData.salePlanVersion, null,
                YearPlanOrgTypeEnum.SN.getKind(), "", "", "");
        initData.snSalePlanMap = snSalePlanModelList.stream()
                .collect(Collectors.toMap(Mc04IslmSalePlanModel::getMc04CgtSaleFoPeriodCode,
                        model -> model, (existing, replacement) -> existing));
        initData.h1SnSalePlanModel = initData.snSalePlanMap.get(
                initData.zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H1.getCode());
        initData.h2SnSalePlanModel = initData.snSalePlanMap.get(
                initData.zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H2.getCode());
        initData.q1SnSalePlanModel = initData.snSalePlanMap.get(
                initData.zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q1.getCode());
        initData.q2SnSalePlanModel = initData.snSalePlanMap.get(
                initData.zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q2.getCode());
        initData.q3SnSalePlanModel = initData.snSalePlanMap.get(
                initData.zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q3.getCode());
        initData.q4SnSalePlanModel = initData.snSalePlanMap.get(
                initData.zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q4.getCode());

        List<Mc04IslmSalePlanModel> swSalePlanModelList = salePlanRepository.listByVersion(
                initData.zaOccurrenceYear, initData.salePlanVersion, null,
                YearPlanOrgTypeEnum.SW.getKind(), "", "", "");
        initData.swSalePlanMap = swSalePlanModelList.stream()
                .collect(Collectors.toMap(Mc04IslmSalePlanModel::getMc04CgtSaleFoPeriodCode,
                        model -> model, (existing, replacement) -> existing));
        initData.h1SwSalePlanModel = initData.swSalePlanMap.get(
                initData.zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H1.getCode());
        initData.h2SwSalePlanModel = initData.swSalePlanMap.get(
                initData.zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H2.getCode());
        initData.q1SwSalePlanModel = initData.swSalePlanMap.get(
                initData.zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q1.getCode());
        initData.q2SwSalePlanModel = initData.swSalePlanMap.get(
                initData.zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q2.getCode());
        initData.q3SwSalePlanModel = initData.swSalePlanMap.get(
                initData.zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q3.getCode());
        initData.q4SwSalePlanModel = initData.swSalePlanMap.get(
                initData.zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q4.getCode());

        initData.qgItemModelList = salePlanItemRepository.listByPlanId(initData.qgModel.getMc04SalePlanId());
        initData.snItemModelList = salePlanItemRepository.listByPlanId(initData.snModel.getMc04SalePlanId());
        initData.swItemModelList = salePlanItemRepository.listByPlanId(initData.swModel.getMc04SalePlanId());
        initData.saleInitItemModelList = saleInitItemRepository.listBySaleInitId(
                initData.saleInitModel.getMc04SaleInitId());
        initData.stockInitItemModelList = stockInitItemRepository.listByStockInitId(
                initData.stockInitModel.getMc04StockInitId());

        return initData;
    }

    private void processVersionHistory(ReReviseInitData initData) {
        initData.opModelList = new ArrayList<>();
        initData.opModelList.add(initData.qgModel);
        initData.opModelList.add(initData.snModel);
        initData.opModelList.add(initData.swModel);
        initData.opModelList.add(initData.h1SnSalePlanModel);
        initData.opModelList.add(initData.h2SnSalePlanModel);
        initData.opModelList.add(initData.q1SnSalePlanModel);
        initData.opModelList.add(initData.q2SnSalePlanModel);
        initData.opModelList.add(initData.q3SnSalePlanModel);
        initData.opModelList.add(initData.q4SnSalePlanModel);
        initData.opModelList.add(initData.h1SwSalePlanModel);
        initData.opModelList.add(initData.h2SwSalePlanModel);
        initData.opModelList.add(initData.q1SwSalePlanModel);
        initData.opModelList.add(initData.q2SwSalePlanModel);
        initData.opModelList.add(initData.q3SwSalePlanModel);
        initData.opModelList.add(initData.q4SwSalePlanModel);

        initData.h1SnSalePlanItemModelList = salePlanItemRepository.listByPlanId(
                initData.h1SnSalePlanModel.getMc04SalePlanId());
        initData.h2SnSalePlanItemModelList = salePlanItemRepository.listByPlanId(
                initData.h2SnSalePlanModel.getMc04SalePlanId());
        initData.q1SnSalePlanItemModelList = salePlanItemRepository.listByPlanId(
                initData.q1SnSalePlanModel.getMc04SalePlanId());
        initData.q2SnSalePlanItemModelList = salePlanItemRepository.listByPlanId(
                initData.q2SnSalePlanModel.getMc04SalePlanId());
        initData.q3SnSalePlanItemModelList = salePlanItemRepository.listByPlanId(
                initData.q3SnSalePlanModel.getMc04SalePlanId());
        initData.q4SnSalePlanItemModelList = salePlanItemRepository.listByPlanId(
                initData.q4SnSalePlanModel.getMc04SalePlanId());
        initData.h1SwSalePlanItemModelList = salePlanItemRepository.listByPlanId(
                initData.h1SwSalePlanModel.getMc04SalePlanId());
        initData.h2SwSalePlanItemModelList = salePlanItemRepository.listByPlanId(
                initData.h2SwSalePlanModel.getMc04SalePlanId());
        initData.q1SwSalePlanItemModelList = salePlanItemRepository.listByPlanId(
                initData.q1SwSalePlanModel.getMc04SalePlanId());
        initData.q2SwSalePlanItemModelList = salePlanItemRepository.listByPlanId(
                initData.q2SwSalePlanModel.getMc04SalePlanId());
        initData.q3SwSalePlanItemModelList = salePlanItemRepository.listByPlanId(
                initData.q3SwSalePlanModel.getMc04SalePlanId());
        initData.q4SwSalePlanItemModelList = salePlanItemRepository.listByPlanId(
                initData.q4SwSalePlanModel.getMc04SalePlanId());

        // 历史版本标记并保存
        initData.opModelList.forEach(item -> item.setMc04IsLastestVersion(LatestVersionEnum.HISTORY.getCode()));
        initData.saleInitModel.setMc04IsLastestVersion(LatestVersionEnum.HISTORY.getCode());
        initData.stockInitModel.setMc04IsLastestVersion(LatestVersionEnum.HISTORY.getCode());
        salePlanRepository.updateBatchById(initData.opModelList);
        saleInitRepository.updateById(initData.saleInitModel);
        stockInitRepository.updateById(initData.stockInitModel);

        // 清理历史版本id 更新版本信息
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddmmhhssSSS");
        initData.planVersion = "PV" + LocalDateTime.now().format(formatter);
        initData.opModelList.forEach(item -> {
            item.setMc04SalePlanId(null);
            item.setMc04CgtSalePlanVersion(initData.planVersion);
            item.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
            item.setMc04SalePlanStatus(SalePlanStatusEnum.DRAFT.getCode());
            emptyModelBaseInfo(item);
        });
        initData.saleInitModel.setMc04SaleInitId(null);
        initData.saleInitModel.setMc04CgtSaleInitVersion(initData.planVersion);
        initData.saleInitModel.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        initData.stockInitModel.setMc04StockInitId(null);
        initData.stockInitModel.setMc04CgtStockInitVersion(initData.planVersion);
        initData.stockInitModel.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        emptyModelBaseInfo(initData.saleInitModel, initData.stockInitModel);
    }

    private IslmSalePlanDto saveNewVersionData(ReReviseInitData initData) {
        // 保存新版本信息并获取id
        initData.opModelList.forEach(item -> salePlanRepository.save(item));
        saleInitRepository.save(initData.saleInitModel);
        stockInitRepository.save(initData.stockInitModel);
        initData.qgModel.setMc04SaleInitId(initData.saleInitModel.getMc04SaleInitId());
        initData.qgModel.setMc04StockInitId(initData.stockInitModel.getMc04StockInitId());
        salePlanItemIdUpdate(initData.qgModel, initData.qgItemModelList);
        salePlanItemIdUpdate(initData.snModel, initData.snItemModelList);
        salePlanItemIdUpdate(initData.swModel, initData.swItemModelList);
        salePlanItemIdUpdate(initData.h1SnSalePlanModel, initData.h1SnSalePlanItemModelList);
        salePlanItemIdUpdate(initData.h2SnSalePlanModel, initData.h2SnSalePlanItemModelList);
        salePlanItemIdUpdate(initData.q1SnSalePlanModel, initData.q1SnSalePlanItemModelList);
        salePlanItemIdUpdate(initData.q2SnSalePlanModel, initData.q2SnSalePlanItemModelList);
        salePlanItemIdUpdate(initData.q3SnSalePlanModel, initData.q3SnSalePlanItemModelList);
        salePlanItemIdUpdate(initData.q4SnSalePlanModel, initData.q4SnSalePlanItemModelList);
        salePlanItemIdUpdate(initData.h1SwSalePlanModel, initData.h1SwSalePlanItemModelList);
        salePlanItemIdUpdate(initData.h2SwSalePlanModel, initData.h2SwSalePlanItemModelList);
        salePlanItemIdUpdate(initData.q1SwSalePlanModel, initData.q1SwSalePlanItemModelList);
        salePlanItemIdUpdate(initData.q2SwSalePlanModel, initData.q2SwSalePlanItemModelList);
        salePlanItemIdUpdate(initData.q3SwSalePlanModel, initData.q3SwSalePlanItemModelList);
        salePlanItemIdUpdate(initData.q4SwSalePlanModel, initData.q4SwSalePlanItemModelList);

        initData.saleInitItemModelList.forEach(item -> {
            item.setMc04SaleInitItemId(null);
            item.setMc04SaleInitId(initData.saleInitModel.getMc04SaleInitId());
            emptyModelBaseInfo(item);
        });
        initData.stockInitItemModelList.forEach(item -> {
            item.setMc04StockInitItemId(null);
            item.setMc04StockInitId(initData.stockInitModel.getMc04StockInitId());
            emptyModelBaseInfo(item);
        });

        List<Mc04IslmSalePlanItemModel> saveItemModelList = new ArrayList<>();
        saveItemModelList.addAll(initData.qgItemModelList);
        saveItemModelList.addAll(initData.snItemModelList);
        saveItemModelList.addAll(initData.swItemModelList);
        saveItemModelList.addAll(initData.h1SnSalePlanItemModelList);
        saveItemModelList.addAll(initData.h2SnSalePlanItemModelList);
        saveItemModelList.addAll(initData.q1SnSalePlanItemModelList);
        saveItemModelList.addAll(initData.q2SnSalePlanItemModelList);
        saveItemModelList.addAll(initData.q3SnSalePlanItemModelList);
        saveItemModelList.addAll(initData.q4SnSalePlanItemModelList);
        saveItemModelList.addAll(initData.h1SwSalePlanItemModelList);
        saveItemModelList.addAll(initData.h2SwSalePlanItemModelList);
        saveItemModelList.addAll(initData.q1SwSalePlanItemModelList);
        saveItemModelList.addAll(initData.q2SwSalePlanItemModelList);
        saveItemModelList.addAll(initData.q3SwSalePlanItemModelList);
        saveItemModelList.addAll(initData.q4SwSalePlanItemModelList);
        if (CollectionUtil.isNotEmpty(saveItemModelList)) {
            salePlanItemRepository.saveBatch(saveItemModelList);
        }
        if (CollectionUtil.isNotEmpty(initData.saleInitItemModelList)) {
            saleInitItemRepository.saveBatch(initData.saleInitItemModelList);
        }
        if (CollectionUtil.isNotEmpty(initData.stockInitItemModelList)) {
            stockInitItemRepository.saveBatch(initData.stockInitItemModelList);
        }
        IslmSalePlanDto salePlanDto = IslmSalePlanConverter.INSTANCE.converterModelToDto(initData.qgModel);
        salePlanDto.setSnMc04SalePlanId(initData.snModel.getMc04SalePlanId());
        salePlanDto.setSwMc04SalePlanId(initData.swModel.getMc04SalePlanId());
        return salePlanDto;
    }

    private static class ReReviseInitData {
        // 基础模型
        Mc04IslmSalePlanModel qgModel;
        Mc04IslmSalePlanModel snModel;
        Mc04IslmSalePlanModel swModel;
        String zaOccurrenceYear;
        String salePlanVersion;
        IslmSaleInitModel saleInitModel;
        IslmStockInitModel stockInitModel;

        // 省内计划模型映射
        Map<String, Mc04IslmSalePlanModel> snSalePlanMap;
        Mc04IslmSalePlanModel h1SnSalePlanModel;
        Mc04IslmSalePlanModel h2SnSalePlanModel;
        Mc04IslmSalePlanModel q1SnSalePlanModel;
        Mc04IslmSalePlanModel q2SnSalePlanModel;
        Mc04IslmSalePlanModel q3SnSalePlanModel;
        Mc04IslmSalePlanModel q4SnSalePlanModel;

        // 省外计划模型映射
        Map<String, Mc04IslmSalePlanModel> swSalePlanMap;
        Mc04IslmSalePlanModel h1SwSalePlanModel;
        Mc04IslmSalePlanModel h2SwSalePlanModel;
        Mc04IslmSalePlanModel q1SwSalePlanModel;
        Mc04IslmSalePlanModel q2SwSalePlanModel;
        Mc04IslmSalePlanModel q3SwSalePlanModel;
        Mc04IslmSalePlanModel q4SwSalePlanModel;

        // 计划项列表
        List<Mc04IslmSalePlanItemModel> qgItemModelList;
        List<Mc04IslmSalePlanItemModel> snItemModelList;
        List<Mc04IslmSalePlanItemModel> swItemModelList;
        List<IslmSaleInitItemModel> saleInitItemModelList;
        List<IslmStockInitItemModel> stockInitItemModelList;

        // 各期间计划项列表
        List<Mc04IslmSalePlanItemModel> h1SnSalePlanItemModelList;
        List<Mc04IslmSalePlanItemModel> h2SnSalePlanItemModelList;
        List<Mc04IslmSalePlanItemModel> q1SnSalePlanItemModelList;
        List<Mc04IslmSalePlanItemModel> q2SnSalePlanItemModelList;
        List<Mc04IslmSalePlanItemModel> q3SnSalePlanItemModelList;
        List<Mc04IslmSalePlanItemModel> q4SnSalePlanItemModelList;
        List<Mc04IslmSalePlanItemModel> h1SwSalePlanItemModelList;
        List<Mc04IslmSalePlanItemModel> h2SwSalePlanItemModelList;
        List<Mc04IslmSalePlanItemModel> q1SwSalePlanItemModelList;
        List<Mc04IslmSalePlanItemModel> q2SwSalePlanItemModelList;
        List<Mc04IslmSalePlanItemModel> q3SwSalePlanItemModelList;
        List<Mc04IslmSalePlanItemModel> q4SwSalePlanItemModelList;

        // 操作模型列表
        List<Mc04IslmSalePlanModel> opModelList;

        // 新版本号
        String planVersion;
    }

    private void setSalePlanItemDetails(IslmSalePlanItem4Revise4SnwDto salePlanItem,
                                        String cartonCode,
                                        YearPlanOrgTypeEnum orgTypeEnum,
                                        String zaOccurrenceYear,
                                        IslmHistoryFormulateTypeData stockHistoryData,
                                        Map<String, Mc04IslmSalePlanItemModel> h1SalePlanItemMap,
                                        Map<String, Mc04IslmSalePlanItemModel> h2SalePlanItemMap,
                                        Map<String, Mc04IslmSalePlanItemModel> q1SalePlanItemMap,
                                        Map<String, Mc04IslmSalePlanItemModel> q2SalePlanItemMap,
                                        Map<String, Mc04IslmSalePlanItemModel> q3SalePlanItemMap,
                                        Map<String, Mc04IslmSalePlanItemModel> q4SalePlanItemMap) {
        BigDecimal lastStockQty = BigDecimalUtil.safeSetBigDecimal(stockHistoryData.getValue(cartonCode, orgTypeEnum, zaOccurrenceYear));
        Mc04IslmSalePlanItemModel h1ItemModel = h1SalePlanItemMap.get(cartonCode);
        Mc04IslmSalePlanItemModel h2ItemModel = h2SalePlanItemMap.get(cartonCode);
        Mc04IslmSalePlanItemModel q1ItemModel = q1SalePlanItemMap.get(cartonCode);
        Mc04IslmSalePlanItemModel q2ItemModel = q2SalePlanItemMap.get(cartonCode);
        Mc04IslmSalePlanItemModel q3ItemModel = q3SalePlanItemMap.get(cartonCode);
        Mc04IslmSalePlanItemModel q4ItemModel = q4SalePlanItemMap.get(cartonCode);
        salePlanItem.setLastStockQty(lastStockQty);
        salePlanItem.setH1SalePlanItem(IslmSalePlanItem4Revise4SnwConverter.INSTANCE.converterModelToDto(h1ItemModel));
        salePlanItem.setH2SalePlanItem(IslmSalePlanItem4Revise4SnwConverter.INSTANCE.converterModelToDto(h2ItemModel));
        salePlanItem.setQ1SalePlanItem(IslmSalePlanItem4Revise4SnwConverter.INSTANCE.converterModelToDto(q1ItemModel));
        salePlanItem.setQ2SalePlanItem(IslmSalePlanItem4Revise4SnwConverter.INSTANCE.converterModelToDto(q2ItemModel));
        salePlanItem.setQ3SalePlanItem(IslmSalePlanItem4Revise4SnwConverter.INSTANCE.converterModelToDto(q3ItemModel));
        salePlanItem.setQ4SalePlanItem(IslmSalePlanItem4Revise4SnwConverter.INSTANCE.converterModelToDto(q4ItemModel));

        salePlanItem.setH1SaleQty(h1ItemModel.getMc04CgtSalePlanAdjustedQty());
        salePlanItem.setH1PlQty(h1ItemModel.getMa02CgtPlAdjustedQty());
        salePlanItem.setH1StkQty(h1ItemModel.getMd03Cgt10thComEndStkQty());
        salePlanItem.setH1XyCarryOverQty(h1ItemModel.getMc04CgtXyCarryOverQty());
        salePlanItem.setH1XyQty(h1ItemModel.getMd02CgtXyOriginalQty());

        salePlanItem.setH2SaleQty(h2ItemModel.getMc04CgtSalePlanAdjustedQty());
        salePlanItem.setH2PlQty(h2ItemModel.getMa02CgtPlAdjusQty());
        salePlanItem.setH2XyCarryOverQty(h2ItemModel.getMc04CgtXyCarryOverQty());
        salePlanItem.setH2XyQty(h2ItemModel.getMd02CgtXyOriginalQty());

        salePlanItem.setQ1SaleQty(q1ItemModel.getMc04CgtSalePlanAdjustedQty());
        salePlanItem.setQ1PlQty(q1ItemModel.getMa02CgtPlAdjustedQty());
        salePlanItem.setQ2SaleQty(q2ItemModel.getMc04CgtSalePlanAdjustedQty());
        salePlanItem.setQ2PlQty(q2ItemModel.getMa02CgtPlAdjustedQty());
        salePlanItem.setQ3SaleQty(q3ItemModel.getMc04CgtSalePlanAdjustedQty());
        salePlanItem.setQ3PlQty(q3ItemModel.getMa02CgtPlAdjustedQty());
        salePlanItem.setQ4SaleQty(q4ItemModel.getMc04CgtSalePlanAdjustedQty());
        salePlanItem.setQ4PlQty(q4ItemModel.getMa02CgtPlAdjustedQty());
    }

    private void initAllotData(Mc04IslmSalePlanItemModel salePlanItemModel,
                               YearPlanOrgTypeEnum orgTypeEnum,
                               String zaOccurrenceYear,
                               IslmHistoryFormulateTypeData stockHistoryData,
                               Map<String, Mc04IslmSalePlanItemModel> h1SalePlanItemMap,
                               Map<String, Mc04IslmSalePlanItemModel> h2SalePlanItemMap,
                               Map<String, Mc04IslmSalePlanItemModel> q1SalePlanItemMap,
                               Map<String, Mc04IslmSalePlanItemModel> q2SalePlanItemMap,
                               Map<String, Mc04IslmSalePlanItemModel> q3SalePlanItemMap,
                               Map<String, Mc04IslmSalePlanItemModel> q4SalePlanItemMap) {
        String cartonCode = salePlanItemModel.getAcCgtCartonCode();
        // 全年销售
        BigDecimal saleAdjustedQty = BigDecimalUtil.safeSetBigDecimal(salePlanItemModel.getMc04CgtSalePlanAdjustedQty());
        // 全年调拨
        BigDecimal plAdjustedQty = BigDecimalUtil.safeSetBigDecimal(salePlanItemModel.getMa02CgtPlAdjustedQty());
        // 年末库存
        BigDecimal comEndStkQty = BigDecimalUtil.safeSetBigDecimal(salePlanItemModel.getMd03Cgt10thComEndStkQty());
        // 去年末库存
        BigDecimal lastStockQty = BigDecimalUtil.safeSetBigDecimal(stockHistoryData.getValue(salePlanItemModel.getAcCgtCartonCode(), orgTypeEnum, zaOccurrenceYear));

        // 去年末下转协议 默认为 去年末库存 一直取整
        BigDecimal lastXyQty = lastStockQty.divide(BigDecimal.valueOf(1), 2, RoundingMode.DOWN);
        // 上半年销售 全年销售50%取整
        BigDecimal h1SaleAdjustedQty = saleAdjustedQty.divide(BigDecimal.valueOf(2), 2, RoundingMode.DOWN);
        // 上半年调拨 全年调拨50%取整
        BigDecimal h1PlAdjustedQty = plAdjustedQty.divide(BigDecimal.valueOf(2), 2, RoundingMode.DOWN);
        // 上半年库存 去年末库存 + 上半年调拨 - 上半年销售
        BigDecimal h1ComEndStkQty = lastStockQty.add(h1PlAdjustedQty).subtract(h1SaleAdjustedQty);
        // 上半年协议 上半年调拨的100%取整
        BigDecimal h1XyOriginalQty = h1PlAdjustedQty.multiply(BigDecimal.valueOf(1)).setScale(2, RoundingMode.DOWN);

        // 上半年下转协议 去年末下转协议 + 上半年协议 - 上半年调拨
        BigDecimal h1XyCarryOverQty = lastXyQty.add(h1XyOriginalQty).subtract(h1PlAdjustedQty);
        // 下半年销售 全年销售 - 上半年销售
        BigDecimal h2SaleAdjustedQty = saleAdjustedQty.subtract(h1SaleAdjustedQty);
        // 下半年调拨 全年调拨 - 上半年调拨
        BigDecimal h2PlAdjustedQty = plAdjustedQty.subtract(h1PlAdjustedQty);
        // 下半年协议 下半年调拨 - 上半年下转协议
        BigDecimal h2XyOriginalQty = h2PlAdjustedQty.subtract(h1XyCarryOverQty);

        // 季度1销售 上半年销售60%取整
        BigDecimal q1SaleAdjustedQty = h1SaleAdjustedQty.multiply(BigDecimal.valueOf(0.6)).setScale(2, RoundingMode.DOWN);
        // 季度1调拨 上半年调拨60%取整
        BigDecimal q1PlAdjustedQty = h1PlAdjustedQty.multiply(BigDecimal.valueOf(0.6)).setScale(2, RoundingMode.DOWN);

        // 季度2销售 上半年销售 - 季度1销售
        BigDecimal q2SaleAdjustedQty = h1SaleAdjustedQty.subtract(q1SaleAdjustedQty);
        // 季度2调拨 上半年调拨 - 季度1调拨
        BigDecimal q2PlAdjustedQty = h1PlAdjustedQty.subtract(q1PlAdjustedQty);

        // 季度3销售 下半年销售取50%
        BigDecimal q3SaleAdjustedQty = h2SaleAdjustedQty.multiply(BigDecimal.valueOf(0.5)).setScale(2, RoundingMode.DOWN);
        // 季度3调拨 下半年调拨取50%
        BigDecimal q3PlAdjustedQty = h2PlAdjustedQty.multiply(BigDecimal.valueOf(0.5)).setScale(2, RoundingMode.DOWN);

        // 季度4销售 下半年销售 - 季度3销售
        BigDecimal q4SaleAdjustedQty = h2SaleAdjustedQty.subtract(q3SaleAdjustedQty);
        // 季度4调拨 下半年调拨 - 季度3调拨
        BigDecimal q4PlAdjustedQty = h2PlAdjustedQty.subtract(q3PlAdjustedQty);
        // 将计算值赋给半年和季度计划对象明细
        setAllotH1Qty(h1SalePlanItemMap, cartonCode, lastXyQty, h1XyOriginalQty, h1SaleAdjustedQty, h1PlAdjustedQty, h1ComEndStkQty);
        setAllotH2Qty(h2SalePlanItemMap, cartonCode, h1XyCarryOverQty, h2SaleAdjustedQty, h2PlAdjustedQty, h2XyOriginalQty);
        setAllotQuarterQty(q1SalePlanItemMap, cartonCode, q1SaleAdjustedQty, q1PlAdjustedQty);
        setAllotQuarterQty(q2SalePlanItemMap, cartonCode, q2SaleAdjustedQty, q2PlAdjustedQty);
        setAllotQuarterQty(q3SalePlanItemMap, cartonCode, q3SaleAdjustedQty, q3PlAdjustedQty);
        setAllotQuarterQty(q4SalePlanItemMap, cartonCode, q4SaleAdjustedQty, q4PlAdjustedQty);
    }

    private void setAllotH1Qty(Map<String, Mc04IslmSalePlanItemModel> h1SalePlanItemMap, String cartonCode,
                               BigDecimal lastXyQty, BigDecimal h1XyOriginalQty, BigDecimal h1SaleAdjustedQty,
                               BigDecimal h1PlAdjustedQty, BigDecimal h1ComEndStkQty) {
        Mc04IslmSalePlanItemModel h1ItemModel = h1SalePlanItemMap.get(cartonCode);
        if (h1ItemModel != null) {
            h1ItemModel.setMc04CgtXyCarryOverQty(lastXyQty);
            h1ItemModel.setMd02CgtXyOriginalQty(h1XyOriginalQty);
            h1ItemModel.setMc04CgtSalePlanAdjustedQty(h1SaleAdjustedQty);
            h1ItemModel.setMa02CgtPlAdjustedQty(h1PlAdjustedQty);
            h1ItemModel.setMd03Cgt10thComEndStkQty(h1ComEndStkQty);
        }
    }

    private void setAllotH2Qty(Map<String, Mc04IslmSalePlanItemModel> h2SalePlanItemMap, String cartonCode,
                               BigDecimal h1XyCarryOverQty, BigDecimal h2SaleAdjustedQty,
                               BigDecimal h2PlAdjustedQty, BigDecimal h2XyOriginalQty) {
        Mc04IslmSalePlanItemModel h2ItemModel = h2SalePlanItemMap.get(cartonCode);
        if (h2ItemModel != null) {
            h2ItemModel.setMc04CgtXyCarryOverQty(h1XyCarryOverQty);
            h2ItemModel.setMc04CgtSalePlanAdjustedQty(h2SaleAdjustedQty);
            h2ItemModel.setMa02CgtPlAdjustedQty(h2PlAdjustedQty);
            h2ItemModel.setMd02CgtXyOriginalQty(h2XyOriginalQty);
        }
    }

    private void setAllotQuarterQty(Map<String, Mc04IslmSalePlanItemModel> quarterSalePlanItemMap, String cartonCode,
                                    BigDecimal q1SaleAdjustedQty, BigDecimal q1PlAdjustedQty) {
        Mc04IslmSalePlanItemModel quarterItemModel = quarterSalePlanItemMap.get(cartonCode);
        if (quarterItemModel != null) {
            quarterItemModel.setMc04CgtSalePlanAdjustedQty(q1SaleAdjustedQty);
            quarterItemModel.setMa02CgtPlAdjustedQty(q1PlAdjustedQty);
        }
    }

    private Boolean allotIsInit(Mc04IslmSalePlanModel snwSalePlanModel, String zaOccurrenceYear, String salePlanVersion) {
        long snwItemCount = salePlanItemRepository.countByPlanId(snwSalePlanModel.getMc04SalePlanId());
        Map<String, Mc04IslmSalePlanModel> salePlanMap = getSalePlanMap(zaOccurrenceYear, salePlanVersion, snwSalePlanModel.getMc04OrgTypeKind());
        Mc04IslmSalePlanModel h1SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H1.getCode());
        Mc04IslmSalePlanModel h2SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H2.getCode());
        Mc04IslmSalePlanModel q1SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q1.getCode());
        Mc04IslmSalePlanModel q2SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q2.getCode());
        Mc04IslmSalePlanModel q3SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q3.getCode());
        Mc04IslmSalePlanModel q4SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q4.getCode());
        long h1ItemCount = salePlanItemRepository.countByPlanId(h1SalePlanModel.getMc04SalePlanId());
        long h2ItemCount = salePlanItemRepository.countByPlanId(h2SalePlanModel.getMc04SalePlanId());
        long q1ItemCount = salePlanItemRepository.countByPlanId(q1SalePlanModel.getMc04SalePlanId());
        long q2ItemCount = salePlanItemRepository.countByPlanId(q2SalePlanModel.getMc04SalePlanId());
        long q3ItemCount = salePlanItemRepository.countByPlanId(q3SalePlanModel.getMc04SalePlanId());
        long q4ItemCount = salePlanItemRepository.countByPlanId(q4SalePlanModel.getMc04SalePlanId());
        return snwItemCount > 0 &&
                snwItemCount == h1ItemCount &&
                snwItemCount == h2ItemCount &&
                snwItemCount == q1ItemCount &&
                snwItemCount == q2ItemCount &&
                snwItemCount == q3ItemCount &&
                snwItemCount == q4ItemCount;
    }

    private HashMap<String, List<Mc04IslmSalePlanItemModel>> getSalePlanItemMap(Mc04IslmSalePlanModel snwSalePlanModel, String zaOccurrenceYear,
                                                                                String salePlanVersion, String salePlanStatus, String mc04OrgTypeKind) {
        Map<String, Mc04IslmSalePlanModel> salePlanMap = getSalePlanMap(zaOccurrenceYear, salePlanVersion, mc04OrgTypeKind);
        Mc04IslmSalePlanModel h1SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H1.getCode());
        Mc04IslmSalePlanModel h2SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H2.getCode());
        Mc04IslmSalePlanModel q1SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q1.getCode());
        Mc04IslmSalePlanModel q2SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q2.getCode());
        Mc04IslmSalePlanModel q3SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q3.getCode());
        Mc04IslmSalePlanModel q4SalePlanModel = salePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q4.getCode());
        List<Mc04IslmSalePlanItemModel> salePlanItemModelList = salePlanItemRepository.listByPlanId(snwSalePlanModel.getMc04SalePlanId());
        List<Mc04IslmSalePlanItemModel> h1SalePlanItemModelList = salePlanItemRepository.listByPlanId(h1SalePlanModel.getMc04SalePlanId());
        List<Mc04IslmSalePlanItemModel> h2SalePlanItemModelList = salePlanItemRepository.listByPlanId(h2SalePlanModel.getMc04SalePlanId());
        List<Mc04IslmSalePlanItemModel> q1SalePlanItemModelList = salePlanItemRepository.listByPlanId(q1SalePlanModel.getMc04SalePlanId());
        List<Mc04IslmSalePlanItemModel> q2SalePlanItemModelList = salePlanItemRepository.listByPlanId(q2SalePlanModel.getMc04SalePlanId());
        List<Mc04IslmSalePlanItemModel> q3SalePlanItemModelList = salePlanItemRepository.listByPlanId(q3SalePlanModel.getMc04SalePlanId());
        List<Mc04IslmSalePlanItemModel> q4SalePlanItemModelList = salePlanItemRepository.listByPlanId(q4SalePlanModel.getMc04SalePlanId());
        HashMap<String, List<Mc04IslmSalePlanItemModel>> salePlanItemMap = new HashMap<>(16);
        salePlanItemMap.put("all", salePlanItemModelList);
        salePlanItemMap.put("h1", h1SalePlanItemModelList);
        salePlanItemMap.put("h2", h2SalePlanItemModelList);
        salePlanItemMap.put("q1", q1SalePlanItemModelList);
        salePlanItemMap.put("q2", q2SalePlanItemModelList);
        salePlanItemMap.put("q3", q3SalePlanItemModelList);
        salePlanItemMap.put("q4", q4SalePlanItemModelList);
        return salePlanItemMap;
    }

    private Map<String, Mc04IslmSalePlanModel> getSalePlanMap(String zaOccurrenceYear, String salePlanVersion, String mc04OrgTypeKind) {
        List<Mc04IslmSalePlanModel> salePlanModelList = salePlanRepository.listByVersion(zaOccurrenceYear, salePlanVersion,
                null, mc04OrgTypeKind, "", "", "");
        return salePlanModelList.stream()
                .collect(Collectors.toMap(Mc04IslmSalePlanModel::getMc04CgtSaleFoPeriodCode,
                        model -> model, (existing, replacement) -> existing));
    }

    private void salePlanItemIdUpdate(Mc04IslmSalePlanModel salePlanModel, List<Mc04IslmSalePlanItemModel> salePlanItemModelList) {
        salePlanItemModelList.forEach(item -> {
            emptyModelBaseInfo(item);
            item.setMc04SalePlanId(salePlanModel.getMc04SalePlanId());
        });
    }

    private void emptyModelBaseInfo(BaseFactor... models) {
        for (BaseFactor model : models) {
            model.setCreateId(null);
            model.setCreateName(null);
            model.setCreateTime(null);
            model.setUpdateId(null);
            model.setUpdateName(null);
            model.setUpdateTime(null);
            model.setIcomCode(null);
        }
    }

    private HashMap<String, HashMap<String, Mc04IslmSalePlanItemModel>> queryAllItemModelMap(Mc04IslmSalePlanModel qgModel) {
        HashMap<String, HashMap<String, Mc04IslmSalePlanItemModel>> allItemModelMap = new HashMap<>(32);
        Mc04IslmSalePlanModel snModel = yearPlanGeneralDomainService.getSnSalePlanModel(qgModel);
        Mc04IslmSalePlanModel swModel = yearPlanGeneralDomainService.getSwSalePlanModel(qgModel);
        String zaOccurrenceYear = qgModel.getZaOccurrenceYear();
        String salePlanVersion = qgModel.getMc04CgtSalePlanVersion();
        List<Mc04IslmSalePlanModel> snSalePlanModelList = salePlanRepository.listByVersion(zaOccurrenceYear, salePlanVersion,
                null, YearPlanOrgTypeEnum.SN.getKind(), "", "", "");
        Map<String, Mc04IslmSalePlanModel> snSalePlanMap = snSalePlanModelList.stream()
                .collect(Collectors.toMap(Mc04IslmSalePlanModel::getMc04CgtSaleFoPeriodCode,
                        model -> model, (existing, replacement) -> existing));
        Mc04IslmSalePlanModel h1SnSalePlanModel = snSalePlanMap.get(zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H1.getCode());
        Mc04IslmSalePlanModel h2SnSalePlanModel = snSalePlanMap.get(zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H2.getCode());
        Mc04IslmSalePlanModel q1SnSalePlanModel = snSalePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q1.getCode());
        Mc04IslmSalePlanModel q2SnSalePlanModel = snSalePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q2.getCode());
        Mc04IslmSalePlanModel q3SnSalePlanModel = snSalePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q3.getCode());
        Mc04IslmSalePlanModel q4SnSalePlanModel = snSalePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q4.getCode());

        List<Mc04IslmSalePlanModel> swSalePlanModelList = salePlanRepository.listByVersion(zaOccurrenceYear, salePlanVersion,
                null, YearPlanOrgTypeEnum.SW.getKind(), "", "", "");
        Map<String, Mc04IslmSalePlanModel> swSalePlanMap = swSalePlanModelList.stream()
                .collect(Collectors.toMap(Mc04IslmSalePlanModel::getMc04CgtSaleFoPeriodCode,
                        model -> model, (existing, replacement) -> existing));
        Mc04IslmSalePlanModel h1SwSalePlanModel = swSalePlanMap.get(zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H1.getCode());
        Mc04IslmSalePlanModel h2SwSalePlanModel = swSalePlanMap.get(zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H2.getCode());
        Mc04IslmSalePlanModel q1SwSalePlanModel = swSalePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q1.getCode());
        Mc04IslmSalePlanModel q2SwSalePlanModel = swSalePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q2.getCode());
        Mc04IslmSalePlanModel q3SwSalePlanModel = swSalePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q3.getCode());
        Mc04IslmSalePlanModel q4SwSalePlanModel = swSalePlanMap.get(zaOccurrenceYear + YearPlanPeriodQuarterEnum.Quarter_Q4.getCode());

        HashMap<String, Mc04IslmSalePlanItemModel> qgItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(qgModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> snItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(snModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> swItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(swModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> h1SnItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(h1SnSalePlanModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> h2SnItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(h2SnSalePlanModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> q1SnItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(q1SnSalePlanModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> q2SnItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(q2SnSalePlanModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> q3SnItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(q3SnSalePlanModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> q4SnItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(q4SnSalePlanModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> h1SwItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(h1SwSalePlanModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> h2SwItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(h2SwSalePlanModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> q1SwItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(q1SwSalePlanModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> q2SwItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(q2SwSalePlanModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> q3SwItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(q3SwSalePlanModel.getMc04SalePlanId()));
        HashMap<String, Mc04IslmSalePlanItemModel> q4SwItemModelMap = exchangeItemModel2Map(salePlanItemRepository.listByPlanId(q4SwSalePlanModel.getMc04SalePlanId()));

        String qgCode = YearPlanOrgTypeEnum.QG.getKind();
        String snCode = YearPlanOrgTypeEnum.SN.getKind();
        String swCode = YearPlanOrgTypeEnum.SW.getKind();
        allItemModelMap.put(qgCode, qgItemModelMap);
        allItemModelMap.put(snCode, snItemModelMap);
        allItemModelMap.put(swCode, swItemModelMap);
        allItemModelMap.put(snCode + YearPlanPeriodHalfYearEnum.HALF_YEAR_H1.getCode(), h1SnItemModelMap);
        allItemModelMap.put(snCode + YearPlanPeriodHalfYearEnum.HALF_YEAR_H2.getCode(), h2SnItemModelMap);
        allItemModelMap.put(snCode + YearPlanPeriodQuarterEnum.Quarter_Q1.getCode(), q1SnItemModelMap);
        allItemModelMap.put(snCode + YearPlanPeriodQuarterEnum.Quarter_Q2.getCode(), q2SnItemModelMap);
        allItemModelMap.put(snCode + YearPlanPeriodQuarterEnum.Quarter_Q3.getCode(), q3SnItemModelMap);
        allItemModelMap.put(snCode + YearPlanPeriodQuarterEnum.Quarter_Q4.getCode(), q4SnItemModelMap);
        allItemModelMap.put(swCode + YearPlanPeriodHalfYearEnum.HALF_YEAR_H1.getCode(), h1SwItemModelMap);
        allItemModelMap.put(swCode + YearPlanPeriodHalfYearEnum.HALF_YEAR_H2.getCode(), h2SwItemModelMap);
        allItemModelMap.put(swCode + YearPlanPeriodQuarterEnum.Quarter_Q1.getCode(), q1SwItemModelMap);
        allItemModelMap.put(swCode + YearPlanPeriodQuarterEnum.Quarter_Q2.getCode(), q2SwItemModelMap);
        allItemModelMap.put(swCode + YearPlanPeriodQuarterEnum.Quarter_Q3.getCode(), q3SwItemModelMap);
        allItemModelMap.put(swCode + YearPlanPeriodQuarterEnum.Quarter_Q4.getCode(), q4SwItemModelMap);
        return allItemModelMap;
    }

    private IslmSalePlanItem4ReviseDto buildReviseDetailItem(Mc04IslmSalePlanItemModel itemModel, Mc04IslmSalePlanItemModel snItem, Mc04IslmSalePlanItemModel swItem,
             Mc04IslmSalePlanItemModel h1SnItem, Mc04IslmSalePlanItemModel h2SnItem,
             Mc04IslmSalePlanItemModel q1SnItem, Mc04IslmSalePlanItemModel q2SnItem, Mc04IslmSalePlanItemModel q3SnItem, Mc04IslmSalePlanItemModel q4SnItem,
             Mc04IslmSalePlanItemModel h1SwItem, Mc04IslmSalePlanItemModel h2SwItem,
             Mc04IslmSalePlanItemModel q1SwItem, Mc04IslmSalePlanItemModel q2SwItem, Mc04IslmSalePlanItemModel q3SwItem, Mc04IslmSalePlanItemModel q4SwItem,
             BigDecimal lastStock, BigDecimal lastInStock, BigDecimal lastOutStock) {
        IslmSalePlanItem4ReviseDto itemDto = IslmSalePlanItem4ReviseConverter.INSTANCE.converterModelToDto(itemModel);
        //省内数据
        itemDto.setSnLastStockQty(lastInStock);
        itemDto.setSnSalePlanQty(snItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSnPlQty(snItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSnStockQty(snItem.getMd03Cgt10thComEndStkQty());
        itemDto.setSnLastXyCarryOverQty(h1SnItem.getMc04CgtXyCarryOverQty());
        itemDto.setSnH1XyQty(h1SnItem.getMd02CgtXyOriginalQty());
        itemDto.setSnH1SalePlanQty(h1SnItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSnH1PlQty(h1SnItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSnH1StockQty(h1SnItem.getMd03Cgt10thComEndStkQty());
        itemDto.setSnH1XyCarryOverQty(h2SnItem.getMc04CgtXyCarryOverQty());
        itemDto.setSnH2SalePlanQty(h2SnItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSnH2PlQty(h2SnItem.getMa02CgtPlAdjustedQty());
        itemDto.setSnH2XyQty(h2SnItem.getMd02CgtXyOriginalQty());
        itemDto.setSnQ1SalePlanQty(q1SnItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSnQ1PlQty(q1SnItem.getMa02CgtPlAdjustedQty());
        itemDto.setSnQ2SalePlanQty(q2SnItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSnQ2PlQty(q2SnItem.getMa02CgtPlAdjustedQty());
        itemDto.setSnQ3SalePlanQty(q3SnItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSnQ3PlQty(q3SnItem.getMa02CgtPlAdjustedQty());
        itemDto.setSnQ4SalePlanQty(q4SnItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSnQ4PlQty(q4SnItem.getMa02CgtPlAdjustedQty());
        //省外数据
        itemDto.setSwLastStockQty(lastOutStock);
        itemDto.setSwSalePlanQty(swItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSwPlQty(swItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSwStockQty(swItem.getMd03Cgt10thComEndStkQty());
        itemDto.setSwLastXyCarryOverQty(h1SwItem.getMc04CgtXyCarryOverQty());
        itemDto.setSwH1XyQty(h1SwItem.getMd02CgtXyOriginalQty());
        itemDto.setSwH1SalePlanQty(h1SwItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSwH1PlQty(h1SwItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSwH1StockQty(h1SwItem.getMd03Cgt10thComEndStkQty());
        itemDto.setSwH1XyCarryOverQty(h2SwItem.getMc04CgtXyCarryOverQty());
        itemDto.setSwH2SalePlanQty(h2SwItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSwH2PlQty(h2SwItem.getMa02CgtPlAdjustedQty());
        itemDto.setSwH2XyQty(h2SwItem.getMd02CgtXyOriginalQty());
        itemDto.setSwQ1SalePlanQty(q1SwItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSwQ1PlQty(q1SwItem.getMa02CgtPlAdjustedQty());
        itemDto.setSwQ2SalePlanQty(q2SwItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSwQ2PlQty(q2SwItem.getMa02CgtPlAdjustedQty());
        itemDto.setSwQ3SalePlanQty(q3SwItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSwQ3PlQty(q3SwItem.getMa02CgtPlAdjustedQty());
        itemDto.setSwQ4SalePlanQty(q4SwItem.getMc04CgtSalePlanAdjustedQty());
        itemDto.setSwQ4PlQty(q4SwItem.getMa02CgtPlAdjustedQty());
        //全国数据
        itemDto.setLastStockQty(lastStock);
        itemDto.setSalePlanQty(itemDto.getMc04CgtSalePlanAdjustedQty());
        itemDto.setPlQty(itemDto.getMa02CgtPlAdjustedQty());
        itemDto.setStockQty(itemDto.getMd03Cgt10thComEndStkQty());
        itemDto.setLastXyCarryOverQty(itemDto.getSwLastXyCarryOverQty().add(itemDto.getSnLastXyCarryOverQty()));
        itemDto.setH1XyQty(itemDto.getSwH1XyQty().add(itemDto.getSnH1XyQty()));
        itemDto.setH1SalePlanQty(itemDto.getSwH1SalePlanQty().add(itemDto.getSnH1SalePlanQty()));
        itemDto.setH1PlQty(itemDto.getSwH1PlQty().add(itemDto.getSnH1PlQty()));
        itemDto.setH1StockQty(itemDto.getSwH1StockQty().add(itemDto.getSnH1StockQty()));
        itemDto.setH1XyCarryOverQty(itemDto.getSwH1XyCarryOverQty().add(itemDto.getSnH1XyCarryOverQty()));
        itemDto.setH2SalePlanQty(itemDto.getSwH2SalePlanQty().add(itemDto.getSnH2SalePlanQty()));
        itemDto.setH2PlQty(itemDto.getSwH2PlQty().add(itemDto.getSnH2PlQty()));
        itemDto.setH2XyQty(itemDto.getSwH2XyQty().add(itemDto.getSnH2XyQty()));
        itemDto.setQ1SalePlanQty(itemDto.getSwQ1SalePlanQty().add(itemDto.getSnQ1SalePlanQty()));
        itemDto.setQ1PlQty(itemDto.getSwQ1PlQty().add(itemDto.getSnQ1PlQty()));
        itemDto.setQ2SalePlanQty(itemDto.getSwQ2SalePlanQty().add(itemDto.getSnQ2SalePlanQty()));
        itemDto.setQ2PlQty(itemDto.getSwQ2PlQty().add(itemDto.getSnQ2PlQty()));
        itemDto.setQ3SalePlanQty(itemDto.getSwQ3SalePlanQty().add(itemDto.getSnQ3SalePlanQty()));
        itemDto.setQ3PlQty(itemDto.getSwQ3PlQty().add(itemDto.getSnQ3PlQty()));
        itemDto.setQ4SalePlanQty(itemDto.getSwQ4SalePlanQty().add(itemDto.getSnQ4SalePlanQty()));
        itemDto.setQ4PlQty(itemDto.getSwQ4PlQty().add(itemDto.getSnQ4PlQty()));
        return itemDto;
    }

    private HashMap<String, Mc04IslmSalePlanItemModel> exchangeItemModel2Map(List<Mc04IslmSalePlanItemModel> salePlanItemModelList) {
        HashMap<String, Mc04IslmSalePlanItemModel> itemMap = new HashMap<>(128);
        for (Mc04IslmSalePlanItemModel itemModel : salePlanItemModelList) {
            itemMap.put(itemModel.getAcCgtCartonCode(), itemModel);
        }
        return itemMap;
    }
}