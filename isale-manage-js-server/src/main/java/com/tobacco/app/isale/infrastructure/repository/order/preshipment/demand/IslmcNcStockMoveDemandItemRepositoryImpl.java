/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.order.preshipment.demand;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tobacco.app.isale.domain.model.order.preshipment.demand.Mc04IslmcNcStockMoveDemandItem;
import com.tobacco.app.isale.domain.repository.order.preshipment.demand.IslmcNcStockMoveDemandItemRepository;
import com.tobacco.app.isale.domain.repository.order.preshipment.demand.IslmcNcStockMoveDemandRepository;
import com.tobacco.app.isale.infrastructure.converter.order.preshipment.demand.Mc04IslmcNcStockMoveDemandItemDOToMc04IslmcNcStockMoveDemandItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcNcStockMoveDemandItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcNcStockMoveDemandItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcNcStockMoveDemandService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/04/22 10:42
 * @description : 销售管理-协议管理(卷烟)-意向采集-意向上报(市场填报) infrastructureRepository
 */
@Slf4j
@Component("IslmcNcStockMoveDemandItemRepository")
@RequiredArgsConstructor
public class IslmcNcStockMoveDemandItemRepositoryImpl implements IslmcNcStockMoveDemandItemRepository {

    private final Mc04IslmcNcStockMoveDemandItemService mc04IslmcNcStockMoveDemandItemService;

    @Override
    public Map<String, List<Mc04IslmcNcStockMoveDemandItem>> list(List<String> ids) {
        LambdaQueryWrapper<Mc04IslmcNcStockMoveDemandItemDO> queryWrapper = Wrappers.lambdaQuery(Mc04IslmcNcStockMoveDemandItemDO.class)
                .in(Mc04IslmcNcStockMoveDemandItemDO::getMc04NcStockMoveDemandId, ids);

        List<Mc04IslmcNcStockMoveDemandItemDO> list = mc04IslmcNcStockMoveDemandItemService.list(queryWrapper);


        return Mc04IslmcNcStockMoveDemandItemDOToMc04IslmcNcStockMoveDemandItemConverter.INSTANCE.converterDosToModels(list).stream()
               .collect(Collectors.groupingBy(Mc04IslmcNcStockMoveDemandItem::getMc04NcStockMoveDemandId));
    }

    @Override
    public List<Mc04IslmcNcStockMoveDemandItem> list(String id) {
        LambdaQueryWrapper<Mc04IslmcNcStockMoveDemandItemDO> queryWrapper = Wrappers.lambdaQuery(Mc04IslmcNcStockMoveDemandItemDO.class)
                .eq(Mc04IslmcNcStockMoveDemandItemDO::getMc04NcStockMoveDemandId, id);

        List<Mc04IslmcNcStockMoveDemandItemDO> list = mc04IslmcNcStockMoveDemandItemService.list(queryWrapper);


        return Mc04IslmcNcStockMoveDemandItemDOToMc04IslmcNcStockMoveDemandItemConverter.INSTANCE.converterDosToModels(list);
    }

    @Override
    public Boolean createBatch(List<Mc04IslmcNcStockMoveDemandItem> items) {
        items.forEach(item -> item.setMc04NcStockMoveDemandItemId(IdUtil.simpleUUID()));
        return mc04IslmcNcStockMoveDemandItemService.saveBatch(
                Mc04IslmcNcStockMoveDemandItemDOToMc04IslmcNcStockMoveDemandItemConverter.INSTANCE.converterModelsToDos(items), 1000);
    }

    @Override
    public Boolean delete(String mc04NcStockMoveDemandId) {
        return mc04IslmcNcStockMoveDemandItemService.remove(
                Wrappers.lambdaQuery(Mc04IslmcNcStockMoveDemandItemDO.class)
                        .eq(Mc04IslmcNcStockMoveDemandItemDO::getMc04NcStockMoveDemandId, mc04NcStockMoveDemandId));
    }
}
