package com.tobacco.app.isale.infrastructure.repository.basic;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomPage;
import com.inspur.ind.constant.CommonConstants;
import com.tobacco.app.isale.domain.model.basic.Mc04IslmInterfaceLog;
import com.tobacco.app.isale.domain.model.basic.Mc04IslmInterfaceLogPage;
import com.tobacco.app.isale.domain.repository.basic.InterfaceLogRepository;
import com.tobacco.app.isale.infrastructure.converter.basic.Mc04IslmInterfaceLogDoToMc04IslmInterfaceLogConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmInterfaceLogDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmInterfaceLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: renyonghui
 * @Since: 2025-08-14
 */
@Component("ISaleManageServiceMc04IslmInterfaceLogDORepository")
public class Mc04IslmInterfaceLogRepositoryImpl implements InterfaceLogRepository {

    private Mc04IslmInterfaceLogService mc04IslmInterfaceLogService;

    @Autowired
    public void setMc04IslmInterfaceLogService(Mc04IslmInterfaceLogService mc04IslmInterfaceLogService) {
        this.mc04IslmInterfaceLogService = mc04IslmInterfaceLogService;
    }

    /**
     * 分页查询
     *
     * @param interfaceLogPage 查询参数
     * @return 分页结果
     */
    @Override
    public Page<Mc04IslmInterfaceLog> page(Mc04IslmInterfaceLogPage interfaceLogPage) {

        CustomPage<Mc04IslmInterfaceLogDO> page = new CustomPage<>(
                interfaceLogPage.getOffset(),
                interfaceLogPage.getLimit(),
                interfaceLogPage.getSort(),
                interfaceLogPage.getOrder()
        );
        Page<Mc04IslmInterfaceLogDO> result = mc04IslmInterfaceLogService.lambdaQuery()
                .eq(StrUtil.isNotBlank(interfaceLogPage.getMc04InterfaceLogChannel()),
                        Mc04IslmInterfaceLogDO::getMc04InterfaceLogChannel,
                        interfaceLogPage.getMc04InterfaceLogChannel())
                .eq(StrUtil.isNotBlank(interfaceLogPage.getMc04InterfaceLogBusiType()),
                        Mc04IslmInterfaceLogDO::getMc04InterfaceLogBusiType,
                        interfaceLogPage.getMc04InterfaceLogBusiType())
                .eq(StrUtil.isNotBlank(interfaceLogPage.getMc04InterfaceLogBusiId()),
                        Mc04IslmInterfaceLogDO::getMc04InterfaceLogBusiId,
                        interfaceLogPage.getMc04InterfaceLogBusiId())
                .eq(StrUtil.isNotBlank(interfaceLogPage.getMc04InterfaceLogStatus()),
                        Mc04IslmInterfaceLogDO::getMc04InterfaceLogStatus,
                        interfaceLogPage.getMc04InterfaceLogStatus())
                .eq(StrUtil.isNotBlank(interfaceLogPage.getMc04InterfaceLogCompStatus()),
                        Mc04IslmInterfaceLogDO::getMc04InterfaceLogCompStatus,
                        interfaceLogPage.getMc04InterfaceLogCompStatus())
                .eq(StrUtil.isNotBlank(interfaceLogPage.getMc04InterfaceLogSystemCode()),
                        Mc04IslmInterfaceLogDO::getMc04InterfaceLogSystemCode,
                        interfaceLogPage.getMc04InterfaceLogSystemCode())
                .page(page);

        return (Page<Mc04IslmInterfaceLog>) result.convert(
                Mc04IslmInterfaceLogDoToMc04IslmInterfaceLogConverter.INSTANCE::converterDoToModel);
    }

    /**
     * 获取全部失败请求
     *
     * @return 全部失败请求
     */
    @Override
    public List<Mc04IslmInterfaceLog> getFailedRequest(List<String> interfaceLogIdIdList) {
        List<Mc04IslmInterfaceLogDO> list = mc04IslmInterfaceLogService.lambdaQuery()
                .eq(Mc04IslmInterfaceLogDO::getMc04InterfaceLogStatus, CommonConstants.NO)
                .eq(Mc04IslmInterfaceLogDO::getMc04InterfaceLogCompStatus, CommonConstants.NO)
                .in(CollUtil.isNotEmpty(interfaceLogIdIdList),
                        Mc04IslmInterfaceLogDO::getMc04InterfaceLogId, interfaceLogIdIdList)
                .list();
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return Mc04IslmInterfaceLogDoToMc04IslmInterfaceLogConverter.INSTANCE.converterDosToModels(list);
    }

    /**
     * 更新补偿状态
     *
     * @param logIdList 主键
     * @return 是否成功
     */
    @Override
    public boolean updateCompStatus(List<String> logIdList) {
        mc04IslmInterfaceLogService.lambdaUpdate()
                .in(Mc04IslmInterfaceLogDO::getMc04InterfaceLogId, logIdList)
                .set(Mc04IslmInterfaceLogDO::getMc04InterfaceLogCompStatus, CommonConstants.YES)
                .update();
        return true;
    }
}