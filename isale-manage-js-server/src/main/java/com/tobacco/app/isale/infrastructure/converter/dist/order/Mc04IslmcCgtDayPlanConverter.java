/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.order;

import com.tobacco.app.isale.domain.model.order.dist.order.DistOrderDayPlan;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtDayPlanDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/08/22 09:18
 * @description : TODO
 */
@Mapper
public interface Mc04IslmcCgtDayPlanConverter extends StructureBaseConverter<Mc04IslmcCgtDayPlanDO, DistOrderDayPlan> {
    Mc04IslmcCgtDayPlanConverter SELF = Mappers.getMapper(Mc04IslmcCgtDayPlanConverter.class);
}
