/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcComItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcComItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcComItemService;
import org.springframework.stereotype.Service;


/**
 * @description : 服务实现类
 *
 * <AUTHOR> wang<PERSON><PERSON>
 * @since : 2025-04-22
 * @email : <EMAIL>
 * @create_time : 2025-04-22
 */
@Service
public class Mc04IslmcComItemServiceImpl extends ServiceImpl<Mc04IslmcComItemMapper, Mc04IslmcComItemDO> implements Mc04IslmcComItemService {

}