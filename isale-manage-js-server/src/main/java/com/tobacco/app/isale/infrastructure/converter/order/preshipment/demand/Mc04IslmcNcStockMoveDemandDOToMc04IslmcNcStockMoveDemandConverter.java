/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.order.preshipment.demand;

import com.alibaba.bizworks.extension.core.converter.IExtensionConverter;
import com.tobacco.app.isale.app.converter.BaseConverter;
import com.tobacco.app.isale.domain.model.order.inventory.dailyinventory.Mc04IslmcCgtStockDay;
import com.tobacco.app.isale.domain.model.order.preshipment.demand.Mc04IslmcNcStockMoveDemand;
import com.tobacco.app.isale.dto.order.preshipment.demand.Mc04IslmcNcStockMoveDemandDTO;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtStockDayDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcNcStockMoveDemandDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * @Author: hujiarong
 * @Email: <EMAIL>
 * @Create: 2025-07-21
 */

@Mapper

public interface Mc04IslmcNcStockMoveDemandDOToMc04IslmcNcStockMoveDemandConverter extends StructureBaseConverter<Mc04IslmcNcStockMoveDemandDO, Mc04IslmcNcStockMoveDemand> {

    Mc04IslmcNcStockMoveDemandDOToMc04IslmcNcStockMoveDemandConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcNcStockMoveDemandDOToMc04IslmcNcStockMoveDemandConverter.class);
}