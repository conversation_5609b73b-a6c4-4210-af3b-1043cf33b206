/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcComDemandFoItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcComDemandFoItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcComDemandFoItemService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: hujiarong
 * @Since: 2025-09-05
 * @Email: <EMAIL>
 * @Create: 2025-09-05
        */
@Service
public class Mc04IslmcComDemandFoItemServiceImpl extends ServiceImpl<Mc04IslmcComDemandFoItemMapper, Mc04IslmcComDemandFoItemDO> implements Mc04IslmcComDemandFoItemService {

}