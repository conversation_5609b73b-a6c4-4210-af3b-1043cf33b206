/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.agreement.basic;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcBatch;
import com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcBatchIndex;
import com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcBatchLine;
import com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcDemandFoIndex;
import com.tobacco.app.isale.domain.repository.agreement.basic.IslmcBatchRepository;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcBatchDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcBatchIndexDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcBatchLineDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcDemandFoIndexDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcBatchIndexService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcBatchLineService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcBatchService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcDemandFoIndexService;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> jinfuli
 * @Email : @inspur.com
 * @Create : 2025/04/24 19:24
 * @Description :
 */
@Component
public class IslmcBatchRepositoryImpl implements IslmcBatchRepository {

    private final Mc04IslmcBatchService mc04IslmcBatchService;
    private final Mc04IslmcDemandFoIndexService mc04IslmcDemandFoIndexService;
    private final Mc04IslmcBatchIndexService mc04IslmcBatchIndexService;
    private final Mc04IslmcBatchLineService mc04IslmcBatchLineService;

    public IslmcBatchRepositoryImpl(Mc04IslmcBatchService mc04IslmcBatchService,
                                    Mc04IslmcDemandFoIndexService mc04IslmcDemandFoIndexService,
                                    Mc04IslmcBatchIndexService mc04IslmcBatchIndexService,
                                    Mc04IslmcBatchLineService mc04IslmcBatchLineService) {
        this.mc04IslmcBatchService = mc04IslmcBatchService;
        this.mc04IslmcDemandFoIndexService = mc04IslmcDemandFoIndexService;
        this.mc04IslmcBatchIndexService = mc04IslmcBatchIndexService;
        this.mc04IslmcBatchLineService = mc04IslmcBatchLineService;
    }

    /**
     * 查询批次数量
     *
     * @param cgtType
     * @param batchType
     * @param dateType
     * @param dateCode
     * @return
     */
    @Override
    public Integer queryBatchQty(
           String cgtType,
           String batchType,
           String dateType,
           String dateCode) {

        QueryWrapper<Mc04IslmcBatchDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Mc04IslmcBatchDO::getMa02TobaProdTradeTypeCode, cgtType)
                .eq(Mc04IslmcBatchDO::getMc04BatchType, batchType)
                .eq(Mc04IslmcBatchDO::getMc04DatePeriodType, dateType)
                .eq(Mc04IslmcBatchDO::getMc04DatePeriodCode, dateCode);
        long count = mc04IslmcBatchService.count(queryWrapper);
        return (int) count;
    }

    /**
     * 上报指标查询
     * @return
     */
    @Override
    public List<Mc04IslmcDemandFoIndex> queryReportIndex() {
        QueryWrapper<Mc04IslmcDemandFoIndexDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Mc04IslmcDemandFoIndexDO::getZaEnableStatus, "1");
        List<Mc04IslmcDemandFoIndexDO> list = mc04IslmcDemandFoIndexService.list(queryWrapper);
        return BeanUtil.copyToList(list, Mc04IslmcDemandFoIndex.class);
    }

    /**
     * 保存批次信息
     * @param mc04IslmcBatch 包含批次详细信息的对象
     * @return
     */
    @Override
    public Boolean saveImcBatch(
           Mc04IslmcBatch mc04IslmcBatch) {
        Mc04IslmcBatchDO mc04IslmcBatchDO = BeanUtil.copyProperties(mc04IslmcBatch, Mc04IslmcBatchDO.class);
        return mc04IslmcBatchService.save(mc04IslmcBatchDO);
    }

    /**
     * 保存批次指标
     * @param mc04IslmcBatchIndexList 指标列表
     * @return
     */
    @Override
    public Boolean saveBatchIndexList(List<Mc04IslmcBatchIndex> mc04IslmcBatchIndexList) {
        List<Mc04IslmcBatchIndexDO> mc04IslmcBatchIndexDOS = BeanUtil.copyToList(mc04IslmcBatchIndexList, Mc04IslmcBatchIndexDO.class);
        return mc04IslmcBatchIndexService.saveBatch(mc04IslmcBatchIndexDOS);
    }

    /**
     * 保存批次明细
     * @param mc04IslmcBatchLines 批次明细列表
     * @return
     */
    @Override
    public Boolean saveBatchLineList(List<Mc04IslmcBatchLine> mc04IslmcBatchLines) {
        List<Mc04IslmcBatchLineDO> mc04IslmcBatchLineDOS = BeanUtil.copyToList(mc04IslmcBatchLines, Mc04IslmcBatchLineDO.class);
        return mc04IslmcBatchLineService.saveBatch(mc04IslmcBatchLineDOS);
    }

    /**
     * 更新批次状态
     * @param mc04IslmcBatchS 批次列表
     * @return
     */
    @Override
    public Boolean updateBatchStatus(
          List<Mc04IslmcBatch> mc04IslmcBatchS) {
        List<Mc04IslmcBatchDO> mc04IslmcBatchDOS = BeanUtil.copyToList(mc04IslmcBatchS, Mc04IslmcBatchDO.class);
        return mc04IslmcBatchService.updateBatchById(mc04IslmcBatchDOS);
    }
}