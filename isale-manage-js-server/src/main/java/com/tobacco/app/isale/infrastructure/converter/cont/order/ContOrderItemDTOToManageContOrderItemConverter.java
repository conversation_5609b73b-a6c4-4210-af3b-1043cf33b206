/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.cont.order;

import com.tobacco.app.isale.domain.model.cont.order.ManageContOrderItem;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isalecenter.client.dto.cont.order.Mc04IslmcContOrderItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/06/12 17:11
 * @description : 订单DTO转换成订单
 */
@Mapper
public interface ContOrderItemDTOToManageContOrderItemConverter
        extends StructureBaseConverter<Mc04IslmcContOrderItemDTO, ManageContOrderItem> {

    ContOrderItemDTOToManageContOrderItemConverter INSTANCE = Mappers.getMapper(ContOrderItemDTOToManageContOrderItemConverter.class);

}
