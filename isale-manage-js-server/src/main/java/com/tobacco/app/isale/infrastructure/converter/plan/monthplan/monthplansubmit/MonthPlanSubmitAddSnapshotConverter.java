/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.monthplan.monthplansubmit;


import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmitAdd;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanSnapshotDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jxy
 * @Date: 2025/8/16
 * @Description:
 */
@Mapper
public interface MonthPlanSubmitAddSnapshotConverter extends StructureBaseConverter<Mc04IslmMonthSalePlanSnapshotDO, MonthPlanSubmitAdd> {

    MonthPlanSubmitAddSnapshotConverter INSTANCE = Mappers.getMapper(MonthPlanSubmitAddSnapshotConverter.class);

}
