/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.order;

import com.tobacco.app.isale.domain.model.order.dist.order.DistOrder;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtDistOrderDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jinfuli
 * @Date: 2025/8/11
 * @Description:
 */
@Mapper
public interface DistOrderConverter extends StructureBaseConverter<Mc04IslmCgtDistOrderDO, DistOrder> {
    DistOrderConverter SELF = Mappers.getMapper(DistOrderConverter.class);
}
