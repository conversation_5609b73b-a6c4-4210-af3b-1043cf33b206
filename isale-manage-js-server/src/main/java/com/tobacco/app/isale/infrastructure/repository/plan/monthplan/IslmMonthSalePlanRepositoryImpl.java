/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.monthplan;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.tobacco.app.isale.domain.enums.plan.monthplan.MonthSalePlanStatusEnum;
import com.tobacco.app.isale.domain.enums.plan.monthplan.SalePlanVersionEnum;
import com.tobacco.app.isale.domain.enums.plan.monthplan.TobaProdTradeTypeCodeEnum;
import com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan;
import com.tobacco.app.isale.domain.repository.plan.monthplan.IslmMonthSalePlanRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.Mc04IslmMonthSalePlanDOToMc04IslmMonthSalePlanConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanService;
import com.tobacco.app.isalecenter.client.api.plan.monthplan.MonthSalePlanServiceAPI;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2025/8/6 14:44
 * @version: V1.0
 * @description:
 **/
@Slf4j
@Component("IslmMonthSalePlanRepositoryImplDORepository")
public class IslmMonthSalePlanRepositoryImpl implements IslmMonthSalePlanRepository {
    private static Logger logger = LoggerFactory.getLogger(MonthSalePlanRepositoryImpl.class);

    private MonthSalePlanServiceAPI monthSalePlanServiceAPI;

    @Autowired
    public void setMonthSalePlanServiceAPI(MonthSalePlanServiceAPI monthSalePlanServiceAPI) {
        this.monthSalePlanServiceAPI = monthSalePlanServiceAPI;
    }

    private Mc04IslmMonthSalePlanService mc04IslmMonthSalePlanService;
    @Autowired
    public void setMc04IslmMonthSalePlanService(Mc04IslmMonthSalePlanService mc04IslmMonthSalePlanService) {
        this.mc04IslmMonthSalePlanService = mc04IslmMonthSalePlanService;
    }

    /**
     *
     * @param ma02PlanMonth 计划月份
     * @param baComOrgCodes 商业公司编码，分割
     * @return List<Mc04IslmMonthSalePlan>
     * @auth guosong02
     * @create_time : 2025-08-06 11:33:37
     * @description : 获取月计划上报查询列表
     */
    @Override
    public List<Mc04IslmMonthSalePlan> getMonthSalePlanList(String ma02PlanMonth, String baComOrgCodes, TobaProdTradeTypeCodeEnum tobaProdTradeTypeCodeEnum) {
        List<Mc04IslmMonthSalePlanDO> mc04IslmMonthSalePlanDOS = mc04IslmMonthSalePlanService.lambdaQuery()
                .ge(Mc04IslmMonthSalePlanDO::getMc04MonthSalePlanStatus,MonthSalePlanStatusEnum.TO_RELEASED.getCode()).
                eq(Mc04IslmMonthSalePlanDO::getMa02PlanMonth, ma02PlanMonth)
                .eq(Mc04IslmMonthSalePlanDO::getMa02TobaProdTradeTypeCode,tobaProdTradeTypeCodeEnum.getCode())
                .eq(Mc04IslmMonthSalePlanDO::getMc04CgtSalePlanVersion, SalePlanVersionEnum.N_PLUS1.getCode())
                .in(Mc04IslmMonthSalePlanDO::getBaComOrgCode, baComOrgCodes.split(","))
                .orderByAsc(Mc04IslmMonthSalePlanDO::getCreateTime)
                .list();
        return Mc04IslmMonthSalePlanDOToMc04IslmMonthSalePlanConverter.INSTANCE.converterDosToModels(mc04IslmMonthSalePlanDOS);
    }


    @Override
    public boolean batchSave(List<Mc04IslmMonthSalePlan> mc04IslmMonthSalePlans) {
        List<Mc04IslmMonthSalePlanDO> mc04IslmMonthSalePlanDOS = Mc04IslmMonthSalePlanDOToMc04IslmMonthSalePlanConverter.INSTANCE.converterModelsToDos(mc04IslmMonthSalePlans);
        boolean flag = mc04IslmMonthSalePlanService.saveBatch(mc04IslmMonthSalePlanDOS);
        return flag;
    }

    @Override
    public List<Mc04IslmMonthSalePlanDO> queryListByIds(List<String> mc04MonthSalePlanItemIds) {
        List<Mc04IslmMonthSalePlanDO> mc04IslmMonthSalePlanDOS = mc04IslmMonthSalePlanService.listByIds(mc04MonthSalePlanItemIds);
        return mc04IslmMonthSalePlanDOS;
    }

    @Override
    public boolean updateBatch(List<Mc04IslmMonthSalePlanDO> mc04IslmMonthSalePlanDOS) {
        return mc04IslmMonthSalePlanService.updateBatchById(mc04IslmMonthSalePlanDOS);
    }

    @Override
    public boolean batchUpdateStatus(List<String> monthSalePlanItemIds, MonthSalePlanStatusEnum monthSalePlanStatusEnum) {
        UpdateWrapper<Mc04IslmMonthSalePlanDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(Mc04IslmMonthSalePlanDO::getMc04MonthSalePlanItemId)
                .set(Mc04IslmMonthSalePlanDO::getMc04MonthSalePlanStatus, monthSalePlanStatusEnum.getCode());
        return mc04IslmMonthSalePlanService.update(updateWrapper);
    }
}
