/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmYcSalePlanItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmYcSalePlanItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmYcSalePlanItemService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: loongxi
 * @Since: 2025-08-06
 * @Email: <EMAIL>
 * @Create: 2025-08-06
 */
@Service
public class Mc04IslmYcSalePlanItemServiceImpl extends ServiceImpl<Mc04IslmYcSalePlanItemMapper, Mc04IslmYcSalePlanItemDO> implements Mc04IslmYcSalePlanItemService {

}