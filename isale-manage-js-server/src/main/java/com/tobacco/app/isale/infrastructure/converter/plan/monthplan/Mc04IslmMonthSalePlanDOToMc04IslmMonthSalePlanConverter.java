/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.monthplan;

import com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadjreview.Mc04IslmMonthSalePlanAdj;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> guosong02
 * @Email : <EMAIL>
 * @Create : 2025/08/06 11:28
 * @Description : 类型转化
 */
@Mapper
public interface Mc04IslmMonthSalePlanDOToMc04IslmMonthSalePlanConverter extends StructureBaseConverter<Mc04IslmMonthSalePlanDO, Mc04IslmMonthSalePlan> {
    Mc04IslmMonthSalePlanDOToMc04IslmMonthSalePlanConverter INSTANCE = Mappers.getMapper(Mc04IslmMonthSalePlanDOToMc04IslmMonthSalePlanConverter.class);

}
