/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.basic;



import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.model.basic.ManageIslmcComRecType;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmit;
import com.tobacco.app.isale.domain.repository.basic.ManageIslmcComRecTypeRepository;
import com.tobacco.app.isale.infrastructure.converter.basic.Mc04IslmcComRecTypeDOToManageIslmcComRecTypeConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcComRecTypeDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcComRecTypeService;
import com.tobacco.app.isale.tools.utils.CustUtil;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR> zhangshch01
 * @email : <EMAIL>
 * @create_time : 2025/08/05 16:00
 * @description : 销售管理-基础数据-客户结算方式 infrastructureRepository
 */
@Slf4j
@Component("ISaleManageIslmcComRecTypeRepositoryImplDORepository")
public class ManageIslmcComRecTypeRepositoryImpl implements ManageIslmcComRecTypeRepository {

    @Autowired
    private Mc04IslmcComRecTypeService mc04IslmcComRecTypeService;


    @Override
    public List<ManageIslmcComRecType> list(String baComOrgCode, String zbSettlementModeCode) {
        QueryWrapper<Mc04IslmcComRecTypeDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .in(StrUtil.isNotBlank(baComOrgCode), Mc04IslmcComRecTypeDO::getBaComOrgCode, Arrays.asList(baComOrgCode.split(",")))
                .eq(StrUtil.isNotBlank(zbSettlementModeCode), Mc04IslmcComRecTypeDO::getZbSettlementModeCode, zbSettlementModeCode)
                .eq(Mc04IslmcComRecTypeDO::getIcomCode, IcomUtils.getIcomCode());
        List<Mc04IslmcComRecTypeDO> list = mc04IslmcComRecTypeService.list(wrapper);
        if (list == null || list.isEmpty()) {
            // 表中未查询到数据 直接返回
            return Collections.emptyList();
        }
        List<String> baComOrgCodeList = list.stream().map(Mc04IslmcComRecTypeDO::getBaComOrgCode).collect(Collectors.toList());
        // 调用客户中心 获取商业公司简称
        Map<String, BusiComDto> busiComDtoMap = CustUtil.getBusiComDtoMap(baComOrgCodeList);
        List<ManageIslmcComRecType> manageIslmcComRecTypes = new ArrayList<>();
        list.forEach(item -> {
            BusiComDto busiComDto = busiComDtoMap.get(item.getBaComOrgCode());
            ManageIslmcComRecType manageIslmcComRecType = new ManageIslmcComRecType();
            manageIslmcComRecType.setBaComOrgCode(item.getBaComOrgCode());
            manageIslmcComRecType.setZbSettlementModeCode(item.getZbSettlementModeCode());
            if (busiComDto != null) {
                manageIslmcComRecType.setMc04ComOrgShortName(busiComDto.getMc04ComOrgShortName());
                manageIslmcComRecType.setMc04ComOrgIsImported(busiComDto.getMc04ComOrgIsImported());
                manageIslmcComRecType.setSeq(busiComDto.getSeq());
            }
            manageIslmcComRecTypes.add(manageIslmcComRecType);
        });


        // 按 productSeq 字段排序
        manageIslmcComRecTypes.sort(Comparator
                .comparing(ManageIslmcComRecType::getMc04ComOrgIsImported, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(ManageIslmcComRecType::getSeq, Comparator.nullsLast(Comparator.naturalOrder()))
        );

        return manageIslmcComRecTypes;
    }

    @Override
    public Boolean save(List<ManageIslmcComRecType> manageIslmcComRecTypes) {
        if (manageIslmcComRecTypes == null || manageIslmcComRecTypes.isEmpty()) {
            return false;
        }
        // 先删后插
        List<String> baComOrgCodes = manageIslmcComRecTypes.stream().map(ManageIslmcComRecType::getBaComOrgCode).collect(Collectors.toList());
        QueryWrapper<Mc04IslmcComRecTypeDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .in(!baComOrgCodes.isEmpty(), Mc04IslmcComRecTypeDO::getBaComOrgCode, baComOrgCodes)
                .eq(Mc04IslmcComRecTypeDO::getIcomCode, IcomUtils.getIcomCode());
        mc04IslmcComRecTypeService.remove(wrapper);
        List<Mc04IslmcComRecTypeDO> mc04IslmcComRecTypeDoS = Mc04IslmcComRecTypeDOToManageIslmcComRecTypeConverter.INSTANCE.converterModelsToDos(manageIslmcComRecTypes);
        return mc04IslmcComRecTypeService.saveBatch(mc04IslmcComRecTypeDoS);
    }
}