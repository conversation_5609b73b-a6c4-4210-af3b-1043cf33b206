/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.monthplan.monthplanadjreview;

import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadj.MonthPlanAdjItemAdd;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanAdjDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> jxy
 * @Email : @inspur.com
 * @Create : 2025/08/14
 * @Description : 类型转化
 */
@Mapper
public interface MonthPlanAdjReviewFlowConverter extends StructureBaseConverter<Mc04IslmMonthSalePlanAdjDO, MonthPlanAdjItemAdd> {
    MonthPlanAdjReviewFlowConverter INSTANCE = Mappers.getMapper(MonthPlanAdjReviewFlowConverter.class);
}
