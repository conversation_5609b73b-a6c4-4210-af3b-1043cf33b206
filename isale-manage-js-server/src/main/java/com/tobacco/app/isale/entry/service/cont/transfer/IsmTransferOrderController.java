/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */

package com.tobacco.app.isale.entry.service.cont.transfer;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.bizworks.core.specification.ddd.ApplicationService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.annotation.ControllerAroundLog;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.base.IndResult;
import com.inspur.ind.icom.IcomUtils;
import com.inspur.ind.uc.permission.PermissionUtils;
import com.tobacco.app.isale.app.api.cont.transfer.TransferOrderService;
import com.tobacco.app.isale.domain.model.cont.transfer.TransferSubmitMessage;
import com.tobacco.app.isale.dto.basic.CgtStockDTO;
import com.tobacco.app.isale.dto.basic.ContDelivWhseDTO;
import com.tobacco.app.isale.dto.common.BaseControllerPage;
import com.tobacco.app.isale.dto.cont.transfer.*;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtStockDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseItemTrayDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcForwardWhseComDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcCgtStockService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContDelivWhseItemTrayService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContDelivWhseService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcForwardWhseComService;
import com.tobacco.app.isale.req.cont.transfer.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR> wuhaoran01
 * @email : <EMAIL>
 * @create_time : 2025/05/19
 * @description : 前置库移库单数据查询
 */
@RestController
@RequestMapping("/ISaleManage/cont/transfer/transferOrder")
@Slf4j
@Api(value = "IsmTransferOrderController", tags = "IsmTransferOrderController管理")
@ApplicationService(domain = "Isale", name = "IsmTransferOrderController管理", desc = "IsmTransferOrderController管理")
public class IsmTransferOrderController {
    @Autowired
    private TransferOrderService transferOrderService;
    @Autowired
    private Mc04IslmcContDelivWhseService mc04IslmcContDelivWhseService;
    @Autowired
    private Mc04IslmcForwardWhseComService mc04IslmcForwardWhseComService;
    @Autowired
    private Mc04IslmcCgtStockService cgtStockService;
    @Autowired
    private Mc04IslmcContDelivWhseItemTrayService trayService;


    @PostMapping("/queryTransferData")
    @ApiOperation(value = "分页查询")
    @ControllerAroundLog(value = "分页查询")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-add"}, logical = Logical.OR)
    public IndResult<BaseControllerPage<WarehouseStockTranBillDTO>> queryTransferData(@Validated @NotNull @RequestBody TransferOrderCenterREQ req) {
        try {
            log.info("分页查询参数:{}", req);
            //添加权限控制
            Page<WarehouseStockTranBillDTO> transferData = transferOrderService.queryTransferData(req);
            transferData.getRecords().forEach(WarehouseStockTranBillDTO::changeUnit);
            return IndResult.success(BaseControllerPage.of(transferData));
        } catch (Exception e) {
            log.error(e.getMessage());
            return IndResult.fail("500", "获取数据失败，请联系管理员！");
        }
    }

    @PostMapping("/queryMonthPlanData")
    @ApiOperation(value = "月计划查询")
    @ControllerAroundLog(value = "月计划查询")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-add"}, logical = Logical.OR)
    public IndResult<BaseControllerPage<TransferMonthPlanDTO>> queryMonthPlanData(@Validated @NotNull @RequestBody TransferOrderCenterREQ req) {
        try {
            log.info("分页查询参数:{}", req);
            if (StringUtils.isEmpty(req.getBaComOrgCode())) {
                String baComCode = String.join(",", PermissionUtils.getComCodeSceneAuthList());
                req.setBaComOrgCode(baComCode);
            }
            req.setIcomCode(IcomUtils.getIcomCode());
            Page<TransferMonthPlanDTO> monthPlanData = transferOrderService.queryMonthPlanData(req);
            monthPlanData.getRecords().forEach(TransferMonthPlanDTO::changeUnit);
            return IndResult.success(BaseControllerPage.of(monthPlanData));
        } catch (Exception e) {
            log.error(e.getMessage());
            return IndResult.fail("500", "获取数据失败，请联系管理员！");
        }
    }

    @PostMapping("/queryStockData")
    @ApiOperation(value = "库存查询")
    @ControllerAroundLog(value = "库存查询")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-add"}, logical = Logical.OR)
    public IndResult<BaseControllerPage<StockDTO>> queryStockData(@Validated @NotNull @RequestBody TransferOrderCenterREQ req) {
        try {
            log.info("分页查询参数:{}", req);
            req.setIcomCode(IcomUtils.getIcomCode());
            Page<StockDTO> stockData = transferOrderService.queryStockData(req);
            stockData.getRecords().forEach(StockDTO::changeUnit);
            return IndResult.success(BaseControllerPage.of(stockData));
        } catch (Exception e) {
            log.error(e.getMessage());
            return IndResult.fail("获取数据失败，请联系管理员！");
        }
    }

    @PostMapping("/createOrUpdate")
    @ApiOperation(value = "新增或更新表单")
    @ControllerAroundLog(value = "新增或更新表单")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-add"}, logical = Logical.OR)
    public IndResult<String> createOrUpdate(@Validated @NotNull @RequestBody TransferOrderSaveCenterREQ saveReq) {
        try {
            saveReq.setIcomCode(IcomUtils.getIcomCode());
            saveReq.getDataList().forEach(TransferOrderDetailDTO::changeUnitToWan);
            if (StringUtils.isBlank(saveReq.getMc04WarehouseStockTranBillId())) {
                saveReq.setMc04WarehouseStockTranBillId(IcomUtils.getNextId("WAREHOUSE_STOCK_TRAN_BILL_CODE"));
            }
            Boolean isSuccess = transferOrderService.createOrUpdate(saveReq);
            if (!isSuccess) {
                throw new CustomException("新建或更新失败！");
            }
            return IndResult.success(saveReq.getMc04WarehouseStockTranBillId());
        } catch (Exception e) {
            log.error(e.getMessage());
            return IndResult.fail("新增移库单失败，请联系管理员！");
        }
    }


    @PostMapping("/getOutBaComList")
    @ApiOperation(value = "获取移出仓库")
    @ControllerAroundLog(value = "获取移出仓库")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-add"}, logical = Logical.OR)
    public IndResult<List<ContDelivWhseDTO>> getOutBaComList(@Validated @NotNull @RequestBody TransferOrderCenterREQ req) {
        try {
            String baComOrgCode = req.getBaComOrgCode();
            String icomCode = IcomUtils.getIcomCode();
            if (StringUtils.isBlank(baComOrgCode)) {
                return IndResult.fail("工业公司编码不能为空!!!！");
            }
            List<Mc04IslmcForwardWhseComDO> comDOList = mc04IslmcForwardWhseComService.lambdaQuery()
                    .eq(Mc04IslmcForwardWhseComDO::getBaComOrgCode, baComOrgCode)
                    .eq(Mc04IslmcForwardWhseComDO::getIcomCode, icomCode)
                    .list();
            List<String> outComIdList = new ArrayList<>();
            if (!comDOList.isEmpty()) {
                for (Mc04IslmcForwardWhseComDO comDO : comDOList) {
                    outComIdList.add(comDO.getMd02CgtOutStorehouseCode());
                }
            }
            List<Mc04IslmcContDelivWhseDO> list = new ArrayList<>();
            if (!outComIdList.isEmpty()) {
                list = mc04IslmcContDelivWhseService.lambdaQuery()
                        .eq(Mc04IslmcContDelivWhseDO::getIcomCode, icomCode)
                        .eq(Mc04IslmcContDelivWhseDO::getIsUse, "1")
                        .in(Mc04IslmcContDelivWhseDO::getMd02CgtOutStorehouseCode, outComIdList)
                        .list();
            }
            return IndResult.success(BeanUtil.copyToList(list, ContDelivWhseDTO.class));
        } catch (Exception e) {
            log.error(e.getMessage());
            return IndResult.fail("工业公司编码不能为空!!!！");
            // return SaleMultiResponse.failure("500", "获取仓库失败，请联系管理员！");
        }
    }

    @PostMapping("/getInBaComList")
    @ApiOperation(value = "获取移入仓库")
    @ControllerAroundLog(value = "获取移入仓库")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-add"}, logical = Logical.OR)
    public IndResult<List<ContDelivWhseDTO>> getInBaComList(@Validated @NotNull @RequestBody TransferOrderCenterREQ req) {
        try {
            String baComOrgCode = req.getBaComOrgCode();
            String icomCode = IcomUtils.getIcomCode();
            if (StringUtils.isBlank(baComOrgCode)) {
                return IndResult.fail("工业公司编码不能为空！");
            }
            List<Mc04IslmcForwardWhseComDO> comDOList = mc04IslmcForwardWhseComService.lambdaQuery()
                    .eq(Mc04IslmcForwardWhseComDO::getBaComOrgCode, baComOrgCode)
                    .eq(Mc04IslmcForwardWhseComDO::getIcomCode, icomCode)
                    .list();
            List<String> inComIdList = new ArrayList<>();
            if (!comDOList.isEmpty()) {
                for (Mc04IslmcForwardWhseComDO comDO : comDOList) {
                    inComIdList.add(comDO.getMd02CgtInStorehouseCode());
                }
            }
            //有可能重复
            List<Mc04IslmcContDelivWhseDO> list = new ArrayList<>();
            if (!inComIdList.isEmpty()) {
                list = mc04IslmcContDelivWhseService.lambdaQuery()
                        .eq(Mc04IslmcContDelivWhseDO::getIcomCode, icomCode)
                        .eq(Mc04IslmcContDelivWhseDO::getIsUse, "1")
                        .in(Mc04IslmcContDelivWhseDO::getMd02CgtOutStorehouseCode, inComIdList)
                        .list();
            }
            return IndResult.success(BeanUtil.copyToList(list, ContDelivWhseDTO.class));
        } catch (Exception e) {
            log.error(e.getMessage());
            return IndResult.fail("获取仓库失败，请联系管理员！");
        }
    }

    @GetMapping("/getCgtStock")
    @ApiOperation(value = "getInCgtStock")
    @ControllerAroundLog(value = "getCgtStock")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-add"}, logical = Logical.OR)
    public IndResult<List<CgtStockDTO>> getCgtStock(@Validated @NotNull @RequestParam String md02CgtOutStorehouseCode) {
        try {
            if (StringUtils.isBlank(md02CgtOutStorehouseCode)) {
                return IndResult.fail("仓库编码不能为空！");
            }
            List<Mc04IslmcCgtStockDO> list = cgtStockService.lambdaQuery()
                    .eq(Mc04IslmcCgtStockDO::getMd02CgtOutStorehouseCode, md02CgtOutStorehouseCode)
                    .list();
            List<CgtStockDTO> cgtStockDTOS = BeanUtil.copyToList(list, CgtStockDTO.class);
            if (!cgtStockDTOS.isEmpty()) {
                for (CgtStockDTO cgtStockDTO : cgtStockDTOS) {
                    Mc04IslmcContDelivWhseItemTrayDO one = trayService.lambdaQuery()
                            .eq(Mc04IslmcContDelivWhseItemTrayDO::getMd02CgtOutStorehouseCode, cgtStockDTO.getMd02CgtOutStorehouseCode())
                            .eq(Mc04IslmcContDelivWhseItemTrayDO::getAcCgtCartonCode, cgtStockDTO.getAcCgtCartonCode())
                            .eq(Mc04IslmcContDelivWhseItemTrayDO::getAcTwoLevelCigCode, cgtStockDTO.getAcTwoLevelCigCode())
                            .one();
                    if (Objects.nonNull(one)) {
                        cgtStockDTO.setPalletTransQty(one.getMa02LogtIcTrayPalletTransQty());
                    }
                }
            }
            cgtStockDTOS.forEach(CgtStockDTO::changeUnit);
            return IndResult.success(cgtStockDTOS);
        } catch (Exception e) {
            log.error(e.getMessage());
            return IndResult.fail("获取仓库实时库存失败,请联系管理员！");
        }
    }

    @PostMapping("/getTransferDetailById")
    @ApiOperation(value = "获取前置库详情编辑查询")
    @ControllerAroundLog(value = "获取前置库详情编辑查询")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-add"}, logical = Logical.OR)
    public IndResult<HashMap<String, List<TransferOrderDetailDTO>>> getTransferDetailById(@Validated @NotNull @RequestBody TransferOrderCenterREQ req) {
        try {
            String billId = req.getMc04WarehouseStockTranBillId();
            String icomCode = IcomUtils.getIcomCode();
            req.setIcomCode(icomCode);
            if (StringUtils.isBlank(billId)) {
                return IndResult.fail("移库单号不能为空！");
            }
            List<TransferOrderDetailDTO> tranBillItemList =
                    transferOrderService.getTransferDetail(req);
            // 单位转化
            tranBillItemList.forEach(item -> {
                item.changeUnitToBox();
                item.calculateTransferAmt();
            });
            HashMap<String, List<TransferOrderDetailDTO>> stringListHashMap = new HashMap<>();
            if (StringUtils.isBlank(req.getIsHalfYearSurplXyQty())) {
                req.setIsHalfYearSurplXyQty("");
            }
            stringListHashMap.put(req.getIsHalfYearSurplXyQty(), tranBillItemList);
            return IndResult.success(stringListHashMap);
        } catch (Exception e) {
            log.error(e.getMessage());
            return IndResult.fail("获取仓库失败，请联系管理员！");
        }
    }

    @PostMapping("/delBill")
    @ApiOperation(value = "删除记录")
    @ControllerAroundLog(value = "删除记录")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-add"}, logical = Logical.OR)
    public IndResult<Boolean> delBill(@Validated @NotNull @RequestBody TransferOrderCenterREQ req) {
        try {
            String billId = req.getMc04WarehouseStockTranBillId();
            String icomCode = IcomUtils.getIcomCode();
            Boolean delBill = transferOrderService.delBill(billId, icomCode);
            return IndResult.success(delBill);
        } catch (Exception e) {
            log.error(e.getMessage());
            return IndResult.fail("删除移库单失败，请联系管理员！");
        }
    }

    @PostMapping("/getTransferOrderDetail")
    @ApiOperation(value = "获取移库单详情")
    @ControllerAroundLog(value = "获取移库单详情")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-add"}, logical = Logical.OR)
    public IndResult<HashMap<String, List<TransferOrderDetailDTO>>> getTransferOrderDetail(@Validated @NotNull @RequestBody TransferOrderCenterREQ req) {
        try {
            req.setIcomCode(IcomUtils.getIcomCode());
            String md02CgtOutStorehouseCode = req.getMd02CgtOutStorehouseCode();
            String md02CgtInStorehouseCode = req.getMd02CgtInStorehouseCode();
            if (md02CgtInStorehouseCode.trim().isEmpty() && md02CgtOutStorehouseCode.trim().isEmpty()) {
                throw new IllegalArgumentException("移出或移出仓库不能为空！");
            }
            List<TransferOrderDetailDTO> list = transferOrderService.getTransferOrderDetail(req);
            list.forEach(TransferOrderDetailDTO::changeUnitToBox);
            HashMap<String, List<TransferOrderDetailDTO>> stringListHashMap = new HashMap<>();
            if (StringUtils.isBlank(req.getIsHalfYearSurplXyQty())) {
                req.setIsHalfYearSurplXyQty("");
            }
            stringListHashMap.put(req.getIsHalfYearSurplXyQty(), list);
            return IndResult.success(stringListHashMap);
        } catch (Exception e) {
            log.error(e.getMessage());
            return IndResult.fail("获取仓库失败，请联系管理员！");
        }
    }

    @PostMapping("/submit")
    @ApiOperation(value = "submit提交事件-移库单提交和撤回")
    @ControllerAroundLog(value = "submit提交事件-移库单提交和撤回")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-add"}, logical = Logical.OR)
    public IndResult<TransferSubmitMessage> submit(@Validated @NotNull @RequestBody TransferOrderSubmitCenterREQ submitReq) {
        try {
            String billIds = submitReq.getMc04WarehouseStockTranBillId();
            if (StringUtils.isBlank(billIds)) {
                return IndResult.fail("移库单号不能为空！");
            }
            submitReq.setIcomCode(IcomUtils.getIcomCode());
            if ("30".equals(submitReq.getStatus())){
                submitReq.setMc04CgtProcessMode("1");
            } else if ("20".equals(submitReq.getStatus())) {
                submitReq.setMc04CgtProcessMode("3");
            }
            TransferSubmitMessage isSuccess = transferOrderService.submit(submitReq);
            return IndResult.success(isSuccess);
        } catch (Exception e) {
            log.error(e.getMessage());
            return IndResult.fail("递交移库单失败，请联系管理员！");
        }
    }

    @PostMapping("/queryPallet")
    @ApiOperation(value = "查询托盘设置信息")
    @ControllerAroundLog(value = "查询托盘设置信息")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-pallet"}, logical = Logical.OR)
    public IndResult<List<TransferOrderPalletDTO>> queryPallet(@Validated @NotNull @RequestBody TransferOrderQueryPalletCenterREQ req) {
        String icomCode = IcomUtils.getIcomCode();
        String baComOrgCode = req.getBaComOrgCode();
        List<TransferOrderPalletDTO> transferOrderPallets = transferOrderService.queryPallet(baComOrgCode, icomCode);
        transferOrderPallets.forEach(TransferOrderPalletDTO::changeUnit);
        return IndResult.success(transferOrderPallets);
    }

    @PostMapping("/setPalletEnabled")
    @ApiOperation(value = "设置是否托盘")
    @ControllerAroundLog(value = "设置是否托盘")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-pallet"}, logical = Logical.OR)
    public IndResult<Boolean> setPalletEnabled(@Validated @NotNull @RequestBody TransferOrderQueryPalletCenterREQ req) {
        req.setIcomCode(IcomUtils.getIcomCode());
        if (StringUtils.isEmpty(req.getBaComOrgCode())) {
            throw new RuntimeException("商业编码为空！");
        }
        Boolean isEnabled = transferOrderService.setPalletEnabled(req);
        return IndResult.success(isEnabled);
    }

    @GetMapping("/initComTree")
    @ApiOperation(value = "初始化公司树")
    @ControllerAroundLog(value = "初始化公司树")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-add"}, logical = Logical.OR)
    public IndResult<String> initComTree() {
        QueryWrapper<Mc04IslmcForwardWhseComDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT ba_com_org_code");
        String code = String.join(",", mc04IslmcForwardWhseComService.listObjs(queryWrapper, Object::toString));
        return IndResult.success(code);
    }

    @PostMapping("/queryWeekSelect")
    @ApiOperation(value = "初始化周选择下拉框")
    @ControllerAroundLog(value = "初始化周选择下拉框")
    @RequiresPermissions(value = {"isale-cont-transfer-transferorder", "isale-cont-transfer-transferorder-add"}, logical = Logical.OR)
    public IndResult<List<TransferOrderWeekSelectDTO>> queryWeekSelect(@Validated @NotNull @RequestBody TransferOrderWeekSelectCenterREQ req) {
        List<TransferOrderWeekSelectDTO> transferOrderWeekPlanSelectDTOS =
                transferOrderService.queryWeekSelect(req);
        return IndResult.success(transferOrderWeekPlanSelectDTOS);
    }

}

