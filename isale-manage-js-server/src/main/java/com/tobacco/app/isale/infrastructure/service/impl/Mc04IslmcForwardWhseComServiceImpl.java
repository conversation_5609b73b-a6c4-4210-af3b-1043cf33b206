/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcForwardWhseComDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcForwardWhseComMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcForwardWhseComService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: renyonghui
 * @Since: 2025-05-20
 * @Email: <EMAIL>
 * @Create: 2025-05-20
 */
@Service
public class Mc04IslmcForwardWhseComServiceImpl extends ServiceImpl<Mc04IslmcForwardWhseComMapper, Mc04IslmcForwardWhseComDO> implements Mc04IslmcForwardWhseComService {

}