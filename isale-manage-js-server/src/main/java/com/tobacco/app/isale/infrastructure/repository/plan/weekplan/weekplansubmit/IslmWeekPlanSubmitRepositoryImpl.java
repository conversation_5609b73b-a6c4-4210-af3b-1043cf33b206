/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有（C）浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.weekplan.weekplansubmit;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.model.date.Mc04IndDataPeriod;
import com.tobacco.app.isale.domain.model.plan.weekplan.weekplansubmit.IslmWeekPlanSubmitQueryResult;
import com.tobacco.app.isale.domain.model.plan.weekplan.weekplansubmit.WeekPlanSubmitAdd;
import com.tobacco.app.isale.domain.repository.plan.weekplan.weekplansubmit.IslmWeekPlanSubmitRepository;
import com.tobacco.app.isale.infrastructure.converter.dist.order.IndDataPeriodConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.weekplan.weekplansubmit.IslmcWeekPlanConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.weekplan.weekplansubmit.Mc04IslmWeekSalePlanConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.weekplan.weekplansubmit.Mc04IslmWeekSalePlanDoToWeekSalePlanRequestConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IndDataPeriodDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmWeekSalePlanDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IndDataPeriodService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmWeekSalePlanService;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.weekplan.weekplansubmit.IslmWeekPlanSubmitMapper;
import com.tobacco.app.isalecenter.client.api.plan.weekplan.WeekSalePlanServiceAPI;
import com.tobacco.app.isalecenter.client.req.plan.weekplan.WeekSalePlanRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/08/05 08:47
 * @description 周计划上报仓储接口
 */
@Component
public class IslmWeekPlanSubmitRepositoryImpl implements IslmWeekPlanSubmitRepository {

    @Autowired
    private IslmWeekPlanSubmitMapper islmWeekPlanSubmitMapper;

    @Autowired
    private IslmcWeekPlanConverter converter;

    @Autowired
    private Mc04IndDataPeriodService mc04IndDataPeriodService;

    @Autowired
    private Mc04IslmWeekSalePlanService mc04IslmWeekSalePlanService;

    @Autowired
    WeekSalePlanServiceAPI weekSalePlanServiceApi;


    /**
     * 查询周计划上报列表
     *
     * @param planMonth
     * @param acCgtCartonCodes
     * @param baComOrgCode
     * @param status
     * @return 周计划上报列表
     */
    @Override
    public List<IslmWeekPlanSubmitQueryResult> findPlansByCondition(String planMonth, List<String> acCgtCartonCodes, String baComOrgCode, String status) {
        return islmWeekPlanSubmitMapper.findPlansByCondition(planMonth, acCgtCartonCodes, baComOrgCode,  status);
    }

    /**
     * 获取月计划总量
     *
     * @param planMonth 计划月份
     * @param acTwoLevelCigCodes 卷烟二级牌号编码
     * @param authorizedComIds 授权公司编码列表
     * @return 月计划总量
     */
    @Override
    public List<Map<String, Object>> getMonthlyPlanTotal(String planMonth, List<String> acTwoLevelCigCodes, List<String> authorizedComIds) {
        return islmWeekPlanSubmitMapper.getMonthlyPlanTotal(planMonth, acTwoLevelCigCodes, authorizedComIds);
    }

    /**
     * 获取解锁状态
     *
     * @param authorizedComIds 授权公司编码列表
     * @return 解锁状态
     */
    @Override
    public List<Map<String, Object>> getUnlockStatus(List<String> authorizedComIds) {
        return islmWeekPlanSubmitMapper.getUnlockStatus(authorizedComIds);
    }

    /**
     * 获取全国计划总量
     *
     * @param cigCodes 卷烟编码列表
     * @return 全国计划总量
     */
    @Override
    public List<Map<String, Object>> getNationalPlanTotals(List<String> cigCodes) {
        return islmWeekPlanSubmitMapper.getNationalPlanTotals(cigCodes);
    }

    /**
     * 获取数据周期
     *
     * @param mc04IndDataPeriod 数据周期
     * @return 数据周期
     */
    @Override
    public List<Mc04IndDataPeriod> getDataPeriods(Mc04IndDataPeriod mc04IndDataPeriod) {
        LambdaQueryWrapper<Mc04IndDataPeriodDO> queryWrapper = Wrappers.lambdaQuery(Mc04IndDataPeriodDO.class);
        queryWrapper.eq(StringUtils.isNotBlank(mc04IndDataPeriod.getMd04MgOccurrenceMonth()), Mc04IndDataPeriodDO::getMd04MgOccurrenceMonth, mc04IndDataPeriod.getMd04MgOccurrenceMonth())
                .eq(StringUtils.isNotBlank(mc04IndDataPeriod.getZaOccurrenceYear()), Mc04IndDataPeriodDO::getZaOccurrenceYear, mc04IndDataPeriod.getZaOccurrenceYear())
                .eq(StringUtils.isNotBlank(mc04IndDataPeriod.getMc04DatePeriodType()), Mc04IndDataPeriodDO::getMc04DatePeriodType, mc04IndDataPeriod.getMc04DatePeriodType())
                .eq(Mc04IndDataPeriodDO::getIcomCode, IcomUtils.getIcomCode());
        return IndDataPeriodConverter.INSTANCE.converterDosToModels(mc04IndDataPeriodService.list(queryWrapper));
    }

    /**
     * 批量插入周计划
     *
     * @param weekPlanSubmitAdds 周计划列表
     * @return 是否成功
     */
    @Override
    public boolean insertBatchWeekPlan(List<WeekPlanSubmitAdd> weekPlanSubmitAdds) {
        List<Mc04IslmWeekSalePlanDO> mc04IslmWeekSalePlanDos = Mc04IslmWeekSalePlanConverter.INSTANCE.converterModelsToDos(weekPlanSubmitAdds);
        return mc04IslmWeekSalePlanService.saveBatch(mc04IslmWeekSalePlanDos);
    }

    /**
     * 批量更新周计划
     *
     * @param weekPlanSubmitAdds 周计划列表
     * @return 是否成功
     */
    @Override
    public boolean updateBatchWeekPlanById(List<WeekPlanSubmitAdd> weekPlanSubmitAdds) {
        List<Mc04IslmWeekSalePlanDO> mc04IslmWeekSalePlanDos = Mc04IslmWeekSalePlanConverter.INSTANCE.converterModelsToDos(weekPlanSubmitAdds);
        return mc04IslmWeekSalePlanService.updateBatchById(mc04IslmWeekSalePlanDos);
    }

    /**
     * 查询城市状态
     *
     * @param ma02PlanMonth
     * @param icomCode
     * @param mc04SalePlanStatus
     * @return
     */
    @Override
    public List<Map<String, Object>> queryOrgStatus(String ma02PlanMonth, String icomCode, String mc04SalePlanStatus) {
        return islmWeekPlanSubmitMapper.queryOrgStatus(ma02PlanMonth, icomCode, mc04SalePlanStatus);
    }

    @Override
    public Boolean deleteDataByConditions(List<String> ma02PlanMonths, List<String> baComOrgCodes, List<String> mc04DatePeriodCodes, String code) {
        LambdaQueryWrapper<Mc04IslmWeekSalePlanDO> queryWrapper = Wrappers.lambdaQuery(Mc04IslmWeekSalePlanDO.class);
        queryWrapper.eq(StringUtils.isNotBlank(code), Mc04IslmWeekSalePlanDO::getMa02TobaProdTradeTypeCode, code)
                .in(CollectionUtils.isNotEmpty(ma02PlanMonths), Mc04IslmWeekSalePlanDO::getMa02PlanMonth, ma02PlanMonths)
                .in(CollectionUtils.isNotEmpty(baComOrgCodes), Mc04IslmWeekSalePlanDO::getBaComOrgCode, baComOrgCodes)
                .in(CollectionUtils.isNotEmpty(mc04DatePeriodCodes), Mc04IslmWeekSalePlanDO::getMc04DatePeriodCode, mc04DatePeriodCodes);
        return mc04IslmWeekSalePlanService.remove(queryWrapper);
    }

    /**
     * 根据ID批量查询周计划
     *
     * @param mc04WeekPlanIds 周计划ID列表
     * @return 周计划列表
     */
    @Override
    public List<WeekPlanSubmitAdd> selectBatchIds(List<String> mc04WeekPlanIds) {
        return Mc04IslmWeekSalePlanConverter.INSTANCE.converterDosToModels(mc04IslmWeekSalePlanService.listByIds(mc04WeekPlanIds));
    }

    /**
     * 批量更新周计划
     *
     * @param weekPlanSubmitAdds 周计划列表
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean pdReviewUpdateBatch(List<WeekPlanSubmitAdd> weekPlanSubmitAdds) {
        boolean result = updateBatchWeekPlanById(weekPlanSubmitAdds);
        if (result) {
            writeMiddleground(weekPlanSubmitAdds);
        }
        return result;
    }

    @Override
    public String getTodayWeek() {
        return islmWeekPlanSubmitMapper.getTodayWeek();
    }

    /**
     * 写入中台
     *
     * @param weekPlanSubmitAdds
     */
    @Transactional(rollbackFor = Exception.class)
    private void writeMiddleground(List<WeekPlanSubmitAdd> weekPlanSubmitAdds) {
        List<String> weekPlanIds = weekPlanSubmitAdds.stream().map(WeekPlanSubmitAdd::getMc04WeekPlanId).distinct().collect(Collectors.toList());
        List<Mc04IslmWeekSalePlanDO> queryResults = mc04IslmWeekSalePlanService.listByIds(weekPlanIds);
        List<WeekSalePlanRequest> weekSalePlanRequests = Mc04IslmWeekSalePlanDoToWeekSalePlanRequestConverter.INSTANCE.converterDosToModels(queryResults);
        weekSalePlanServiceApi.createBatch(weekSalePlanRequests);
    }
}
