/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.yearplan;

import com.tobacco.app.isale.app.converter.BaseConverter;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlan;
import com.tobacco.app.isale.dto.plan.yearplan.Mc04SalePlanDto;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSalePlanDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SalePlanDtoToMc04IslmSalePlanConverter
        extends BaseConverter<Mc04IslmSalePlanDO, Mc04SalePlanDto, Mc04IslmSalePlan> {
    SalePlanDtoToMc04IslmSalePlanConverter INSTANCE =
            Mappers.getMapper(SalePlanDtoToMc04IslmSalePlanConverter.class);
}
