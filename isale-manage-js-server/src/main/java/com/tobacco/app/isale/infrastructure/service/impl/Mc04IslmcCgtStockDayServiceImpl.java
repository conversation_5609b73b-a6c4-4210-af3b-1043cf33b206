/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtStockDayDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcCgtStockDayMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcCgtStockDayService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: hujiarong
 * @Since: 2025-07-21
 * @Email: <EMAIL>
 * @Create: 2025-07-21
 */
@Service
public class Mc04IslmcCgtStockDayServiceImpl extends ServiceImpl<Mc04IslmcCgtStockDayMapper, Mc04IslmcCgtStockDayDO> implements Mc04IslmcCgtStockDayService {

}