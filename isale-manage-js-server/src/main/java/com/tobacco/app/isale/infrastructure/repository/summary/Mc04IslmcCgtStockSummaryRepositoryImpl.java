package com.tobacco.app.isale.infrastructure.repository.summary;


import com.tobacco.app.isale.domain.repository.order.inventory.dailyinventory.CgtStockDayRepository;
import com.tobacco.app.isale.domain.repository.summary.CgtStockSummaryRepository;
import com.tobacco.app.isale.tools.utils.InterHttpUtils;
import com.tobacco.app.isalecenter.common.exception.CustomException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: hujiarong
 * @Since: 2025-07-21
 */
@Component("Mc04IslmcCgtStockSummaryRepository")
@Slf4j
@RequiredArgsConstructor
public class Mc04IslmcCgtStockSummaryRepositoryImpl implements CgtStockSummaryRepository {

    private static Logger logger = LoggerFactory.getLogger(Mc04IslmcCgtStockSummaryRepositoryImpl.class);

    private final InterHttpUtils interHttpUtils;

    @Override
    public List<Map<String,Object>> pull(HashMap<String,Object> params) throws CustomException {
        String msgid = "T_YX_ZHRBB";
        Map<String, Object> data = interHttpUtils.createData(msgid, "综合日报表接口", params);
        Map<String, Object> result = interHttpUtils.postPull("HUB_" + msgid, data);
        return (List<Map<String,Object>>) result.get("row");
    }

}