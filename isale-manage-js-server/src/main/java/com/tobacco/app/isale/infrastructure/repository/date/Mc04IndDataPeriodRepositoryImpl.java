package com.tobacco.app.isale.infrastructure.repository.date;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tobacco.app.isale.domain.model.date.Mc04IndDataPeriod;
import com.tobacco.app.isale.domain.repository.date.DataPeriodRepository;
import com.tobacco.app.isale.infrastructure.converter.date.Mc04IndDataPeriodDOToMc04IndDataPeriodConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IndDataPeriodDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IndDataPeriodService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: hujiarong
 * @Since: 2025-07-28
 */
@Component("ISaleManageMc04IndDataPeriodDORepository")
@RequiredArgsConstructor
public class Mc04IndDataPeriodRepositoryImpl implements DataPeriodRepository {

    private final Mc04IndDataPeriodService mc04IndDataPeriodService;

    @Override
    public Mc04IndDataPeriod today() {
        LambdaQueryWrapper<Mc04IndDataPeriodDO> queryWrapper = Wrappers.lambdaQuery(Mc04IndDataPeriodDO.class);
        queryWrapper.eq(Mc04IndDataPeriodDO::getMc04DatePeriodType, "T11");
        queryWrapper.eq(Mc04IndDataPeriodDO::getMc04DatePeriodBeginDate, DateUtil.format(DateUtil.date(), "yyyyMMdd"));

        List<Mc04IndDataPeriodDO> list = mc04IndDataPeriodService.list(queryWrapper);
        if (CollUtil.isNotEmpty(list) && list.size() == 1){
            return Mc04IndDataPeriodDOToMc04IndDataPeriodConverter.INSTANCE.converterDoToModel(list.get(0));
        }
        return null;
    }

    @Override
    public Mc04IndDataPeriod nextMonthFirstWeekLastDay() {
        LambdaQueryWrapper<Mc04IndDataPeriodDO> queryWrapper = Wrappers.lambdaQuery(Mc04IndDataPeriodDO.class);

        String yearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));

        queryWrapper.eq(Mc04IndDataPeriodDO::getMc04DatePeriodType, "T05");
        queryWrapper.eq(Mc04IndDataPeriodDO::getMc04DatePeriodCode, yearMonth + "1");

        List<Mc04IndDataPeriodDO> list = mc04IndDataPeriodService.list(queryWrapper);
        if (CollUtil.isNotEmpty(list) && list.size() == 1){
            return Mc04IndDataPeriodDOToMc04IndDataPeriodConverter.INSTANCE.converterDoToModel(list.get(0));
        }
        return null;
    }
}