/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.date;

import com.tobacco.app.isale.domain.model.date.Mc04IndDataPeriod;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IndDataPeriodDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: hujiarong
 * @Email: <EMAIL>
 * @Create: 2025-07-28
 */

@Mapper

public interface Mc04IndDataPeriodDOToMc04IndDataPeriodConverter extends StructureBaseConverter<Mc04IndDataPeriodDO, Mc04IndDataPeriod> {

        Mc04IndDataPeriodDOToMc04IndDataPeriodConverter INSTANCE =
            Mappers.getMapper(Mc04IndDataPeriodDOToMc04IndDataPeriodConverter.class);


}