/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanAdjDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmMonthSalePlanAdjMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanAdjService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: jinfuli
 * @Since: 2025-08-11
 * @Email: <EMAIL>
 * @Create: 2025-08-11
 */
@Service
public class Mc04IslmMonthSalePlanAdjServiceImpl extends ServiceImpl<Mc04IslmMonthSalePlanAdjMapper, Mc04IslmMonthSalePlanAdjDO> implements Mc04IslmMonthSalePlanAdjService {

}