package com.tobacco.app.isale.infrastructure.repository.basic;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.model.basic.ManageContDelivWhse;
import com.tobacco.app.isale.domain.repository.basic.ManageContDelivWhseRepository;
import com.tobacco.app.isale.infrastructure.converter.basic.Mc04IslmcContDelivWhseDOToManageContDelivWhseConverter;
import com.tobacco.app.isale.infrastructure.converter.dist.order.ContDelivWhseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcForwardWhseComDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContDelivWhseService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcForwardWhseComService;
import com.tobacco.app.isalecenter.common.constants.SaleCenterConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: wangluhao01
 * @Since: 2025-05-23
 */
@Slf4j
@Component("ISaleContDelivWhseDORepository")
public class ManageContDelivWhseRepositoryImpl implements ManageContDelivWhseRepository {

    @Autowired
    private Mc04IslmcContDelivWhseService mc04IslmcContDelivWhseService;

    @Autowired
    private Mc04IslmcForwardWhseComService mc04IslmcForwardWhseComService;

    /**
     * @return List<ManageContDelivWhse>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-26 15:45:16
     * @description : 查询所有发货仓库信息
     */
    @Override
    public List<ManageContDelivWhse> queryContDelivWhseList(List<String> whseBusiTypeList) {
        QueryWrapper<Mc04IslmcContDelivWhseDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .in(CollectionUtil.isNotEmpty(whseBusiTypeList), Mc04IslmcContDelivWhseDO::getCbLogtWhseBusiType1, whseBusiTypeList)
                .eq(Mc04IslmcContDelivWhseDO::getIsUse, SaleCenterConstants.YES)
                .eq(Mc04IslmcContDelivWhseDO::getIcomCode, IcomUtils.getIcomCode())
                .orderByAsc((Mc04IslmcContDelivWhseDO::getSeq));
        List<Mc04IslmcContDelivWhseDO> list = mc04IslmcContDelivWhseService.list(wrapper);
        return Mc04IslmcContDelivWhseDOToManageContDelivWhseConverter.INSTANCE.converterDosToModels(list);
    }

    /**
     * @param comCode 协议单位
     * @return List<ManageContDelivWhse>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-26 15:45:16
     * @description : 查询协议单位发货仓库信息
     */
    @Override
    public List<ManageContDelivWhse> queryComContDelivWhseList(String comCode) {
        List<Mc04IslmcForwardWhseComDO> forwardWhseComDOList = mc04IslmcForwardWhseComService.lambdaQuery()
                .eq(Mc04IslmcForwardWhseComDO::getBaComOrgCode, comCode)
                .eq(Mc04IslmcForwardWhseComDO::getIcomCode, IcomUtils.getIcomCode())
                .list();
        if (CollectionUtil.isEmpty(forwardWhseComDOList)) {
            return Collections.emptyList();
        }

        List<String> whseCodeList = forwardWhseComDOList.stream()
                .map(Mc04IslmcForwardWhseComDO::getMd02CgtInStorehouseCode)
                .collect(Collectors.toList());
        List<Mc04IslmcContDelivWhseDO> whseList = mc04IslmcContDelivWhseService.lambdaQuery()
                .in(Mc04IslmcContDelivWhseDO::getMd02CgtOutStorehouseCode, whseCodeList)
                .eq(Mc04IslmcContDelivWhseDO::getIsUse, SaleCenterConstants.YES)
                .list();
        if (CollUtil.isEmpty(whseList)) {
            return Collections.emptyList();
        }
        return ContDelivWhseConverter.INSTANCE.convert(whseList);
    }

    @Override
    public List<ManageContDelivWhse> queryContDelivWhseListByCbLogtWhseBusiType1s(List<String> cbLogtWhseBusiType1s) {
        QueryWrapper<Mc04IslmcContDelivWhseDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .notIn(CollUtil.isNotEmpty(cbLogtWhseBusiType1s), Mc04IslmcContDelivWhseDO::getCbLogtWhseBusiType1, cbLogtWhseBusiType1s)
                .eq(Mc04IslmcContDelivWhseDO::getIsUse, SaleCenterConstants.YES)
                .eq(Mc04IslmcContDelivWhseDO::getIcomCode, IcomUtils.getIcomCode())
                .orderByAsc((Mc04IslmcContDelivWhseDO::getSeq));
        List<Mc04IslmcContDelivWhseDO> list = mc04IslmcContDelivWhseService.list(wrapper);
        return Mc04IslmcContDelivWhseDOToManageContDelivWhseConverter.INSTANCE.converterDosToModels(list);
    }

    /**
     * 根据仓库编码查询仓库信息
     *
     * @param outStorehouseCodeList 仓库编码
     * @return List<ManageContDelivWhse> 仓库信息
     * <AUTHOR> zhangshch01
     * @create_time : 2025-05-26 15:45:16
     * @description : 根据仓库编码查询仓库信息
     */
    @Override
    public List<ManageContDelivWhse> queryContDelivWhseListByPk(List<String> outStorehouseCodeList) {
        return mc04IslmcContDelivWhseService.listByIds(outStorehouseCodeList)
                .stream()
                .map(Mc04IslmcContDelivWhseDOToManageContDelivWhseConverter.INSTANCE::converterDoToModel)
                .collect(Collectors.toList());
    }
}