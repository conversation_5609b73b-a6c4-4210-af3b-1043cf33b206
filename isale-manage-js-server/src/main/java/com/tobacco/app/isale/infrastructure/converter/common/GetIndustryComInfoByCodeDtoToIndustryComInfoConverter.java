/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.common;

import com.tobacco.app.isale.domain.model.common.IndustryComInfo;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.sc.icust.dto.icom.GetIndustryComInfoByCodeDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/08/04 16:36
 * @description : 转换器
 */
@Mapper
public interface GetIndustryComInfoByCodeDtoToIndustryComInfoConverter
        extends StructureBaseConverter<GetIndustryComInfoByCodeDto, IndustryComInfo> {

    GetIndustryComInfoByCodeDtoToIndustryComInfoConverter INSTANCE =
            Mappers.getMapper(GetIndustryComInfoByCodeDtoToIndustryComInfoConverter.class);

    /**
     * 转换
     *
     * @param doObject GetIndustryComInfoByCodeDto
     * @return IndustryComInfo
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-04 16:58:23
     * @description : 转换
     */

    @Override
    @Mappings({@Mapping(source = "IComBankAccountDtoList", target = "IComBankAccountList"),
            @Mapping(source = "IComTaxDtoList", target = "IComTaxList")})
    IndustryComInfo converterDoToModel(GetIndustryComInfoByCodeDto doObject);
}
