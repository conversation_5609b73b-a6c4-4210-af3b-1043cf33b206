/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.monthplan.monthplanreview;

import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmitAdd;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> jxy
 * @Email : @inspur.com
 * @Create : 2025/08/14
 * @Description : 类型转化
 */
@Mapper
public interface MonthPlanReviewFlowConverter extends StructureBaseConverter<Mc04IslmMonthSalePlanDO, MonthPlanSubmitAdd> {
    MonthPlanReviewFlowConverter INSTANCE = Mappers.getMapper(MonthPlanReviewFlowConverter.class);
}
