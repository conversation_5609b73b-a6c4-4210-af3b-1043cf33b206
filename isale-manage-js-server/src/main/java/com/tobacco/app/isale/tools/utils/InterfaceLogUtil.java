/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.tools.utils;

import cn.hutool.core.collection.CollUtil;
import com.inspur.ind.constant.CommonConstants;
import com.lamboegg.common.utils.spring.SpringBootContextUtil;
import com.tobacco.app.isale.domain.enums.basic.InterfaceLogBusiTypeEnum;
import com.tobacco.app.isale.domain.enums.basic.InterfaceLogChannelEnum;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmInterfaceLogDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmInterfaceLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wang<PERSON><PERSON>01
 * @email : wanglu<PERSON><EMAIL>
 * @create_time : 2025/07/22 10:10
 * @description : 日志工具类
 */
@Component
@Slf4j
public class InterfaceLogUtil {

    private static Mc04IslmInterfaceLogService mc04IslmInterfaceLogService;

    private static Mc04IslmInterfaceLogService getMc04IslmInterfaceLogService() {
        if (mc04IslmInterfaceLogService == null) {
            Mc04IslmInterfaceLogService bean = SpringBootContextUtil.getBean(Mc04IslmInterfaceLogService.class);
            if (bean == null) {
                log.error("mc04IslmInterfaceLogService bean is null!");
            }
            mc04IslmInterfaceLogService = bean;
        }
        return mc04IslmInterfaceLogService;
    }

    /**
     * 添加失败日志
     *
     * @param channel         {@link InterfaceLogChannelEnum}
     * @param busiType        {@link InterfaceLogBusiTypeEnum}
     * @param busiId          业务主键
     * @param systemCode      系统编码(主要是使用ESB的接口, 可能是ESB转发问题 也可能是真正第三方问题)
     * @param allowDuplicates 是否允许保存重复失败日志, 由于合同存在推送撤回的情况,允许重复保存
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-16 10:40:39
     * @description : 添加失败日志
     */
    public static void addErrorLog(String channel, String busiType, String busiId,
                                   String systemCode, boolean allowDuplicates) {
        addErrorLogList(channel, busiType, Collections.singletonList(busiId), systemCode, allowDuplicates);
    }


    /**
     * 添加失败日志
     *
     * @param channel         {@link InterfaceLogChannelEnum}
     * @param busiType        {@link InterfaceLogBusiTypeEnum}
     * @param busiId          业务主键
     * @param systemCode      系统编码(主要是使用ESB的接口, 可能是ESB转发问题 也可能是真正第三方问题)
     * @param allowDuplicates 是否允许保存重复失败日志, 由于合同存在推送撤回的情况,允许重复保存
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-16 10:40:39
     * @description : 添加失败日志
     */
    public static void addErrorLog(InterfaceLogChannelEnum channel,
                                   InterfaceLogBusiTypeEnum busiType, String busiId,
                                   String systemCode, boolean allowDuplicates) {
        addErrorLogList(channel.getChannel(), busiType.getType(),
                Collections.singletonList(busiId), systemCode, allowDuplicates);
    }


    /**
     * 添加失败日志
     *
     * @param channel         {@link InterfaceLogChannelEnum}
     * @param busiType        {@link InterfaceLogBusiTypeEnum}
     * @param busiIdList      业务主键
     * @param systemCode      系统编码(主要是使用ESB的接口, 可能是ESB转发问题 也可能是真正第三方问题)
     * @param allowDuplicates 是否允许保存重复失败日志, 由于合同存在推送撤回的情况,允许重复保存
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-16 10:40:39
     * @description : 添加失败日志
     */
    public static void addErrorLogList(String channel, String busiType, List<String> busiIdList,
                                       String systemCode, boolean allowDuplicates) {
        try {
            if (!allowDuplicates) {
                List<Mc04IslmInterfaceLogDO> list = getMc04IslmInterfaceLogService().lambdaQuery()
                        .eq(Mc04IslmInterfaceLogDO::getMc04InterfaceLogChannel, channel)
                        .eq(Mc04IslmInterfaceLogDO::getMc04InterfaceLogBusiType, busiType)
                        .eq(Mc04IslmInterfaceLogDO::getMc04InterfaceLogBusiId, busiIdList)
                        .eq(Mc04IslmInterfaceLogDO::getMc04InterfaceLogStatus, CommonConstants.NO)
                        .eq(Mc04IslmInterfaceLogDO::getMc04InterfaceLogCompStatus, CommonConstants.NO)
                        .list();
                Set<String> isHasSet = list.stream()
                        .map(Mc04IslmInterfaceLogDO::getMc04InterfaceLogBusiId)
                        .collect(Collectors.toSet());
                if (CollUtil.isNotEmpty(isHasSet)) {
                    log.info("{}已存在失败且未补推的日志，忽略重复数据", CollUtil.join(isHasSet, ","));
                    return;
                }
                busiIdList.removeAll(isHasSet);
            }
            addLogList(channel, busiType, busiIdList, systemCode, CommonConstants.NO);
        } catch (Exception e) {
            log.error("{}-{}-{}添加失败日志失败", channel, busiType, CollUtil.join(busiIdList, ","), e);
        }
    }

    /**
     * 添加成功日志
     *
     * @param channel    {@link InterfaceLogChannelEnum}
     * @param busiType   {@link InterfaceLogBusiTypeEnum}
     * @param busiId 业务主键
     * @param systemCode 系统编码(主要是使用ESB的接口, 可能是ESB转发问题 也可能是真正第三方问题)
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-16 10:40:39
     * @description : 添加失败日志
     */
    public static void addSuccessLog(String channel, String busiType, String busiId, String systemCode) {
        addSuccessLogList(channel, busiType, Collections.singletonList(busiId), systemCode);
    }


    /**
     * 添加成功日志
     *
     * @param channel    {@link InterfaceLogChannelEnum}
     * @param busiType   {@link InterfaceLogBusiTypeEnum}
     * @param busiId 业务主键
     * @param systemCode 系统编码(主要是使用ESB的接口, 可能是ESB转发问题 也可能是真正第三方问题)
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-16 10:40:39
     * @description : 添加失败日志
     */
    public static void addSuccessLog(InterfaceLogChannelEnum channel,
                                     InterfaceLogBusiTypeEnum busiType, String busiId, String systemCode) {
        addSuccessLogList(channel.getChannel(), busiType.getType(), Collections.singletonList(busiId), systemCode);
    }

    /**
     * 添加成功日志
     *
     * @param channel    {@link InterfaceLogChannelEnum}
     * @param busiType   {@link InterfaceLogBusiTypeEnum}
     * @param busiIdList 业务主键
     * @param systemCode 系统编码(主要是使用ESB的接口, 可能是ESB转发问题 也可能是真正第三方问题)
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-16 10:40:39
     * @description : 添加失败日志
     */
    public static void addSuccessLogList(String channel, String busiType, List<String> busiIdList, String systemCode) {
        String group = InterfaceLogBusiTypeEnum.getGroupByType(busiType);
        if (CollUtil.isEmpty(InterfaceLogBusiTypeEnum.getTypeListByGroup(group))) {
            log.info("{}未配置日志，忽略", busiType);
            return;
        }
        //特殊分组 后面只要有一次成功将同组的全部置为成功
        List<String> typeListByGroup = InterfaceLogBusiTypeEnum.getTypeListByGroup(group);
        busiIdList.forEach(busiId -> {
            //成功的话将历史失败的更改为成功
            getMc04IslmInterfaceLogService().lambdaUpdate()
                    .eq(Mc04IslmInterfaceLogDO::getMc04InterfaceLogChannel, channel)
                    .in(Mc04IslmInterfaceLogDO::getMc04InterfaceLogBusiType, typeListByGroup)
                    .eq(Mc04IslmInterfaceLogDO::getMc04InterfaceLogBusiId, busiId)
                    .eq(Mc04IslmInterfaceLogDO::getMc04InterfaceLogStatus, CommonConstants.NO)
                    .eq(Mc04IslmInterfaceLogDO::getMc04InterfaceLogCompStatus, CommonConstants.NO)
                    .set(Mc04IslmInterfaceLogDO::getMc04InterfaceLogCompStatus, CommonConstants.YES)
                    .update();
        });
        //插入成功日志
        addLogList(channel, busiType, busiIdList, systemCode, CommonConstants.YES);
    }


    /**
     * 添加日志
     *
     * @param channel    {@link InterfaceLogChannelEnum}
     * @param busiType   {@link InterfaceLogBusiTypeEnum}
     * @param busiIdList 业务主键
     * @param systemCode 系统编码(主要是使用ESB的接口, 可能是ESB转发问题 也可能是真正第三方问题)
     * @param status     状态
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-16 10:40:39
     * @description : 添加日志
     **/
    public static void addLogList(String channel, String busiType, List<String> busiIdList, String systemCode, String status) {
        List<Mc04IslmInterfaceLogDO> mc04IslmInterfaceLogDoList = busiIdList.stream()
                .map(busiId -> {
                    Mc04IslmInterfaceLogDO mc04IslmInterfaceLogDO = new Mc04IslmInterfaceLogDO();
                    mc04IslmInterfaceLogDO.setMc04InterfaceLogChannel(channel);
                    mc04IslmInterfaceLogDO.setMc04InterfaceLogBusiType(busiType);
                    mc04IslmInterfaceLogDO.setMc04InterfaceLogBusiId(busiId);
                    mc04IslmInterfaceLogDO.setMc04InterfaceLogStatus(status);
                    mc04IslmInterfaceLogDO.setMc04InterfaceLogCompStatus(CommonConstants.NO);
                    mc04IslmInterfaceLogDO.setMc04InterfaceLogSystemCode(systemCode);
                    return mc04IslmInterfaceLogDO;
                }).collect(Collectors.toList());
        getMc04IslmInterfaceLogService().saveBatch(mc04IslmInterfaceLogDoList);
    }

}
