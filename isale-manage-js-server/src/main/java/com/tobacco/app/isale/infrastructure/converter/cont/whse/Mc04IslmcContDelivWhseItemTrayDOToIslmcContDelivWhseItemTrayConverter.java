package com.tobacco.app.isale.infrastructure.converter.cont.whse;


import com.tobacco.app.isale.domain.model.cont.whse.IslmcContDelivWhseItemTray;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseItemTrayDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface Mc04IslmcContDelivWhseItemTrayDOToIslmcContDelivWhseItemTrayConverter
        extends StructureBaseConverter<Mc04IslmcContDelivWhseItemTrayDO, IslmcContDelivWhseItemTray> {
    Mc04IslmcContDelivWhseItemTrayDOToIslmcContDelivWhseItemTrayConverter INSTANCE =  Mappers.getMapper(Mc04IslmcContDelivWhseItemTrayDOToIslmcContDelivWhseItemTrayConverter.class);
}
