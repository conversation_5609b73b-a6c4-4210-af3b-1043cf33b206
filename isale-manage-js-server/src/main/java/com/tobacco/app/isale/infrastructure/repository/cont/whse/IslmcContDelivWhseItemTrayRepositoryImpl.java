package com.tobacco.app.isale.infrastructure.repository.cont.whse;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tobacco.app.isale.domain.model.cont.whse.IslmcContDelivWhseItemTray;
import com.tobacco.app.isale.domain.repository.cont.whse.IslmcContDelivWhseItemTrayRepository;
import com.tobacco.app.isale.infrastructure.converter.cont.whse.Mc04IslmcContDelivWhseItemTrayDOToIslmcContDelivWhseItemTrayConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseItemTrayDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContDelivWhseItemTrayService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContDelivWhseService;
import com.tobacco.app.isale.infrastructure.tunnel.database.cont.whse.IslmcContDelivWhseItemTrayMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component("IslmcContDelivWhseItemTrayRepository")
public class IslmcContDelivWhseItemTrayRepositoryImpl implements IslmcContDelivWhseItemTrayRepository {

    private Mc04IslmcContDelivWhseService mc04IslmcContDelivWhseService;
    @Resource
    private IslmcContDelivWhseItemTrayMapper islmcContDelivWhseItemTrayMapper;

    @Autowired
    public void setMc04IslmcContDelivWhseService(Mc04IslmcContDelivWhseService mc04IslmcContDelivWhseService) {
        this.mc04IslmcContDelivWhseService = mc04IslmcContDelivWhseService;
    }

    private Mc04IslmcContDelivWhseItemTrayService mc04IslmcContDelivWhseItemTrayService;

    @Autowired
    public void setMc04IslmcContDelivWhseItemTrayService(Mc04IslmcContDelivWhseItemTrayService mc04IslmcContDelivWhseItemTrayService){
        this.mc04IslmcContDelivWhseItemTrayService = mc04IslmcContDelivWhseItemTrayService;
    }

    /**
     * @param
     * @return List<IslmcContDelivWhseItemTray>
     * <AUTHOR> wmd
     * @create_time : 2025-08-04
     * @description : 获取所有发货托盘数据
     */
    @Override
    public List<IslmcContDelivWhseItemTray> getAll(){
        List<Mc04IslmcContDelivWhseItemTrayDO> list = mc04IslmcContDelivWhseItemTrayService.list().stream().filter(
                item ->"1".equals(Optional.ofNullable(item.getMd03LogtTrayCombTspTrayType()).orElse(""))
        ).collect(Collectors.toList());
        return Mc04IslmcContDelivWhseItemTrayDOToIslmcContDelivWhseItemTrayConverter.INSTANCE
                .converterDosToModels(list);
    }

    /**
     * @param  twoLevelList
     * @return Boolean
     * <AUTHOR> wmd
     * @create_time : 2025-08-04
     * @description : 删除已有托盘量的表数据
     */
    @Override
    public Boolean deleteQtyed(List<String> twoLevelList){
        if (ObjectUtil.isEmpty(twoLevelList)) {
            return true;
        }
        QueryWrapper<Mc04IslmcContDelivWhseItemTrayDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .in(ObjectUtil.isNotEmpty(twoLevelList),Mc04IslmcContDelivWhseItemTrayDO::getAcTwoLevelCigCode ,twoLevelList);
        Boolean b = mc04IslmcContDelivWhseItemTrayService.remove(wrapper);
        return b;
    }

    /**
     * @param
     * @return List<String>
     * <AUTHOR> wmd
     * @create_time : 2025-08-04
     * @description : 获取所有要插入的仓库信息
     */
    @Override
    public List<String> getWhseList(){
        QueryWrapper<Mc04IslmcContDelivWhseDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(Mc04IslmcContDelivWhseDO::getIsUse,'1');
        List<Mc04IslmcContDelivWhseDO> list = mc04IslmcContDelivWhseService.list(wrapper);
        List<String> whseCodeList = list.stream().map(
                Mc04IslmcContDelivWhseDO::getMd02CgtOutStorehouseCode
        ).collect(Collectors.toList());
        return whseCodeList;
    }

    /**
     * @param saveList 保存参数
     * @return Boolean
     * <AUTHOR> wmd
     * @create_time : 2025-08-04
     * @description : 保存托盘量修改
     */
    @Override
    public Boolean save(List<IslmcContDelivWhseItemTray> saveList){
        List<Mc04IslmcContDelivWhseItemTrayDO> saveDOList = Mc04IslmcContDelivWhseItemTrayDOToIslmcContDelivWhseItemTrayConverter.INSTANCE
                .converterModelsToDos(saveList);
        Boolean b = mc04IslmcContDelivWhseItemTrayService.saveBatch(saveDOList);
        return b;
    }

}
