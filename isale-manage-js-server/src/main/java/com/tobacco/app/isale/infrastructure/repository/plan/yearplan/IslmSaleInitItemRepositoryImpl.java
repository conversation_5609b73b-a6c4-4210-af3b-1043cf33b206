/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.yearplan;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.inspur.ind.util.IDUtils;
import com.tobacco.app.isale.domain.model.plan.yearplan.IslmSaleInitItemModel;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmSaleInitItemRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmSaleInitItemDoConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSaleInitItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmSaleInitItemService;
import com.tobacco.app.isale.tools.utils.OrderedIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 销量初始化明细 领域仓库实现
 *
 * @Author: longxi
 * @Since: 2025-08-07
 */
@Component("IslmSaleInitItemRepository")
public class IslmSaleInitItemRepositoryImpl implements IslmSaleInitItemRepository {

    @Autowired
    private Mc04IslmSaleInitItemService islmSaleInitItemService;

    @Override
    public List<IslmSaleInitItemModel> listBySaleInitId(String mc04SaleInitId) {
        LambdaQueryWrapper<Mc04IslmSaleInitItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmSaleInitItemDO::getMc04SaleInitId, mc04SaleInitId);
        List<Mc04IslmSaleInitItemDO> saleInitItemDoList = islmSaleInitItemService.list(queryWrapper);
        return Mc04IslmSaleInitItemDoConverter.INSTANCE.converterDosToModels(saleInitItemDoList);
    }

    @Override
    public boolean saveBatch(List<IslmSaleInitItemModel> saleInitItemModelList) {
        if (CollectionUtil.isNotEmpty(saleInitItemModelList)) {
            saleInitItemModelList.forEach(item -> item.setMc04SaleInitItemId(OrderedIdGenerator.generateOrderedId32()));
        }
        List<Mc04IslmSaleInitItemDO> mc04IslmSaleInitItemDoList = Mc04IslmSaleInitItemDoConverter.INSTANCE.converterModelsToDos(saleInitItemModelList);
        return islmSaleInitItemService.saveBatch(mc04IslmSaleInitItemDoList);
    }

    @Override
    public boolean updateBatch(List<IslmSaleInitItemModel> saleInitItemModelList) {
        List<Mc04IslmSaleInitItemDO> mc04IslmSaleInitItemDoList = Mc04IslmSaleInitItemDoConverter.INSTANCE.converterModelsToDos(saleInitItemModelList);
        return islmSaleInitItemService.updateBatchById(mc04IslmSaleInitItemDoList);
    }
}
