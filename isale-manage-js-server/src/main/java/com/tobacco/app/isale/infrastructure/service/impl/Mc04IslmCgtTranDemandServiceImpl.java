/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtTranDemandDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmCgtTranDemandService;
import org.springframework.stereotype.Service;
import com.tobacco.app.isale.infrastructure.tunnel.database.supply.psc.allocdemand.manage.Mc04IslmCgtTranDemandMapper;


/**
 * @Description: 服务实现类
 *
 * @Author: liwensheng
 * @Since: 2025-08-14
 * @Email: <EMAIL>
 * @Create: 2025-08-14
 */
@Service
public class Mc04IslmCgtTranDemandServiceImpl extends ServiceImpl<Mc04IslmCgtTranDemandMapper, Mc04IslmCgtTranDemandDO> implements Mc04IslmCgtTranDemandService {

}