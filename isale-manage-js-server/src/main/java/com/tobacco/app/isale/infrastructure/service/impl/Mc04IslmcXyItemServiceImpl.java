/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcXyItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcXyItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcXyItemService;
import org.springframework.stereotype.Service;


/**
 * @description : 服务实现类
 *
 * <AUTHOR> wanglu<PERSON>01
 * @since : 2025-05-22
 * @email : <EMAIL>
 * @create_time : 2025-05-22
 */
@Service
public class Mc04IslmcXyItemServiceImpl extends ServiceImpl<Mc04IslmcXyItemMapper, Mc04IslmcXyItemDO> implements Mc04IslmcXyItemService {

}