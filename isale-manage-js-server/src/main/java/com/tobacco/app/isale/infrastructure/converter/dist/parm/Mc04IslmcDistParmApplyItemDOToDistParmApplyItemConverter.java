/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.parm;

import com.tobacco.app.isale.domain.model.order.dist.parm.DistParmApplyItem;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcDistParmApplyItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> liu<PERSON><PERSON>
 * @create_time : 2025/06/05 19:18
 * @description : 配货参数申请数据转化
 */
@Mapper
public interface Mc04IslmcDistParmApplyItemDOToDistParmApplyItemConverter extends StructureBaseConverter<Mc04IslmcDistParmApplyItemDO, DistParmApplyItem> {

    Mc04IslmcDistParmApplyItemDOToDistParmApplyItemConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcDistParmApplyItemDOToDistParmApplyItemConverter.class);

}
