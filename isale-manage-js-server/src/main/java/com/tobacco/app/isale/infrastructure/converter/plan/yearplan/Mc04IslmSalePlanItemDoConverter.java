/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.yearplan;

import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanItemModel;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSalePlanItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 年度计划编制 数据转换器
 * <p>
 * IslmSalePlanItemConverter
 *
 * @Author: longxi
 * @Since: 2025-08-09
 */

@Mapper
public interface Mc04IslmSalePlanItemDoConverter extends StructureBaseConverter<Mc04IslmSalePlanItemDO, Mc04IslmSalePlanItemModel> {

    Mc04IslmSalePlanItemDoConverter INSTANCE = Mappers.getMapper(Mc04IslmSalePlanItemDoConverter.class);

}