/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.order.dist.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.base.CustomPage;
import com.inspur.ind.constant.CommonConstants;
import com.inspur.ind.icom.IcomUtils;
import com.inspur.ind.uc.permission.PermissionUtils;
import com.inspur.ind.util.DateUtils;
import com.inspur.ind.util.IDUtils;
import com.inspur.ind.workday.WorkdayUtils;
import com.inspur.x1.ac.dict.dto.AcDictMapDTO;
import com.inspur.x1.ac.dict.utils.AcDictUtil;
import com.inspur.x1.ac.rule.utils.AcRuleUtil;
import com.tobacco.app.isale.domain.constants.DistConstants;
import com.tobacco.app.isale.domain.enums.cont.ContStatusEnum;
import com.tobacco.app.isale.domain.enums.order.dist.order.DistOrderStatusEnum;
import com.tobacco.app.isale.domain.model.common.ComItem;
import com.tobacco.app.isale.domain.model.date.Mc04IndDataPeriod;
import com.tobacco.app.isale.domain.model.order.dist.order.*;
import com.tobacco.app.isale.domain.repository.nation.subsys.ISaleNationSubsysRepository;
import com.tobacco.app.isale.domain.repository.order.dist.order.DistOrderRealtimeRepository;
import com.tobacco.app.isale.infrastructure.converter.dist.order.*;
import com.tobacco.app.isale.infrastructure.entity.*;
import com.tobacco.app.isale.infrastructure.repository.common.ComItemRepositoryImpl;
import com.tobacco.app.isale.infrastructure.service.api.*;
import com.tobacco.app.isale.infrastructure.tunnel.database.order.dist.order.IslmDistOrderMapper;
import com.tobacco.app.isale.tools.utils.*;
import com.tobacco.app.isalecenter.client.api.cont.order.ContOrderApi;
import com.tobacco.app.isalecenter.client.api.order.distParm.DistParmAPI;
import com.tobacco.app.isalecenter.client.api.plan.monthplan.MonthSalePlanServiceAPI;
import com.tobacco.app.isalecenter.client.api.plan.weekplan.WeekSalePlanServiceAPI;
import com.tobacco.app.isalecenter.client.api.xy.XyAPI;
import com.tobacco.app.isalecenter.client.dto.common.SaleSingleResponse;
import com.tobacco.app.isalecenter.client.dto.cont.order.Mc04IslmcContOrderDTO;
import com.tobacco.app.isalecenter.client.dto.cont.order.Mc04IslmcContOrderItemDTO;
import com.tobacco.app.isalecenter.client.dto.order.distParm.Mc04IslmcDistParmDTO;
import com.tobacco.app.isalecenter.client.dto.order.distParm.Mc04IslmcDistParmItemDTO;
import com.tobacco.app.isalecenter.client.dto.plan.monthplan.MonthSalePlanDTO;
import com.tobacco.app.isalecenter.client.dto.plan.weekplan.WeekSalePlanDTO;
import com.tobacco.app.isalecenter.client.dto.xy.Mc04IslmcXyDTO;
import com.tobacco.app.isalecenter.client.dto.xy.Mc04IslmcXyItemDTO;
import com.tobacco.app.isalecenter.client.req.cont.order.Mc04IslmcContOrderREQ;
import com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmREQ;
import com.tobacco.app.isalecenter.client.req.plan.monthplan.MonthSalePlanQueryREQ;
import com.tobacco.app.isalecenter.client.req.plan.weekplan.WeekSalePlanQueryREQ;
import com.tobacco.app.isalecenter.client.req.xy.Mc04IslmcXyQueryREQ;
import com.tobacco.sc.icommodity.dto.common.constant.dto.item.IccItemDetailDTO;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liuwancheng
 * @create_time : 2025/07/28 13:18
 * @description : 实时配货服务实现
 */
@Slf4j
@Component("ISaleDistOrderRealtimeRepository")
public class DistOrderRealtimeRepositoryImpl implements DistOrderRealtimeRepository {

    @Resource
    private ComItemRepositoryImpl comItemRepositoryImpl;

    @Resource
    private IslmDistOrderMapper islmDistOrderMapper;

    @Resource
    private ISaleNationSubsysRepository iSaleNationSubsysRepository;

    @Resource
    private MonthSalePlanServiceAPI monthSalePlanServiceApi;

    @Resource
    private WeekSalePlanServiceAPI weekSalePlanServiceApi;

    @Resource
    private XyAPI xyApi;

    @Resource
    private ContOrderApi contOrderApi;

    @Resource
    private NationDistRegionService nationDistRegionService;

    @Resource
    private NationDistReceiveRegionService nationDistReceiveRegionService;

    @Resource
    private Mc04IndDataPeriodService mc04IndDataPeriodService;

    @Resource
    private DistParmAPI distParmApi;

    @Resource
    private Mc04IslmCgtDistOrderService mc04IslmCgtDistOrderService;

    @Resource
    private Mc04IslmcCgtDayPlanService mc04IslmcCgtDayPlanService;

    @Resource
    private Mc04IslmcCgtDistOrderLogService mc04IslmcCgtDistOrderLogService;

    @Resource
    private NationDistPreviewService nationDistPreviewService;

    @Resource
    private NationDistService nationDistService;

    @Resource
    private NationDistPreviewItemService nationDistPreviewItemService;

    @Resource
    private Mc04IslmMonthSalePlanAdjService mc04IslmMonthSalePlanAdjService;

    @Resource
    private Mc04IslmcComItemContDelivWhseService mc04IslmcComItemContDelivWhseService;

    @Resource
    private Mc04IslmcContDelivWhseItemTrayService mc04IslmcContDelivWhseItemTrayService;

    /**
     * 创建 DistOrderCom 对象
     *
     * @param baComOrgCode    公司组织代码
     * @param busiComDto      业务公司
     * @param distRegionDO    配货发运区域
     * @param receiveRegionDO 配货收货区域
     */
    private DistOrderCom createDistOrderCom(String baComOrgCode, BusiComDto busiComDto, NationDistRegionDO distRegionDO, NationDistReceiveRegionDO receiveRegionDO) {
        DistOrderCom item = new DistOrderCom();
        item.setBaComOrgCode(baComOrgCode);
        if (busiComDto != null) {
            item.setBaComOrgName(busiComDto.getMc04ComOrgShortName());
            item.setSeq(busiComDto.getSeq());
        }
        if (distRegionDO != null) {
            item.setMd02CgtDistRegionName(distRegionDO.getRegionName());
            item.setMd02CgtDistRegionCode(distRegionDO.getRegionCode());
        }

        if (receiveRegionDO != null) {
            item.setMd02DistReceiveregionCode(receiveRegionDO.getMd02DistReceiveregionCode());
            item.setMd02DistReceiveregionName(receiveRegionDO.getMd02DistReceiveregionName());
        }
        return item;
    }

    /**
     * 创建实时配货订单
     *
     * @param distOrderDos 需要保存的配货订单数据
     * @param distOrder    传过来的数据
     */
    private void processSaveDistOrder(List<Mc04IslmCgtDistOrderDO> distOrderDos, DistOrder distOrder) {
        // 配货订单公共部分处理
        for (Mc04IslmCgtDistOrderDO item : distOrderDos) {
            // 处理必填字段
            item.setMc04CgtDistOrderCode(distOrder.getMc04CgtDistOrderCode());
            item.setBaComOrgCode(distOrder.getBaComOrgCode());
            item.setMa02TobaProdTradeTypeCode(distOrder.getMa02TobaProdTradeTypeCode());
            item.setMc04CgtDistOrderType(distOrder.getMc04CgtDistOrderType());
            item.setMc04CgtDistOrderStatus(distOrder.getMc04CgtDistOrderStatus());
            item.setMc04CgtDistOrderDistDate(distOrder.getMc04CgtDistOrderDistDate());
            item.setIcomCode(IcomUtils.getIcomCode());
            // 如果已经有创建时间等取原来值
            if (StrUtil.isNotEmpty(distOrder.getCreateId())) {
                item.setCreateId(distOrder.getCreateId());
                item.setCreateName(distOrder.getCreateName());
                item.setCreateTime(distOrder.getCreateTime());
            }
            // 处理用户选择的字段
            item.setMa02PlanMonth(distOrder.getMa02PlanMonth());
            item.setMc04MonthSalePlanType(distOrder.getMc04MonthSalePlanType());
            item.setMc04DatePeriodCode(distOrder.getMc04DatePeriodCode());
            item.setMd02CgtDistRegionCode(distOrder.getMd02CgtDistRegionCode());
            item.setMd02DistReceiveregionCode(distOrder.getMd02DistReceiveregionCode());
            item.setMd02CgtXyNo(distOrder.getMd02CgtXyNo());
            item.setMc04CgtXyPeriodCode(distOrder.getMc04CgtXyPeriodCode());
            item.setMc04ContExpecOutDate(distOrder.getMc04ContExpecOutDate());
            item.setMc04ContExpecArrivalDate(distOrder.getMc04ContExpecArrivalDate());
            item.setMd02CgtInStorehouseCode(distOrder.getMd02CgtInStorehouseCode());
            item.setMc04CgtPraInStorehouseCode(distOrder.getMc04CgtPraInStorehouseCode());
            item.setMd03LogtIcApptBegin(distOrder.getMd03LogtIcApptBegin());
            item.setMd03LogtIcApptEnd(distOrder.getMd03LogtIcApptEnd());
            item.setMc04ContZeroClockType(distOrder.getMc04ContZeroClockType());
            item.setMf04ApprovalInfo(distOrder.getMf04ApprovalInfo());
            item.setZaRemark(distOrder.getZaRemark());
        }
    }

    /**
     * 保存配货订单日计划
     *
     * @param distOrder 配货订单
     * @param items     配货订单信息
     */
    private List<Mc04IslmCgtDistOrderDO> saveDistOrders(DistOrder distOrder, List<DistOrderItem> items) {
        String mc04CgtDistOrderCode = distOrder.getMc04CgtDistOrderCode();
        // 先查询配货单
        List<Mc04IslmCgtDistOrderDO> dbDistOrderDos = getDistOrderDos(mc04CgtDistOrderCode);
        // 如果存在先按配货订单代码删除
        if (CollectionUtil.isNotEmpty(dbDistOrderDos)) {
            mc04IslmCgtDistOrderService.remove(new LambdaQueryWrapper<Mc04IslmCgtDistOrderDO>().eq(Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderCode, mc04CgtDistOrderCode));
        }
        List<Mc04IslmCgtDistOrderDO> distOrderDos = DistOrderItemToMc04IslmCgtDistOrderDOConverter.SELF.converterModelsToDos(items);
        // 配货订单数据处理
        processSaveDistOrder(distOrderDos, distOrder);
        // 因mybatis  plus 批量插入单条处理 实际很快、可以使用
        mc04IslmCgtDistOrderService.saveBatch(distOrderDos);
        return distOrderDos;
    }


    /**
     * 查询配货订单预览
     *
     * @param reqmemberCode             商业公司编码
     * @param distregionCode            发运地区编码
     * @param md02DistReceiveregionCode 收货地区编码
     * @param billDate                  日期
     * @return NationDistPreviewDO  配货订单预览
     */
    private NationDistPreviewDO getDistPreviewDo(String reqmemberCode, String distregionCode, String md02DistReceiveregionCode, String billDate) {
        QueryWrapper<NationDistPreviewDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(NationDistPreviewDO::getReqmemberCode, reqmemberCode).eq(NationDistPreviewDO::getDistregionCode, distregionCode).eq(NationDistPreviewDO::getMd02DistReceiveregionCode, md02DistReceiveregionCode).eq(NationDistPreviewDO::getBillDate, billDate).last("limit 1");
        return nationDistPreviewService.getOne(queryWrapper);
    }

    /**
     * 保存配货订单日计划
     *
     * @param mc04CgtDistOrderCode 配货订单代码
     * @param distOrderDos         配货订单信息
     */
    @Override
    public void saveDistOrderDayPlans(String mc04CgtDistOrderCode, List<Mc04IslmCgtDistOrderDO> distOrderDos) {
        // 先查询配货单
        QueryWrapper<Mc04IslmcCgtDayPlanDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmcCgtDayPlanDO::getMc04CgtDistOrderCode, mc04CgtDistOrderCode).eq(Mc04IslmcCgtDayPlanDO::getMa02TobaProdTradeTypeCode, CommonConstants.NO);
        List<Mc04IslmcCgtDayPlanDO> dbDayPlanDos = mc04IslmcCgtDayPlanService.list(queryWrapper);
        // 如果存在先按配货订单代码删除
        if (CollectionUtil.isNotEmpty(dbDayPlanDos)) {
            mc04IslmcCgtDayPlanService.remove(new LambdaQueryWrapper<Mc04IslmcCgtDayPlanDO>().eq(Mc04IslmcCgtDayPlanDO::getMc04CgtDistOrderCode, mc04CgtDistOrderCode));
        }
        List<Mc04IslmcCgtDayPlanDO> dayPlanDos = Mc04IslmcCgtDayPlanDOToMc04IslmCgtDistOrderDOConverter.SELF.converterModelsToDos(distOrderDos);

        // 配货单最终量
        Map<String, BigDecimal> cfmMap = distOrderDos.stream().collect(Collectors.toMap(Mc04IslmCgtDistOrderDO::getAcTwoLevelCigCode, Mc04IslmCgtDistOrderDO::getMd02CgtDistConfirmQty));
        dayPlanDos.forEach(item -> {
            BigDecimal cfmQty = cfmMap.get(item.getAcTwoLevelCigCode());
            item.setMc04CgtDayPlanId(IDUtils.randomUUID(DistConstants.ID_LEN));
            item.setMc04CgtDayPlanCode(item.getMc04CgtDistOrderCode() + item.getMd02CgtOutStorehouseCode() + item.getMd03LogtTrayCombTspTrayType() + 1);
            // 未发布未生效
            item.setMc04CgtDayPlanStatus(CommonConstants.NO);
            item.setMd02TobaProdTradeOrderEffectStatusCode(CommonConstants.NO);
            // 计划量和原始计划量
            item.setMa02CgtPlResolOrigplanQty(cfmQty);
            item.setMa02CgtPlAdjustedQty(cfmQty);
        });
        mc04IslmcCgtDayPlanService.saveBatch(dayPlanDos);
    }

    /**
     * 保存配货订单日志
     *
     * @param distOrder 配货订单
     * @param curStatus 当前状态
     */
    @Override
    public void saveDistOrderLog(DistOrder distOrder, String curStatus) {
        // 默认pc
        String client = distOrder.getMc04CgtDistOrderClient() == null ? "pc" : distOrder.getMc04CgtDistOrderClient();
        Mc04IslmcCgtDistOrderLogDO distOrderLog = new Mc04IslmcCgtDistOrderLogDO();
        distOrderLog.setMc04CgtDistOrderCode(distOrder.getMc04CgtDistOrderCode());
        distOrderLog.setMc04CgtDistOrderCurrentStatus(curStatus);
        distOrderLog.setMc04CgtDistOrderNextStatus(distOrder.getMc04CgtDistOrderStatus());
        distOrderLog.setMc04CgtDistOrderClient(client);
        mc04IslmcCgtDistOrderLogService.save(distOrderLog);
    }

    /**
     * 批量保存配货订单日志
     *
     * @param mc04CgtDistOrderCodes 配货订单代码列表
     * @param curStatus             当前状态
     * @param nextStatus            下一个状态
     */
    private void saveDistOrderLogs(List<String> mc04CgtDistOrderCodes, String curStatus, String nextStatus) {
        // 默认pc
        String client = "pc";
        List<Mc04IslmcCgtDistOrderLogDO> distOrderLogs = new ArrayList<>();
        for (String mc04CgtDistOrderCode : mc04CgtDistOrderCodes) {
            Mc04IslmcCgtDistOrderLogDO distOrderLog = new Mc04IslmcCgtDistOrderLogDO();
            distOrderLog.setMc04CgtDistOrderCode(mc04CgtDistOrderCode);
            distOrderLog.setMc04CgtDistOrderCurrentStatus(curStatus);
            distOrderLog.setMc04CgtDistOrderNextStatus(nextStatus);
            distOrderLog.setMc04CgtDistOrderClient(client);
            distOrderLogs.add(distOrderLog);
        }
        mc04IslmcCgtDistOrderLogService.saveBatch(distOrderLogs);
    }

    /**
     * 获取周期内执行量 一种取该协议周期的合同10到60状态的合同量
     *
     * @param param 配货订单列
     *              begintStatus:查合同开始状态
     * @return 执行量列表
     */
    private List<DistOrderCols> getExecuteQty(DistOrderColsParam param, String beginStatus) {
        Mc04IslmcContOrderREQ req = BeanUtil.copyProperties(param, Mc04IslmcContOrderREQ.class);
        List<String> statusList = new ArrayList<>();
        // 状态未设置取鉴章状态
        beginStatus = StrUtil.isBlank(beginStatus) ? ContStatusEnum.CONT_STATUS_10.getStatus() : beginStatus;
        for (String status : ContStatusEnum.getStatusList()) {
            // 状态取起始状态到60区间内的状态
            if (status.compareTo(beginStatus) >= 0 && status.compareTo(ContStatusEnum.CONT_STATUS_60.getStatus()) <= 0) {
                statusList.add(status);
            }
        }
        // 获取10-60状态合同
        req.setMc04CgtTradeContStatusList(statusList);
        SaleSingleResponse<List<Mc04IslmcContOrderDTO>> resp = contOrderApi.list(req);
        Assert.isTrue(resp.isSuccess() && !Objects.isNull(resp.getData()), () -> new CustomException("获取合同数据失败"));
        Map<String, List<Mc04IslmcContOrderItemDTO>> groupMap = resp.getData().stream().filter(Objects::nonNull).map(Mc04IslmcContOrderDTO::getContOrderItems).filter(CollectionUtil::isNotEmpty).flatMap(List::stream).collect(Collectors.groupingBy(item -> item.getAcCgtCartonCode() + "_" + item.getAcTwoLevelCigCode()));

        // 获取执行量
        List<DistOrderCols> res = new ArrayList<>();
        groupMap.forEach((key, value) -> {
            DistOrderCols col = new DistOrderCols();
            col.setAcTwoLevelCigCode(key.split("_")[1]);
            col.setAcCgtCartonCode(key.split("_")[0]);
            col.setExecuteQty(BigDecimal.ZERO);
            for (Mc04IslmcContOrderItemDTO item : value) {
                col.setExecuteQty(col.getExecuteQty().add(item.getMd02CgtTradeContQty()));
            }
            res.add(col);
        });
        return res;
    }

    /**
     * 获取周期内执行量 取该协议周期的日计划量-该协议周期的已解除合同量
     *
     * @return 执行量列表
     */
    private List<DistOrderCols> getExecuteQtyWithDayPlan(DistOrderColsParam param) {
        // 周期内日计划量
        List<DistOrderCols> list = islmDistOrderMapper.getDayPlanData(param);

        Mc04IslmcContOrderREQ req = BeanUtil.copyProperties(param, Mc04IslmcContOrderREQ.class);
        // 获取解除状态合同
        req.setMc04CgtTradeContStatusList(Collections.singletonList(ContStatusEnum.CONT_STATUS_90.getStatus()));
        SaleSingleResponse<List<Mc04IslmcContOrderDTO>> resp = contOrderApi.list(req);
        Assert.isTrue(resp.isSuccess() && !Objects.isNull(resp.getData()), () -> new CustomException("获取已解除状态合同失败"));
        // 获取解除状态合同map
        Map<String, BigDecimal> contOrderItemMap = resp.getData().stream().filter(Objects::nonNull).map(Mc04IslmcContOrderDTO::getContOrderItems).filter(CollectionUtil::isNotEmpty).flatMap(List::stream).collect(Collectors.groupingBy(item -> item.getAcCgtCartonCode() + "_" + item.getAcTwoLevelCigCode(), Collectors.reducing(BigDecimal.ZERO, item -> BigDecimalUtil.getZeroBigDeciamal(item.getMd02CgtTradeContQty()), BigDecimal::add)));
        list.forEach(item -> item.setExecuteQty(item.getExecuteQty().subtract(contOrderItemMap.getOrDefault(item.getAcCgtCartonCode() + "_" + item.getAcTwoLevelCigCode(), BigDecimal.ZERO))));
        return list;
    }

    /**
     * 获取配货参数
     *
     * @param icomCode                  工业公司编码
     * @param baComOrgCode              公司code编码
     * @param md02CgtDistRegionCode     配送区域编码
     * @param md02DistReceiveregionCode 收货区域编码
     * @return Mc04IslmcDistParmDTO
     */
    public Mc04IslmcDistParmDTO getDistParm(String icomCode, String baComOrgCode, String md02CgtDistRegionCode, String md02DistReceiveregionCode) {
        Mc04IslmcDistParmREQ req = new Mc04IslmcDistParmREQ();
        req.setMd02CgtDistRegionCode(StrUtil.isNotBlank(md02CgtDistRegionCode) ? md02CgtDistRegionCode : "");
        req.setMd02DistReceiveregionCode(StrUtil.isNotBlank(md02DistReceiveregionCode) ? md02DistReceiveregionCode : "");
        req.setMa02CgtTradeReqMembCode(baComOrgCode);
        req.setIcomCode(icomCode);
        SaleSingleResponse<List<Mc04IslmcDistParmDTO>> response = distParmApi.list(req);
        Assert.isTrue(response.isSuccess(), () -> new CustomException("获取配货参数失败"));
        if (CollectionUtil.isNotEmpty(response.getData())) {
            return response.getData().get(0);
        }
        return null;
    }

    /**
     * 获取配货订单
     *
     * @param mc04CgtDistOrderCode 配货订单代码
     * @return 配货订单
     */
    private List<Mc04IslmCgtDistOrderDO> getDistOrderDos(String mc04CgtDistOrderCode) {
        QueryWrapper<Mc04IslmCgtDistOrderDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderCode, mc04CgtDistOrderCode).eq(Mc04IslmCgtDistOrderDO::getMa02TobaProdTradeTypeCode, CommonConstants.NO);
        return mc04IslmCgtDistOrderService.list(queryWrapper);
    }

    /**
     * 初始化配货单
     *
     * @param icomCode                  工业编码
     * @param baComOrgCode              商业公司编码
     * @param md02CgtDistRegionCode     配送区域编码
     * @param md02DistReceiveregionCode 收货区域编码
     * @param distParmDto               配货参数
     * @return 实时配货单
     */
    private DistOrder initDistOrder(String icomCode, String baComOrgCode, String md02CgtDistRegionCode, String md02DistReceiveregionCode, Mc04IslmcDistParmDTO distParmDto) {
        // 中心获取配货参数
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String today = LocalDate.now().format(dateTimeFormatter);
        // 初始化
        DistOrder distOrder = new DistOrder();
        distOrder.setIcomCode(icomCode);
        distOrder.setBaComOrgCode(baComOrgCode);
        distOrder.setMd02CgtDistRegionCode(md02CgtDistRegionCode);
        distOrder.setMd02DistReceiveregionCode(md02DistReceiveregionCode);
        // 协议和月份默认取当前
        String month = today.substring(0, 6);
        distOrder.setMa02PlanMonth(month);
        distOrder.setMc04CgtXyPeriodCode(DateUtils.getAgreementPeriodCode(month));
        // 配货日期
        distOrder.setMc04CgtDistOrderDistDate(today);
        // 不存在配货单编号，初始化一条配货单编号
        List<ComItem> comItemList = comItemRepositoryImpl.getComItemList(CommonConstants.NO, baComOrgCode);
        List<DistOrderItem> distOrderItems = comItemList.stream().map(comItem -> {
            DistOrderItem orderItem = new DistOrderItem();
            orderItem.setAcCgtCartonCode(comItem.getAcCgtCartonCode());
            orderItem.setAcTwoLevelCigCode(comItem.getAcTwoLevelCigCode());
            orderItem.setMd03LogtTrayCombTspTrayType(comItem.getMd03LogtTrayCombTspTrayType());
            // 初始化值，防止前端不能双向绑定
            orderItem.setMc04CgtDistOrderSaleAreaReqQty(null);
            return orderItem;
        }).collect(Collectors.toList());
        // 获取配货参数
        if (ObjectUtil.isNotNull(distParmDto)) {
            // 发货日期
            distOrder.setMc04ContExpecOutDate(LocalDate.now().plusDays(BigDecimalUtil.getZeroBigDeciamal(distParmDto.getMd02CgtDistPrepDays()).intValue()).format(dateTimeFormatter));
            // 收货日期
            distOrder.setMc04ContExpecArrivalDate(LocalDate.parse(distOrder.getMc04ContExpecOutDate(), dateTimeFormatter).plusDays(BigDecimalUtil.getZeroBigDeciamal(distParmDto.getMd02CgtDistOnWayDays()).intValue()).format(dateTimeFormatter));
        } else {
            // 发货收货日期
            distOrder.setMc04ContExpecOutDate(LocalDate.now().plusDays(1).format(dateTimeFormatter));
            distOrder.setMc04ContExpecArrivalDate(LocalDate.now().plusDays(2).format(dateTimeFormatter));
        }

        // 设置卷烟发货仓库
        setDelivWhse(distOrderItems, baComOrgCode, icomCode);

        distOrder.setDistOrderItems(distOrderItems);
        return distOrder;
    }

    /**
     * 设置卷烟发货仓库
     *
     * @param distOrderItems 配货订单卷烟列表
     * @param baComOrgCode   商业公司编码
     * @param icomCode       工业公司编码
     */
    private void setDelivWhse(List<DistOrderItem> distOrderItems, String baComOrgCode, String icomCode) {
        // 二级牌号列表
        List<String> twoLevelCigCodeList = distOrderItems.stream().map(DistOrderItem::getAcTwoLevelCigCode).collect(Collectors.toList());
        // 各卷烟的默认发货仓库
        QueryWrapper<Mc04IslmcComItemContDelivWhseDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(Mc04IslmcComItemContDelivWhseDO::getAcTwoLevelCigCode, twoLevelCigCodeList).eq(Mc04IslmcComItemContDelivWhseDO::getBaComOrgCode, baComOrgCode);
        List<Mc04IslmcComItemContDelivWhseDO> delivList = mc04IslmcComItemContDelivWhseService.list(queryWrapper);
        Map<String, String> delivMap = delivList.stream().collect(Collectors.toMap(Mc04IslmcComItemContDelivWhseDO::getAcTwoLevelCigCode, Mc04IslmcComItemContDelivWhseDO::getMd02CgtOutStorehouseCode));
        // 机构参数设置的统一默认发货仓库
        String defaultDelivWhse = AcRuleUtil.getRuleValue(DistConstants.ISALE_ORDER_DIST_DEF_DELIV_WHSE, icomCode);
        distOrderItems.forEach(item -> {
            String delivWhse = delivMap.get(item.getAcTwoLevelCigCode());
            item.setMd02CgtOutStorehouseCode(StrUtil.isNotBlank(delivWhse) ? delivWhse : defaultDelivWhse);
        });
    }

    /**
     * 设置卷烟相关信息 卷烟名称、卷烟顺序、调拨价条、配货单位、是否新品、是否网配规格
     *
     * @param distOrder   配货订单
     * @param distParmDto 配货参数
     */
    @Override
    public void setCgtInfo(DistOrder distOrder, Mc04IslmcDistParmDTO distParmDto) {
        if (distParmDto == null) {
            distParmDto = getDistParm(distOrder.getIcomCode(), distOrder.getBaComOrgCode(), distOrder.getMd02CgtDistRegionCode(), distOrder.getMd02DistReceiveregionCode());
        }
        // 可能没有配货参数
        List<String> productCodeList = distParmDto == null ? new ArrayList<>() : distParmDto.getMc04IslmcDistParmItemList().stream().map(Mc04IslmcDistParmItemDTO::getAcCgtCartonCode).collect(Collectors.toList());
        List<DistOrderItem> distOrderItems = distOrder.getDistOrderItems();
        List<String> twoLevelCigCodeList = distOrderItems.stream().map(DistOrderItem::getAcTwoLevelCigCode).collect(Collectors.toList());
        Map<String, IccItemDetailDTO> iccItemDetailDtoMap = CommodityUtil.getIccItemDetailDtoMap(twoLevelCigCodeList);
        distOrderItems.forEach(item -> {
            IccItemDetailDTO iccItemDetail = iccItemDetailDtoMap.get(item.getAcTwoLevelCigCode());
            item.setAcCgtName(iccItemDetail.getProductName());
            item.setAcTwoLevelCigName(iccItemDetail.getAcTwoLevelCigName());
            item.setProductSeq(iccItemDetail.getProductSeq());
            // 调拨价条
            item.setAcMateTaxTranPr(iccItemDetail.getAcMateTaxTranPr());
            // 卷烟件装支数
            BigDecimal packageQty3 = new BigDecimal(iccItemDetail.getPackageQty3());
            // 配货单位=卷烟件装支数/10000
            item.setDistSize(packageQty3.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
            // 是否新品
            item.setIsNewProduct(iccItemDetail.getIsNewProduct());
            // 是否网配规格
            item.setIsDistParamProduct(productCodeList.contains(item.getAcCgtCartonCode()) ? CommonConstants.YES : CommonConstants.NO);
        });
        // 按卷烟顺序排序
        distOrderItems = distOrderItems.stream().sorted(Comparator.comparing(DistOrderItem::getProductSeq)).collect(Collectors.toList());
        distOrder.setDistOrderItems(distOrderItems);
    }

    /**
     * 设置地市名称，发运地区名称，收货地区名称
     *
     * @param distOrder 配货订单
     */
    private void setName(DistOrder distOrder) {
        String icomCode = distOrder.getIcomCode();
        String baComOrgCode = distOrder.getBaComOrgCode();
        BusiComDto busiComDto = CustUtil.getBusiComDto(baComOrgCode);
        Assert.notNull(busiComDto, () -> new CustomException("获取地市失败"));
        assert busiComDto != null;
        distOrder.setBaComOrgName(busiComDto.getMc04ComOrgShortName());
        // 发运地区编码不为空则设置名称
        String distRegionCode = distOrder.getMd02CgtDistRegionCode();
        if (StrUtil.isNotBlank(distRegionCode)) {
            // 配货发运地区查询
            QueryWrapper<NationDistRegionDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(NationDistRegionDO::getMemberCode, icomCode).eq(NationDistRegionDO::getRegionCode, distRegionCode);
            List<NationDistRegionDO> distRegionList = nationDistRegionService.list(queryWrapper);
            if (CollUtil.isNotEmpty(distRegionList)) {
                distOrder.setMd02CgtDistRegionName(distRegionList.get(0).getRegionName());
            }
        }
        // 收货地区编码不为空则设置名称
        String md02DistReceiveregionCode = distOrder.getMd02DistReceiveregionCode();
        if (StrUtil.isNotBlank(md02DistReceiveregionCode)) {
            // 配货收货地区查询
            QueryWrapper<NationDistReceiveRegionDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(NationDistReceiveRegionDO::getMa02CgtTradeReqMembCode, baComOrgCode).eq(NationDistReceiveRegionDO::getMd02DistReceiveregionCode, md02DistReceiveregionCode);
            List<NationDistReceiveRegionDO> distReceiveRegionDOList = nationDistReceiveRegionService.list(queryWrapper);
            if (CollUtil.isNotEmpty(distReceiveRegionDOList)) {
                distOrder.setMd02DistReceiveregionName(distReceiveRegionDOList.get(0).getMd02DistReceiveregionName());
            }
        }
    }

    /**
     * 获取协议数据
     *
     * @param param 协议参数
     * @return 协议数据
     */
    private Mc04IslmcXyDTO getAgreement(Mc04IslmcXyQueryREQ param) {
        // 获取协议数据
        Mc04IslmcXyQueryREQ request = BeanUtil.copyProperties(param, Mc04IslmcXyQueryREQ.class);
        SaleSingleResponse<List<Mc04IslmcXyDTO>> resp = xyApi.list(request);
        Assert.isTrue(resp.isSuccess() && CollectionUtil.isNotEmpty(resp.getData()), () -> new CustomException("获取协议数据失败"));
        return resp.getData().get(0);
    }

    /**
     * 判断当前日期是否在协议显示区间内
     *
     * @param split 协议显示区间
     * @return true表示在区间内
     */
    private boolean isInAgreeRange(List<String> split) {
        String today = DateUtils.getToday();
        // 判断当前日期是否在两期协议显示区间内
        for (String s : split) {
            List<String> split1 = StrUtil.split(s, "-");
            if (Integer.parseInt(today.substring(4, 8)) >= Integer.parseInt(split1.get(0), 10) && Integer.parseInt(today.substring(4, 8)) <= Integer.parseInt(split1.get(1), 10)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 增加配货地市
     *
     * @param result            配货地市列表
     * @param comCode           会员编码
     * @param busiComDto        业务公司信息
     * @param distRegionList    发货地区列表
     * @param receiveRegionList 收货地区列表
     */
    private void addDistOrderComs(List<DistOrderCom> result, String comCode, BusiComDto busiComDto, List<NationDistRegionDO> distRegionList, List<NationDistReceiveRegionDO> receiveRegionList) {
        if (CollUtil.isNotEmpty(distRegionList)) {
            // 有发货地区时的处理逻辑
            distRegionList.forEach(distRegionDO -> {
                if (CollectionUtil.isNotEmpty(receiveRegionList)) {
                    // 有收货地区配置
                    receiveRegionList.forEach(receiveRegionDO -> {
                        DistOrderCom item = createDistOrderCom(comCode, busiComDto, distRegionDO, receiveRegionDO);
                        result.add(item);
                    });
                } else {
                    // 无收货地区配置
                    DistOrderCom item = createDistOrderCom(comCode, busiComDto, distRegionDO, null);
                    result.add(item);
                }
            });
        } else {
            // 无发货地区时的处理逻辑
            if (CollectionUtil.isNotEmpty(receiveRegionList)) {
                // 有收货地区配置
                receiveRegionList.forEach(receiveRegionDO -> {
                    DistOrderCom item = createDistOrderCom(comCode, busiComDto, null, receiveRegionDO);
                    result.add(item);
                });
            } else {
                // 无收货地区配置
                DistOrderCom item = createDistOrderCom(comCode, busiComDto, null, null);
                result.add(item);
            }
        }
    }

    /**
     * 作废配货订单
     *
     * @param mc04CgtDistOrderCode 配货订单代码
     * @return 错误信息 空表示成功
     */
    @Override
    public String cancel(String mc04CgtDistOrderCode) {
        List<Mc04IslmCgtDistOrderDO> dbDistOrderDos = getDistOrderDos(mc04CgtDistOrderCode);
        // 如果存在先按配货订单代码删除
        if (CollectionUtil.isNotEmpty(dbDistOrderDos)) {
            // 更新作废状态
            updateDistOrderStatus(mc04CgtDistOrderCode, DistOrderStatusEnum.SALE_SUBMIT.getCode(), DistOrderStatusEnum.CANCEL.getCode());
        } else {
            return "配货订单不存在";
        }
        return null;
    }

    /**
     * 配货订单状态
     *
     * @param mc04CgtDistOrderCode 配货订单代码
     * @param curStatus            当前状态
     * @param nextStatus           下一个状态
     */

    @Override
    public void updateDistOrderStatus(String mc04CgtDistOrderCode, String curStatus, String nextStatus) {
        saveBatchDistOrderStatus(Collections.singletonList(mc04CgtDistOrderCode), curStatus, nextStatus, null);
    }

    /**
     * 反馈配货订单给行业子系统
     *
     * @param mc04CgtDistOrderCode 配货订单代码
     * @return 错误信息 空表示成功
     */
    @Override
    public String pushDistToNation(String mc04CgtDistOrderCode) {
        // 先查询配货单
        DistOrder dbDistOrder = getDistOrder(mc04CgtDistOrderCode);
        if (ObjectUtil.isNotEmpty(dbDistOrder)) {
            if (!StrUtil.equals(dbDistOrder.getMc04CgtDistOrderStatus(), DistOrderStatusEnum.SALE_FEEDBACK.getCode())) {
                return "配货单状态异常";
            }
            return iSaleNationSubsysRepository.pushDistToNation(mc04CgtDistOrderCode);
        } else {
            return "未找到该配货单";
        }
    }

    /**
     * 推送删除配货订单
     *
     * @param mc04CgtDistOrderCode 配货订单代码
     * @return 错误信息 空表示成功
     */
    @Override
    public String pushDeleteDistToNation(String mc04CgtDistOrderCode) {
        // 行业配货单生效状态 2已作废
        String effectStatusCancel = "2";
        // 查询nation_dist表如果有调用删除接口
        QueryWrapper<NationDistDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(NationDistDO::getItfpk, mc04CgtDistOrderCode).lt(NationDistDO::getEffectStatus, effectStatusCancel);
        List<NationDistDO> nationDistDos = nationDistService.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(nationDistDos)) {
            // 有调用删除接口
            String msg = iSaleNationSubsysRepository.pushDeleteDistToNation(mc04CgtDistOrderCode);
            if (StrUtil.isBlank(msg)) {
                // 返回无错误信息标识成功，nation_dist表生效状态改成2已作废
                UpdateWrapper<NationDistDO> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(NationDistDO::getItfpk, mc04CgtDistOrderCode).set(NationDistDO::getEffectStatus, effectStatusCancel);
                nationDistService.update(updateWrapper);
            }
            return msg;
        }
        return null;
    }

    /**
     * 保存配货订单
     *
     * @param distOrder 配货订单
     * @param curStatus 当前状态
     * @param isSave    是否保存
     * @return 配货订单
     */
    @Override
    public DistOrder saveDistOrder(DistOrder distOrder, String curStatus, boolean isSave) {
        List<DistOrderItem> items = distOrder.getDistOrderItems();
        String mc04CgtDistOrderCode = distOrder.getMc04CgtDistOrderCode();
        // 配货订单数据处理
        List<Mc04IslmCgtDistOrderDO> distOrderDos = saveDistOrders(distOrder, items);
        if (!isSave) {
            // 保存日计划
            saveDistOrderDayPlans(mc04CgtDistOrderCode, distOrderDos);
            // 增加日志
            saveDistOrderLog(distOrder, curStatus);
        }
        return distOrder;
    }

    /**
     * 批量更新配货订单状态和对应值
     *
     * @param items     配货订单明细列表
     * @param curStatus 当前状态
     */
    @Override
    public void saveBatchDistOrderStatusAndQty(List<Mc04IslmCgtDistOrder> items, String curStatus) {
        String nextStatus = items.get(0).getMc04CgtDistOrderStatus();
        // 批量更新调用行业成功地配货订单的状态和对应的量
        mc04IslmCgtDistOrderService.updateBatchById(Mc04IslmCgtDistOrderToMc04IslmCgtDistOrderDOConverter.SELF.converterModelsToDos(items));
        // 增加日志
        List<String> mc04CgtDistOrderCodes = items.stream().map(Mc04IslmCgtDistOrder::getMc04CgtDistOrderCode).distinct().collect(Collectors.toList());
        saveDistOrderLogs(mc04CgtDistOrderCodes, curStatus, nextStatus);
    }

    /**
     * 批量更新配货订单状态
     *
     * @param mc04CgtDistOrderCodes 配货订单代码列表
     * @param curStatus             当前状态
     * @param nextStatus            下一个状态
     * @param mf04ApprovalInfo      审批意见
     */
    @Override
    public void saveBatchDistOrderStatus(List<String> mc04CgtDistOrderCodes, String curStatus, String nextStatus, String mf04ApprovalInfo) {
        // 批量更新配货订单的状态
        UpdateWrapper<Mc04IslmCgtDistOrderDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderCode, mc04CgtDistOrderCodes).set(StrUtil.isNotBlank(mf04ApprovalInfo), Mc04IslmCgtDistOrderDO::getMf04ApprovalInfo, mf04ApprovalInfo).set(Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderStatus, nextStatus);
        mc04IslmCgtDistOrderService.update(updateWrapper);
        // 增加日志
        saveDistOrderLogs(mc04CgtDistOrderCodes, curStatus, nextStatus);
    }

    /**
     * 获取月度计划数据
     *
     * @param param 配货订单列
     * @return 月度计划数据
     */
    @Override
    public List<DistOrderCols> queryMonthPlanData(DistOrderColsParam param) {
        String icomCode = IcomUtils.getIcomCode();
        param.setIcomCode(icomCode);
        List<String> ruleCodes = Arrays.asList(DistConstants.ISALE_ORDER_DIST_REMAIN_TYPE, DistConstants.ISALE_ORDER_DIST_BEGIN_STATUS);
        Map<String, String> ruleMap = getRules(ruleCodes, icomCode);
        String isaleOrderDistRemainType = ruleMap.get(DistConstants.ISALE_ORDER_DIST_REMAIN_TYPE);

        // 获取月度计划数据
        MonthSalePlanQueryREQ request = BeanUtil.copyProperties(param, MonthSalePlanQueryREQ.class);
        SaleSingleResponse<List<MonthSalePlanDTO>> response = monthSalePlanServiceApi.list(request);
        Assert.isTrue(response.isSuccess(), () -> new CustomException("获取月度计划数据失败"));
        List<MonthSalePlanDTO> monthPlanList = response.getData();

        // 执行量
        String isaleOrderDistBeginStatus = ruleMap.get(DistConstants.ISALE_ORDER_DIST_BEGIN_STATUS);
        // 方式1求执行量，周期内isaleOrderDistBeginStatus（10或20）到60状态的合同量
        List<DistOrderCols> contExecuteList = getExecuteQty(param, isaleOrderDistBeginStatus);
        Map<String, BigDecimal> contExecuteMap = contExecuteList.stream().collect(Collectors.toMap(DistOrderCols::getAcTwoLevelCigCode, DistOrderCols::getExecuteQty, (v1, v2) -> v1));
        // 方式2求执行量，周期内日计划的量 - 周期内已解除（90状态）合同的合同量
        List<DistOrderCols> dayPlanExecuteList = getExecuteQtyWithDayPlan(param);
        Map<String, BigDecimal> dayPlanExecuteMap = dayPlanExecuteList.stream().collect(Collectors.toMap(DistOrderCols::getAcTwoLevelCigCode, DistOrderCols::getExecuteQty, (v1, v2) -> v1));
        // 月计划调整量
        QueryWrapper<Mc04IslmMonthSalePlanAdjDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmMonthSalePlanAdjDO::getMa02PlanMonth, param.getMa02PlanMonth()).lt(Mc04IslmMonthSalePlanAdjDO::getMc04MonthSalePlanStatus, "40").eq(Mc04IslmMonthSalePlanAdjDO::getBaComOrgCode, param.getBaComOrgCode()).eq(Mc04IslmMonthSalePlanAdjDO::getMc04MonthSalePlanType, param.getMc04MonthSalePlanType()).eq(Mc04IslmMonthSalePlanAdjDO::getIcomCode, icomCode).eq(Mc04IslmMonthSalePlanAdjDO::getMc04IsLatestAdjust, CommonConstants.YES).lt(Mc04IslmMonthSalePlanAdjDO::getMc04CgtAllotPlanReportAdjQty, BigDecimal.ZERO);
        List<Mc04IslmMonthSalePlanAdjDO> adjustList = mc04IslmMonthSalePlanAdjService.list(queryWrapper);
        Map<String, Mc04IslmMonthSalePlanAdjDO> adjustMap = adjustList.stream().collect(Collectors.toMap(Mc04IslmMonthSalePlanAdjDO::getAcTwoLevelCigCode, v -> v, (v1, v2) -> v1));

        // 组织数据
        List<DistOrderCols> list = new ArrayList<>();
        monthPlanList.forEach(monthPlan -> {
            DistOrderCols item = new DistOrderCols();
            item.setDayPlanExecuteQty(dayPlanExecuteMap.get(monthPlan.getAcTwoLevelCigCode()));
            item.setContExecuteQty(contExecuteMap.get(monthPlan.getAcTwoLevelCigCode()));
            item.setOriginQty(monthPlan.getMa02CgtPlAdjustedQty());
            item.setContRemainQty(monthPlan.getMa02CgtPlAdjustedQty().subtract(BigDecimalUtil.getZeroBigDeciamal(contExecuteMap.get(monthPlan.getAcTwoLevelCigCode()))));
            item.setDayPlanRemainQty(monthPlan.getMa02CgtPlAdjustedQty().subtract(BigDecimalUtil.getZeroBigDeciamal(dayPlanExecuteMap.get(monthPlan.getAcTwoLevelCigCode()))));
            item.setAcTwoLevelCigCode(monthPlan.getAcTwoLevelCigCode());
            item.setAcCgtCartonCode(monthPlan.getAcCgtCartonCode());
            if (CommonConstants.YES.equals(isaleOrderDistRemainType)) {
                item.setRemainQty(item.getContRemainQty());
            } else {
                item.setRemainQty(item.getDayPlanRemainQty());
            }
            item.setAdjMonthQty(BigDecimalUtil.getBigDecimalOrZero(adjustMap.get(monthPlan.getAcTwoLevelCigCode()), Mc04IslmMonthSalePlanAdjDO::getMc04CgtAllotPlanReportAdjQty));
            list.add(item);
        });
        return list;
    }

    /**
     * 根据协议编码获取协议周期
     *
     * @param md02CgtXyNo 协议编码
     * @return 协议周期
     */
    @Override
    public String getMc04CgtXyPeriodCode(String md02CgtXyNo) {
        Mc04IslmcXyQueryREQ request = new Mc04IslmcXyQueryREQ();
        request.setMd02CgtXyNo(md02CgtXyNo);
        Mc04IslmcXyDTO xy = getAgreement(request);
        return xy.getMc04CgtXyPeriodCode();
    }

    /**
     * 获取协议数据
     *
     * @param param 配货订单列
     * @return 协议数据
     */
    @Override
    public List<DistOrderCols> queryAgreementData(DistOrderColsParam param) {
        String icomCode = IcomUtils.getIcomCode();
        List<String> ruleCodes = Arrays.asList(DistConstants.ISALE_ORDER_DIST_REMAIN_TYPE, DistConstants.ISALE_ORDER_DIST_BEGIN_STATUS);
        Map<String, String> ruleMap = getRules(ruleCodes, icomCode);
        String isaleOrderDistRemainType = ruleMap.get(DistConstants.ISALE_ORDER_DIST_REMAIN_TYPE);
        param.setIcomCode(icomCode);
        // 获取协议数据
        Mc04IslmcXyQueryREQ request = BeanUtil.copyProperties(param, Mc04IslmcXyQueryREQ.class);
        Mc04IslmcXyDTO xy = getAgreement(request);
        List<Mc04IslmcXyItemDTO> xyDistOrderItems = xy.getXyItems();

        // 执行量
        String isaleOrderDistBeginStatus = ruleMap.get(DistConstants.ISALE_ORDER_DIST_BEGIN_STATUS);
        // 方式1求执行量，周期内isaleOrderDistBeginStatus（10或20）到60状态的合同量
        List<DistOrderCols> contExecuteList = getExecuteQty(param, isaleOrderDistBeginStatus);
        Map<String, BigDecimal> contExecuteMap = contExecuteList.stream().collect(Collectors.toMap(DistOrderCols::getAcCgtCartonCode, DistOrderCols::getExecuteQty, (v1, v2) -> v1));
        // 方式2求执行量，周期内日计划的量 - 周期内已解除（90状态）合同的合同量
        List<DistOrderCols> dayPlanExecuteList = getExecuteQtyWithDayPlan(param);
        Map<String, BigDecimal> dayPlanExecuteMap = dayPlanExecuteList.stream().collect(Collectors.toMap(DistOrderCols::getAcCgtCartonCode, DistOrderCols::getExecuteQty, (v1, v2) -> v1));

        // 组织数据
        List<DistOrderCols> list = new ArrayList<>();
        xyDistOrderItems.forEach(xyItem -> {
            DistOrderCols item = new DistOrderCols();
            item.setOriginQty(xyItem.getMd02CgtXyAdjustedQty());
            item.setAcCgtCartonCode(xyItem.getAcCgtCartonCode());
            item.setContExecuteQty(contExecuteMap.get(xyItem.getAcCgtCartonCode()));
            item.setDayPlanExecuteQty(dayPlanExecuteMap.get(xyItem.getAcCgtCartonCode()));
            item.setContRemainQty(xyItem.getMd02CgtXyAdjustedQty().subtract(BigDecimalUtil.getZeroBigDeciamal(contExecuteMap.get(xyItem.getAcCgtCartonCode()))));
            item.setDayPlanRemainQty(xyItem.getMd02CgtXyAdjustedQty().subtract(BigDecimalUtil.getZeroBigDeciamal(dayPlanExecuteMap.get(xyItem.getAcCgtCartonCode()))));
            if (CommonConstants.YES.equals(isaleOrderDistRemainType)) {
                item.setRemainQty(item.getContRemainQty());
            } else {
                item.setRemainQty(item.getDayPlanRemainQty());
            }
            list.add(item);
        });
        return list;
    }

    /**
     * 获取周度计划数据
     *
     * @param param 配货订单列
     * @return 周度计划数据
     */
    @Override
    public List<DistOrderCols> queryWeekPlanData(DistOrderColsParam param) {

        String icomCode = IcomUtils.getIcomCode();
        param.setIcomCode(icomCode);

        // 获取周计划数据
        WeekSalePlanQueryREQ request = BeanUtil.copyProperties(param, WeekSalePlanQueryREQ.class);
        SaleSingleResponse<List<WeekSalePlanDTO>> response = weekSalePlanServiceApi.list(request);
        Assert.isTrue(response.isSuccess(), () -> new CustomException("获取周计划数据失败"));
        List<WeekSalePlanDTO> weekPlanList = response.getData();

        // 执行量
        // 周计划只有方式2求执行量，周期内日计划的量 - 周期内已解除（90状态）合同的合同量
        List<DistOrderCols> dayPlanExecuteList = getExecuteQtyWithDayPlan(param);
        Map<String, BigDecimal> dayPlanExecuteMap = dayPlanExecuteList.stream().collect(Collectors.toMap(DistOrderCols::getAcTwoLevelCigCode, DistOrderCols::getExecuteQty));

        // 组织数据
        List<DistOrderCols> list = new ArrayList<>();
        weekPlanList.forEach(weekSalePlanDTO -> {
            DistOrderCols item = new DistOrderCols();
            BigDecimal originQty = BigDecimalUtil.getZeroBigDeciamal(weekSalePlanDTO.getMa02CgtPlQty());
            BigDecimal excuteQty = BigDecimalUtil.getZeroBigDeciamal(dayPlanExecuteMap.get(weekSalePlanDTO.getAcTwoLevelCigCode()));
            // 周计划量需要根据紧急货源调整  周紧缺货源可供量占比
            item.setOriginQty(originQty);
            item.setAcTwoLevelCigCode(weekSalePlanDTO.getAcTwoLevelCigCode());
            item.setAcCgtCartonCode(weekSalePlanDTO.getAcCgtCartonCode());
            item.setExecuteQty(excuteQty);
            item.setRemainQty(originQty.subtract(excuteQty));
            item.setMc03AvailableUnventory(weekSalePlanDTO.getMc03AvailableUnventory());
            list.add(item);
        });
        return list;
    }


    /**
     * 获取月度计划类型
     *
     * @param monthCode 月度编码
     * @param cgtType   卷烟类型
     * @return 月度计划类型 map K:月计划类型编码 V:月计划类型名称
     */
    @Override
    public List<Map<String, String>> queryMonthPlanType(String monthCode, String cgtType) {

        MonthSalePlanQueryREQ request = new MonthSalePlanQueryREQ();
        request.setMa02TobaProdTradeTypeCode(cgtType);
        request.setIcomCode(IcomUtils.getIcomCode());
        request.setMa02PlanMonth(monthCode);
        SaleSingleResponse<List<MonthSalePlanDTO>> response = monthSalePlanServiceApi.list(request);
        Assert.isTrue(response.isSuccess() && !Objects.isNull(response.getData()), () -> new CustomException("获取月度计划数据失败"));
        Set<String> types = response.getData().stream().map(MonthSalePlanDTO::getMc04MonthSalePlanType).collect(Collectors.toSet());
        //ISLM_DIST_MONTH_PLAN_TYPE
        List<AcDictMapDTO> islmDistMonthPlanType = AcDictUtil.getDictList("ISALE_PLAN_MONTH_PLAN_TYPE");
        List<Map<String, String>> res = new ArrayList<>();
        for (AcDictMapDTO item : islmDistMonthPlanType) {
            if (types.contains(item.getK())) {
                Map<String, String> map = new HashMap<>(4);
                map.put("K", item.getK());
                map.put("V", item.getV());
                res.add(map);
            }
        }
        return res;
    }

    /**
     * 获取配货公司列表
     *
     * @param distOrder 公司code编码列表
     * @return 配货公司列表
     */
    @Override
    public List<DistOrderCom> queryDistComList(DistOrder distOrder) {
        String icomCode = IcomUtils.getIcomCode();
        // 所有的协议单位用于取名称
        Collection<BusiComDto> allAgreementCom = CustUtil.getAllAgreementCom(icomCode);
        Map<String, BusiComDto> busiComDtoMap = allAgreementCom.stream().collect(Collectors.toMap(BusiComDto::getBaComOrgCode, Function.identity(), (v1, v2) -> v1));
        // 有权限的地市
        List<String> permissionComList = ISalePermissionUtils.getPermissionComList(distOrder.getBaComOrgCodes());
        // 过滤一下协议单位
        permissionComList.retainAll(busiComDtoMap.keySet());
        // 配货发运地区查询
        QueryWrapper<NationDistRegionDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(NationDistRegionDO::getMemberCode, icomCode).eq(NationDistRegionDO::getIsseal, "N");
        List<NationDistRegionDO> distRegionList = nationDistRegionService.list(queryWrapper);

        // 构建结果列表
        List<DistOrderCom> result = new ArrayList<>();
        List<NationDistReceiveRegionDO> distReceiveRegionDOList = new ArrayList<>();
        // 是否启用按仓库配货
        String enableWhse = AcRuleUtil.getRuleValue(DistConstants.ISALE_ORDER_DIST_ENABLE_WHSE, icomCode);
        if (CommonConstants.YES.equals(enableWhse)) {
            // 配货收货地区查询
            QueryWrapper<NationDistReceiveRegionDO> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.lambda().in(NationDistReceiveRegionDO::getMa02CgtTradeReqMembCode, permissionComList).eq(NationDistReceiveRegionDO::getIsseal, "N");
            distReceiveRegionDOList = nationDistReceiveRegionService.list(queryWrapper1);
        }

        Map<String, List<NationDistReceiveRegionDO>> reciveMap = distReceiveRegionDOList.stream().collect(Collectors.groupingBy(NationDistReceiveRegionDO::getMa02CgtTradeReqMembCode));
        permissionComList.forEach(comCode -> {
            BusiComDto busiComDto = busiComDtoMap.get(comCode);
            List<NationDistReceiveRegionDO> receiveRegionList = reciveMap.get(comCode);

            addDistOrderComs(result, comCode, busiComDto, distRegionList, receiveRegionList);
        });
        return result.stream().sorted(Comparator.comparing(DistOrderCom::getSeq, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(DistOrderCom::getMd02CgtDistRegionCode, Comparator.nullsLast(String::compareTo)).thenComparing(DistOrderCom::getMd02DistReceiveregionCode, Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());
    }


    /**
     * 查询配货公司列表是否显示额外列（发货地区列，收货地区列）
     *
     * @param distOrder 请求参数
     * @return 是否显示额外列（发货地区列，收货地区列）
     */
    @Override
    public DistOrderInitStatus queryDistComExtCols(DistOrder distOrder) {

        DistOrderInitStatus res = DistOrderInitStatus.builder().isShowDelivName(CommonConstants.YES).isShowSupplyName(CommonConstants.YES).build();
        List<String> permissionComList = PermissionUtils.getComCodeSceneAuthList();
        String icomCode = IcomUtils.getIcomCode();
        QueryWrapper<NationDistRegionDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(NationDistRegionDO::getMemberCode, icomCode).eq(NationDistRegionDO::getIsseal, "N");
        List<NationDistRegionDO> distRegionList = nationDistRegionService.list(queryWrapper);
        if (CollUtil.isEmpty(distRegionList)) {
            res.setIsShowSupplyName(CommonConstants.NO);
        }
        QueryWrapper<NationDistReceiveRegionDO> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.lambda().in(NationDistReceiveRegionDO::getMa02CgtTradeReqMembCode, permissionComList).eq(NationDistReceiveRegionDO::getIsseal, "N");
        List<NationDistReceiveRegionDO> distReceiveRegionDOList = nationDistReceiveRegionService.list(queryWrapper1);
        if (CollUtil.isEmpty(distReceiveRegionDOList)) {
            res.setIsShowDelivName(CommonConstants.NO);
        }
        return res;
    }

    /**
     * 查询配货公司列表
     *
     * @param domainReq 公司code编码列表
     * @return 配货公司列表
     */
    @Override
    public CustomPage<DistOrder> queryDistOrderPage(DistOrderPage domainReq) {
        CustomPage<DistOrder> page = new CustomPage<>(domainReq.getOffset(), domainReq.getLimit(), "", "");
        String icomCode = IcomUtils.getIcomCode();
        domainReq.setIcomCode(icomCode);
        return islmDistOrderMapper.queryDistOrderWithPage(page, domainReq);
    }

    /**
     * 获取配货订单列表
     *
     * @param distOrder 配货订单查询条件
     * @return 配货订单列表
     */
    @Override
    public List<DistOrder> queryDistOrderList(DistOrder distOrder) {
        return islmDistOrderMapper.queryDistOrderList(distOrder);
    }

    /**
     * 获取数据周期列表
     *
     * @param mc04IndDataPeriod 数据周期列表
     * @return 数据周期列表
     */
    @Override
    public List<Mc04IndDataPeriod> queryDataPeriodList(Mc04IndDataPeriod mc04IndDataPeriod) {
        // 日期类型为空就查询业务规则查看类型
        String icomCode = IcomUtils.getIcomCode();
        if (StrUtil.isBlank(mc04IndDataPeriod.getMc04DatePeriodType())) {
            String isaleOrderDistWeekType = AcRuleUtil.getRuleValue(DistConstants.ISALE_ORDER_DIST_WEEK_TYPE, icomCode);
            mc04IndDataPeriod.setMc04DatePeriodType(isaleOrderDistWeekType);
        }
        String isaleOrderDistWeekCount = AcRuleUtil.getRuleValue(DistConstants.ISALE_ORDER_DIST_WEEK_COUNT, icomCode);
        // 默认取两周
        isaleOrderDistWeekCount = StrUtil.isBlank(isaleOrderDistWeekCount) ? "2" : isaleOrderDistWeekCount;
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String weekCountDay = LocalDate.now().plusWeeks(Integer.parseInt(isaleOrderDistWeekCount)).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        LambdaQueryWrapper<Mc04IndDataPeriodDO> queryWrapper = Wrappers.lambdaQuery(Mc04IndDataPeriodDO.class);
        // 如果是明细页面 不用按时间段取值 直接按周期编码取
        queryWrapper.eq(StrUtil.isNotBlank(mc04IndDataPeriod.getMd04MgOccurrenceMonth()), Mc04IndDataPeriodDO::getMd04MgOccurrenceMonth, mc04IndDataPeriod.getMd04MgOccurrenceMonth()).eq(StrUtil.isNotBlank(mc04IndDataPeriod.getZaOccurrenceYear()), Mc04IndDataPeriodDO::getZaOccurrenceYear, mc04IndDataPeriod.getZaOccurrenceYear()).eq(StrUtil.isNotBlank(mc04IndDataPeriod.getMc04DatePeriodType()), Mc04IndDataPeriodDO::getMc04DatePeriodType, mc04IndDataPeriod.getMc04DatePeriodType()).eq(StrUtil.isNotBlank(mc04IndDataPeriod.getMc04DatePeriodCode()), Mc04IndDataPeriodDO::getMc04DatePeriodCode, mc04IndDataPeriod.getMc04DatePeriodCode()).ge(StrUtil.isBlank(mc04IndDataPeriod.getMc04DatePeriodCode()), Mc04IndDataPeriodDO::getMc04DatePeriodEndDate, today).le(StrUtil.isBlank(mc04IndDataPeriod.getMc04DatePeriodCode()), Mc04IndDataPeriodDO::getMc04DatePeriodBeginDate, weekCountDay).eq(Mc04IndDataPeriodDO::getIcomCode, IcomUtils.getIcomCode());
        List<Mc04IndDataPeriodDO> list = mc04IndDataPeriodService.list(queryWrapper);
        return IndDataPeriodConverter.INSTANCE.converterDosToModels(list);
    }

    /**
     * 设置托盘容量
     *
     * @param distOrder 配货订单
     */
    @Override
    public void setTrayCap(DistOrder distOrder) {
        String icomCode = IcomUtils.getIcomCode();
        List<DistOrderItem> distOrderItems = distOrder.getDistOrderItems();
        List<String> twoLevelCigCodeList = distOrderItems.stream().map(DistOrderItem::getAcTwoLevelCigCode).collect(Collectors.toList());
        // 托盘容量
        QueryWrapper<Mc04IslmcContDelivWhseItemTrayDO> queryWrapper = new QueryWrapper<>();
        // 每个仓库的二级牌号托盘容量
        queryWrapper.lambda().in(Mc04IslmcContDelivWhseItemTrayDO::getAcTwoLevelCigCode, twoLevelCigCodeList).in(CollectionUtil.isNotEmpty(twoLevelCigCodeList), Mc04IslmcContDelivWhseItemTrayDO::getAcTwoLevelCigCode, twoLevelCigCodeList).eq(Mc04IslmcContDelivWhseItemTrayDO::getIcomCode, icomCode);
        List<Mc04IslmcContDelivWhseItemTrayDO> whseItemTrays = mc04IslmcContDelivWhseItemTrayService.list(queryWrapper);
        // 二级牌号对应的托盘容量 跟仓库无关
        Map<String, Mc04IslmcContDelivWhseItemTrayDO> whseItemTrayMap = whseItemTrays.stream().collect(Collectors.toMap(Mc04IslmcContDelivWhseItemTrayDO::getAcTwoLevelCigCode, item -> item, (v1, v2) -> v1));
        // 是否显示托盘容量列
        AtomicReference<String> showTrayCap = new AtomicReference<>(CommonConstants.NO);
        distOrderItems.forEach(item -> {
            Mc04IslmcContDelivWhseItemTrayDO mc04IslmcContDelivWhseItemTrayDO = whseItemTrayMap.get(item.getAcTwoLevelCigCode());
            // 托盘类型
            if (mc04IslmcContDelivWhseItemTrayDO != null && CommonConstants.YES.equals(item.getMd03LogtTrayCombTspTrayType())) {
                showTrayCap.set(CommonConstants.YES);
                // 托盘容量
                item.setMa02LogtIcTrayPalletTransQty(mc04IslmcContDelivWhseItemTrayDO.getMa02LogtIcTrayPalletTransQty());
            }
        });
        distOrder.setShowTrayCap(showTrayCap.get());
    }

    /**
     * 初始化实时配货单
     *
     * @param baComOrgCode              公司code编码
     * @param md02CgtDistRegionCode     配送区域编码
     * @param md02DistReceiveregionCode 收货区域编码
     * @param mc04CgtDistOrderCode      配货订单代码
     * @return 实时配货单
     */
    @Override
    public DistOrder initDistOrderRealTime(String baComOrgCode, String md02CgtDistRegionCode, String md02DistReceiveregionCode, String mc04CgtDistOrderCode) {
        // 中心获取配货参数
        DistOrder distOrder;
        String icomCode = IcomUtils.getIcomCode();
        // 获取配货参数
        Mc04IslmcDistParmDTO distParmDto = null;
        // 不存在配货单编号，初始化一条配货单
        if (StrUtil.isBlank(mc04CgtDistOrderCode)) {
            distParmDto = getDistParm(icomCode, baComOrgCode, md02CgtDistRegionCode, md02DistReceiveregionCode);
            distOrder = initDistOrder(icomCode, baComOrgCode, md02CgtDistRegionCode, md02DistReceiveregionCode, distParmDto);
        } else {
            // 存在配货单编号，查询
            distOrder = getDistOrder(mc04CgtDistOrderCode);
        }
        // 卷烟名称、卷烟顺序、调拨价条、配货单位、是否新品、是否网配规格
        setCgtInfo(distOrder, distParmDto);
        // 设置地市名称，发运地区名称，收货地区名称
        setName(distOrder);
        // 托盘容量
        setTrayCap(distOrder);
        // 添加业务规则
        addRules(distOrder);

        return distOrder;
    }

    /**
     * 添加业务规则
     *
     * @param distOrder 配货订单
     */
    @Override
    public void addRules(DistOrder distOrder) {
        String baComOrgCode = distOrder.getBaComOrgCode();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String today = LocalDate.now().format(dateTimeFormatter);
        // 添加业务规则
        List<String> ruleCodes = Arrays.asList(DistConstants.ISALE_ORDER_DIST_CTR_FA_DAY, DistConstants.ISALE_ORDER_DIST_ENABLE_WHSE, DistConstants.ISALE_ORDER_DIST_SHOW_DELIVERY, DistConstants.ISALE_ORDER_DIST_CTR_AGREE, DistConstants.ISALE_ORDER_DIST_REMAIN_TYPE, DistConstants.ISALE_ORDER_DIST_TIME_RANGE, DistConstants.ISALE_ORDER_DIST_STATUS_RANGE, DistConstants.ISALE_ORDER_DIST_AGREE_RANGE, DistConstants.ISALE_ORDER_DIST_AGREE_TYPE, DistConstants.ISALE_ORDER_DIST_CTR_MONTH, DistConstants.ISALE_ORDER_DIST_NC_MONTH_COM, DistConstants.ISALE_ORDER_DIST_SHOW_WEEK, DistConstants.ISALE_ORDER_DIST_CTR_WEEK, DistConstants.ISALE_ORDER_DIST_NC_WEEK_COM, DistConstants.ISALE_ORDER_DIST_CTR_TRAY, DistConstants.ISALE_ORDER_DIST_CTR_STOCK, DistConstants.ISALE_ORDER_CONT_ZERO_RANGE);
        // 将业务规则值设置到DistOrder对象的对应字段中
        Map<String, String> ruleMap = getRules(ruleCodes, IcomUtils.getIcomCode());
        // 扎帐日和最后三个工作日
        distOrder.setCtrFaDay(ruleMap.get(DistConstants.ISALE_ORDER_DIST_CTR_FA_DAY));
        if (CommonConstants.YES.equals(ruleMap.get(DistConstants.ISALE_ORDER_DIST_CTR_FA_DAY))) {
            List<String> monthWorkdays = WorkdayUtils.getMonthWorkdays(today);
            List<String> lastThreeWorkDay = monthWorkdays.subList(monthWorkdays.size() - 3, monthWorkdays.size());
            distOrder.setLastThreeWorkDay(lastThreeWorkDay);
        }
        distOrder.setEnableWhse(ruleMap.get(DistConstants.ISALE_ORDER_DIST_ENABLE_WHSE));
        distOrder.setShowDelivery(ruleMap.get(DistConstants.ISALE_ORDER_DIST_SHOW_DELIVERY));
        distOrder.setCtrAgree(ruleMap.get(DistConstants.ISALE_ORDER_DIST_CTR_AGREE));
        distOrder.setRemainType(ruleMap.get(DistConstants.ISALE_ORDER_DIST_REMAIN_TYPE));
        distOrder.setTimeRange(ruleMap.get(DistConstants.ISALE_ORDER_DIST_TIME_RANGE));
        distOrder.setStatusRange(ruleMap.get(DistConstants.ISALE_ORDER_DIST_STATUS_RANGE));
        String isaleOrderDistAgreeRange = ruleMap.get(DistConstants.ISALE_ORDER_DIST_AGREE_RANGE);
        // 日期是否在此区间内 未配置有默认值1215-1230,0101-0115,0615-0715
        if (StrUtil.isBlank(isaleOrderDistAgreeRange)) {
            isaleOrderDistAgreeRange = "1215-1230,0101-0115,0615-0715";
        }
        // 判断当前日期是否在两期协议显示区间内
        List<String> split = StrUtil.split(isaleOrderDistAgreeRange, ",");
        distOrder.setAgreeRange(isInAgreeRange(split) ? CommonConstants.YES : CommonConstants.NO);
        distOrder.setAgreeType(ruleMap.get(DistConstants.ISALE_ORDER_DIST_AGREE_TYPE));
        distOrder.setCtrMonth(ruleMap.get(DistConstants.ISALE_ORDER_DIST_CTR_MONTH));

        String isaleOrderDistNcMonthCom = ruleMap.get(DistConstants.ISALE_ORDER_DIST_NC_MONTH_COM) == null ? "" : ruleMap.get(DistConstants.ISALE_ORDER_DIST_NC_MONTH_COM);
        // 配置的特殊地市不控制月计划
        if (CommonConstants.YES.equals(ruleMap.get(DistConstants.ISALE_ORDER_DIST_CTR_MONTH)) && StrUtil.split(isaleOrderDistNcMonthCom, DistConstants.SPLIT_CHAR).contains(baComOrgCode)) {
            distOrder.setCtrMonth(CommonConstants.NO);
        }
        distOrder.setShowWeek(ruleMap.get(DistConstants.ISALE_ORDER_DIST_SHOW_WEEK));
        distOrder.setCtrWeek(ruleMap.get(DistConstants.ISALE_ORDER_DIST_CTR_WEEK));
        String isaleOrderDistNcWeekCom = ruleMap.get(DistConstants.ISALE_ORDER_DIST_NC_WEEK_COM) == null ? "" : ruleMap.get(DistConstants.ISALE_ORDER_DIST_NC_WEEK_COM);
        // 配置的特殊地市不控制周计划
        if (CommonConstants.YES.equals(ruleMap.get(DistConstants.ISALE_ORDER_DIST_CTR_WEEK)) && StrUtil.split(isaleOrderDistNcWeekCom, DistConstants.SPLIT_CHAR).contains(baComOrgCode)) {
            distOrder.setCtrWeek(CommonConstants.NO);
        }
        distOrder.setCtrTray(ruleMap.get(DistConstants.ISALE_ORDER_DIST_CTR_TRAY));
        distOrder.setCtrStock(ruleMap.get(DistConstants.ISALE_ORDER_DIST_CTR_STOCK));
        distOrder.setContZeroRange(ruleMap.get(DistConstants.ISALE_ORDER_CONT_ZERO_RANGE));
    }

    /**
     * 获取配货订单
     *
     * @param mc04CgtDistOrderCode 配货订单代码
     * @return 配货订单
     */
    @Override
    public DistOrder getDistOrder(String mc04CgtDistOrderCode) {
        List<Mc04IslmCgtDistOrderDO> distOrderDos = getDistOrderDos(mc04CgtDistOrderCode);
        Assert.notEmpty(distOrderDos, () -> new CustomException("获取配货订单失败"));
        // 取第一条记录的数据
        Mc04IslmCgtDistOrderDO distOrderDO = distOrderDos.get(0);
        DistOrder distOrder = DistOrderConverter.SELF.converterDoToModel(distOrderDO);
        List<DistOrderItem> distOrderItems = DistOrderItemToMc04IslmCgtDistOrderDOConverter.SELF.converterDosToModels(distOrderDos);
        distOrder.setDistOrderItems(distOrderItems);
        return distOrder;
    }

    /**
     * 获取配货订单预览表
     *
     * @param nationDistPreview 配货订单预览
     * @return 配货订单预览
     */
    @Override
    public NationDistPreview getDistPreview(NationDistPreview nationDistPreview) {
        String supmemberCode = IcomUtils.getIcomCode();
        String reqmemberCode = nationDistPreview.getReqmemberCode();
        String distregionCode = StrUtil.isBlank(nationDistPreview.getDistregionCode()) ? "" : nationDistPreview.getDistregionCode();
        String md02DistReceiveregionCode = StrUtil.isBlank(nationDistPreview.getMd02DistReceiveregionCode()) ? "" : nationDistPreview.getMd02DistReceiveregionCode();
        String billDate = StrUtil.isBlank(nationDistPreview.getBillDate()) ? LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) : nationDistPreview.getBillDate();
        NationDistPreviewDO one = getDistPreviewDo(reqmemberCode, distregionCode, md02DistReceiveregionCode, billDate);
        // 因返回不是空 属性值为空
        if (ObjectUtil.isEmpty(one)) {
            // 如果没有数据先调用行业配货单预览接口再查询
            iSaleNationSubsysRepository.downloadDistPreview(billDate, supmemberCode, reqmemberCode, distregionCode, md02DistReceiveregionCode);
            // 调用接口后再查询
            one = getDistPreviewDo(reqmemberCode, distregionCode, md02DistReceiveregionCode, billDate);
            // 还空的话直接返回
            if (ObjectUtil.isEmpty(one)) {
                return NationDistPreviewConverter.INSTANCE.converterDoToModel(one);
            }
        }
        QueryWrapper<NationDistPreviewItemDO> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.lambda().eq(NationDistPreviewItemDO::getPk, one.getPk());
        List<NationDistPreviewItemDO> list = nationDistPreviewItemService.list(queryWrapper1);
        one.setNationDistPreviewItemList(list);
        return NationDistPreviewConverter.INSTANCE.converterDoToModel(one);
    }


    /**
     * 异步获取业务规则
     *
     * @return 业务规则map
     */
    @Override
    public Map<String, String> getRules(List<String> ruleCodes, String icomCode) {
        Map<String, String> ruleMap = new HashMap<>(4);
        if (CollectionUtil.isEmpty(ruleCodes)) {
            return ruleMap;
        }
        // 创建自定义线程池
        ExecutorService executorService = ThreadPoolManager.getCommonPool();
        try {
            // 创建 CompletableFuture 列表
            List<CompletableFuture<AbstractMap.SimpleEntry<String, String>>> futures = ruleCodes.stream().map(ruleCode -> CompletableFuture.supplyAsync(() -> new AbstractMap.SimpleEntry<>(ruleCode, AcRuleUtil.getRuleValue(ruleCode, icomCode)), executorService)).collect(Collectors.toList());

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            // 收集结果
            ruleMap = futures.stream().map(CompletableFuture::join).filter(entry -> entry != null && entry.getValue() != null).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        } catch (Exception e) {
            log.error("获取业务规则异常", e);
        } finally {
            executorService.shutdown();
        }
        return ruleMap;
    }

    /**
     * 查询二级牌号的日计划列表
     *
     * @return 日计划列表
     */
    @Override
    public List<DistOrderDayPlan> getTwoLevelDayPlans() {
        return islmDistOrderMapper.getTwoLevelDayPlans();
    }

    /**
     * 获取网配订单列表
     *
     * @param orderCodes 网配订单编码列表
     * @return 网配订单列表
     */
    @Override
    public List<Mc04IslmCgtDistOrder> getDistOrderList(Collection<String> orderCodes) {
        List<Mc04IslmCgtDistOrderDO> list = mc04IslmCgtDistOrderService.lambdaQuery().in(Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderCode, orderCodes).list();
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return Mc04IslmCgtDistOrderToMc04IslmCgtDistOrderDOConverter.SELF.converterDosToModels(list);
    }

    /**
     * 删除网配订单日计划
     *
     * @param distOrderCodeList 网配订单编码列表
     */
    @Override
    public void removeDayPlanByDistOrderCode(List<String> distOrderCodeList) {
        mc04IslmcCgtDayPlanService.lambdaUpdate().in(Mc04IslmcCgtDayPlanDO::getMc04CgtDistOrderCode, distOrderCodeList).remove();
    }

    /**
     * 保存网配订单日计划
     *
     * @param successDayPlanList 网配订单日计划列表
     */
    @Override
    public void saveBatchDayPlan(List<DistOrderDayPlan> successDayPlanList) {
        List<Mc04IslmcCgtDayPlanDO> dos = Mc04IslmcCgtDayPlanConverter.SELF.converterModelsToDos(successDayPlanList);
        mc04IslmcCgtDayPlanService.saveBatch(dos);
    }


    /**
     * 获取配货订单日志
     *
     * @param req 配货订单日志参数
     * @return 配货订单日志列表
     */
    @Override
    public List<DistOrderLog> getDistOrderLog(DistOrderLog req) {
        QueryWrapper<Mc04IslmcCgtDistOrderLogDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmcCgtDistOrderLogDO::getMc04CgtDistOrderCode, req.getMc04CgtDistOrderCode()).eq(Mc04IslmcCgtDistOrderLogDO::getIcomCode, IcomUtils.getIcomCode()).orderByAsc(Mc04IslmcCgtDistOrderLogDO::getCreateTime);
        List<Mc04IslmcCgtDistOrderLogDO> list = mc04IslmcCgtDistOrderLogService.list(queryWrapper);
        return DistOrderLogToDistOrderLogDOConverter.INSTANCE.converterDosToModels(list);
    }

}