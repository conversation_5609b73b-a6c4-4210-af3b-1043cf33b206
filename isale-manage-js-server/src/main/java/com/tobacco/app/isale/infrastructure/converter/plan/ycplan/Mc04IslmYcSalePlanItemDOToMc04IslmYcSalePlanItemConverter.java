/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.ycplan;

import com.tobacco.app.isale.domain.model.plan.ycplan.Mc04IslmYcSalePlanItem;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmYcSalePlanItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: loongxi
 * @Email: <EMAIL>
 * @Create: 2025-08-06
 */

@Mapper
public interface Mc04IslmYcSalePlanItemDOToMc04IslmYcSalePlanItemConverter extends StructureBaseConverter<Mc04IslmYcSalePlanItemDO, Mc04IslmYcSalePlanItem> {

        Mc04IslmYcSalePlanItemDOToMc04IslmYcSalePlanItemConverter INSTANCE =
            Mappers.getMapper(Mc04IslmYcSalePlanItemDOToMc04IslmYcSalePlanItemConverter.class);

}