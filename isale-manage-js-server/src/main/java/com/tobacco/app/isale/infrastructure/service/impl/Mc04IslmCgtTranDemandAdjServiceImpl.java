/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtTranDemandAdjDO;
import com.tobacco.app.isale.infrastructure.tunnel.database.supply.psc.allocdemand.adjust.Mc04IslmCgtTranDemandAdjMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmCgtTranDemandAdjService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: liwensheng
 * @Since: 2025-08-14
 * @Email: <EMAIL>
 * @Create: 2025-08-14
 */
@Service
public class Mc04IslmCgtTranDemandAdjServiceImpl extends ServiceImpl<Mc04IslmCgtTranDemandAdjMapper, Mc04IslmCgtTranDemandAdjDO> implements Mc04IslmCgtTranDemandAdjService {

}