package com.tobacco.app.isale.infrastructure.converter.order.ret.bill.apply; /**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */

import com.tobacco.app.isale.domain.model.order.ret.bill.apply.ApplyDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.apply.ApplyItemDomain;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtReturnBillDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtReturnBillItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ReturnBillItemDOToModelConverter extends StructureBaseConverter<Mc04IslmcCgtReturnBillItemDO, ApplyItemDomain> {
    ReturnBillItemDOToModelConverter INSTANCE = Mappers.getMapper(ReturnBillItemDOToModelConverter.class);
}

