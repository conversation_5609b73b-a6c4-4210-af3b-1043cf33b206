package com.tobacco.app.isale.infrastructure.entity;


import com.alibaba.bizworks.core.specification.Field;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 配货预览主表中间表 视图层对象/值对象
 * </p>
 *
 * @Author: jinfuli
 * @Since: 2025-08-11
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("nation_dist_preview")
public class NationDistPreviewDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标识编码
     */
    @Field(value = "标识编码", name = "标识编码")
    private String pk;

    /**
     * 单据日期
     */
    @Field(value = "单据日期", name = "单据日期")
    private String billDate;

    /**
     * 供方编码
     */
    @Field(value = "供方编码", name = "供方编码")
    private String supmemberCode;

    /**
     * 供方名称
     */
    @Field(value = "供方名称", name = "供方名称")
    private String supmemberName;

    /**
     * 需方编码
     */
    @Field(value = "需方编码", name = "需方编码")
    private String reqmemberCode;

    /**
     * 需方名称
     */
    @Field(value = "需方名称", name = "需方名称")
    private String reqmemberName;

    /**
     * 配货发运地区编码
     */
    @Field(value = "配货发运地区编码", name = "配货发运地区编码")
    private String distregionCode;

    /**
     * 配货发运地区名称
     */
    @Field(value = "配货发运地区名称", name = "配货发运地区名称")
    private String distregionName;

    /**
     * 配货收货地区编码
     */
    @Field(value = "配货收货地区编码", name = "配货收货地区编码")
    private String md02DistReceiveregionCode;

    /**
     * 配货收货地区名称
     */
    @Field(value = "配货收货地区名称", name = "配货收货地区名称")
    private String md02DistReceiveregionName;
    /**
     * 配货预览主表中间表 从表
     */
    @Field(value = "配货预览主表中间表 从表", name = "配货预览主表中间表 从表")
    @TableField(exist = false)
    private List<NationDistPreviewItemDO> nationDistPreviewItemList;

}
