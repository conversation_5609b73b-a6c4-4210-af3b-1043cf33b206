package com.tobacco.app.isale;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 启动器
 *
 * <AUTHOR>
 */
@EnableTransactionManagement
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class})
@EnableFeignClients(basePackages ={"com.tobacco.app" })
public class ISaleManageServerApplication {
	public static void main(String[] args) {
		SpringApplication.run(ISaleManageServerApplication.class, args);
	}
}
