/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.inspur.ind.base.CustomException;
import com.tobacco.app.isale.domain.model.com.BusiCom;
import com.tobacco.app.isale.domain.model.common.IndustryComInfo;
import com.tobacco.app.isale.domain.repository.common.CustRepository;
import com.tobacco.app.isale.infrastructure.converter.common.BusiComDtoToBusiComConverter;
import com.tobacco.app.isale.infrastructure.converter.common.GetIndustryComInfoByCodeDtoToIndustryComInfoConverter;
import com.tobacco.app.isale.tools.utils.CustUtil;
import com.tobacco.sc.icust.client.api.icom.IcomServiceAPI;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import com.tobacco.sc.icust.dto.common.KCSingleResponse;
import com.tobacco.sc.icust.dto.icom.GetIndustryComInfoByCodeDto;
import com.tobacco.sc.icust.req.icom.GetIndustryComInfoByCodeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/05/15 09:06
 * @description : 商品中心相关接口
 */
@Slf4j
@Component("ISaleCustRepositoryImplDORepository")
public class CustRepositoryImpl implements CustRepository {

    private IcomServiceAPI icomServiceApi;

    @Autowired
    public void setIcomServiceApi(IcomServiceAPI icomServiceApi) {
        this.icomServiceApi = icomServiceApi;
    }

    /**
     * 获取地市信息
     *
     * @param baComOrgCodeList 地市编码
     * @return Map<String, BusiCom>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-28 15:01:07
     * @description : 获取地市信息
     */
    @Override
    public Map<String, BusiCom> getBusiComMap(List<String> baComOrgCodeList) {
        Collection<BusiComDto> busiComDtos = CustUtil.getBusiComDtos(baComOrgCodeList);
        if (CollUtil.isEmpty(busiComDtos)) {
            return Collections.emptyMap();
        }
        List<BusiCom> busiComList = busiComDtos.stream()
                .map(BusiComDtoToBusiComConverter.INSTANCE::converterDoToModel)
                .collect(Collectors.toList());
        return busiComList.stream().collect(Collectors.toMap(BusiCom::getBaComOrgCode, Function.identity()));
    }

    /**
     * 获取工业信息
     *
     * @param icomCode 工业编码
     * @return IndustryComInfo
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-28 15:01:07
     * @description : 获取工业信息
     */
    @Override
    public IndustryComInfo getIndustryComInfo(String icomCode) {
        GetIndustryComInfoByCodeRequest getIndustryComInfoByCodeRequest = new GetIndustryComInfoByCodeRequest();
        getIndustryComInfoByCodeRequest.setIcomCode(icomCode);
        KCSingleResponse<GetIndustryComInfoByCodeDto> response =
                icomServiceApi.getIndustryComInfoByCode(getIndustryComInfoByCodeRequest);

        Assert.notNull(response, () -> new CustomException("客户中心获取工业信息失败"));
        Assert.isTrue(response.isSuccess(), () -> {
            log.error("客户中心获取工业信息失败, 客户中心返回失败信息:{}", response.getMessage());
            return new CustomException("客户中心获取工业信息失败");
        });
        GetIndustryComInfoByCodeDto data = response.getData();
        Assert.notNull(data, () -> new CustomException("商品中心获取商品信息失败"));

        return GetIndustryComInfoByCodeDtoToIndustryComInfoConverter.INSTANCE
                .converterDoToModel(data);
    }
}
