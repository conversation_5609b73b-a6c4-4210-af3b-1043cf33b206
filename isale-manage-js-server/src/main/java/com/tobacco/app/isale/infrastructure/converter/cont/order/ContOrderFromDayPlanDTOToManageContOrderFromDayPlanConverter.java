/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.cont.order;

import com.tobacco.app.isale.domain.model.cont.order.ManageContOrderFromDayPlan;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isalecenter.client.dto.cont.order.ContOrderFromDayPlanDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/05/22 19:31
 * @description : 数据转化
 */
@Mapper
public interface ContOrderFromDayPlanDTOToManageContOrderFromDayPlanConverter
        extends StructureBaseConverter<ContOrderFromDayPlanDTO, ManageContOrderFromDayPlan> {

    ContOrderFromDayPlanDTOToManageContOrderFromDayPlanConverter INSTANCE =
            Mappers.getMapper(ContOrderFromDayPlanDTOToManageContOrderFromDayPlanConverter.class);

}
