/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.yearplan;


import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmBrandPlanItemModel;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmBrandPlanItemRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmBrandPlanItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmBrandPlanItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmBrandPlanItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 品牌发展规划明细领域仓库实现
 *
 * @Author: longxi
 * @Since: 2025-08-07
 */
@Component("IslmBrandPlanItemRepository")
public class IslmBrandPlanItemRepositoryImpl implements IslmBrandPlanItemRepository {

    @Autowired
    private Mc04IslmBrandPlanItemService mc04IslmBrandPlanItemService;

    @Override
    public List<Mc04IslmBrandPlanItemModel> query() {
        return null;
    }

    @Override
    public List<Mc04IslmBrandPlanItemModel> submit() {
        return null;
    }

    @Override
    public List<Mc04IslmBrandPlanItemModel> listByBrandPlanId(String mc04CgtBrandPlanId) {
        LambdaQueryChainWrapper<Mc04IslmBrandPlanItemDO> lambdaQuery = mc04IslmBrandPlanItemService.lambdaQuery();
        List<Mc04IslmBrandPlanItemDO> doList = lambdaQuery.eq(Mc04IslmBrandPlanItemDO::getMc04BrandPlanId, mc04CgtBrandPlanId).list();
        return Mc04IslmBrandPlanItemConverter.INSTANCE.converterDosToModels(doList);
    }

    @Override
    public void saveBatch(List<Mc04IslmBrandPlanItemDO> qgBrandPlanItemDos) {
        mc04IslmBrandPlanItemService.saveBatch(qgBrandPlanItemDos);
    }
}
