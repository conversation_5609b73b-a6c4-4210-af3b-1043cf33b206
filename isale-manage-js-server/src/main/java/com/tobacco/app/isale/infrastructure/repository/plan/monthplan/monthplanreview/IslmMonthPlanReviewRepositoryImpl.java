/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.monthplan.monthplanreview;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.tobacco.app.isale.domain.enums.plan.monthplan.MonthSalePlanStatusEnum;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanreview.Mc04MonthPlanReviewQuery;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanreview.Mc04MonthPlanReviewQueryResult;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmitAdd;
import com.tobacco.app.isale.domain.repository.plan.monthplan.monthplanreview.IslmMonthPlanReviewRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.monthplanreview.MonthPlanReviewFlowConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.monthplansubmit.MonthPlanSubmitAddConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmMonthSalePlanMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanService;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan.monthplanreview.IslmMonthPlanReviewMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * @Author: jiaxiyang
 * @Date: 2025/8/05
 * @Description:
 */
@Component("IslmMonthPlanReviewRepository")
public class IslmMonthPlanReviewRepositoryImpl implements IslmMonthPlanReviewRepository {

    @Autowired
    Mc04IslmMonthSalePlanMapper insurancePlanMapper;

    @Autowired
    IslmMonthPlanReviewMapper islmMonthPlanReviewMapper;

    @Autowired
    Mc04IslmMonthSalePlanService islmMonthSalePlanService;

    /**
     * 查询卷烟树状态
     *
     * @param ma02PlanMonth
     * @param icomCode
     * @return
     */
    @Override
    public List<Map<String, Object>> queryCgtStatus(String ma02PlanMonth, String icomCode) {
        return insurancePlanMapper.queryCgtStatus(ma02PlanMonth, icomCode);
    }

    /**
     * 获取列表
     *
     */
    @Override
    public List<Mc04MonthPlanReviewQueryResult> getMonthPlanBusiList(Mc04MonthPlanReviewQuery condition) {
        return islmMonthPlanReviewMapper.getMonthPlanReviewList(condition);
    }

    @Override
    public String getDataDate() {
        return islmMonthPlanReviewMapper.getDataDate();
    }

    @Override
    public String getPlanSubjectType(String zaOccurrenceYear, String ma02TobaProdTradeTypeCode) {
        return islmMonthPlanReviewMapper.getPlanSubjectType(zaOccurrenceYear, ma02TobaProdTradeTypeCode);
    }

    @Override
    public List<Map<String, Object>> getSaleStkDayList(String acCgtCartonCode, String mc04PurchaseSaleStkDate) {
        return islmMonthPlanReviewMapper.getSaleStkDayList(acCgtCartonCode, mc04PurchaseSaleStkDate);
    }

    /**
     * 根据id批量更新
     *
     * @param monthPlanReviewFlows
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateBatchById(List<MonthPlanSubmitAdd> monthPlanReviewFlows) {
        List<Mc04IslmMonthSalePlanDO> monthSalePlanDOList = MonthPlanReviewFlowConverter.INSTANCE.converterModelsToDos(monthPlanReviewFlows);
        return islmMonthSalePlanService.updateBatchById(monthSalePlanDOList);
    }

    /**
     * 根据条件查询
     *
     * @param baComOrgCodes
     * @param ma02PlanMonth
     * @param mc04CgtSalePlanVersion
     * @param status
     * @return
     */
    @Override
    public List<MonthPlanSubmitAdd> selectList(List<String> baComOrgCodes, String ma02PlanMonth, String mc04CgtSalePlanVersion, String status) {
        QueryWrapper<Mc04IslmMonthSalePlanDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmMonthSalePlanDO::getMa02PlanMonth, ma02PlanMonth)
                .eq(Mc04IslmMonthSalePlanDO::getMc04CgtSalePlanVersion, mc04CgtSalePlanVersion)
                .eq(Mc04IslmMonthSalePlanDO::getMc04MonthSalePlanStatus, status)
                .in(Mc04IslmMonthSalePlanDO::getBaComOrgCode, baComOrgCodes);
        return MonthPlanSubmitAddConverter.INSTANCE.converterDosToModels(islmMonthSalePlanService.list(queryWrapper));
    }

    /**
     * 查询行情价格
     *
     * @param acCgtCartonCode 卷烟编码
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @return
     */
    @Override
    public List<Map<String, Object>> getMarketPrice(String acCgtCartonCode, String startTime, String endTime) {
        return islmMonthPlanReviewMapper.getMarketPrice(acCgtCartonCode, startTime, endTime);
    }

    /**
     * 根据id批量查询
     *
     * @param mc04MonthSalePlanItemIds
     * @return
     */
    @Override
    public List<MonthPlanSubmitAdd> selectBatchIds(List<String> mc04MonthSalePlanItemIds) {
        return MonthPlanSubmitAddConverter.INSTANCE.converterDosToModels(islmMonthSalePlanService.listByIds(mc04MonthSalePlanItemIds));
    }

    @Override
    public List<Map<String, Object>> getPurchSaleStk(List<String> baComOrgCodeList, String zaOccurrenceMonth, String acCgtCartonCode) {
        return islmMonthPlanReviewMapper.getPurchSaleStk(baComOrgCodeList, zaOccurrenceMonth, acCgtCartonCode);
    }

    @Override
    public List<Map<String, Object>> getPurchSaleStk(List<String> baComOrgCodeList, String zaOccurrenceMonth, List<String> acCgtCartonCodes) {
        return islmMonthPlanReviewMapper.getPurchSaleStkList(baComOrgCodeList, zaOccurrenceMonth, acCgtCartonCodes);
    }

    @Override
    public List<Map<String, Object>> getPlanMAllotActualtyExe(List<String> baComOrgCodeList, String zaOccurrenceMonth) {
        return islmMonthPlanReviewMapper.getPlanMAllotActualtyExe(baComOrgCodeList, zaOccurrenceMonth);
    }

    @Override
    public Boolean updateBatchByPlanIds(List<String> planIds) {
        UpdateWrapper<Mc04IslmMonthSalePlanDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(Mc04IslmMonthSalePlanDO::getMc04MonthSalePlanId, planIds)
                        .set(Mc04IslmMonthSalePlanDO::getMc04MonthSalePlanStatus, MonthSalePlanStatusEnum.REJECT.getCode());
        boolean updateFlag = islmMonthSalePlanService.update(updateWrapper);
        return updateFlag;
    }

    @Override
    public List<MonthPlanSubmitAdd> queryListByPlanIds(List<String> mc04MonthSalePlanIds) {
        QueryWrapper<Mc04IslmMonthSalePlanDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(Mc04IslmMonthSalePlanDO::getMc04MonthSalePlanId, mc04MonthSalePlanIds);
        List<Mc04IslmMonthSalePlanDO> mc04IslmMonthSalePlanDOS = islmMonthSalePlanService.list(queryWrapper);
        return MonthPlanSubmitAddConverter.INSTANCE.converterDosToModels(mc04IslmMonthSalePlanDOS);
    }
}
