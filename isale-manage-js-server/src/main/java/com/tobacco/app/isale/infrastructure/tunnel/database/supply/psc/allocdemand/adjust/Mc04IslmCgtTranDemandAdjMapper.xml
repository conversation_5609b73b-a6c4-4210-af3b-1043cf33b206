<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tobacco.app.isale.infrastructure.tunnel.database.supply.psc.allocdemand.adjust.Mc04IslmCgtTranDemandAdjMapper">
    <select id="queryAllocdemandAdjustPage" parameterType="map" resultType="com.tobacco.app.isale.domain.model.supply.psc.allocdemand.adjust.AllocdemandAdjustDomain">
        select
        -- 变更编码
        a.mc04_month_sale_plan_adj_id as mc04MonthSalePlanAdjId,
        -- 计划年月
        a.ma02_plan_month as ma02PlanMonth,
        -- 计划类型
        a.mc03_cgt_prod_pl_type as mc03CgtProdPlType,
        -- 变更日期
        a.mc04_cgt_tran_demand_adj_date as mc04CgtTranDemandAdjDate,
        -- 调拨需求变更(累加值)
        sum(b.ma02_cgt_pl_adjus_qty) as ma02CgtPlAdjusQtySum,

        sum(b.mc04_cgt_allot_plan_pd_confirm_adj_qty) as mc04CgtAllotPlanPdConfirmAdjQtySum,
        -- 制单人
        a.create_name as createName,
        -- 制单日期
        a.create_time as createTime,
        -- 状态
        a.mc04_cgt_tran_demand_adj_status as mc04CgtTranDemandAdjStatus
        from
        mc04_islm_cgt_tran_demand_adj a
        left join
        mc04_islm_cgt_tran_demand_adj_item b
        on a.mc04_month_sale_plan_adj_id = b.mc04_month_sale_plan_adj_id
        where
        1=1
        -- 逻辑删除标识，只查询未删除的数据
        -- 只查询状态不为90的数据
        and a.mc04_cgt_tran_demand_adj_status != '90'

        -- 时间周期代码查询条件(可为空)
        <if test="ma02PlanMonth != null and ma02PlanMonth !=''">
            and a.ma02_plan_month = #{ma02PlanMonth}
        </if>

        -- 计划类型查询条件(可为空)
        <if test="mc03CgtProdPlType != null and mc03CgtProdPlType !=''">
            and a.mc03_cgt_prod_pl_type = #{mc03CgtProdPlType}
        </if>

        -- 变更日期查询条件(可为空)
        <if test="mc04CgtTranDemandAdjDate != null and mc04CgtTranDemandAdjDate !=''">
            and a.mc04_cgt_tran_demand_adj_date = #{mc04CgtTranDemandAdjDate}
        </if>

        -- 状态查询条件(可为空)
        <if test="mc04CgtTranDemandAdjStatus != null and mc04CgtTranDemandAdjStatus !=''">
            and a.mc04_cgt_tran_demand_adj_status = #{mc04CgtTranDemandAdjStatus}
        </if>

        -- 按主表关键字段分组，满足聚合需求
        group by
        a.mc04_month_sale_plan_adj_id,
        a.ma02_plan_month,
        a.mc03_cgt_prod_pl_type,
        a.mc04_cgt_tran_demand_adj_date,
        a.create_name,
        a.create_time,
        a.mc04_cgt_tran_demand_adj_status
    </select>



</mapper>
