/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmMonthSalePlanMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: jinfuli
 * @Since: 2025-06-12
 * @Email: <EMAIL>
 * @Create: 2025-06-12
 */
@Service
public class Mc04IslmMonthSalePlanServiceImpl extends ServiceImpl<Mc04IslmMonthSalePlanMapper, Mc04IslmMonthSalePlanDO> implements Mc04IslmMonthSalePlanService {

}