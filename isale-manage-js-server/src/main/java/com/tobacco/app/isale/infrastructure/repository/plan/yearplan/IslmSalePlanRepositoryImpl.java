/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.yearplan;


import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.BaseFactor;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.base.CustomPage;
import com.tobacco.app.isale.domain.enums.plan.yearplan.YearPlanAllotCityStatusEnum;
import com.tobacco.app.isale.domain.enums.plan.yearplan.YearPlanOrgTypeEnum;
import com.tobacco.app.isale.domain.enums.plan.yearplan.YearPlanPeriodTypeEnum;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlan;
import com.tobacco.app.isale.domain.enums.plan.ycplan.LatestVersionEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.SalePlanStatusEnum;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanModel;
import com.tobacco.app.isale.domain.model.plan.yearplan.YearPlanAllotCenterPage;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmSalePlanRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmSalePlanDOToMc04IslmSalePlanConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmSalePlanDoConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSalePlanDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmSalePlanService;
import com.tobacco.app.isale.tools.utils.OrderedIdGenerator;
import com.tobacco.sc.icust.client.api.com.ComTreeServiceAPI;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 年度销售计划领域仓库实现
 *
 * @Author: longxi
 * @Since: 2025-08-07
 */
@Component("IslmSalePlanRepository")
public class IslmSalePlanRepositoryImpl implements IslmSalePlanRepository {

    @Autowired
    private Mc04IslmSalePlanService mc04IslmSalePlanService;
    @Resource
    private ComTreeServiceAPI comTreeServiceAPI;

    @Override
    public boolean save(Mc04IslmSalePlanModel mc04IslmSalePlanModel) {
        if (mc04IslmSalePlanModel != null) {
            mc04IslmSalePlanModel.setMc04SalePlanId(OrderedIdGenerator.generateOrderedId32());
        }
        Mc04IslmSalePlanDO mc04IslmSalePlanDo = Mc04IslmSalePlanDoConverter.INSTANCE.converterModelToDo(mc04IslmSalePlanModel);
        return mc04IslmSalePlanService.save(mc04IslmSalePlanDo);
    }

    @Override
    public boolean saveBatch(List<Mc04IslmSalePlanModel> mc04IslmSalePlanModelList) {
        mc04IslmSalePlanModelList.forEach(mc04IslmSalePlanModel -> mc04IslmSalePlanModel.setMc04SalePlanId(OrderedIdGenerator.generateOrderedId32()));
        List<Mc04IslmSalePlanDO> mc04IslmSalePlanDoList = Mc04IslmSalePlanDoConverter.INSTANCE.converterModelsToDos(mc04IslmSalePlanModelList);
        return mc04IslmSalePlanService.saveBatch(mc04IslmSalePlanDoList);
    }

    @Override
    public Page<Mc04IslmSalePlanModel> page(CustomPage<?> customPage, String startYear, String endYear, String type, String status,
                                            String orgTypeKind, String orgTypeCode, String periodType, String periodCode) {
        LambdaQueryWrapper<Mc04IslmSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(orgTypeKind)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04OrgTypeKind, orgTypeKind);
        }
        if (StrUtil.isNotBlank(orgTypeCode)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04OrgTypeCode, orgTypeCode);
        }
        if (StrUtil.isNotBlank(periodType)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodType, periodType);
        }
        if (StrUtil.isNotBlank(periodCode)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodCode, periodCode);
        }
        if (StrUtil.isNotBlank(type)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04PlanSubjectType, type);
        }
        if (StrUtil.isNotBlank(status)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04SalePlanStatus, status);
        }
        if (StrUtil.isNotBlank(startYear) && StrUtil.isNotBlank(endYear)) {
            queryWrapper.ge(Mc04IslmSalePlanDO::getZaOccurrenceYear, startYear)
                    .le(Mc04IslmSalePlanDO::getZaOccurrenceYear, endYear);
        }
        queryWrapper.eq(Mc04IslmSalePlanDO::getMc04IsLastestVersion, LatestVersionEnum.CURRENT.getCode())
                .orderByDesc(BaseFactor::getUpdateTime);
        long size = customPage.getSize();
        long current = customPage.getCurrent();
        Page<Mc04IslmSalePlanDO> page = new Page<>(current, size);
        Page<Mc04IslmSalePlanDO> doPage = mc04IslmSalePlanService.page(page, queryWrapper);
        List<Mc04IslmSalePlanModel> modelList = Mc04IslmSalePlanDoConverter.INSTANCE.converterDosToModels(doPage.getRecords());
        Page<Mc04IslmSalePlanModel> modelPage = new Page<>(current, size, doPage.getTotal());
        modelPage.setRecords(modelList);
        return modelPage;
    }

    @Override
    public List<Mc04IslmSalePlanModel> list(String zaOccurrenceYear, String planSubjectType, String status, String orgTypeKind, String orgTypeCodes, String mc04CgtSalePlanVersion, String isLatestVersion) {
        LambdaQueryWrapper<Mc04IslmSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(zaOccurrenceYear)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getZaOccurrenceYear, zaOccurrenceYear);
        }
        if (StrUtil.isNotBlank(planSubjectType)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04PlanSubjectType, planSubjectType);
        }
        if (StrUtil.isNotBlank(status)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04SalePlanStatus, status);
        }
        if (StrUtil.isNotBlank(orgTypeKind)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04OrgTypeKind, orgTypeKind);
        }
        if (StrUtil.isNotBlank(orgTypeCodes)) {
            String[] orgTypeCodeArray = orgTypeCodes.split(",");
            if (orgTypeCodeArray.length > 1) {
                queryWrapper.in(Mc04IslmSalePlanDO::getMc04OrgTypeCode, Arrays.asList(orgTypeCodeArray));
            } else {
                queryWrapper.eq(Mc04IslmSalePlanDO::getMc04OrgTypeCode, orgTypeCodeArray[0]);
            }
        }
        if (StrUtil.isNotBlank(mc04CgtSalePlanVersion)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSalePlanVersion, mc04CgtSalePlanVersion);
        }
        if (StrUtil.isNotBlank(isLatestVersion)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04IsLastestVersion, isLatestVersion);
        }
        List<Mc04IslmSalePlanDO> doList = mc04IslmSalePlanService.list(queryWrapper);
        return Mc04IslmSalePlanDoConverter.INSTANCE.converterDosToModels(doList);
    }


    @Override
    public Mc04IslmSalePlanModel getById(String mc04SalePlanId) {
        Mc04IslmSalePlanDO planDo = mc04IslmSalePlanService.getById(mc04SalePlanId);
        return Mc04IslmSalePlanDoConverter.INSTANCE.converterDoToModel(planDo);
    }

    @Override
    public boolean updateById(Mc04IslmSalePlanModel mc04IslmSalePlanModel) {
        return mc04IslmSalePlanService.updateById(Mc04IslmSalePlanDoConverter.INSTANCE.converterModelToDo(mc04IslmSalePlanModel));
    }

    @Override
    public boolean updateBatchById(List<Mc04IslmSalePlanModel> mc04IslmSalePlanModelList) {
        List<Mc04IslmSalePlanDO> mc04IslmSalePlanDoS = Mc04IslmSalePlanDoConverter.INSTANCE.converterModelsToDos(mc04IslmSalePlanModelList);
        boolean status = mc04IslmSalePlanService.updateBatchById(mc04IslmSalePlanDoS);
        if (status) {
            for (int i = 0; i < mc04IslmSalePlanDoS.size(); i++) {
                Mc04IslmSalePlanDO mc04IslmSalePlanDO = mc04IslmSalePlanDoS.get(i);
                Mc04IslmSalePlanModel mc04IslmSalePlanModel = mc04IslmSalePlanModelList.get(i);
                mc04IslmSalePlanModel.setMc04SalePlanId(mc04IslmSalePlanDO.getMc04SalePlanId());
            }
        }
        return status;
    }

    @Override
    public Mc04IslmSalePlanModel latest(String zaOccurrenceYear, String saleFoPeriodType, String saleFoPeriodCode) {
        LambdaQueryWrapper<Mc04IslmSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(saleFoPeriodType)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodType, saleFoPeriodType);
        }
        if (StrUtil.isNotBlank(saleFoPeriodCode)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodCode, saleFoPeriodCode);
        }
        queryWrapper.eq(Mc04IslmSalePlanDO::getZaOccurrenceYear, zaOccurrenceYear)
                .eq(Mc04IslmSalePlanDO::getMc04SalePlanStatus, SalePlanStatusEnum.PUBLISHED.getCode())
                .orderByDesc(Mc04IslmSalePlanDO::getMc04CgtSalePlanVersion);
        List<Mc04IslmSalePlanDO> list = mc04IslmSalePlanService.list(queryWrapper);
        if (list.size() > 0) {
            Mc04IslmSalePlanDO mc04IslmSalePlanDO = list.get(0);
            return Mc04IslmSalePlanDoConverter.INSTANCE.converterDoToModel(mc04IslmSalePlanDO);
        }
        return null;
    }

    @Override
    public List<Mc04IslmSalePlanModel> listByVersion(String zaOccurrenceYear, String mc04CgtSalePlanVersion, String mc04SalePlanStatus,
                                                     String mc04OrgTypeKind, String mc04OrgTypeCode, String periodType, String periodCode) {
        LambdaQueryWrapper<Mc04IslmSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(zaOccurrenceYear)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getZaOccurrenceYear, zaOccurrenceYear);
        } else {
            throw new CustomException("编制年份信息缺失无法查询");
        }
        if (StrUtil.isNotBlank(mc04SalePlanStatus)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04SalePlanStatus, mc04SalePlanStatus);
        }
        if (StrUtil.isNotBlank(mc04CgtSalePlanVersion)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSalePlanVersion, mc04CgtSalePlanVersion);
        }
        if (StrUtil.isNotBlank(mc04OrgTypeKind)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04OrgTypeKind, mc04OrgTypeKind);
        }
        if (StrUtil.isNotBlank(mc04OrgTypeCode)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04OrgTypeCode, mc04OrgTypeCode);
        }
        if (StrUtil.isNotBlank(periodType)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodType, periodType);
        }
        if (StrUtil.isNotBlank(periodCode)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodCode, periodCode);
        }
        queryWrapper.orderByDesc(BaseFactor::getCreateTime);
        return Mc04IslmSalePlanDoConverter.INSTANCE.converterDosToModels(mc04IslmSalePlanService.list(queryWrapper));
    }

    @Override
    public List<Mc04IslmSalePlanModel> getAllotPlanDataByMultiParam(String mc04SalePlanId, String salePlanStatus, List<BusiComDto> cityList, String cgtSaleFoPeriodCode, String cgtSaleFoPeriodType, String orgTypeKind) {
        // 获取基础数据 根据点击的某个省待分解计划id获取省分解计划
        Mc04IslmSalePlanDO provincePlan = mc04IslmSalePlanService.getById(mc04SalePlanId);
        // 构建查询条件, 查询条件为：计划主题名称、计划状态为30（待分解到地市）、计划周期（季度）、计划组织分类代码为省或地市代码
        LambdaQueryWrapper<Mc04IslmSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(Mc04IslmSalePlanDO::getMc04PlanSubjectName, provincePlan.getMc04PlanSubjectName()) // todo 数据库数据问题，临时先注释
        queryWrapper.eq(Mc04IslmSalePlanDO::getZaOccurrenceYear, provincePlan.getZaOccurrenceYear());

        // 待分解计划状态
        if (StrUtil.isNotBlank(salePlanStatus)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04SalePlanStatus, salePlanStatus);
        }
        // 计划组织分类代码
        if (StrUtil.isNotBlank(orgTypeKind)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04OrgTypeKind, orgTypeKind);
        }
        // 根据传入的参数决定使用periodCode还是periodType进行查询
        if (StrUtil.isNotBlank(cgtSaleFoPeriodType)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodType, cgtSaleFoPeriodType);
        }
        if (StrUtil.isNotBlank(cgtSaleFoPeriodCode)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodCode, cgtSaleFoPeriodCode);
        }

        // 处理地市参数，如果cityList为空或null，则只查询省计划数据
        if (cityList != null && !cityList.isEmpty()) {
            queryWrapper.in(Mc04IslmSalePlanDO::getMc04OrgTypeCode,
                    Stream.concat(
                            Stream.of(provincePlan.getMc04OrgTypeCode()),
                            cityList.stream().map(BusiComDto::getBaCityOrgCode)
                    ).collect(Collectors.toList())
            );
        }

        return Mc04IslmSalePlanDoConverter.INSTANCE.converterDosToModels(mc04IslmSalePlanService.list(queryWrapper));
    }

    @Override
    public List<Mc04IslmSalePlanModel> listByPlanIds(List<String> mc04SalePlanIds) {
        if (mc04SalePlanIds == null || mc04SalePlanIds.isEmpty()) {
            return new ArrayList<>();
        }
        List<Mc04IslmSalePlanDO> doList = mc04IslmSalePlanService.listByIds(mc04SalePlanIds);
        return Mc04IslmSalePlanDoConverter.INSTANCE.converterDosToModels(doList);
    }

    @Override
    public Long countByVersion(String zaOccurrenceYear, String salePlanVersion, String mc04OrgTypeKind, String mc04OrgTypeCode, String periodType, String periodCode) {
        LambdaQueryWrapper<Mc04IslmSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(zaOccurrenceYear)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getZaOccurrenceYear, zaOccurrenceYear);
        } else {
            throw new CustomException("编制年份信息缺失无法查询");
        }
        if (StrUtil.isNotBlank(salePlanVersion)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSalePlanVersion, salePlanVersion);
        }
        if (StrUtil.isNotBlank(mc04OrgTypeKind)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04OrgTypeKind, mc04OrgTypeKind);
        }
        if (StrUtil.isNotBlank(mc04OrgTypeCode)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04OrgTypeCode, mc04OrgTypeCode);
        }
        if (StrUtil.isNotBlank(periodType)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodType, periodType);
        }
        if (StrUtil.isNotBlank(periodCode)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodCode, periodCode);
        }
        return mc04IslmSalePlanService.count(queryWrapper);
    }

    @Override
    public Page<Mc04IslmSalePlan> queryPage(YearPlanAllotCenterPage yearPlanAllotCenterPage) {
        Page<Mc04IslmSalePlan> pageResult = new Page<>();
        //查主表
        CustomPage<Mc04IslmSalePlanDO> page = new CustomPage<>(
                yearPlanAllotCenterPage.getOffset(),
                yearPlanAllotCenterPage.getLimit(),
                yearPlanAllotCenterPage.getSort(),
                yearPlanAllotCenterPage.getOrder()
        );

        List<String> zaOccurrenceYearRange = yearPlanAllotCenterPage.getZaOccurrenceYearRange();
        String mc04PlanSubjectType = yearPlanAllotCenterPage.getMc04PlanSubjectType();
        String mc04SalePlanStatus = yearPlanAllotCenterPage.getMc04SalePlanStatus();
        //卷烟为0，雪茄烟为1
        String ma02TobaProdTradeTypeCode = "0";
        String startYear = null;
        String endYear = null;
        if (zaOccurrenceYearRange != null && zaOccurrenceYearRange.size() >= 2) {
            startYear = zaOccurrenceYearRange.get(0);
            endYear = zaOccurrenceYearRange.get(1);
        }


        //查询数据库
        LambdaQueryWrapper<Mc04IslmSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        // 处理年份范围 - 当zaOccurrenceYear为空时不添加年份条件
        if (StringUtils.isNotBlank(mc04PlanSubjectType)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04PlanSubjectType, mc04PlanSubjectType);
        }
        if (StringUtils.isNotBlank(mc04SalePlanStatus)) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04SalePlanStatus, mc04SalePlanStatus);
        }
        if (StringUtils.isNotBlank(startYear) && StringUtils.isNotBlank(endYear)) {
            queryWrapper.ge(Mc04IslmSalePlanDO::getZaOccurrenceYear, startYear)
                    .le(Mc04IslmSalePlanDO::getZaOccurrenceYear, endYear);
        }

        queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodType, YearPlanPeriodTypeEnum.YEAR.getCode());
        queryWrapper.eq(Mc04IslmSalePlanDO::getMc04OrgTypeKind, YearPlanOrgTypeEnum.QG.getKind());
        queryWrapper.orderByDesc(Mc04IslmSalePlanDO::getCreateTime);
        Page<Mc04IslmSalePlanDO> pageDoResult = mc04IslmSalePlanService.page(page, queryWrapper);
        if (ObjectUtil.isNull(pageDoResult) || pageDoResult.getTotal() == 0) {
            return pageResult;
        }
        pageResult.setRecords(
                pageDoResult.getRecords().stream().map(Mc04IslmSalePlanDOToMc04IslmSalePlanConverter.INSTANCE::converterDoToModel)
                        .collect(Collectors.toList())
        );
        pageResult.setTotal(pageDoResult.getTotal());
        pageResult.setCurrent(page.getCurrent());
        pageResult.setSize(page.getSize());
        pageResult.setPages(page.getPages());
        return pageResult;
    }

    @Override
    public Mc04IslmSalePlan getPlanById(String mc04SalePlanId) {
        return Mc04IslmSalePlanDOToMc04IslmSalePlanConverter.INSTANCE.converterDoToModel(mc04IslmSalePlanService.getById(mc04SalePlanId));
    }

    @Override
    public Mc04IslmSalePlan getLatestVersionSalePlanById(String mc04SalePlanId,boolean needLatest) {
        Mc04IslmSalePlanDO mc04IslmSalePlanDO = mc04IslmSalePlanService.getById(mc04SalePlanId);
        Assert.notNull(mc04IslmSalePlanDO, () -> new CustomException("当前计划不存在"));
        //检查这个计划是否是当前版本，避免获取历史版本
        if (needLatest){
            Assert.isTrue(mc04IslmSalePlanDO.getMc04IsLastestVersion().equals(LatestVersionEnum.CURRENT.getCode()), () -> new CustomException("当前计划不是最新版本！"));
        }
        //计划由多行plan 组成，所以主要通过这一行获取业务年份
        String zaOccurrenceYear = mc04IslmSalePlanDO.getZaOccurrenceYear();
        Assert.notNull(zaOccurrenceYear, () -> new CustomException("计划业务年份异常！请联系管理员"));
        //获取当前销售调拨计划版本
//        String mc04CgtSalePlanVersion = mc04IslmSalePlanDO.getMc04CgtSalePlanVersion();
//        Assert.notNull(mc04CgtSalePlanVersion, () -> new CustomException("计划版本异常！请联系管理员"));
        return Mc04IslmSalePlanDOToMc04IslmSalePlanConverter.INSTANCE.converterDoToModel(mc04IslmSalePlanDO);
    }

    @Override
    public List<Mc04IslmSalePlan> getSalePlanList(Mc04IslmSalePlan plan, List<String> provinceCodeList, String nextYear, String lastYear) {

        LambdaQueryWrapper<Mc04IslmSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        // 1.获取该省计划中库存、销售计划、调拨计划、预计库存
// 主逻辑变得非常清晰
        if (StringUtils.isBlank(nextYear)) {
            buildQueryForBlankNextYear(queryWrapper, plan, lastYear);
        } else {
            buildQueryForNextYear(queryWrapper, nextYear);
        }


        if (provinceCodeList != null && provinceCodeList.size() > 0) {
            queryWrapper.in(Mc04IslmSalePlanDO::getMc04OrgTypeCode, provinceCodeList);
        }
        if (StringUtils.isNotBlank(plan.getMc04PlanSubjectType())) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04PlanSubjectType, plan.getMc04PlanSubjectType());
        }
//        if (StringUtils.isNotBlank(plan.getMc04CgtSalePlanVersion())) {
//            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSalePlanVersion, plan.getMc04CgtSalePlanVersion());
//        }
        if (StringUtils.isNotBlank(plan.getMc04OrgTypeKind())) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04OrgTypeKind, plan.getMc04OrgTypeKind());
        }
        if (StringUtils.isNotBlank(plan.getMc04OrgTypeCode())) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04OrgTypeCode, plan.getMc04OrgTypeCode());
        }
        if (StringUtils.isNotBlank(plan.getMc04SalePlanStatus())) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04SalePlanStatus, plan.getMc04SalePlanStatus());
        }
        if (StringUtils.isNotBlank(plan.getMc04CgtSaleFoPeriodType())) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodType, plan.getMc04CgtSaleFoPeriodType());
        }
        if (StringUtils.isNotBlank(plan.getMc04IsLastestVersion())) {
            queryWrapper.eq(Mc04IslmSalePlanDO::getMc04IsLastestVersion, plan.getMc04IsLastestVersion());
        }
        List<Mc04IslmSalePlanDO> mc04IslmSalePlanDOList = mc04IslmSalePlanService.list(queryWrapper);
        return mc04IslmSalePlanDOList.stream().map(Mc04IslmSalePlanDOToMc04IslmSalePlanConverter.INSTANCE::converterDoToModel).collect(Collectors.toList());
    }

    @Override
    public boolean batchAddSalePlan(List<Mc04IslmSalePlan> mc04IslmSalePlanList) {
        return mc04IslmSalePlanService.saveBatch(mc04IslmSalePlanList.stream().map(Mc04IslmSalePlanDOToMc04IslmSalePlanConverter.INSTANCE::converterModelToDo).collect(Collectors.toList()));
    }


    @Override
    public boolean removeByIds(List<String> planIdList) {
        return mc04IslmSalePlanService.removeByIds(planIdList);
    }


    @Override
    public boolean updatePlanBatchById(List<Mc04IslmSalePlan> planList) {
        return mc04IslmSalePlanService.updateBatchById(planList.stream().map(Mc04IslmSalePlanDOToMc04IslmSalePlanConverter.INSTANCE::converterModelToDo).collect(Collectors.toList()));
    }

    private void buildQueryForBlankNextYear(LambdaQueryWrapper<Mc04IslmSalePlanDO> queryWrapper, Mc04IslmSalePlan plan, String lastYear) {
        if (StringUtils.isBlank(plan.getZaOccurrenceYear())) {
            return; // 如果基准年为空，则不做任何条件添加
        }

        int baseYear = Integer.parseInt(plan.getZaOccurrenceYear()) - 1;
        List<String> yearsToQuery = buildYearList(baseYear, plan, lastYear);

        if (!yearsToQuery.isEmpty()) {
            queryWrapper.in(Mc04IslmSalePlanDO::getZaOccurrenceYear, yearsToQuery);
        }
    }

    private List<String> buildYearList(int baseYear, Mc04IslmSalePlan plan, String lastYear) {
        List<String> years = new ArrayList<>();
        //总是添加 baseYear
        years.add(String.valueOf(baseYear));

        if (StringUtils.isNotBlank(lastYear)) {
            // 如果 lastYear 有值，直接添加它
            years.add(lastYear);
        } else {
            // 如果 lastYear 为空，根据类型决定添加哪个年份
            addYearBasedOnType(years, baseYear, plan);
        }
        return years;
    }

    private void addYearBasedOnType(List<String> years, int baseYear, Mc04IslmSalePlan plan) {
        years.add(plan.getZaOccurrenceYear());
        // 其他类型不添加额外年份
    }

    private void buildQueryForNextYear(LambdaQueryWrapper<Mc04IslmSalePlanDO> queryWrapper, String nextYear) {
        queryWrapper.eq(Mc04IslmSalePlanDO::getZaOccurrenceYear, nextYear);
    }

    @Override
    public List<Mc04IslmSalePlanModel> listAllCityRevise(String zaOccurrenceYear, String planSubjectType) {
        LambdaQueryWrapper<Mc04IslmSalePlanDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmSalePlanDO::getZaOccurrenceYear, zaOccurrenceYear);
        queryWrapper.eq(Mc04IslmSalePlanDO::getMc04PlanSubjectType, planSubjectType);
        queryWrapper.eq(Mc04IslmSalePlanDO::getMc04CgtSaleFoPeriodCode, zaOccurrenceYear);
        queryWrapper.eq(Mc04IslmSalePlanDO::getMc04OrgTypeKind, YearPlanOrgTypeEnum.CITY.getKind());
        //待发布状态视作分解完成
        queryWrapper.eq(Mc04IslmSalePlanDO::getMc04SalePlanStatus, YearPlanAllotCityStatusEnum.PROVINCIAL_PENDING.getCode());
        List<Mc04IslmSalePlanDO> list = mc04IslmSalePlanService.list(queryWrapper);
        return Mc04IslmSalePlanDoConverter.INSTANCE.converterDosToModels(list);
    }
}
