/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.supply.psc.allocdemand;

import com.tobacco.app.isale.domain.model.supply.psc.allocdemand.TranDemandAdjItemDomain;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtTranDemandAdjItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: qifengyu
 * @Email: <EMAIL>
 * @Create: 2025-07-25
 */

@Mapper

public interface TranDemandAdjItemDomainToDoConverter extends StructureBaseConverter<Mc04IslmCgtTranDemandAdjItemDO, TranDemandAdjItemDomain> {
    TranDemandAdjItemDomainToDoConverter INSTANCE = Mappers.getMapper(TranDemandAdjItemDomainToDoConverter.class);
}