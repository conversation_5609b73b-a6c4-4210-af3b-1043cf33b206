/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.monthplan;


import com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlan;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: jinfuli
 * @Date: 2025/6/13
 * @Description:
 */
@Mapper
public interface MonthPlanSubmitConverter extends StructureBaseConverter<Mc04IslmMonthSalePlanDO, Mc04IslmMonthSalePlan> {

    MonthPlanSubmitConverter INSTANCE = Mappers.getMapper(MonthPlanSubmitConverter.class);
    @Mappings({})
    @Override
    Mc04IslmMonthSalePlanDO converterModelToDo(Mc04IslmMonthSalePlan mc04IslmMonthSalePlan);
    @Mappings({})
    @Override
    Mc04IslmMonthSalePlan converterDoToModel(Mc04IslmMonthSalePlanDO DO);
    @Mappings({})
    @Override
    List<Mc04IslmMonthSalePlanDO> converterModelsToDos(List<Mc04IslmMonthSalePlan> mc04IslmMonthSalePlans);
    @Mappings({})
    @Override
    List<Mc04IslmMonthSalePlan> converterDosToModels(List<Mc04IslmMonthSalePlanDO> DOList);

}
