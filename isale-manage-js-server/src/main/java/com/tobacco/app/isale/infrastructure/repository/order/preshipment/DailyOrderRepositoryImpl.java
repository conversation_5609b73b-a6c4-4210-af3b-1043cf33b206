/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.order.preshipment;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.constant.CommonConstants;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.constants.DistConstants;
import com.tobacco.app.isale.domain.enums.cont.ContStatusEnum;
import com.tobacco.app.isale.domain.model.order.dist.order.DistOrder;
import com.tobacco.app.isale.domain.model.order.preshipment.*;
import com.tobacco.app.isale.domain.repository.order.dist.order.DistOrderRealtimeRepository;
import com.tobacco.app.isale.domain.repository.order.preshipment.DailyOrderRepository;
import com.tobacco.app.isale.infrastructure.converter.order.preshipment.DailyOrderConverter;
import com.tobacco.app.isale.infrastructure.converter.order.preshipment.DailyOrderItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtDistOrderDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseItemTrayDO;
import com.tobacco.app.isale.infrastructure.entity.NationDistReceiveRegionDO;
import com.tobacco.app.isale.infrastructure.entity.NationDistRegionDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmCgtDistOrderService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContDelivWhseItemTrayService;
import com.tobacco.app.isale.infrastructure.service.api.NationDistReceiveRegionService;
import com.tobacco.app.isale.infrastructure.service.api.NationDistRegionService;
import com.tobacco.app.isale.infrastructure.tunnel.database.cont.order.DailyOrderMapper;
import com.tobacco.app.isale.tools.utils.BigDecimalUtil;
import com.tobacco.app.isalecenter.client.api.cont.order.ContOrderApi;
import com.tobacco.app.isalecenter.client.api.order.distParm.DistParmAPI;
import com.tobacco.app.isalecenter.client.api.plan.monthplan.MonthSalePlanServiceAPI;
import com.tobacco.app.isalecenter.client.dto.common.SaleSingleResponse;
import com.tobacco.app.isalecenter.client.dto.cont.order.Mc04IslmcContOrderDTO;
import com.tobacco.app.isalecenter.client.dto.cont.order.Mc04IslmcContOrderItemDTO;
import com.tobacco.app.isalecenter.client.dto.order.distParm.Mc04IslmcDistParmDTO;
import com.tobacco.app.isalecenter.client.dto.plan.monthplan.MonthSalePlanDTO;
import com.tobacco.app.isalecenter.client.req.cont.order.Mc04IslmcContOrderREQ;
import com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmREQ;
import com.tobacco.app.isalecenter.client.req.plan.monthplan.MonthSalePlanQueryREQ;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: jinfuli
 * @Date: 2025/8/25
 * @Description: 日要货上报持久层实现类
 */
@Repository
public class DailyOrderRepositoryImpl implements DailyOrderRepository {

    private final DailyOrderMapper dailyOrderMapper;
    private final Mc04IslmCgtDistOrderService mc04IslmCgtDistOrderService;
    private final DistParmAPI distParmAPI;
    private final NationDistRegionService nationDistRegionService;
    private final NationDistReceiveRegionService nationDistReceiveRegionService;
    private final Mc04IslmcContDelivWhseItemTrayService mc04IslmcContDelivWhseItemTrayService;
    private final ContOrderApi contOrderApi;
    private final MonthSalePlanServiceAPI monthSalePlanServiceAPI;
    private final DistOrderRealtimeRepository distOrderRealtimeRepository;

    public DailyOrderRepositoryImpl(DailyOrderMapper dailyOrderMapper, Mc04IslmCgtDistOrderService mc04IslmCgtDistOrderService, DistParmAPI distParmAPI, NationDistRegionService nationDistRegionService, NationDistReceiveRegionService nationDistReceiveRegionService, Mc04IslmcContDelivWhseItemTrayService mc04IslmcContDelivWhseItemTrayService, ContOrderApi contOrderApi, MonthSalePlanServiceAPI monthSalePlanServiceAPI, DistOrderRealtimeRepository distOrderRealtimeRepository) {
        this.dailyOrderMapper = dailyOrderMapper;
        this.mc04IslmCgtDistOrderService = mc04IslmCgtDistOrderService;
        this.distParmAPI = distParmAPI;
        this.nationDistRegionService = nationDistRegionService;
        this.nationDistReceiveRegionService = nationDistReceiveRegionService;
        this.mc04IslmcContDelivWhseItemTrayService = mc04IslmcContDelivWhseItemTrayService;
        this.contOrderApi = contOrderApi;
        this.monthSalePlanServiceAPI = monthSalePlanServiceAPI;
        this.distOrderRealtimeRepository = distOrderRealtimeRepository;
    }

    /**
     * 查询协议单位日销售数据
     *
     * @param dailyOrderDomainREQ 查询参数
     * @return 协议单位日销售数据
     */
    @Override
    public List<XyOrgSaleDay> queryXyOrgSaleData(DailyOrderDomainREQ dailyOrderDomainREQ) {
        return dailyOrderMapper.queryXyOrgSaleData(dailyOrderDomainREQ);
    }

    /**
     * 查询协议单位日销售数据
     *
     * @param req 查询参数
     * @return 协议单位日销售数据
     */
    @Override
    public List<PreshipmentPlan> queryPreshipmentPlanData(DailyOrderDomainREQ req) {
        if (StrUtil.isBlank(req.getIcomCode())) {
            req.setIcomCode(IcomUtils.getIcomCode());
        }
        req.setZaOccurrenceYear(req.getMa02PlanMonth().substring(0, 4));
        return dailyOrderMapper.queryPreshipmentPlanData(req);
    }

    /**
     * 根据订单编号查询订单信息
     *
     * @param mc04CgtDistOrderCode 订单编号
     * @return 订单信息
     */
    @Override
    public DailyOrder getDailyOrder(String mc04CgtDistOrderCode) {
        LambdaQueryWrapper<Mc04IslmCgtDistOrderDO> eq = new LambdaQueryWrapper<Mc04IslmCgtDistOrderDO>().eq(Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderCode, mc04CgtDistOrderCode);
        List<Mc04IslmCgtDistOrderDO> list = mc04IslmCgtDistOrderService.list(eq);
        Assert.isTrue(CollectionUtil.isNotEmpty(list), () -> new CustomException("日要货上报单不存在！"));
        List<DailyOrderItem> dailyOrders = DailyOrderItemConverter.SELF.converterDosToModels(list);
        DailyOrder dailyOrder = DailyOrderConverter.SELF.converterDoToModel(list.get(0));
        dailyOrder.setDailyOrderItems(dailyOrders);
        return dailyOrder;
    }

    /**
     * 获取配货参数
     *
     * @param icomCode                  工业公司编码
     * @param baComOrgCode              公司code编码
     * @param md02CgtDistRegionCode     配送区域编码
     * @param md02DistReceiveregionCode 收货区域编码
     * @return Mc04IslmcDistParmDTO
     */
    public Mc04IslmcDistParmDTO getDistParm(String icomCode, String baComOrgCode, String md02CgtDistRegionCode, String md02DistReceiveregionCode) {
        Mc04IslmcDistParmREQ req = new Mc04IslmcDistParmREQ();
        req.setMd02CgtDistRegionCode(StrUtil.isNotBlank(md02CgtDistRegionCode) ? md02CgtDistRegionCode : "");
        req.setMd02DistReceiveregionCode(StrUtil.isNotBlank(md02DistReceiveregionCode) ? md02DistReceiveregionCode : "");
        req.setBaComOrgCode(baComOrgCode);
        req.setIcomCode(icomCode);
        SaleSingleResponse<List<Mc04IslmcDistParmDTO>> response = distParmAPI.list(req);
        Assert.isTrue(response.isSuccess(), () -> new CustomException("获取配货参数失败"));
        if (CollectionUtil.isNotEmpty(response.getData())) {
            return response.getData().get(0);
        }
        return null;
    }

    /**
     * 发运地区查询
     */
    @Override
    public List<NationDistRegion> getDistRegion(NationDistRegion nationDistRegion) {
        // 配货发运地区查询
        QueryWrapper<NationDistRegionDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(NationDistRegionDO::getMemberCode, nationDistRegion.getMemberCode())
                .eq(NationDistRegionDO::getRegionCode, nationDistRegion.getRegionCode());
        List<NationDistRegionDO> distRegionList = nationDistRegionService.list(queryWrapper);

        return distRegionList.stream().map(item -> {
            return new NationDistRegion(item.getPk(), item.getMemberCode(), item.getMemberName(), item.getRegionCode(), item.getRegionName(), item.getIsseal(), item.getRemark());
        }).collect(Collectors.toList());
    }

    /**
     * 收货地区查询
     *
     * @param nationDistReceiveRegion 收货地区
     * @return 收货地区列表
     */
    @Override
    public List<NationDistReceiveRegion> getDistReceiveRegion(NationDistReceiveRegion nationDistReceiveRegion) {
        // 配货收货地区查询
        QueryWrapper<NationDistReceiveRegionDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(NationDistReceiveRegionDO::getMa02CgtTradeReqMembCode, nationDistReceiveRegion.getMa02CgtTradeReqMembCode())
                .eq(NationDistReceiveRegionDO::getMd02DistReceiveregionCode, nationDistReceiveRegion.getMd02DistReceiveregionCode());
        List<NationDistReceiveRegionDO> distReceiveRegionDOList = nationDistReceiveRegionService.list(queryWrapper);
        return distReceiveRegionDOList.stream().map(distReceiveRegionDO -> new NationDistReceiveRegion(distReceiveRegionDO.getPk(), distReceiveRegionDO.getMa02CgtTradeReqMembCode(), distReceiveRegionDO.getMd02CgtTradeReqMembName(), distReceiveRegionDO.getMd02DistReceiveregionCode(), distReceiveRegionDO.getMd02DistReceiveregionName(), distReceiveRegionDO.getIsseal(), distReceiveRegionDO.getRemark())).collect(Collectors.toList());
    }

    /**
     * 获取卷烟发货仓库的托盘信息
     */
    @Override
    public List<Mc04IslmcContDelivWhseItemTray> getContDelivWhseItemTray(Mc04IslmcContDelivWhseItemTray req) {
        // 托盘容量
        QueryWrapper<Mc04IslmcContDelivWhseItemTrayDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(CollectionUtil.isNotEmpty(req.getAcTwoLevelCigCodse()), Mc04IslmcContDelivWhseItemTrayDO::getAcTwoLevelCigCode, req.getAcTwoLevelCigCodse())
                .eq(Mc04IslmcContDelivWhseItemTrayDO::getIcomCode, req.getIcomCode());
        List<Mc04IslmcContDelivWhseItemTrayDO> whseItemTrays =
                mc04IslmcContDelivWhseItemTrayService.list(queryWrapper);
        return whseItemTrays.stream().map(item -> {
            Mc04IslmcContDelivWhseItemTray mc04IslmcContDelivWhseItemTray = new Mc04IslmcContDelivWhseItemTray();
            mc04IslmcContDelivWhseItemTray.setMd02CgtOutStorehouseCode(item.getMd02CgtOutStorehouseCode());
            mc04IslmcContDelivWhseItemTray.setAcCgtCartonCode(item.getAcCgtCartonCode());
            mc04IslmcContDelivWhseItemTray.setAcTwoLevelCigCode(item.getAcTwoLevelCigCode());
            mc04IslmcContDelivWhseItemTray.setMa02LogtIcTrayPalletTransQty(item.getMa02LogtIcTrayPalletTransQty());
            mc04IslmcContDelivWhseItemTray.setMd03LogtTrayCombTspTrayType(item.getMd03LogtTrayCombTspTrayType());
            return mc04IslmcContDelivWhseItemTray;
        }).collect(Collectors.toList());
    }

    /**
     * 查询月开票累计和常规+零点月实际剩余计划
     *
     * @param req 公司编码，月份，烟草类型（卷烟，雪茄）
     * @return 月开票累计和常规+零点月实际剩余计划
     */
    @Override
    public List<MonthData> queryMonthData(DailyOrderDomainREQ req) {
        String icomCode = IcomUtils.getIcomCode();
        req.setIcomCode(icomCode);
        // 销售中心取合同量
        Mc04IslmcContOrderREQ mc04IslmcContOrderREQ = new Mc04IslmcContOrderREQ();
        mc04IslmcContOrderREQ.setIcomCode(icomCode);
        mc04IslmcContOrderREQ.setBaComOrgCode(req.getBaComOrgCode());
        mc04IslmcContOrderREQ.setMa02PlanMonth(req.getMa02PlanMonth());
        mc04IslmcContOrderREQ.setMa02TobaProdTradeTypeCode(req.getMa02TobaProdTradeTypeCode());
        SaleSingleResponse<List<Mc04IslmcContOrderDTO>> list = contOrderApi.list(mc04IslmcContOrderREQ);
        Assert.isTrue(list.isSuccess() && !Objects.isNull(list.getData()), () -> new CustomException("获取合同数据失败"));
        Map<String, BigDecimal> contOrderMap = list.getData().stream()
                .filter(item -> !Objects.isNull(item) && !ContStatusEnum.CONT_STATUS_90.getStatus().equals(item.getMc04CgtTradeContStatus()))
                .map(Mc04IslmcContOrderDTO::getContOrderItems)
                .filter(CollectionUtil::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.groupingBy(item -> item.getAcTwoLevelCigCode() + "-" + item.getAcCgtCartonCode(),
                        Collectors.reducing(BigDecimal.ZERO, Mc04IslmcContOrderItemDTO::getMd02CgtTradeContQty, BigDecimal::add)));
        // 查月计划
        MonthSalePlanQueryREQ monthReq = new MonthSalePlanQueryREQ();
        monthReq.setBaComOrgCode(req.getBaComOrgCode());
        monthReq.setMa02TobaProdTradeTypeCode(req.getMa02TobaProdTradeTypeCode());
        monthReq.setMa02PlanMonth(req.getMa02PlanMonth());
        monthReq.setIcomCode(req.getIcomCode());
        monthReq.setMc04MonthSalePlanTypes(Arrays.asList(DistConstants.MONTH_SALE_PLAN_TYPE_10, DistConstants.MONTH_SALE_PLAN_TYPE_10));
        SaleSingleResponse<List<MonthSalePlanDTO>> montResp = monthSalePlanServiceAPI.list(monthReq);
        Assert.isTrue(montResp.isSuccess() && !Objects.isNull(montResp.getData()), () -> new CustomException("获取月度计划数据失败"));
        List<MonthSalePlanDTO> monthData = montResp.getData();
        // 查日计划获得执行量
        List<MonthData> dayPlan = dailyOrderMapper.getDayPlan(req);
        Map<String, BigDecimal> dayMap = dayPlan.stream().collect(Collectors
                .toMap(item -> item.getAcTwoLevelCigCode() + "-" + item.getAcCgtCartonCode(),
                        v -> v.getRegularZeroQty(),
                        (v1, v2) -> v1));
        // 组织结果数据
        List<MonthData> res = new ArrayList<>();
        monthData.forEach(item -> {
            MonthData month = new MonthData();
            month.setBaComOrgCode(item.getBaComOrgCode());
            month.setAcTwoLevelCigCode(item.getAcTwoLevelCigCode());
            month.setAcCgtCartonCode(item.getAcCgtCartonCode());
            String key = item.getAcTwoLevelCigCode() + "-" + item.getAcCgtCartonCode();
            month.setMonthInvoiceSum(contOrderMap.get(key));
            month.setRegularZeroRemainQty(BigDecimalUtil.getZeroBigDeciamal(item.getMc04CgtSalePlanAdjustedQty().subtract(BigDecimalUtil.getZeroBigDeciamal(dayMap.get(key)))));
            contOrderMap.remove(key);
            res.add(month);
        });
        // 再遍历一下合同表
        contOrderMap.forEach((key, value) -> {
            MonthData month = new MonthData();
            String[] split = key.split("-");
            month.setBaComOrgCode(req.getBaComOrgCode());
            month.setAcTwoLevelCigCode(split[0]);
            month.setAcCgtCartonCode(split.length > 1 ? split[1] : "");
            month.setMonthInvoiceSum(value);
            res.add(month);
        });
        return res;
    }

    /**
     * 保存日要货单
     *
     * @param dailyOrder 日要货单
     * @param curStatus  当前状态
     * @param isSave     是否保存
     * @return 日要货单
     */
    @Override
    public DailyOrder saveDailyOrder(DailyOrder dailyOrder, String curStatus, boolean isSave) {
        List<DailyOrderItem> items = dailyOrder.getDailyOrderItems();
        String mc04CgtDistOrderCode = dailyOrder.getMc04CgtDistOrderCode();
        // 日要货单数据处理
        List<Mc04IslmCgtDistOrderDO> distOrderDos = saveDistOrders(dailyOrder, items);
        if (!isSave) {
            // 保存日计划
            distOrderRealtimeRepository.saveDistOrderDayPlans(mc04CgtDistOrderCode, distOrderDos);
            DistOrder distOrder = DistOrder.builder().mc04CgtDistOrderStatus(dailyOrder.getMc04CgtDistOrderStatus()).mc04CgtDistOrderCode(mc04CgtDistOrderCode).mc04CgtDistOrderClient(dailyOrder.getMc04CgtDistOrderClient()).build();
            // 增加日志
            distOrderRealtimeRepository.saveDistOrderLog(distOrder, curStatus);
        }
        return dailyOrder;
    }

    /**
     * 保存配货订单日计划
     *
     * @param dailyOrder 配货订单
     * @param items      配货订单信息
     */
    private List<Mc04IslmCgtDistOrderDO> saveDistOrders(DailyOrder dailyOrder, List<DailyOrderItem> items) {
        String mc04CgtDistOrderCode = dailyOrder.getMc04CgtDistOrderCode();
        // 先查询配货单
        QueryWrapper<Mc04IslmCgtDistOrderDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderCode, mc04CgtDistOrderCode).eq(Mc04IslmCgtDistOrderDO::getMa02TobaProdTradeTypeCode, CommonConstants.NO);
        List<Mc04IslmCgtDistOrderDO> dbDistOrderDos = mc04IslmCgtDistOrderService.list(queryWrapper);
        // 如果存在先按配货订单代码删除
        if (CollectionUtil.isNotEmpty(dbDistOrderDos)) {
            mc04IslmCgtDistOrderService.remove(new LambdaQueryWrapper<Mc04IslmCgtDistOrderDO>().eq(Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderCode, mc04CgtDistOrderCode));
        }
        List<Mc04IslmCgtDistOrderDO> distOrderDos = DailyOrderItemConverter.SELF.converterModelsToDos(items);
        // 配货订单数据处理
        processSaveDistOrder(distOrderDos, dailyOrder);
        // 因mybatis  plus 批量插入单条处理 实际很快、可以使用
        mc04IslmCgtDistOrderService.saveBatch(distOrderDos);
        return distOrderDos;
    }

    /**
     * 创建实时配货订单
     *
     * @param distOrderDos 需要保存的配货订单数据
     * @param dailyOrder   传过来的数据
     */
    private void processSaveDistOrder(List<Mc04IslmCgtDistOrderDO> distOrderDos, DailyOrder dailyOrder) {
        // 配货订单公共部分处理
        for (Mc04IslmCgtDistOrderDO item : distOrderDos) {
            // 处理必填字段
            item.setMc04CgtDistOrderCode(dailyOrder.getMc04CgtDistOrderCode());
            item.setBaComOrgCode(dailyOrder.getBaComOrgCode());
            item.setMa02TobaProdTradeTypeCode(dailyOrder.getMa02TobaProdTradeTypeCode());
            item.setMc04CgtDistOrderType(dailyOrder.getMc04CgtDistOrderType());
            item.setMc04CgtDistOrderStatus(dailyOrder.getMc04CgtDistOrderStatus());
            item.setMc04CgtDistOrderDistDate(dailyOrder.getMc04CgtDistOrderDistDate());
            item.setIcomCode(IcomUtils.getIcomCode());
            // 如果已经有创建时间等取原来值
            if (StrUtil.isNotEmpty(dailyOrder.getCreateId())) {
                item.setCreateId(dailyOrder.getCreateId());
                item.setCreateName(dailyOrder.getCreateName());
                item.setCreateTime(dailyOrder.getCreateTime());
            }
            // 处理用户选择的字段
            item.setMa02PlanMonth(dailyOrder.getMa02PlanMonth());
            item.setMc04MonthSalePlanType(dailyOrder.getMc04MonthSalePlanType());
            item.setMc04DatePeriodCode(dailyOrder.getMc04DatePeriodCode());
            item.setMd02CgtDistRegionCode(dailyOrder.getMd02CgtDistRegionCode());
            item.setMd02DistReceiveregionCode(dailyOrder.getMd02DistReceiveregionCode());
            item.setMd02CgtXyNo(dailyOrder.getMd02CgtXyNo());
            item.setMc04CgtXyPeriodCode(dailyOrder.getMc04CgtXyPeriodCode());
            item.setMc04ContExpecOutDate(dailyOrder.getMc04ContExpecOutDate());
            item.setMc04ContExpecArrivalDate(dailyOrder.getMc04ContExpecArrivalDate());
            item.setMd02CgtInStorehouseCode(dailyOrder.getMd02CgtInStorehouseCode());
            item.setMc04CgtPraInStorehouseCode(dailyOrder.getMc04CgtPraInStorehouseCode());
            item.setMd03LogtIcApptBegin(dailyOrder.getMd03LogtIcApptBegin());
            item.setMd03LogtIcApptEnd(dailyOrder.getMd03LogtIcApptEnd());
            item.setMc04ContZeroClockType(dailyOrder.getMc04ContZeroClockType());
            item.setMf04ApprovalInfo(dailyOrder.getMf04ApprovalInfo());
            item.setZaRemark(dailyOrder.getZaRemark());
        }
    }

}
