/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.order.ret.apply;

import com.tobacco.app.isale.domain.model.order.ret.apply.Mc04IslmContOrderPage;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: liuwancheng
 * @Email: <EMAIL>
 * @Create: 2025-07-30
 */

@Mapper

public interface Mc04IslmContOrderDOToMc04IslmContOrderPageConverter extends StructureBaseConverter<Mc04IslmContOrderDO, Mc04IslmContOrderPage> {

        Mc04IslmContOrderDOToMc04IslmContOrderPageConverter INSTANCE =
            Mappers.getMapper(Mc04IslmContOrderDOToMc04IslmContOrderPageConverter.class);

}