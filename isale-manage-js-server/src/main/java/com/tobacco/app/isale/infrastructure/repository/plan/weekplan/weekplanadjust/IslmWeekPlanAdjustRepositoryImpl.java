/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.weekplan.weekplanadjust;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tobacco.app.isale.domain.constants.WeekPlanConstants;
import com.tobacco.app.isale.domain.enums.common.ProdTradeTypeCodeEnum;
import com.tobacco.app.isale.domain.model.plan.weekplan.weekplanadjust.IslmWeekPlanAdjAdd;
import com.tobacco.app.isale.domain.model.plan.weekplan.weekplansubmit.IslmcWeekPlanItem;
import com.tobacco.app.isale.domain.repository.plan.weekplan.weekplanadjust.IslmWeekPlanAdjustRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.weekplan.weekplanadjust.Mc04IslmWeekSalePlanAdjConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.weekplan.weekplansubmit.Mc04IslmWeekSalePlanDoToWeekSalePlanRequestConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmWeekSalePlanAdjDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmWeekSalePlanDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmWeekSalePlanAdjService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmWeekSalePlanService;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.weekplan.weekplanadjust.IslmWeekPlanAdjustMapper;
import com.tobacco.app.isalecenter.client.api.plan.weekplan.WeekSalePlanServiceAPI;
import com.tobacco.app.isalecenter.client.req.plan.weekplan.WeekSalePlanRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/8/27
 * @description 描述
 */
@Slf4j
@Component
public class IslmWeekPlanAdjustRepositoryImpl implements IslmWeekPlanAdjustRepository {

    @Autowired
    private IslmWeekPlanAdjustMapper islmWeekPlanAdjustMapper;

    @Autowired
    private Mc04IslmWeekSalePlanAdjService mc04IslmWeekSalePlanAdjService;

    @Autowired
    private Mc04IslmWeekSalePlanService mc04IslmWeekSalePlanService;

    @Autowired
    private WeekSalePlanServiceAPI weekSalePlanServiceApi;

    /**
     * 查询月份对应的周编码列表
     * @param planMonth 计划月份
     * @return 周计划调整列表
     */
    @Override
    public List<String> findWeekPlanAdjustByCondition(String planMonth) {
        try {
            return islmWeekPlanAdjustMapper.findWeekPlanAdjustByCondition(planMonth);
        } catch (Exception e) {
            throw new RuntimeException("查询分解周代码失败");
        }
    }

    /**
     * 获取周计划调整列表详细数据
     * @param planMonth 计划月份
     * @param baComOrgCode 授权公司列表
     * @param week 周列表
     * @return 周计划调整列表
     */
    @Override
    public List<IslmcWeekPlanItem> findAdjustPlansByCondition(String planMonth, String baComOrgCode, List<String> week) {
        try {
            return  islmWeekPlanAdjustMapper.findAdjustPlansByCondition(planMonth, baComOrgCode, week);
        } catch (Exception e) {
            log.error("查询周计划上报列表失败，planMonth={}", planMonth, e);
            throw new RuntimeException("查询周计划上报列表失败");
        }
    }
/**
     * 获取解锁状态
     * @param baComOrgCodes 周计划调整列表
     * @return 解锁状态
     */
    @Override
    public List<Map<String, String>> getUnlockStatus(List<String> baComOrgCodes) {
        try {
            return islmWeekPlanAdjustMapper.getUnlockStatus(baComOrgCodes);
        } catch (Exception e) {

            throw new RuntimeException("获取解锁状态失败", e);
        }
    }

    /**
     * 获取全国计划总量
     * @param acTwoLevelCigCodes 周计划调整列表
     * @return 全国计划总量
     */
    @Override
    public List<Map<String, Object>> getNationalPlanTotals(List<String> acTwoLevelCigCodes) {
        try {
            return islmWeekPlanAdjustMapper.getNationalPlanTotals(acTwoLevelCigCodes);
        } catch (Exception e) {
            throw new RuntimeException("获取全国计划总量失败", e);
        }
    }

    /**
     * 保存周计划调整列表
     * @param islmWeekPlanAdjAdds 周计划调整列表
     * @return 保存结果
     */
    @Override
    public Boolean save(List<IslmWeekPlanAdjAdd> islmWeekPlanAdjAdds) {
        List<Mc04IslmWeekSalePlanAdjDO> doList = Mc04IslmWeekSalePlanAdjConverter.INSTANCE.converterModelsToDos(islmWeekPlanAdjAdds);
        return mc04IslmWeekSalePlanAdjService.saveBatch(doList);
    }

    /**
     * 更新周计划调整列表
     * @param planMonths 周计划调整列表
     * @param orgCodes 周计划调整列表
     * @param acCgtCartonCodes 周计划调整列表
     * @param mc04DatePeriodCodes 周计划调整列表
     * @param code 周计划调整列表
     */
    @Override
    public void updateLatestAdjust(List<String> planMonths, List<String> orgCodes, List<String> acCgtCartonCodes, List<String> mc04DatePeriodCodes, String code) {
        QueryWrapper<Mc04IslmWeekSalePlanAdjDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmWeekSalePlanAdjDO::getMa02TobaProdTradeTypeCode, code)
                .in(Mc04IslmWeekSalePlanAdjDO::getMa02TobaProdTradeTypeCode, planMonths)
                .in(Mc04IslmWeekSalePlanAdjDO::getBaComOrgCode, orgCodes)
                .in(Mc04IslmWeekSalePlanAdjDO::getAcCgtCartonCode, acCgtCartonCodes)
                .in(Mc04IslmWeekSalePlanAdjDO::getMc04DatePeriodCode, mc04DatePeriodCodes);
        Mc04IslmWeekSalePlanAdjDO mc04IslmWeekSalePlanAdjDO = new Mc04IslmWeekSalePlanAdjDO();
        mc04IslmWeekSalePlanAdjDO.setMc04IsLatestAdjust(WeekPlanConstants.MC04_IS_LATEST_ADJUST_ZERO);
        mc04IslmWeekSalePlanAdjService.update(mc04IslmWeekSalePlanAdjDO, queryWrapper);
    }

    /**
     * 更新周计划与中间表
     *
     * @param islmWeekPlanAdjAdds 周计划调整列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateWeekSalePlanAndWriteMiddleground(List<IslmWeekPlanAdjAdd> islmWeekPlanAdjAdds) {
        List<String> ma02PlanMonths = islmWeekPlanAdjAdds.stream().map(IslmWeekPlanAdjAdd::getMa02PlanMonth).distinct().collect(Collectors.toList());
        List<String> baComOrgCodes = islmWeekPlanAdjAdds.stream().map(IslmWeekPlanAdjAdd::getBaComOrgCode).distinct().collect(Collectors.toList());
        List<String> acCgtCartonCodes = islmWeekPlanAdjAdds.stream().map(IslmWeekPlanAdjAdd::getAcCgtCartonCode).distinct().collect(Collectors.toList());
        List<String> mc04DatePeriodCodes = islmWeekPlanAdjAdds.stream().map(IslmWeekPlanAdjAdd::getMc04DatePeriodCode).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<Mc04IslmWeekSalePlanDO> queryWrapper = Wrappers.lambdaQuery(Mc04IslmWeekSalePlanDO.class);
        queryWrapper.eq(Mc04IslmWeekSalePlanDO::getMa02TobaProdTradeTypeCode, ProdTradeTypeCodeEnum.PROD_TRADE_TYPE_CODE_0.getCode())
                .in(Mc04IslmWeekSalePlanDO::getMa02PlanMonth, ma02PlanMonths)
                .in(Mc04IslmWeekSalePlanDO::getBaComOrgCode, baComOrgCodes)
                .in(Mc04IslmWeekSalePlanDO::getAcCgtCartonCode, acCgtCartonCodes)
                .in(Mc04IslmWeekSalePlanDO::getMc04DatePeriodCode, mc04DatePeriodCodes);
        List<Mc04IslmWeekSalePlanDO> mc04IslmWeekSalePlanDoList = mc04IslmWeekSalePlanService.list(queryWrapper);

        List<Mc04IslmWeekSalePlanDO> updateList = new ArrayList<>();
        mc04IslmWeekSalePlanDoList.forEach(mc04IslmWeekSalePlanDo -> {
            IslmWeekPlanAdjAdd islmWeekPlanAdjAdd = islmWeekPlanAdjAdds.stream().filter(item -> item.getMa02PlanMonth().equals(mc04IslmWeekSalePlanDo.getMa02PlanMonth())
                    && item.getBaComOrgCode().equals(mc04IslmWeekSalePlanDo.getBaComOrgCode())
                    && item.getAcCgtCartonCode().equals(mc04IslmWeekSalePlanDo.getAcCgtCartonCode())
                    && item.getMc04DatePeriodCode().equals(mc04IslmWeekSalePlanDo.getMc04DatePeriodCode())).findFirst().orElse(null);
            if (islmWeekPlanAdjAdd != null) {
                mc04IslmWeekSalePlanDo.setMa02CgtPlQty(islmWeekPlanAdjAdd.getMa02CgtPlAdjustedQty());

                Mc04IslmWeekSalePlanDO update = new Mc04IslmWeekSalePlanDO();
                update.setMc04WeekPlanId(mc04IslmWeekSalePlanDo.getMc04WeekPlanId());
                update.setMa02CgtPlQty(islmWeekPlanAdjAdd.getMa02CgtPlAdjustedQty());
                updateList.add(update);
            }
        });
        // 更新mc04_islm_week_sale_plan表ma02_cgt_pl_qty字段
        boolean updateResult = mc04IslmWeekSalePlanService.updateBatchById(updateList);
        if (updateResult) {
            // 数据写入中台
            List<WeekSalePlanRequest> weekSalePlanRequests = Mc04IslmWeekSalePlanDoToWeekSalePlanRequestConverter.INSTANCE.converterDosToModels(mc04IslmWeekSalePlanDoList);
            weekSalePlanServiceApi.createBatch(weekSalePlanRequests);
        }
    }

}
