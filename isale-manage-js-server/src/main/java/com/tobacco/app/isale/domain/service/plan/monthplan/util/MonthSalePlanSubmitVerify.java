/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.domain.service.plan.monthplan.util;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.inspur.ind.base.IndResult;
import com.inspur.ind.icom.IcomUtils;
import com.inspur.x1.ac.rule.utils.AcRuleUtil;
import com.tobacco.app.isale.domain.constants.MonthPlanConstants;
import com.tobacco.app.isale.domain.enums.common.ProdTradeTypeCodeEnum;
import com.tobacco.app.isale.domain.enums.plan.monthplan.MonthSalePlanVerifyEnum;
import com.tobacco.app.isale.domain.model.monthplan.Mc04IslmMonthSalePlanLock;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanQuarterItem;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmitAdd;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmitBasicItem;
import com.tobacco.app.isale.domain.repository.plan.monthplan.monthplansubmit.MonthPlanSubmitRepository;
import com.tobacco.app.isale.tools.utils.CommodityUtil;
import com.tobacco.app.isale.tools.utils.ISaleDateUtil;
import com.tobacco.app.isalecenter.client.dto.plan.monthplan.MonthSalePlanDTO;
import com.tobacco.app.isalecenter.client.dto.xy.Mc04IslmcXyBatchDTO;
import com.tobacco.app.isalecenter.client.req.plan.monthplan.MonthSalePlanQueryBatchREQ;
import com.tobacco.app.isalecenter.client.req.xy.Mc04IslmcXyQueryBatchREQ;
import com.tobacco.sc.icommodity.dto.common.constant.dto.product.IccProductDetailDTO;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 月计划校验
 * @Author: hzs
 * @Date: 2025/8/12 15:47
 **/
@Component
public class MonthSalePlanSubmitVerify {

    private final MonthPlanSubmitRepository islmMonthPlanSubmitRepository;

    public MonthSalePlanSubmitVerify(MonthPlanSubmitRepository islmMonthPlanSubmitRepository) {
        this.islmMonthPlanSubmitRepository = islmMonthPlanSubmitRepository;
    }

    /**
     * 月计划上报校验
     *
     * @param req  请求数据
     * @param type 调用的类型，取MonthSalePlanVerifyEnum枚举中数据
     * @return
     */
    public IndResult<Object> verification(List<MonthPlanSubmitAdd> req, String type) {
        if (CollectionUtil.isEmpty(req)) {
            return IndResult.fail("月计划上报数据为空");
        }
        String zaOccurrenceMonth = req.get(0).getZaOccurrenceMonth();
        List<String> acCgtCartonCodeList = req.stream().map(MonthPlanSubmitAdd::getAcCgtCartonCode).distinct().collect(Collectors.toList());
        List<String> bacomOrgCodeList = req.stream().map(MonthPlanSubmitAdd::getBaComOrgCode).distinct().collect(Collectors.toList());

        //判断月计划量是否已经解锁
        Map<String, Boolean> unlockMap = isUnlock(req, type);

        //过滤出没解锁的地市
        List<MonthPlanSubmitAdd> filterReq = req.stream()
                .filter(req1 -> {
                    Boolean unlock = unlockMap.get(req1.getBaComOrgCode());
                    // 不为空，并且当unlock为false时才包含该元素
                    return unlock != null && !unlock;
                })
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(filterReq)) {
            return IndResult.success();
        }


        //调用商品中心获取卷烟件装支数
        Map<String, String> acCgtCartonPackageQty3Map = getAcCgtCartonPackageQty3Map(filterReq);

        //校验单规格填报量必须是该规格件的倍数
        IndResult indResult = verifyCigSpec(filterReq, acCgtCartonPackageQty3Map, type);
        if (indResult.isFail()) {
            return indResult;
        }
        //协议剩余量校验
        IndResult indResult1 = verifySelfYearPlan(filterReq, type);
        if (indResult1.isFail()) {
            return indResult1;
        }
        //获取卷烟季度调拨量
        Map<String, Map<String, BigDecimal>> quarterPlanMap = getQuarterPlan(filterReq);

        //获取当前月计划量
        Map<String, BigDecimal> currentMonthQty = getCurrentMonthQty(zaOccurrenceMonth, acCgtCartonCodeList, bacomOrgCodeList);
        //获取上个月计划量
        Map<String, BigDecimal> lastMonthQty = getLastMonthQty(zaOccurrenceMonth, bacomOrgCodeList);

        //根据isReport参数将月计划数据进行分组
        Map<String, Map<String, List<MonthPlanSubmitAdd>>> groupReqMap = getGroupReqMap(filterReq, type);

        //校验季度
        IndResult indResult2 = verifyQuarter(filterReq, zaOccurrenceMonth, groupReqMap, quarterPlanMap, type, currentMonthQty, lastMonthQty);

        return indResult2;
    }

    /**
     * 判断月计划量是否已经解锁
     *
     * @param req
     * @param type
     * @return
     */
    private Map<String, Boolean> isUnlock(List<MonthPlanSubmitAdd> req, String type) {
        Map<String, Boolean> map = new HashMap<>();
        if (MonthSalePlanVerifyEnum.REPORT.getCode().equals(type)) {
            Mc04IslmMonthSalePlanLock islmMonthSalePlanLock = islmMonthPlanSubmitRepository.getIslmMonthSalePlanLock(req.get(0).getBaComOrgCode(), MonthPlanConstants.BUSINESS_TYPE_CODE_30, MonthPlanConstants.TOBA_PROD_TRADE_TYPE_CODE_0);
            if (ObjUtil.isEmpty(islmMonthSalePlanLock) || MonthPlanConstants.AS_LOCK_STATE_1.equals(islmMonthSalePlanLock.getMa02AsLockState())) {
                map.put(req.get(0).getBaComOrgCode(), false);

            } else {
                map.put(req.get(0).getBaComOrgCode(), true);
            }
            return map;
        } else {
            List<String> baComOrgCodeList = req.stream().map(MonthPlanSubmitAdd::getBaComOrgCode).distinct().collect(Collectors.toList());
            List<Mc04IslmMonthSalePlanLock> planLock = islmMonthPlanSubmitRepository.getIslmMonthSalePlanLockList(baComOrgCodeList, MonthPlanConstants.BUSINESS_TYPE_CODE_30, MonthPlanConstants.TOBA_PROD_TRADE_TYPE_CODE_0);
            return planLock.stream()
                    .collect(Collectors.toMap(
                            Mc04IslmMonthSalePlanLock::getBaComOrgCode,
                            lock -> MonthPlanConstants.AS_LOCK_STATE_0.equals(lock.getMa02AsLockState())
                    ));
        }

    }

    /**
     * 根据isReport参数将月计划数据进行分组
     *
     * @param req
     * @param type
     * @return
     **/
    private Map<String, Map<String, List<MonthPlanSubmitAdd>>> getGroupReqMap(List<MonthPlanSubmitAdd> req, String type) {
        Map<String, Map<String, List<MonthPlanSubmitAdd>>> map = new HashMap<>();
        if (MonthSalePlanVerifyEnum.REPORT.getCode().equals(type)) {
            Map<String, List<MonthPlanSubmitAdd>> collect = req.stream()
                    .collect(Collectors.groupingBy(
                            MonthPlanSubmitAdd::getAcCgtCartonCode,
                            Collectors.toList()
                    ));
            map.put("cartonCode", collect);
            Map<String, List<MonthPlanSubmitAdd>> collect2 = req.stream()
                    .collect(Collectors.groupingBy(
                            MonthPlanSubmitAdd::getMa02PlanMonth,
                            Collectors.toList()
                    ));
            map.put("month", collect2);

            return map;
        } else {
            Map<String, List<MonthPlanSubmitAdd>> collect = req.stream()
                    .collect(Collectors.groupingBy(
                            item -> item.getBaComOrgCode() + "-" + item.getAcCgtCartonCode(),
                            Collectors.toList()
                    ));
            map.put("cartonCode", collect);
            Map<String, List<MonthPlanSubmitAdd>> collect2 = req.stream()
                    .collect(Collectors.groupingBy(
                            item -> item.getBaComOrgCode() + "-" + item.getMa02PlanMonth(),
                            Collectors.toList()
                    ));
            map.put("month", collect2);
            return map;
        }

    }

    /**
     * 获取卷烟件装支数
     *
     * @param req
     * @return
     */
    private Map<String, String> getAcCgtCartonPackageQty3Map(List<MonthPlanSubmitAdd> req) {
        List<String> acCgtCartonCodeList = req.stream()
                .map(MonthPlanSubmitAdd::getAcCgtCartonCode)
                .distinct()
                .collect(Collectors.toList());
        Collection<IccProductDetailDTO> iccProductDetailDtos = CommodityUtil.getIccProductDetailDtos(acCgtCartonCodeList);
        return iccProductDetailDtos.stream()
                .collect(Collectors.toMap(
                        IccProductDetailDTO::getProductCode,
                        IccProductDetailDTO::getPackageQty3
                ));
    }

    /**
     * 验证单规格填报量必须是该规格件的倍数
     *
     * @param req
     * @param acCgtCartonPackageQty3Map
     * @param type
     * @return
     */
    private IndResult verifyCigSpec(List<MonthPlanSubmitAdd> req, Map<String, String> acCgtCartonPackageQty3Map, String type) {
        StringBuilder errorMsg = new StringBuilder();
        for (MonthPlanSubmitAdd item : req) {
            String acCgtCartonCode = item.getAcCgtCartonCode();
            BigDecimal packageQty3 = new BigDecimal(acCgtCartonPackageQty3Map.get(acCgtCartonCode));
            if (MonthSalePlanVerifyEnum.REPORT.getCode().equals(type)) {
                //月计划上报
                BigDecimal mc04CgtSalePlanReportQty = ObjUtil.isNotEmpty(item.getMc04CgtSalePlanReportQty()) ? item.getMc04CgtSalePlanReportQty() : BigDecimal.ZERO;
                BigDecimal mc04CgtAllotPlanReportQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanReportQty()) ? item.getMc04CgtAllotPlanReportQty() : BigDecimal.ZERO;

                BigDecimal remainder = mc04CgtSalePlanReportQty.multiply(new BigDecimal("10000")).remainder(packageQty3);
                if (remainder.compareTo(BigDecimal.ZERO) != 0) {
                    errorMsg.append("[").append(item.getMa02PlanMonth()).append("]计划月份的卷烟[").append(item.getAcCgtName())
                            .append("]的销售计划销区上报量必须是该规格件倍数；");
                }


                BigDecimal remainder2 = mc04CgtAllotPlanReportQty.multiply(new BigDecimal("10000")).remainder(packageQty3);
                if (remainder2.compareTo(BigDecimal.ZERO) != 0) {
                    errorMsg.append("[").append(item.getMa02PlanMonth()).append("]计划月份的卷烟[").append(item.getAcCgtName())
                            .append("]的调拨计划销区上报量必须是该规格件倍数；");
                }


            } else if (MonthSalePlanVerifyEnum.REGION_CONFIRM.getCode().equals(type)) {
                //审核
                BigDecimal mc04CgtSalePlanRegionConfirmQty = ObjUtil.isNotEmpty(item.getMc04CgtSalePlanRegionConfirmQty()) ? item.getMc04CgtSalePlanRegionConfirmQty() : BigDecimal.ZERO;

                BigDecimal remainder = mc04CgtSalePlanRegionConfirmQty.multiply(new BigDecimal("10000")).remainder(packageQty3);
                if (remainder.compareTo(BigDecimal.ZERO) != 0) {
                    errorMsg.append("卷烟[").append(item.getAcCgtName())
                            .append("]的销售计划区域确认量必须是该规格件倍数；");
                }


                BigDecimal mc04CgtAllotPlanRegionConfirmQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanRegionConfirmQty()) ? item.getMc04CgtAllotPlanRegionConfirmQty() : BigDecimal.ZERO;

                BigDecimal remainder2 = mc04CgtAllotPlanRegionConfirmQty.multiply(new BigDecimal("10000")).remainder(packageQty3);
                if (remainder2.compareTo(BigDecimal.ZERO) != 0) {
                    errorMsg.append("卷烟[").append(item.getAcCgtName())
                            .append("]的调拨计划区域确认量必须是该规格件倍数；");
                }


            } else if (MonthSalePlanVerifyEnum.BRAND_VERIFY.getCode().equals(type)) {
                BigDecimal mc04CgtSalePlanBdConfirmQty = ObjUtil.isNotEmpty(item.getMc04CgtSalePlanBdConfirmQty()) ? item.getMc04CgtSalePlanBdConfirmQty() : BigDecimal.ZERO;

                BigDecimal remainder = mc04CgtSalePlanBdConfirmQty.multiply(new BigDecimal("10000")).remainder(packageQty3);
                if (remainder.compareTo(BigDecimal.ZERO) != 0) {
                    errorMsg.append("卷烟[").append(item.getAcCgtName())
                            .append("]的销售计划品牌部确认量必须是该规格件倍数；");
                }


                BigDecimal mc04CgtAllotPlanBdConfirmQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanBdConfirmQty()) ? item.getMc04CgtAllotPlanBdConfirmQty() : BigDecimal.ZERO;

                BigDecimal remainder2 = mc04CgtAllotPlanBdConfirmQty.multiply(new BigDecimal("10000")).remainder(packageQty3);
                if (remainder2.compareTo(BigDecimal.ZERO) != 0) {
                    errorMsg.append("卷烟[").append(item.getAcCgtName())
                            .append("]的调拨计划品牌部确认量必须是该规格件倍数；");
                }


            } else if (MonthSalePlanVerifyEnum.PLAN_VERIFY.getCode().equals(type)) {
                BigDecimal mc04CgtSalePlanPdConfirmQty = ObjUtil.isNotEmpty(item.getMc04CgtSalePlanPdConfirmQty()) ? item.getMc04CgtSalePlanPdConfirmQty() : BigDecimal.ZERO;

                BigDecimal remainder = mc04CgtSalePlanPdConfirmQty.multiply(new BigDecimal("10000")).remainder(packageQty3);
                if (remainder.compareTo(BigDecimal.ZERO) != 0) {
                    errorMsg.append("卷烟[").append(item.getAcCgtName())
                            .append("]的销售计划计划部确认量必须是该规格件倍数；");
                }


                BigDecimal mc04CgtAllotPlanPdConfirmQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanPdConfirmQty()) ? item.getMc04CgtAllotPlanPdConfirmQty() : BigDecimal.ZERO;

                BigDecimal remainder2 = mc04CgtAllotPlanPdConfirmQty.multiply(new BigDecimal("10000")).remainder(packageQty3);
                if (remainder2.compareTo(BigDecimal.ZERO) != 0) {
                    errorMsg.append("卷烟[").append(item.getAcCgtName())
                            .append("]的调拨计划计划部确认量必须是该规格件倍数；");
                }


            } else if (MonthSalePlanVerifyEnum.TO_RELEASED.getCode().equals(type)) {
                BigDecimal mc04CgtSalePlanPdPublishQty = ObjUtil.isNotEmpty(item.getMc04CgtSalePlanPdPublishQty()) ? item.getMc04CgtSalePlanPdPublishQty() : BigDecimal.ZERO;

                BigDecimal remainder = mc04CgtSalePlanPdPublishQty.multiply(new BigDecimal("10000")).remainder(packageQty3);
                if (remainder.compareTo(BigDecimal.ZERO) != 0) {
                    errorMsg.append("卷烟[").append(item.getAcCgtName())
                            .append("]的销售计划计划部发布量必须是该规格件倍数；");
                }


                BigDecimal mc04CgtAllotPlanPdPublishQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanPdPublishQty()) ? item.getMc04CgtAllotPlanPdPublishQty() : BigDecimal.ZERO;

                BigDecimal remainder2 = mc04CgtAllotPlanPdPublishQty.multiply(new BigDecimal("10000")).remainder(packageQty3);
                if (remainder2.compareTo(BigDecimal.ZERO) != 0) {
                    errorMsg.append("卷烟[").append(item.getAcCgtName())
                            .append("]的调拨计划计划部发布量必须是该规格件倍数；");
                }
            }
        }

        if (errorMsg.length() > 0) {
            return IndResult.fail(errorMsg.toString());
        }


        return IndResult.success();
    }

    /**
     * 验证协议量
     *
     * @param req
     * @return
     */
    private IndResult verifySelfYearPlan(List<MonthPlanSubmitAdd> req, String type) {

        String icomCode = IcomUtils.getIcomCode();
        //月计划上报是否受协议控制
        String ruleValue = AcRuleUtil.getRuleValue(MonthPlanConstants.ISALE_MP_SUBMIT_AGMT_CTR, icomCode);
        if (ObjUtil.isNotNull(ruleValue) && MonthPlanConstants.ISALE_MP_SUBMIT_AGMT_CTR_0.equals(ruleValue)) {
            return IndResult.success();
        }

        //获取计划月份
        String planMonth = ISaleDateUtil.getOffsetMonth(req.get(0).getZaOccurrenceMonth(), 1);
        //半年
        String halfYear = ISaleDateUtil.getHalfYearFromMonthCode(planMonth);
        // 获取计划类型
        String lastHalfYear = null;
        if (MonthPlanConstants.SELF_YEAR_PALAN.contains(planMonth.substring(4, 6))) {
            String lastMonth = ISaleDateUtil.getOffsetMonth(planMonth, -1);
            lastHalfYear = ISaleDateUtil.getHalfYearFromMonthCode(lastMonth);
        }
        List<String> baComOrgCodeList = req.stream().map(MonthPlanSubmitAdd::getBaComOrgCode).distinct().collect(Collectors.toList());
        //获取协议半年执行量
        List<MonthPlanSubmitBasicItem> basicItemList = islmMonthPlanSubmitRepository.getSelfYearPlan(baComOrgCodeList, halfYear, null, lastHalfYear);
        Map<String, MonthPlanSubmitBasicItem> basicItemMap = basicItemList.stream()
                .collect(Collectors.toMap(
                        dto -> dto.getBaComOrgCode() + "-" + dto.getAcCgtCartonCode(),
                        dto -> dto,
                        (existing, replacement) -> existing
                ));
        //获取协议半年计划量
        Mc04IslmcXyQueryBatchREQ request = new Mc04IslmcXyQueryBatchREQ();
        request.setIcomCode(IcomUtils.getIcomCode());
        request.setMa02TobaProdTradeTypeCode("0");
        List<String> halfYearList = new ArrayList<>();
        halfYearList.add(halfYear);
        if (StrUtil.isNotBlank(lastHalfYear)) {
            halfYearList.add(lastHalfYear);
        }
        request.setMc04CgtXyPeriodCodes(halfYearList);
        request.setBaComOrgCodes(baComOrgCodeList);
        List<Mc04IslmcXyBatchDTO> data = islmMonthPlanSubmitRepository.getMc04IslmcXyBatch(request);
        Map<String, Mc04IslmcXyBatchDTO> xyBatchDTOMap = data.stream()
                .collect(Collectors.toMap(
                        dto -> dto.getBaComOrgCode() + "-" + dto.getMc04CgtXyPeriodCode() + "-" + dto.getAcCgtCartonCode(),
                        dto -> dto,
                        (existing, replacement) -> existing
                ));
        //获取半年协议
        StringBuilder errorMsg = new StringBuilder();
        //只获取n+1月的数据
        List<MonthPlanSubmitAdd> planMonthData = req.stream()
                .filter(item -> planMonth.equals(item.getMa02PlanMonth()))
                .collect(Collectors.toList());
        //按地市和卷烟编码分组
        Map<String, List<MonthPlanSubmitAdd>> planMonthDataMap = planMonthData.stream().collect(Collectors.groupingBy(
                item -> item.getBaComOrgCode() + "-" + item.getAcCgtCartonCode(),
                Collectors.toList()
        ));
        String finalLastHalfYear = lastHalfYear;
        planMonthDataMap.forEach((key, value) -> {
            String[] split = key.split("-");
            String baComOrgCode = split[0];
            String acCgtCartonCode = split[1];
            //协议计划量的key
            String xyBatchDTOKey = baComOrgCode + "-" + halfYear + "-" + acCgtCartonCode;
            //获取半年协议量
            Mc04IslmcXyBatchDTO mc04IslmcXyBatchDTO = xyBatchDTOMap.get(xyBatchDTOKey);
            BigDecimal newXyNumber = ObjUtil.isNotNull(mc04IslmcXyBatchDTO) ? mc04IslmcXyBatchDTO.getNewXyNumber() : BigDecimal.ZERO;
            //获取半年协议执行量
            MonthPlanSubmitBasicItem monthPlanSubmitBasicItem = basicItemMap.get(key);
            //本半年协议执行量
            BigDecimal nowSelfYearExecuteQty = ObjUtil.isNotNull(monthPlanSubmitBasicItem) ? monthPlanSubmitBasicItem.getNowSelfYearExecuteQty() : BigDecimal.ZERO;
            if (ObjUtil.isEmpty(nowSelfYearExecuteQty)) {
                nowSelfYearExecuteQty = BigDecimal.ZERO;
            }
            //本半年协议剩余量
            BigDecimal nowSelfYearQty = ObjUtil.isNotNull(newXyNumber) ?
                    newXyNumber.subtract(nowSelfYearExecuteQty) : BigDecimal.ZERO;
            if (MonthPlanConstants.SELF_YEAR_PALAN.contains(planMonth.substring(4, 6))) {
                //上半年协议key
                String xyBatchDTOLastKey = baComOrgCode + "-" + finalLastHalfYear + "-" + acCgtCartonCode;
                //获取上半年协议量
                Mc04IslmcXyBatchDTO mc04IslmcXyBatchLastDTO = xyBatchDTOMap.get(xyBatchDTOLastKey);
                //获取上半年协议计划量
                BigDecimal lastXyNumber = ObjUtil.isNotNull(mc04IslmcXyBatchLastDTO) ? mc04IslmcXyBatchLastDTO.getNewXyNumber() : BigDecimal.ZERO;
                //获取上半年协议执行量
                BigDecimal lastSelfYearExecuteQty = ObjUtil.isNotNull(monthPlanSubmitBasicItem) ? monthPlanSubmitBasicItem.getLastSelfYearExecuteQty() : BigDecimal.ZERO;
                if (ObjUtil.isEmpty(lastSelfYearExecuteQty)) {
                    lastSelfYearExecuteQty = BigDecimal.ZERO;
                }
                //上半年协议剩余量
                BigDecimal lastSelfYearQty = ObjUtil.isNotNull(lastXyNumber) ?
                        lastXyNumber.subtract(lastSelfYearExecuteQty) : BigDecimal.ZERO;
                // 按mc04MonthSalePlanType分成两个list，20是一个，其他类型是另一个
                Map<Boolean, List<MonthPlanSubmitAdd>> partitioned = value.stream()
                        .collect(Collectors.partitioningBy(
                                item -> "20".equals(item.getMc04MonthSalePlanType())
                        ));

                List<MonthPlanSubmitAdd> type20List = partitioned.get(true);  // 下转协议计划，正常一条数据
                if (CollectionUtil.isNotEmpty(type20List)) {
                    MonthPlanSubmitAdd item = type20List.get(0);
                    BigDecimal mc04CgtAllotPlanReportQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanReportQty()) ? item.getMc04CgtAllotPlanReportQty() : BigDecimal.ZERO;
                    if (MonthSalePlanVerifyEnum.REPORT.getCode().equals(type)) {
                        if (lastSelfYearQty.compareTo(mc04CgtAllotPlanReportQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的下转协议调拨计划销区上报量大于协议剩余量；");
                        }

                    } else if (MonthSalePlanVerifyEnum.REGION_CONFIRM.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanRegionConfirmQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanRegionConfirmQty()) ? item.getMc04CgtAllotPlanRegionConfirmQty() : BigDecimal.ZERO;

                        if (lastSelfYearQty.compareTo(mc04CgtAllotPlanRegionConfirmQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的下转协议调拨计划区域确认量大于协议剩余量；");
                        }


                    } else if (MonthSalePlanVerifyEnum.BRAND_VERIFY.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanBdConfirmQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanBdConfirmQty()) ? item.getMc04CgtAllotPlanBdConfirmQty() : BigDecimal.ZERO;

                        if (lastSelfYearQty.compareTo(mc04CgtAllotPlanBdConfirmQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的下转协议调拨计划品牌部确认量大于协议剩余量；");
                        }


                    } else if (MonthSalePlanVerifyEnum.PLAN_VERIFY.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanPdConfirmQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanPdConfirmQty()) ? item.getMc04CgtAllotPlanPdConfirmQty() : BigDecimal.ZERO;

                        if (lastSelfYearQty.compareTo(mc04CgtAllotPlanPdConfirmQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的下转协议调拨计划计划部确认量大于协议剩余量；");
                        }


                    } else if (MonthSalePlanVerifyEnum.TO_RELEASED.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanPdPublishQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanPdPublishQty()) ? item.getMc04CgtAllotPlanPdPublishQty() : BigDecimal.ZERO;

                        if (lastSelfYearQty.compareTo(mc04CgtAllotPlanPdPublishQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的下转协议调拨计划计划部发布量大于协议剩余量；");
                        }
                    }

                }
                List<MonthPlanSubmitAdd> otherTypeList = partitioned.get(false); //常规和零点计划，正常一条或两条数据
                if (CollectionUtil.isNotEmpty(otherTypeList)) {
                    MonthPlanSubmitAdd item = otherTypeList.get(0);
                    if (MonthSalePlanVerifyEnum.REPORT.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanReportQty = otherTypeList.stream().map(MonthPlanSubmitAdd::getMc04CgtAllotPlanReportQty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (nowSelfYearQty.compareTo(mc04CgtAllotPlanReportQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的常规协议加零点协议调拨计划销区上报量大于协议剩余量；");
                        }

                    } else if (MonthSalePlanVerifyEnum.REGION_CONFIRM.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanRegionConfirmQty = otherTypeList.stream().map(MonthPlanSubmitAdd::getMc04CgtAllotPlanRegionConfirmQty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                        if (nowSelfYearQty.compareTo(mc04CgtAllotPlanRegionConfirmQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的常规协议加零点协议调拨计划区域确认量大于协议剩余量；");
                        }


                    } else if (MonthSalePlanVerifyEnum.BRAND_VERIFY.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanBdConfirmQty = otherTypeList.stream().map(MonthPlanSubmitAdd::getMc04CgtAllotPlanBdConfirmQty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                        if (nowSelfYearQty.compareTo(mc04CgtAllotPlanBdConfirmQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的常规协议加零点协议调拨计划品牌部确认量大于协议剩余量；");
                        }


                    } else if (MonthSalePlanVerifyEnum.PLAN_VERIFY.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanPdConfirmQty = otherTypeList.stream().map(MonthPlanSubmitAdd::getMc04CgtAllotPlanPdConfirmQty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                        if (nowSelfYearQty.compareTo(mc04CgtAllotPlanPdConfirmQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的常规协议加零点协议调拨计划计划部确认量大于协议剩余量；");
                        }


                    } else if (MonthSalePlanVerifyEnum.TO_RELEASED.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanPdPublishQty = otherTypeList.stream().map(MonthPlanSubmitAdd::getMc04CgtAllotPlanPdPublishQty)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                        if (nowSelfYearQty.compareTo(mc04CgtAllotPlanPdPublishQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的常规协议加零点协议调拨计划计划部发布量大于协议剩余量；");
                        }
                    }

                }


            } else {
                //非1月和7月只有一条数据
                value.forEach(item -> {

                    if (MonthSalePlanVerifyEnum.REPORT.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanReportQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanReportQty()) ? item.getMc04CgtAllotPlanReportQty() : BigDecimal.ZERO;
                        if (nowSelfYearQty.compareTo(mc04CgtAllotPlanReportQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的调拨计划销区上报量大于协议剩余量；");
                        }

                    } else if (MonthSalePlanVerifyEnum.REGION_CONFIRM.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanRegionConfirmQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanRegionConfirmQty()) ? item.getMc04CgtAllotPlanRegionConfirmQty() : BigDecimal.ZERO;

                        if (nowSelfYearQty.compareTo(mc04CgtAllotPlanRegionConfirmQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的调拨计划区域确认量大于协议剩余量；");
                        }


                    } else if (MonthSalePlanVerifyEnum.BRAND_VERIFY.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanBdConfirmQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanBdConfirmQty()) ? item.getMc04CgtAllotPlanBdConfirmQty() : BigDecimal.ZERO;

                        if (nowSelfYearQty.compareTo(mc04CgtAllotPlanBdConfirmQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的调拨计划品牌部确认量大于协议剩余量；");
                        }


                    } else if (MonthSalePlanVerifyEnum.PLAN_VERIFY.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanPdConfirmQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanPdConfirmQty()) ? item.getMc04CgtAllotPlanPdConfirmQty() : BigDecimal.ZERO;

                        if (nowSelfYearQty.compareTo(mc04CgtAllotPlanPdConfirmQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的调拨计划计划部确认量大于协议剩余量；");
                        }


                    } else if (MonthSalePlanVerifyEnum.TO_RELEASED.getCode().equals(type)) {
                        BigDecimal mc04CgtAllotPlanPdPublishQty = ObjUtil.isNotEmpty(item.getMc04CgtAllotPlanPdPublishQty()) ? item.getMc04CgtAllotPlanPdPublishQty() : BigDecimal.ZERO;

                        if (nowSelfYearQty.compareTo(mc04CgtAllotPlanPdPublishQty) < 0) {
                            errorMsg.append("卷烟[").append(item.getAcCgtName())
                                    .append("]的调拨计划计划部发布量大于协议剩余量；");
                        }
                    }
                });

            }
        });
        if (errorMsg.length() > 0) {
            return IndResult.fail(errorMsg.toString());
        }

        return IndResult.success();
    }

    /**
     * 获取季度计划数量
     *
     * @param req
     * @return
     */
    private Map<String, Map<String, BigDecimal>> getQuarterPlan(List<MonthPlanSubmitAdd> req) {
        List<String> planMonthList = req.stream().map(MonthPlanSubmitAdd::getMa02PlanMonth).distinct().collect(Collectors.toList());
        String zaOccurrenceMonth = req.get(0).getZaOccurrenceMonth();

        List<String> comIds = req.stream().map(MonthPlanSubmitAdd::getBaComOrgCode).distinct().collect(Collectors.toList());

        //季度
        String planQuarter = ISaleDateUtil.getQuarterFromMonthCode(ISaleDateUtil.getOffsetMonth(zaOccurrenceMonth, 1));
        String nextTwoMonthQuarter = ISaleDateUtil.getQuarterFromMonthCode(ISaleDateUtil.getOffsetMonth(zaOccurrenceMonth, 3));
        // 判断是否为同一季度
        boolean isSameQuarter = planQuarter.equals(nextTwoMonthQuarter);
        //年
        String year = planQuarter.substring(0, 4);
        String year2 = nextTwoMonthQuarter.substring(0, 4);
        //判断年份是否相同
        boolean isSameYear = year.equals(year2);

        String planSubjectType = islmMonthPlanSubmitRepository.getPlanSubjectType(year);
        List<String> quarters = new ArrayList<>(Collections.singletonList(planQuarter));
        if (!isSameQuarter && planMonthList.size() > 1) {
            quarters.add(nextTwoMonthQuarter);
        }
        List<MonthPlanQuarterItem> quarterItemList = islmMonthPlanSubmitRepository.getQuarterItemList(year, comIds, planSubjectType, quarters);
        if (!isSameYear && planMonthList.size() > 1) {
            String subjectType = islmMonthPlanSubmitRepository.getPlanSubjectType(year2);
            List<MonthPlanQuarterItem> itemList = islmMonthPlanSubmitRepository.getQuarterItemList(year2, comIds, subjectType, Collections.singletonList(nextTwoMonthQuarter));
            quarterItemList.addAll(itemList);
        }
        //季度数据按地市和卷烟编码、季度返回map
//        quarterItemList.stream().
//                collect(Collectors.toMap(
//                        item -> item.getBaComOrgCode() + "-" + item.getAcCgtCartonCode() + "-" + item.getMc04CgtSaleFoPeriodCode(),
//                        item -> item,
//                        (v1, v2) -> v1));
        Map<String, BigDecimal> cartonCodeQtyMap = quarterItemList.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getBaComOrgCode() + "-" + item.getAcCgtCartonCode() + "-" + item.getMc04CgtSaleFoPeriodCode(),
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                MonthPlanQuarterItem::getMa02CgtPlAdjustedQty,
                                BigDecimal::add
                        )
                ));

        Map<String, BigDecimal> sumQtyMap = quarterItemList.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getBaComOrgCode() + "-" + item.getMc04CgtSaleFoPeriodCode(),
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                MonthPlanQuarterItem::getMa02CgtPlAdjustedQty,
                                BigDecimal::add
                        )
                ));
        Map<String, Map<String, BigDecimal>> resultMap = new HashMap<>();
        resultMap.put("cartonCodeQtyMap", cartonCodeQtyMap);
        resultMap.put("sumQtyMap", sumQtyMap);
        return resultMap;
    }


    /**
     * 校验季度数据
     *
     * @param req               原始数据
     * @param zaOccurrenceMonth 业务月份
     * @param groupReqMap       分组数据
     * @param quarterPlanMap    季度调拨计划数据
     * @param type              类型
     * @param currentMonthQty   本月调拨计划量
     * @param lastMonthQty      上月调拨实际量
     * @return
     */
    private IndResult verifyQuarter(List<MonthPlanSubmitAdd> req, String zaOccurrenceMonth, Map<String, Map<String, List<MonthPlanSubmitAdd>>> groupReqMap, Map<String, Map<String, BigDecimal>> quarterPlanMap,
                                    String type, Map<String, BigDecimal> currentMonthQty, Map<String, BigDecimal> lastMonthQty) {
        String icomCode = IcomUtils.getIcomCode();

        //月计划上报是否受季度控制
        String ruleValue = AcRuleUtil.getRuleValue(MonthPlanConstants.ISALE_MP_SUBMIT_QTR_CTR, icomCode);
        if (ObjUtil.isNotNull(ruleValue) && MonthPlanConstants.ISALE_MP_SUBMIT_QTR_CTR_0.equals(ruleValue)) {
            return IndResult.success();
        }
        //校验季度内填报总量或季度内填报量+N-1月份实际量+N月发布量不能超过季度计划量；
        Map<String, List<MonthPlanSubmitAdd>> monthGroupReqMap = groupReqMap.get("month");
        Map<String, BigDecimal> sumQtyMap = quarterPlanMap.get("sumQtyMap");
        IndResult indResult = verifyQuarterSumQty(req, monthGroupReqMap, sumQtyMap, zaOccurrenceMonth, type, currentMonthQty, lastMonthQty);
        if (!indResult.isSuccess()) {
            return indResult;
        }


        //单月填报总量（调拨计划量）不能超过季度计划量*业务规则ISALE_MP_SUBMIT_QTR_PER_MAX，其中1月和2月不限制
        IndResult verifyMonthSumQty = verifyMonthSumQty(monthGroupReqMap, sumQtyMap, type);
        if (!verifyMonthSumQty.isSuccess()) {
            return verifyMonthSumQty;
        }
        //单规格填报量（调拨计划量）不能超过该规格季度计划量*业务规则ISALE_MP_SUBMIT_QTR_PER_SPEC（季度计划量小于业务规则ISALE_MP_SUBMIT_QTR_PER_MIN（默认100）的规格不限制），其中1月和2月不限制；
        Map<String, List<MonthPlanSubmitAdd>> cartonCodeGroupReqMap = groupReqMap.get("cartonCode");
        Map<String, BigDecimal> cartonCodeQtyMap = quarterPlanMap.get("cartonCodeQtyMap");
        IndResult verifyCartonCodeSumQty = verifyCartonCodeSumQty(cartonCodeGroupReqMap, cartonCodeQtyMap, type, zaOccurrenceMonth);
        if (!verifyCartonCodeSumQty.isSuccess()) {
            return verifyCartonCodeSumQty;
        }


        return IndResult.success();
    }

    /**
     * 校验季度内填报总量或季度内填报量+N-2月份实际量+N-1月发布量不能超过季度计划量
     *
     * @param req               原始数据
     * @param monthGroupReqMap  按月分组的原始数据
     * @param sumQtyMap         季度计划数据
     * @param zaOccurrenceMonth 业务月份
     * @param type              审核类型
     * @param currentMonthQty   当月调拨计划量
     * @param lastMonthQty      N-1月调拨实际量
     * @return
     */
    private IndResult verifyQuarterSumQty(List<MonthPlanSubmitAdd> req, Map<String, List<MonthPlanSubmitAdd>> monthGroupReqMap,
                                          Map<String, BigDecimal> sumQtyMap, String zaOccurrenceMonth, String type, Map<String, BigDecimal> currentMonthQty, Map<String, BigDecimal> lastMonthQty) {
        StringBuilder errorMsg = new StringBuilder();
//        int monthCount = monthGroupReqMap.size();
        if (MonthSalePlanVerifyEnum.REPORT.getCode().equals(type) || MonthSalePlanVerifyEnum.REGION_CONFIRM.getCode().equals(type)) {
            String planMonth = ISaleDateUtil.getOffsetMonth(zaOccurrenceMonth, 1);
            String planMonth2 = ISaleDateUtil.getOffsetMonth(zaOccurrenceMonth, 2);
            String planMonth3 = ISaleDateUtil.getOffsetMonth(zaOccurrenceMonth, 3);
            String quarter = ISaleDateUtil.getQuarterFromMonthCode(planMonth);
            String quarter2 = ISaleDateUtil.getQuarterFromMonthCode(planMonth2);
            String quarter3 = ISaleDateUtil.getQuarterFromMonthCode(planMonth3);
            if (quarter.equals(quarter2) && quarter.equals(quarter3)) {
                //三个在同一个季度
                Map<String, BigDecimal> sumComOrgQty = req.stream()
                        .collect(Collectors.groupingBy(
                                MonthPlanSubmitAdd::getBaComOrgCode,
                                Collectors.reducing(
                                        BigDecimal.ZERO,
                                        MonthPlanSubmitAdd::getMc04CgtAllotPlanReportQty,
                                        BigDecimal::add
                                )
                        ));
                sumComOrgQty.forEach((key, value) -> {
                    String sumQtyMapKey = key + "-" + quarter;
                    BigDecimal qty = sumQtyMap.getOrDefault(sumQtyMapKey, BigDecimal.ZERO);
                    if (qty.compareTo(value) < 0) {
                        errorMsg.append("地市[").append(key).append("]").append("三个月调拨计划量超过当季度总量；");
                    }
                });

            } else if (quarter.equals(quarter2)) {
                //前两个月在一个季度
                if (MonthSalePlanVerifyEnum.REPORT.getCode().equals(type)) {
                    //第一个月数据
                    BigDecimal monthQty = monthGroupReqMap.getOrDefault(planMonth, Collections.emptyList()).stream()
                            .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanReportQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //第二个月数据
                    BigDecimal monthQt2 = monthGroupReqMap.getOrDefault(planMonth2, Collections.emptyList()).stream()
                            .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanReportQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //第三个月数据
                    BigDecimal monthQt3 = monthGroupReqMap.getOrDefault(planMonth3, Collections.emptyList()).stream()
                            .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanReportQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //当月数据
                    String baComOrgCode = req.get(0).getBaComOrgCode();
                    BigDecimal currentQty = currentMonthQty.getOrDefault(baComOrgCode, BigDecimal.ZERO);
                    //第一个季度数据
                    BigDecimal quarterQty = monthQty.add(monthQt2).add(currentQty);
                    String key1 = baComOrgCode + "-" + quarter;
                    String key2 = baComOrgCode + "-" + quarter3;
                    if (quarterQty.compareTo(sumQtyMap.getOrDefault(key1, BigDecimal.ZERO)) > 0) {
                        errorMsg.append("前两个月调拨计划上报量超过当季度总量；");
                    }
                    if (monthQt3.compareTo(sumQtyMap.getOrDefault(key2, BigDecimal.ZERO)) > 0) {
                        errorMsg.append("第三个月调拨计划上报量超过当季度总量；");
                    }
                } else {
                    Map<String, List<MonthPlanSubmitAdd>> comOrgMap = req.stream().collect(Collectors.groupingBy(MonthPlanSubmitAdd::getBaComOrgCode));
                    comOrgMap.forEach((key, value) -> {
                        //第一个月数据
                        BigDecimal monthQty = monthGroupReqMap.getOrDefault(key + "-" + planMonth, Collections.emptyList()).stream()
                                .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanRegionConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //第二个月数据
                        BigDecimal monthQt2 = monthGroupReqMap.getOrDefault(key + "-" + planMonth2, Collections.emptyList()).stream()
                                .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanRegionConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //第三个月数据
                        BigDecimal monthQt3 = monthGroupReqMap.getOrDefault(key + "-" + planMonth3, Collections.emptyList()).stream()
                                .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanRegionConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //当月数据
                        BigDecimal currentQty = currentMonthQty.getOrDefault(key, BigDecimal.ZERO);
                        //第一个季度数据
                        BigDecimal quarterQty = monthQty.add(monthQt2).add(currentQty);
                        String key1 = key + "-" + quarter;
                        String key2 = key + "-" + quarter3;
                        if (quarterQty.compareTo(sumQtyMap.getOrDefault(key1, BigDecimal.ZERO)) > 0) {
                            errorMsg.append("地市[").append(key).append("]").append("前两个月调拨区域确认量超过当季度总量；");
                        }
                        if (monthQt3.compareTo(sumQtyMap.getOrDefault(key2, BigDecimal.ZERO)) > 0) {
                            errorMsg.append("地市[").append(key).append("]").append("第三个月调拨区域确认量超过当季度总量；");
                        }
                    });

                }

            } else {
                //后两个月一个季度
                if (MonthSalePlanVerifyEnum.REPORT.getCode().equals(type)) {
                    //第一个月数据
                    BigDecimal monthQty = monthGroupReqMap.getOrDefault(planMonth, Collections.emptyList()).stream()
                            .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanReportQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //第二个月数据
                    BigDecimal monthQt2 = monthGroupReqMap.getOrDefault(planMonth2, Collections.emptyList()).stream()
                            .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanReportQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //第三个月数据
                    BigDecimal monthQt3 = monthGroupReqMap.getOrDefault(planMonth3, Collections.emptyList()).stream()
                            .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanReportQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //当月数据
                    String baComOrgCode = req.get(0).getBaComOrgCode();
                    BigDecimal currentQty = currentMonthQty.getOrDefault(baComOrgCode, BigDecimal.ZERO);
                    BigDecimal lastQty = lastMonthQty.getOrDefault(baComOrgCode, BigDecimal.ZERO);
                    //第一个季度数据
                    BigDecimal quarterQty = monthQty.add(lastQty).add(currentQty);
                    String key1 = baComOrgCode + "-" + quarter;
                    String key2 = baComOrgCode + "-" + quarter3;
                    if (quarterQty.compareTo(sumQtyMap.getOrDefault(key1, BigDecimal.ZERO)) > 0) {
                        errorMsg.append("第一个月调拨计划上报量超过当季度总量；");
                    }
                    BigDecimal quarterQty2 = monthQt2.add(monthQt3);
                    if (quarterQty2.compareTo(sumQtyMap.getOrDefault(key2, BigDecimal.ZERO)) > 0) {
                        errorMsg.append("后两个月调拨计划上报量超过当季度总量；");
                    }
                } else {
                    Map<String, List<MonthPlanSubmitAdd>> comOrgMap = req.stream().collect(Collectors.groupingBy(MonthPlanSubmitAdd::getBaComOrgCode));
                    comOrgMap.forEach((key, value) -> {
                        //第一个月数据
                        BigDecimal monthQty = monthGroupReqMap.getOrDefault(key + "-" + planMonth, Collections.emptyList()).stream()
                                .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanRegionConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //第二个月数据
                        BigDecimal monthQt2 = monthGroupReqMap.getOrDefault(key + "-" + planMonth2, Collections.emptyList()).stream()
                                .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanRegionConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //第三个月数据
                        BigDecimal monthQt3 = monthGroupReqMap.getOrDefault(key + "-" + planMonth3, Collections.emptyList()).stream()
                                .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanRegionConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //当月数据
                        BigDecimal currentQty = currentMonthQty.getOrDefault(key, BigDecimal.ZERO);
                        BigDecimal lastQty = lastMonthQty.getOrDefault(key, BigDecimal.ZERO);
                        //第一个季度数据
                        BigDecimal quarterQty = monthQty.add(lastQty).add(currentQty);
                        String key1 = key + "-" + quarter;
                        String key2 = key + "-" + quarter3;
                        if (quarterQty.compareTo(sumQtyMap.getOrDefault(key1, BigDecimal.ZERO)) > 0) {
                            errorMsg.append("地市[").append(key).append("]").append("第一个月调拨区域确认量超过当季度总量；");
                        }
                        BigDecimal quarterQty2 = monthQt2.add(monthQt3);
                        if (quarterQty2.compareTo(sumQtyMap.getOrDefault(key2, BigDecimal.ZERO)) > 0) {
                            errorMsg.append("地市[").append(key).append("]").append("后两个月调拨区域确认量超过当季度总量；");
                        }
                    });

                }
            }
        } else {
            //除去提报和区域确认
            String planMonth = ISaleDateUtil.getOffsetMonth(zaOccurrenceMonth, 1);
            //季度
            String quarter = ISaleDateUtil.getQuarterFromMonthCode(planMonth);
            //判断是第几个月
            int monthPositionInQuarter = getMonthPositionInQuarter(planMonth);
            //按地市分组
            Map<String, List<MonthPlanSubmitAdd>> comOrgMap = req.stream().collect(Collectors.groupingBy(MonthPlanSubmitAdd::getBaComOrgCode));
            comOrgMap.forEach((key, value) -> {
                if (MonthSalePlanVerifyEnum.BRAND_VERIFY.getCode().equals(type)) {
                    //品牌部
                    BigDecimal monthQty = monthGroupReqMap.getOrDefault(key + "-" + planMonth, Collections.emptyList()).stream()
                            .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanBdConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //季度数据
                    String key1 = key + "-" + quarter;
                    BigDecimal quarterQty = sumQtyMap.getOrDefault(key1, BigDecimal.ZERO);
                    if (monthPositionInQuarter == 1) {
                        if (monthQty.compareTo(quarterQty) > 0) {
                            errorMsg.append("地市[").append(key).append("]").append("调拨计划品牌部确认量超过当季度总量；");
                        }

                    } else if (monthPositionInQuarter == 2) {
                        //当月数据
                        BigDecimal currentQty = currentMonthQty.getOrDefault(key, BigDecimal.ZERO);
                        BigDecimal add = monthQty.add(currentQty);
                        if (add.compareTo(quarterQty) > 0) {
                            errorMsg.append("地市[").append(key).append("]").append("调拨计划品牌部确认量超过当季度总量；");
                        }
                    } else {
                        //当月数据
                        BigDecimal currentQty = currentMonthQty.getOrDefault(key, BigDecimal.ZERO);
                        BigDecimal lastQty = lastMonthQty.getOrDefault(key, BigDecimal.ZERO);
                        BigDecimal add = monthQty.add(currentQty).add(lastQty);
                        if (add.compareTo(quarterQty) > 0) {
                            errorMsg.append("地市[").append(key).append("]").append("调拨计划品牌部确认量超过当季度总量；");
                        }
                    }
                } else if (MonthSalePlanVerifyEnum.PLAN_VERIFY.getCode().equals(type)) {
                    //计划部
                    BigDecimal monthQty = monthGroupReqMap.getOrDefault(key + "-" + planMonth, Collections.emptyList()).stream()
                            .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanPdConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //季度数据
                    String key1 = key + "-" + quarter;
                    BigDecimal quarterQty = sumQtyMap.getOrDefault(key1, BigDecimal.ZERO);
                    if (monthPositionInQuarter == 1) {
                        if (monthQty.compareTo(quarterQty) > 0) {
                            errorMsg.append("地市[").append(key).append("]").append("调拨计划计划部确认量超过当季度总量；");
                        }

                    } else if (monthPositionInQuarter == 2) {
                        //当月数据
                        BigDecimal currentQty = currentMonthQty.getOrDefault(key, BigDecimal.ZERO);
                        BigDecimal add = monthQty.add(currentQty);
                        if (add.compareTo(quarterQty) > 0) {
                            errorMsg.append("地市[").append(key).append("]").append("调拨计划计划部确认量超过当季度总量；");
                        }
                    } else {
                        //当月数据
                        BigDecimal currentQty = currentMonthQty.getOrDefault(key, BigDecimal.ZERO);
                        BigDecimal lastQty = lastMonthQty.getOrDefault(key, BigDecimal.ZERO);
                        BigDecimal add = monthQty.add(currentQty).add(lastQty);
                        if (add.compareTo(quarterQty) > 0) {
                            errorMsg.append("地市[").append(key).append("]").append("调拨计划计划部确认量超过当季度总量；");
                        }
                    }
                } else if (MonthSalePlanVerifyEnum.TO_RELEASED.getCode().equals(type)) {
                    //计划部
                    BigDecimal monthQty = monthGroupReqMap.getOrDefault(key + "-" + planMonth, Collections.emptyList()).stream()
                            .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanPdPublishQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    //季度数据
                    String key1 = key + "-" + quarter;
                    BigDecimal quarterQty = sumQtyMap.getOrDefault(key1, BigDecimal.ZERO);
                    if (monthPositionInQuarter == 1) {
                        if (monthQty.compareTo(quarterQty) > 0) {
                            errorMsg.append("地市[").append(key).append("]").append("调拨计划计划部发布量超过当季度总量；");
                        }

                    } else if (monthPositionInQuarter == 2) {
                        //当月数据
                        BigDecimal currentQty = currentMonthQty.getOrDefault(key, BigDecimal.ZERO);
                        BigDecimal add = monthQty.add(currentQty);
                        if (add.compareTo(quarterQty) > 0) {
                            errorMsg.append("地市[").append(key).append("]").append("调拨计划计划部发布量超过当季度总量；");
                        }
                    } else {
                        //当月数据
                        BigDecimal currentQty = currentMonthQty.getOrDefault(key, BigDecimal.ZERO);
                        BigDecimal lastQty = lastMonthQty.getOrDefault(key, BigDecimal.ZERO);
                        BigDecimal add = monthQty.add(currentQty).add(lastQty);
                        if (add.compareTo(quarterQty) > 0) {
                            errorMsg.append("地市[").append(key).append("]").append("调拨计划计划部发布量超过当季度总量；");
                        }
                    }
                }

            });

        }
        if (errorMsg.length() > 0) {
            return IndResult.fail(errorMsg.toString());
        }


        return IndResult.success();
    }

    /**
     * 单月填报总量（销售计划量、调拨计划量）不能超过季度计划量*业务规则ISALE_MP_SUBMIT_QTR_PER_MAX，其中1月和2月不限制
     *
     * @param monthGroupReqMap 按地市、月分组
     * @param sumQtyMap        季度调拨总量
     * @param type             类型
     * @return
     */
    private IndResult verifyMonthSumQty(Map<String, List<MonthPlanSubmitAdd>> monthGroupReqMap,
                                        Map<String, BigDecimal> sumQtyMap, String type) {
        StringBuilder errorMsg = new StringBuilder();
        String icomCode = IcomUtils.getIcomCode();
        //单月占季度比例最大值
        String ruleValue = AcRuleUtil.getRuleValue(MonthPlanConstants.ISALE_MP_SUBMIT_QTR_PER_MAX, icomCode);
        BigDecimal maxQty = new BigDecimal(ruleValue.substring(0, 2));
        if (StrUtil.isBlank(ruleValue)) {
            return IndResult.success();
        }
        //校验最小调拨量，小于等于时不做单月和规格校验
        String minQty = AcRuleUtil.getRuleValue(MonthPlanConstants.ISALE_MP_SUBMIT_QTR_PER_MIN, icomCode);
        if (StrUtil.isBlank(minQty)) {
            minQty = MonthPlanConstants.ISALE_MP_SUBMIT_QTR_PER_MIN_VALUE;
        }
        if (MonthSalePlanVerifyEnum.REPORT.getCode().equals(type)) {
            for (Map.Entry<String, List<MonthPlanSubmitAdd>> entry : monthGroupReqMap.entrySet()) {
                String key = entry.getKey();
                if (MonthPlanConstants.FESTIVAL_MONTH.contains(key.substring(4, 6))) {
                    continue;
                }
                List<MonthPlanSubmitAdd> value = entry.getValue();
                String baComOrgCode = value.get(0).getBaComOrgCode();
                String quarter = ISaleDateUtil.getQuarterFromMonthCode(key);
                //季度数据
                String key1 = baComOrgCode + "-" + quarter;
                BigDecimal quarterQty = sumQtyMap.getOrDefault(key1, BigDecimal.ZERO);
                //忽略小于最小调拨量的数据
                if (quarterQty.compareTo(new BigDecimal(minQty)) <= 0) {
                    continue;
                }
                BigDecimal monthQty = value.stream()
                        .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanReportQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal multiply = monthQty.divide(quarterQty, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                if (multiply.compareTo(maxQty) > 0) {
                    errorMsg.append(key).append("月调拨计划销区上报量超过当季度总量的").append(ruleValue).append("；");
                }
            }

        } else if (MonthSalePlanVerifyEnum.REGION_CONFIRM.getCode().equals(type)) {
            for (Map.Entry<String, List<MonthPlanSubmitAdd>> entry : monthGroupReqMap.entrySet()) {
                String key = entry.getKey();
                String[] split = key.split("-");
                String baComOrgCode = split[0];
                String planMonth = split[1];
                if (MonthPlanConstants.FESTIVAL_MONTH.contains(planMonth.substring(4, 6))) {
                    continue;
                }
                List<MonthPlanSubmitAdd> value = entry.getValue();
                String quarter = ISaleDateUtil.getQuarterFromMonthCode(planMonth);
                //季度数据
                String key1 = baComOrgCode + "-" + quarter;
                BigDecimal quarterQty = sumQtyMap.getOrDefault(key1, BigDecimal.ZERO);
                //忽略小于最小调拨量的数据
                if (quarterQty.compareTo(new BigDecimal(minQty)) <= 0) {
                    continue;
                }
                BigDecimal monthQty = value.stream()
                        .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanRegionConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal multiply = monthQty.divide(quarterQty, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                if (multiply.compareTo(maxQty) > 0) {
                    errorMsg.append("地市[").append(baComOrgCode).append("]，").append(planMonth).append("调拨计划区域确认量超过当季度总量的").append(ruleValue).append("；");
                }
            }
        } else if (MonthSalePlanVerifyEnum.PLAN_VERIFY.getCode().equals(type)) {
            for (Map.Entry<String, List<MonthPlanSubmitAdd>> entry : monthGroupReqMap.entrySet()) {
                String key = entry.getKey();
                String[] split = key.split("-");
                String baComOrgCode = split[0];
                String planMonth = split[1];
                if (MonthPlanConstants.FESTIVAL_MONTH.contains(planMonth.substring(4, 6))) {
                    continue;
                }
                List<MonthPlanSubmitAdd> value = entry.getValue();
                String quarter = ISaleDateUtil.getQuarterFromMonthCode(planMonth);
                //季度数据
                String key1 = baComOrgCode + "-" + quarter;
                BigDecimal quarterQty = sumQtyMap.getOrDefault(key1, BigDecimal.ZERO);
                //忽略小于最小调拨量的数据
                if (quarterQty.compareTo(new BigDecimal(minQty)) <= 0) {
                    continue;
                }
                BigDecimal monthQty = value.stream()
                        .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanPdConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal multiply = monthQty.divide(quarterQty, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                if (multiply.compareTo(maxQty) > 0) {
                    errorMsg.append("地市[").append(baComOrgCode).append("]，").append(planMonth).append("调拨计划计划部确认量超过当季度总量的").append(ruleValue).append("；");
                }
            }
        } else if (MonthSalePlanVerifyEnum.BRAND_VERIFY.getCode().equals(type)){
            for (Map.Entry<String, List<MonthPlanSubmitAdd>> entry : monthGroupReqMap.entrySet()) {
                String key = entry.getKey();
                String[] split = key.split("-");
                String baComOrgCode = split[0];
                String planMonth = split[1];
                if (MonthPlanConstants.FESTIVAL_MONTH.contains(planMonth.substring(4, 6))) {
                    continue;
                }
                List<MonthPlanSubmitAdd> value = entry.getValue();
                String quarter = ISaleDateUtil.getQuarterFromMonthCode(planMonth);
                //季度数据
                String key1 = baComOrgCode + "-" + quarter;
                BigDecimal quarterQty = sumQtyMap.getOrDefault(key1, BigDecimal.ZERO);
                //忽略小于最小调拨量的数据
                if (quarterQty.compareTo(new BigDecimal(minQty)) <= 0) {
                    continue;
                }
                BigDecimal monthQty = value.stream()
                        .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanBdConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal multiply = monthQty.divide(quarterQty, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                if (multiply.compareTo(maxQty) > 0) {
                    errorMsg.append("地市[").append(baComOrgCode).append("]，").append(planMonth).append("调拨计划品牌部确认量超过当季度总量的").append(ruleValue).append("；");
                }
            }
        } else if (MonthSalePlanVerifyEnum.TO_RELEASED.getCode().equals(type)) {
            for (Map.Entry<String, List<MonthPlanSubmitAdd>> entry : monthGroupReqMap.entrySet()) {
                String key = entry.getKey();
                String[] split = key.split("-");
                String baComOrgCode = split[0];
                String planMonth = split[1];
                if (MonthPlanConstants.FESTIVAL_MONTH.contains(planMonth.substring(4, 6))) {
                    continue;
                }
                List<MonthPlanSubmitAdd> value = entry.getValue();
                String quarter = ISaleDateUtil.getQuarterFromMonthCode(planMonth);
                //季度数据
                String key1 = baComOrgCode + "-" + quarter;
                BigDecimal quarterQty = sumQtyMap.getOrDefault(key1, BigDecimal.ZERO);
                //忽略小于最小调拨量的数据
                if (quarterQty.compareTo(new BigDecimal(minQty)) <= 0) {
                    continue;
                }
                BigDecimal monthQty = value.stream()
                        .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanPdPublishQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal multiply = monthQty.divide(quarterQty, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                if (multiply.compareTo(maxQty) > 0) {
                    errorMsg.append("地市[").append(baComOrgCode).append("]，").append(planMonth).append("调拨计划品牌部发布量超过当季度总量的").append(ruleValue).append("；");
                }
            }
        }



        if (errorMsg.length() > 0) {
            return IndResult.fail(errorMsg.toString());
        }


        return IndResult.success();
    }

    /**
     * 单规格填报量（调拨计划量）不能超过该规格季度计划量*业务规则ISALE_MP_SUBMIT_QTR_PER_SPEC（季度计划量小于业务规则ISALE_MP_SUBMIT_QTR_PER_MIN（默认100）的规格不限制），其中1月和2月不限制；
     *
     * @param cartonCodeGroupReqMap 按规格分组的原始数据
     * @param cartonCodeQtyMap      按规格分组的季度总量数据
     * @param type                  类型
     * @return
     */
    private IndResult verifyCartonCodeSumQty(Map<String, List<MonthPlanSubmitAdd>> cartonCodeGroupReqMap,
                                             Map<String, BigDecimal> cartonCodeQtyMap, String type, String zaOccurrenceMonth) {
        StringBuilder errorMsg = new StringBuilder();
        String icomCode = IcomUtils.getIcomCode();

        String specQty = AcRuleUtil.getRuleValue(MonthPlanConstants.ISALE_MP_SUBMIT_QTR_PER_SPEC, icomCode);
        if (StrUtil.isBlank(specQty)) {
            return IndResult.success();
        }
        BigDecimal maxQty = new BigDecimal(specQty.substring(0, 2));

        //校验最小调拨量，小于等于时不做单月和规格校验
        String minQty = AcRuleUtil.getRuleValue(MonthPlanConstants.ISALE_MP_SUBMIT_QTR_PER_MIN, icomCode);
        if (StrUtil.isBlank(minQty)) {
            minQty = MonthPlanConstants.ISALE_MP_SUBMIT_QTR_PER_MIN_VALUE;
        }
        if (MonthSalePlanVerifyEnum.REPORT.getCode().equals(type)) {
            for (Map.Entry<String, List<MonthPlanSubmitAdd>> entry : cartonCodeGroupReqMap.entrySet()) {
                String cartonCode = entry.getKey();
                List<MonthPlanSubmitAdd> value = entry.getValue();
                String baComOrgCode = value.get(0).getBaComOrgCode();
                String acCgtName = value.get(0).getAcCgtName();
                //过滤出非1、2、12的月份数据
                value = value.stream()
                        .filter(item -> !MonthPlanConstants.FESTIVAL_MONTH.contains(item.getMa02PlanMonth().substring(4, 6)))
                        .collect(Collectors.toList());
                //按月分组
                Map<String, List<MonthPlanSubmitAdd>> partitioned = value.stream().collect(
                        Collectors.groupingBy(MonthPlanSubmitAdd::getMa02PlanMonth));
                for (Map.Entry<String, List<MonthPlanSubmitAdd>> item : partitioned.entrySet()) {
                    String month = item.getKey();
                    List<MonthPlanSubmitAdd> list = item.getValue();
                    String quarter = ISaleDateUtil.getQuarterFromMonthCode(month);
                    //季度数据
                    String key1 = baComOrgCode + "-" + cartonCode + "-" + quarter;
                    BigDecimal quarterQty = cartonCodeQtyMap.getOrDefault(key1, BigDecimal.ZERO);
                    //忽略小于最小调拨量的数据
                    if (quarterQty.compareTo(new BigDecimal(minQty)) <= 0) {
                        continue;
                    }
                    BigDecimal monthQty = list.stream()
                            .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanReportQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal multiply = monthQty.divide(quarterQty, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                    if (multiply.compareTo(maxQty) > 0) {
                        errorMsg.append(acCgtName).append("月调拨计划销区上报量超过当季度该规格总量的").append(specQty).append("；");
                    }
                }


            }

        } else if (MonthSalePlanVerifyEnum.REGION_CONFIRM.getCode().equals(type)) {
            for (Map.Entry<String, List<MonthPlanSubmitAdd>> entry : cartonCodeGroupReqMap.entrySet()) {
                String key = entry.getKey();
                String[] split = key.split("-");
                String baComOrgCode = split[0];
                String cartonCode = split[1];
                List<MonthPlanSubmitAdd> value = entry.getValue();
                String acCgtName = value.get(0).getAcCgtName();
                //月份数据
                //过滤出非1、2、12的月份数据
                value = value.stream()
                        .filter(item -> !MonthPlanConstants.FESTIVAL_MONTH.contains(item.getMa02PlanMonth().substring(4, 6)))
                        .collect(Collectors.toList());
                //按月分组
                Map<String, List<MonthPlanSubmitAdd>> partitioned = value.stream().collect(
                        Collectors.groupingBy(MonthPlanSubmitAdd::getMa02PlanMonth));
                for (Map.Entry<String, List<MonthPlanSubmitAdd>> item : partitioned.entrySet()) {
                    String month = item.getKey();
                    List<MonthPlanSubmitAdd> list = item.getValue();
                    String quarter = ISaleDateUtil.getQuarterFromMonthCode(month);
                    //季度数据
                    String key1 = baComOrgCode + "-" + cartonCode + "-" + quarter;
                    BigDecimal quarterQty = cartonCodeQtyMap.getOrDefault(key1, BigDecimal.ZERO);
                    //忽略小于最小调拨量的数据
                    if (quarterQty.compareTo(new BigDecimal(minQty)) <= 0) {
                        continue;
                    }
                    BigDecimal monthQty = list.stream()
                            .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanRegionConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal multiply = monthQty.divide(quarterQty, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                    if (multiply.compareTo(maxQty) > 0) {
                        errorMsg.append("地市[").append(baComOrgCode).append("]，").append(acCgtName).append("调拨计划区域确认量超过当季度该规格总量的").append(specQty).append("；");
                    }
                }
            }
        } else if (MonthSalePlanVerifyEnum.PLAN_VERIFY.getCode().equals(type)) {
            for (Map.Entry<String, List<MonthPlanSubmitAdd>> entry : cartonCodeGroupReqMap.entrySet()) {
                String key = entry.getKey();
                String[] split = key.split("-");
                String baComOrgCode = split[0];
                String cartonCode = split[1];
                List<MonthPlanSubmitAdd> value = entry.getValue();
                String month = value.get(0).getMa02PlanMonth();
                String acCgtName = value.get(0).getAcCgtName();
                if (MonthPlanConstants.FESTIVAL_MONTH.contains(month.substring(4, 6))) {
                    continue;
                }
                String quarter = ISaleDateUtil.getQuarterFromMonthCode(month);
                //季度数据
                String key1 = baComOrgCode + "-" + cartonCode + "-" + quarter;
                BigDecimal quarterQty = cartonCodeQtyMap.getOrDefault(key1, BigDecimal.ZERO);
                //忽略小于最小调拨量的数据
                if (quarterQty.compareTo(new BigDecimal(minQty)) <= 0) {
                    continue;
                }
                BigDecimal monthQty = value.stream()
                        .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanPdConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal multiply = monthQty.divide(quarterQty, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                if (multiply.compareTo(maxQty) > 0) {
                    errorMsg.append("地市[").append(baComOrgCode).append("]，").append(acCgtName).append("调拨计划计划部确认量超过当季度该规格总量的").append(specQty).append("；");
                }
            }
        } else if (MonthSalePlanVerifyEnum.BRAND_VERIFY.getCode().equals(type)) {
            for (Map.Entry<String, List<MonthPlanSubmitAdd>> entry : cartonCodeGroupReqMap.entrySet()) {
                String key = entry.getKey();
                String[] split = key.split("-");
                String baComOrgCode = split[0];
                String cartonCode = split[1];
                List<MonthPlanSubmitAdd> value = entry.getValue();
                String month = value.get(0).getMa02PlanMonth();
                String acCgtName = value.get(0).getAcCgtName();
                if (MonthPlanConstants.FESTIVAL_MONTH.contains(month.substring(4, 6))) {
                    continue;
                }
                String quarter = ISaleDateUtil.getQuarterFromMonthCode(month);
                //季度数据
                String key1 = baComOrgCode + "-" + cartonCode + "-" + quarter;
                BigDecimal quarterQty = cartonCodeQtyMap.getOrDefault(key1, BigDecimal.ZERO);
                //忽略小于最小调拨量的数据
                if (quarterQty.compareTo(new BigDecimal(minQty)) <= 0) {
                    continue;
                }
                BigDecimal monthQty = value.stream()
                        .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanBdConfirmQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal multiply = monthQty.divide(quarterQty, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                if (multiply.compareTo(maxQty) > 0) {
                    errorMsg.append("地市[").append(baComOrgCode).append("]，").append(acCgtName).append("调拨计划品牌部确认量超过当季度该规格总量的").append(specQty).append("；");
                }
            }
        } else if (MonthSalePlanVerifyEnum.TO_RELEASED.getCode().equals(type)) {
            for (Map.Entry<String, List<MonthPlanSubmitAdd>> entry : cartonCodeGroupReqMap.entrySet()) {
                String key = entry.getKey();
                String[] split = key.split("-");
                String baComOrgCode = split[0];
                String cartonCode = split[1];
                List<MonthPlanSubmitAdd> value = entry.getValue();
                String month = value.get(0).getMa02PlanMonth();
                String acCgtName = value.get(0).getAcCgtName();
                if (MonthPlanConstants.FESTIVAL_MONTH.contains(month.substring(4, 6))) {
                    continue;
                }
                String quarter = ISaleDateUtil.getQuarterFromMonthCode(month);
                //季度数据
                String key1 = baComOrgCode + "-" + cartonCode + "-" + quarter;
                BigDecimal quarterQty = cartonCodeQtyMap.getOrDefault(key1, BigDecimal.ZERO);
                //忽略小于最小调拨量的数据
                if (quarterQty.compareTo(new BigDecimal(minQty)) <= 0) {
                    continue;
                }
                BigDecimal monthQty = value.stream()
                        .map(MonthPlanSubmitAdd::getMc04CgtAllotPlanPdPublishQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal multiply = monthQty.divide(quarterQty, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                if (multiply.compareTo(maxQty) > 0) {
                    errorMsg.append("地市[").append(baComOrgCode).append("]，").append(acCgtName).append("调拨计划品牌部发布量超过当季度该规格总量的").append(specQty).append("；");
                }
            }
        }


        if (errorMsg.length() > 0) {
            return IndResult.fail(errorMsg.toString());
        }

        return IndResult.success();
    }

    /**
     * 获取当前月销售计划数据
     *
     * @param zaOccurrenceMonth
     * @param acCgtCartonCodeList
     * @param baComOrgCodeList
     * @return
     */
    private Map<String, BigDecimal> getCurrentMonthQty(String zaOccurrenceMonth, List<String> acCgtCartonCodeList, List<String> baComOrgCodeList) {
        MonthSalePlanQueryBatchREQ monthSalePlanQueryREQ = new MonthSalePlanQueryBatchREQ();
        monthSalePlanQueryREQ.setMa02PlanMonths(Collections.singletonList(zaOccurrenceMonth));
        monthSalePlanQueryREQ.setBaComOrgCodes(baComOrgCodeList);
        monthSalePlanQueryREQ.setIcomCode(IcomUtils.getIcomCode());
        monthSalePlanQueryREQ.setAcCgtCartonCodes(acCgtCartonCodeList);
        monthSalePlanQueryREQ.setMa02TobaProdTradeTypeCode(ProdTradeTypeCodeEnum.PROD_TRADE_TYPE_CODE_0.getCode());

        List<MonthSalePlanDTO> monthSalePlanDTOS = islmMonthPlanSubmitRepository.getSaleListBatch(monthSalePlanQueryREQ);
        return monthSalePlanDTOS.stream()
                .collect(Collectors.groupingBy(
                        MonthSalePlanDTO::getBaComOrgCode,
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                MonthSalePlanDTO::getMa02CgtPlAdjustedQty,
                                BigDecimal::add
                        )
                ));

    }

    /**
     * 获取上个月实际调拨计划数据
     *
     * @param zaOccurrenceMonth
     * @param baComOrgCodeList
     * @return
     */
    private Map<String, BigDecimal> getLastMonthQty(String zaOccurrenceMonth, List<String> baComOrgCodeList) {

        List<MonthPlanQuarterItem> lastMonthQty = islmMonthPlanSubmitRepository.getLastMonthQty(baComOrgCodeList, ISaleDateUtil.getOffsetMonth(zaOccurrenceMonth, -1), IcomUtils.getIcomCode());

        return lastMonthQty.stream().collect(Collectors.toMap(
                MonthPlanQuarterItem::getBaComOrgCode,
                MonthPlanQuarterItem::getLastMonthAllotActualQty,
                (v1, v2) -> v1));


    }

    /**
     * 判断指定月份是所在季度的第几个月
     *
     * @param planMonth 计划月份，格式为 "yyyyMM"
     * @return 该月在季度中的位置（1-3）
     */
    private int getMonthPositionInQuarter(String planMonth) {
        // 获取该月所属的季度，格式为 "yyyyQn"，其中 n 为季度（1-4）
        String quarter = ISaleDateUtil.getQuarterFromMonthCode(planMonth);

        // 提取季度
        int quarterNumber = Integer.parseInt(quarter.substring(5));

        // 计算季度的起始月份
        int startMonthOfQuarter = (quarterNumber - 1) * 3 + 1;

        // 提取计划月份的月份部分
        int planMonthNumber = Integer.parseInt(planMonth.substring(4));

        // 计算该月是季度中的第几个月
        return planMonthNumber - startMonthOfQuarter + 1;
    }
}
