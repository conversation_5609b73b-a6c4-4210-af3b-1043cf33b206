/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmBusiActionLogDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmBusiActionLogMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmBusiActionLogService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: wlh
 * @Since: 2025-07-22
 * @Create: 2025-07-22
 */
@Service
public class Mc04IslmBusiActionLogServiceImpl extends ServiceImpl<Mc04IslmBusiActionLogMapper, Mc04IslmBusiActionLogDO> implements Mc04IslmBusiActionLogService {

}