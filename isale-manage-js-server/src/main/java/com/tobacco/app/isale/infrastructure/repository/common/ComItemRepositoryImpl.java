/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.inspur.ind.base.CustomException;
import com.tobacco.app.isale.domain.model.common.ComItem;
import com.tobacco.app.isale.domain.repository.common.ComItemRepository;
import com.tobacco.app.isale.infrastructure.converter.common.Mc04IslmcComItemDOToComItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcComItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcComItemService;
import com.tobacco.app.isalecenter.common.constants.SaleCenterConstants;
import com.tobacco.app.isalecenter.common.enums.AcOneLevelClassTypeCodeEnum;
import com.tobacco.app.isalecenter.common.enums.ProdTradeTypeCodeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/05/09 09:35
 * @description : 地市卷烟实现
 */
@Component("ISaleComItemDORepository")
public class ComItemRepositoryImpl implements ComItemRepository {

    private Mc04IslmcComItemService mc04IslmcComItemService;

    @Autowired
    public void setMc04IslmcComItemService(Mc04IslmcComItemService mc04IslmcComItemService) {
        this.mc04IslmcComItemService = mc04IslmcComItemService;
    }

    /**
     * @param cgtType 业务类型
     * @param comIds  协议单位编码
     * @return List<IndDemandFoComItem>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-22 16:32:21
     * @description : 获取地市卷烟
     */
    @Override
    public List<ComItem> getComItemList(String cgtType, String comIds) {
        // 将逗号分隔的公司ID字符串转换为列表
        List<String> comIdList = StrUtil.split(comIds, ",", true, true);
        return getComItemList(cgtType, comIdList);
    }

    /**
     * @param cgtType   业务类型 {@link ProdTradeTypeCodeEnum}
     * @param comIdList 协议单位编码
     * @return List<IndDemandFoComItem>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-22 16:32:21
     * @description : 获取地市卷烟
     */
    @Override
    public List<ComItem> getComItemList(String cgtType, List<String> comIdList) {
        String acOneLevelClassTypeCode = getAcOneLevelClassTypeCode(cgtType);

        // 根据公司ID列表查询有效的公司商品项
        List<Mc04IslmcComItemDO> comItemList = mc04IslmcComItemService.lambdaQuery()
                .in(CollUtil.isNotEmpty(comIdList), Mc04IslmcComItemDO::getBaComOrgCode, comIdList)
                .eq(Mc04IslmcComItemDO::getIsUse, SaleCenterConstants.YES)
                .eq(Mc04IslmcComItemDO::getAcOneLevelClassTypeCode, acOneLevelClassTypeCode)
                .list();
        // 如果未找到公司商品项，则返回空列表
        if (CollUtil.isEmpty(comItemList)) {
            return Collections.emptyList();
        }
        return Mc04IslmcComItemDOToComItemConverter.INSTANCE.converterDosToModels(comItemList);
    }

    private static String getAcOneLevelClassTypeCode(String cgtType) {
        String acOneLevelClassTypeCode;
        if (cgtType.equals(ProdTradeTypeCodeEnum.PROD_TRADE_TYPE_CODE_0.getCode())) {
            acOneLevelClassTypeCode = AcOneLevelClassTypeCodeEnum.AC_ONE_LEVEL_CLASS_TYPE_CODE_01.getCode();
        } else if (cgtType.equals(ProdTradeTypeCodeEnum.PROD_TRADE_TYPE_CODE_1.getCode())) {
            acOneLevelClassTypeCode = AcOneLevelClassTypeCodeEnum.AC_ONE_LEVEL_CLASS_TYPE_CODE_02.getCode();
        } else {
            throw new CustomException("业务类型错误");
        }
        return acOneLevelClassTypeCode;
    }
}
