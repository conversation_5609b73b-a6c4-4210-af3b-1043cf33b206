/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.order;

import com.tobacco.app.isale.domain.model.order.dist.order.Mc04IslmCgtDistOrder;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtDistOrderDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/08/21 17:20
 * @description : TODO
 */
@Mapper
public interface Mc04IslmCgtDistOrderToMc04IslmCgtDistOrderDOConverter extends StructureBaseConverter<Mc04IslmCgtDistOrderDO, Mc04IslmCgtDistOrder> {
    Mc04IslmCgtDistOrderToMc04IslmCgtDistOrderDOConverter SELF = Mappers.getMapper(Mc04IslmCgtDistOrderToMc04IslmCgtDistOrderDOConverter.class);
}