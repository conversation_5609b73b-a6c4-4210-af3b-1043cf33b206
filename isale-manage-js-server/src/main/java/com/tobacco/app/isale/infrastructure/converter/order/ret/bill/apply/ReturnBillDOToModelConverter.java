package com.tobacco.app.isale.infrastructure.converter.order.ret.bill.apply; /**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */

import com.tobacco.app.isale.domain.model.order.ret.bill.ReturnBillDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.apply.ApplyDomain;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtReturnBillDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ReturnBillDOToModelConverter extends StructureBaseConverter<Mc04IslmcCgtReturnBillDO, ReturnBillDomain> {
    ReturnBillDOToModelConverter INSTANCE = Mappers.getMapper(ReturnBillDOToModelConverter.class);
}

