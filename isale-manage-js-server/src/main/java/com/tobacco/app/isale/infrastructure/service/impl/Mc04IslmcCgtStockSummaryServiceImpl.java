/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtStockSummaryDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcCgtStockSummaryMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcCgtStockSummaryService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: hujiarong
 * @Since: 2025-08-22
 * @Email: <EMAIL>
 * @Create: 2025-08-22
        */
@Service
public class Mc04IslmcCgtStockSummaryServiceImpl extends ServiceImpl<Mc04IslmcCgtStockSummaryMapper, Mc04IslmcCgtStockSummaryDO> implements Mc04IslmcCgtStockSummaryService {

}