/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.ycplan;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tobacco.app.isale.domain.model.plan.ycplan.YcPlanCity;
import com.tobacco.app.isale.domain.repository.plan.ycplan.YcPlanCityRepository;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmYcSalePlanDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmYcSalePlanItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmYcSalePlanItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmYcSalePlanService;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.ycplan.IslmYcSalePlanMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: loongxi
 * @Since: 2025-08-07
 */
@Component("ISaleMc04IslmYcPlanCityDORepository")
public class YcPlanCityRepositoryImpl implements YcPlanCityRepository {


}