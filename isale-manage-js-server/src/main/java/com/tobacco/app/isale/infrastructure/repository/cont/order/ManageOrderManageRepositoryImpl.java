package com.tobacco.app.isale.infrastructure.repository.cont.order;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.base.CustomPage;
import com.inspur.ind.constant.CommonConstants;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.constants.DistConstants;
import com.tobacco.app.isale.domain.enums.basic.BusiActionLogBusiTypeEnum;
import com.tobacco.app.isale.domain.enums.basic.InterfaceLogBusiTypeEnum;
import com.tobacco.app.isale.domain.enums.basic.InterfaceLogChannelEnum;
import com.tobacco.app.isale.domain.enums.cont.ContStatusEnum;
import com.tobacco.app.isale.domain.model.cont.order.*;
import com.tobacco.app.isale.domain.model.order.ret.bill.ContOrderDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.ContOrderItemDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.ReturnBillDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.ReturnBillItemDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.contmanage.ContManageDomain;
import com.tobacco.app.isale.domain.repository.cont.order.ManageOrderManageRepository;
import com.tobacco.app.isale.infrastructure.converter.cont.order.ContOrderDTOToManageContOrderConverter;
import com.tobacco.app.isale.infrastructure.converter.cont.order.Mc04IslmContOrderDOToManageContOrderConverter;
import com.tobacco.app.isale.infrastructure.converter.cont.order.Mc04IslmContOrderItemDOToManageContOrderItemConverter;
import com.tobacco.app.isale.infrastructure.converter.cont.order.Mc04IslmcContOrderReqToManageContOrderConverter;
import com.tobacco.app.isale.infrastructure.converter.order.ret.bill.ContOrderDomainToDoConverter;
import com.tobacco.app.isale.infrastructure.converter.order.ret.bill.ContOrderItemDomainToDoConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderItemDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContReachWhseDO;
import com.tobacco.app.isale.infrastructure.entity.*;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmContOrderItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmContOrderService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContDelivWhseService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContReachWhseService;
import com.tobacco.app.isale.infrastructure.tunnel.database.cont.order.ManageOrderManageMapper;
import com.tobacco.app.isale.third.service.api.inter.ThreePartySysApi;
import com.tobacco.app.isale.tools.utils.*;
import com.tobacco.app.isalecenter.client.api.cont.order.ContOrderApi;
import com.tobacco.app.isalecenter.client.dto.cont.order.Mc04IslmcContOrderDTO;
import com.tobacco.app.isalecenter.client.req.cont.order.Mc04IslmcContOrderREQ;
import com.tobacco.sc.icommodity.dto.common.constant.dto.item.IccItemDetailDTO;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tobacco.app.isale.domain.enums.cont.ContStatusEnum.CONT_STATUS_00;
import static com.tobacco.app.isale.domain.enums.order.ret.bill.IsaleProdTradeType.PROD_TRADE_TYPE_10;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: wangluhao01
 * @Since: 2025-06-16
 */
@Slf4j
@Component("ISaleManageManageOrderManageRepository")
public class ManageOrderManageRepositoryImpl implements ManageOrderManageRepository {

    private ContOrderApi contOrderApi;

    @Autowired
    public void setContOrderApi(ContOrderApi contOrderApi) {
        this.contOrderApi = contOrderApi;
    }

    private Mc04IslmContOrderService mc04IslmContOrderService;

    @Autowired
    public void setMc04IslmContOrderService(Mc04IslmContOrderService mc04IslmContOrderService) {
        this.mc04IslmContOrderService = mc04IslmContOrderService;
    }

    private Mc04IslmContOrderItemService mc04IslmContOrderItemService;

    @Autowired
    public void setMc04IslmContOrderItemService(Mc04IslmContOrderItemService mc04IslmContOrderItemService) {
        this.mc04IslmContOrderItemService = mc04IslmContOrderItemService;
    }

    private ManageOrderManageMapper manageOrderManageMapper;

    @Autowired
    public void setManageOrderManageMapper(ManageOrderManageMapper manageOrderManageMapper) {
        this.manageOrderManageMapper = manageOrderManageMapper;
    }

    private Mc04IslmcContDelivWhseService mc04IslmcContDelivWhseService;

    @Autowired
    public void setMc04IslmcContDelivWhseService(Mc04IslmcContDelivWhseService mc04IslmcContDelivWhseService) {
        this.mc04IslmcContDelivWhseService = mc04IslmcContDelivWhseService;
    }

    private Mc04IslmcContReachWhseService mc04IslmcContReachWhseService;

    @Autowired
    public void setMc04IslmcContReachWhseService(Mc04IslmcContReachWhseService mc04IslmcContReachWhseService) {
        this.mc04IslmcContReachWhseService = mc04IslmcContReachWhseService;
    }

    private ThreePartySysApi threePartySysApi;

    @Override
    public Map<String,BigDecimal> getContItemTotal(String distOrderCode){
        List<Mc04IslmContOrderDO> contOrderDOList = mc04IslmContOrderService.lambdaQuery().eq(Mc04IslmContOrderDO::getMc04CgtDistOrderCode, distOrderCode).list();
        if (contOrderDOList == null || contOrderDOList.isEmpty()) {
            return new HashMap<>();
        }
        List<String> contOrderIdList = contOrderDOList.stream().map(Mc04IslmContOrderDO::getMc04ContOrderId).collect(Collectors.toList());
        List<Mc04IslmContOrderItemDO> contOrderItemDOList = mc04IslmContOrderItemService.lambdaQuery().in(Mc04IslmContOrderItemDO::getMc04ContOrderId, contOrderIdList).list();
        if (contOrderItemDOList == null || contOrderItemDOList.isEmpty()) {
            return new HashMap<>();
        }
        Map<String,BigDecimal> resultMap =  new HashMap<>();

        for (Mc04IslmContOrderItemDO mc04IslmContOrderItemDO : contOrderItemDOList) {
            String acTwoLevelCigCode = mc04IslmContOrderItemDO.getAcTwoLevelCigCode();
            BigDecimal qty = mc04IslmContOrderItemDO.getMd02CgtTradeContQty();
            if (qty != null) {
                resultMap.merge(acTwoLevelCigCode, qty, BigDecimal::add);
            }
        }
        return resultMap;
    }



    /**
     * @param orderPage 查询参数
     * @return Page<ManageContOrder>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-06-16 15:31:12
     * @description : 分页查询合同
     */
    @Override
    public Page<ManageContOrder> queryOrderPage(ManageOrderPage orderPage) {
        Page<ManageContOrder> pageResult = new Page<>();
        //查主表
        CustomPage<Mc04IslmContOrderDO> page = new CustomPage<>(
                orderPage.getOffset(),
                orderPage.getLimit(),
                orderPage.getSort(),
                orderPage.getOrder()
        );
        List<String> contNoList = orderPage.getContNoList();

        String contNo = CollUtil.isNotEmpty(contNoList) && contNoList.size() == 1 ? contNoList.get(0) : null;

        Page<Mc04IslmContOrderDO> pageDoResult = mc04IslmContOrderService.lambdaQuery()
                .eq(StrUtil.isNotBlank(orderPage.getProdTradeTypeCode()),
                        Mc04IslmContOrderDO::getMa02TobaProdTradeTypeCode, orderPage.getProdTradeTypeCode())
                .ge(StrUtil.isNotBlank(orderPage.getMc04ContExpecOutDateBegin()),
                        Mc04IslmContOrderDO::getMc04ContExpecOutDate, orderPage.getMc04ContExpecOutDateBegin())
                .le(StrUtil.isNotBlank(orderPage.getMc04ContExpecOutDateEnd()),
                        Mc04IslmContOrderDO::getMc04ContExpecOutDate, orderPage.getMc04ContExpecOutDateEnd())
                .in(CollUtil.isNotEmpty(orderPage.getComIdList()),
                        Mc04IslmContOrderDO::getBaComOrgCode, orderPage.getComIdList())
                .in(CollUtil.isNotEmpty(orderPage.getContStatusList()),
                        Mc04IslmContOrderDO::getMc04CgtTradeContStatus, orderPage.getContStatusList())
                .in(CollUtil.isNotEmpty(orderPage.getContZeroClockTypeList()),
                        Mc04IslmContOrderDO::getMc04ContZeroClockType, orderPage.getContZeroClockTypeList())
                .in(CollUtil.isNotEmpty(contNoList) && contNoList.size() > 1,
                        Mc04IslmContOrderDO::getMd02CgtTradeContNo, contNoList)
                .in(CollUtil.isNotEmpty(orderPage.getOutStorehouseCodeList()),
                        Mc04IslmContOrderDO::getMd02CgtOutStorehouseCode, orderPage.getOutStorehouseCodeList())
                .like(CollUtil.isNotEmpty(contNoList) && contNoList.size() == 1,
                        Mc04IslmContOrderDO::getMd02CgtTradeContNo, contNo)
                .eq(Mc04IslmContOrderDO::getIcomCode, IcomUtils.getIcomCode())
                .orderByAsc(Mc04IslmContOrderDO::getMc04ContExpecOutDate)
                .page(page);
        if (ObjectUtil.isNull(pageDoResult) || pageDoResult.getTotal() == 0) {
            return pageResult;
        }
        pageResult.setTotal(pageDoResult.getTotal());
        pageResult.setCurrent(pageDoResult.getCurrent());
        pageResult.setSize(pageDoResult.getSize());

        //获取从表
        List<String> contOrderIdList = pageDoResult.getRecords().stream()
                .map(Mc04IslmContOrderDO::getMc04ContOrderId)
                .collect(Collectors.toList());
        List<Mc04IslmContOrderItemDO> itemList = mc04IslmContOrderItemService.lambdaQuery()
                .in(Mc04IslmContOrderItemDO::getMc04ContOrderId, contOrderIdList)
                .list();
        Map<String, List<Mc04IslmContOrderItemDO>> contOrderIdMap = itemList.stream()
                .collect(Collectors.groupingBy(Mc04IslmContOrderItemDO::getMc04ContOrderId));
        //获取地市名称
        List<String> comIdList = pageDoResult.getRecords().stream()
                .map(Mc04IslmContOrderDO::getBaComOrgCode)
                .collect(Collectors.toList());
        Map<String, BusiComDto> busiComDtoMap = CustUtil.getBusiComDtoMap(comIdList);
        //获取卷烟名称
        List<String> acTwoLevelCigCodeList = itemList.stream()
                .map(Mc04IslmContOrderItemDO::getAcTwoLevelCigCode)
                .collect(Collectors.toList());
        Map<String, IccItemDetailDTO> itemDetailDtoMap = CommodityUtil.getIccItemDetailDtoMap(acTwoLevelCigCodeList);

        //获取发货仓库信息
        List<String> outStorehouseCodeList = pageDoResult.getRecords().stream()
                .map(Mc04IslmContOrderDO::getMd02CgtOutStorehouseCode)
                .collect(Collectors.toList());

        List<Mc04IslmcContDelivWhseDO> contDelivWhseDoList = mc04IslmcContDelivWhseService.lambdaQuery()
                .in(Mc04IslmcContDelivWhseDO::getMd02CgtOutStorehouseCode, outStorehouseCodeList)
                .list();

        Map<String, Mc04IslmcContDelivWhseDO> contDelivWhseDoMap = contDelivWhseDoList.stream()
                .collect(Collectors.toMap(Mc04IslmcContDelivWhseDO::getMd02CgtOutStorehouseCode, Function.identity()));

        //获取到货仓库信息
        List<String> inStorehouseCodeList = pageDoResult.getRecords().stream()
                .map(Mc04IslmContOrderDO::getMd02CgtInStorehouseCode)
                .collect(Collectors.toList());

        List<Mc04IslmcContReachWhseDO> contReachWhseDoList = mc04IslmcContReachWhseService.lambdaQuery()
                .in(Mc04IslmcContReachWhseDO::getMd02CgtInStorehouseCode, inStorehouseCodeList)
                .list();

        Map<String, Mc04IslmcContReachWhseDO> contReachWhseDoMap = contReachWhseDoList.stream()
                .collect(Collectors.toMap(Mc04IslmcContReachWhseDO::getMd02CgtInStorehouseCode, Function.identity()));

        //拼接数据
        return (Page<ManageContOrder>) pageDoResult.convert(contOrder -> {
            ManageContOrder order = Mc04IslmContOrderDOToManageContOrderConverter.INSTANCE
                    .converterDoToModel(contOrder);
            BusiComDto busiComDto = busiComDtoMap.get(contOrder.getBaComOrgCode());
            Assert.notNull(busiComDto, () -> new CustomException("中心未获取到" +
                    contOrder.getBaComOrgCode() + "的协议单位信息"));
            order.setBaComOrgName(busiComDto.getBaComOrgName());
            order.setMc04ComOrgShortName(busiComDto.getMc04ComOrgShortName());

            //拼接发货仓库
            Mc04IslmcContDelivWhseDO contDelivWhse = contDelivWhseDoMap.get(contOrder.getMd02CgtOutStorehouseCode());
            Assert.notNull(contDelivWhse, () -> new CustomException("未获取到" +
                    contOrder.getMd02CgtOutStorehouseCode() + "的发货仓库信息"));
            order.setMd02CgtOutStorehouseShortName(contDelivWhse.getCbCbLogtWhseNameAbbrev());

            //拼接到货仓库信息
            Mc04IslmcContReachWhseDO contReachWhse = contReachWhseDoMap.get(contOrder.getMd02CgtInStorehouseCode());
            Assert.notNull(contReachWhse, () -> new CustomException("未获取到" +
                    contOrder.getMd02CgtInStorehouseCode() + "的收货仓库信息"));
            order.setMd02CgtInStorehouseName(contReachWhse.getMd02CgtInStorehouseName());

            List<Mc04IslmContOrderItemDO> orderItemDos = contOrderIdMap.get(contOrder.getMc04ContOrderId());
            Assert.notEmpty(orderItemDos, () -> new CustomException("合同从表丢失"));
            List<ManageContOrderItem> orderItemList =
                    Mc04IslmContOrderItemDOToManageContOrderItemConverter.INSTANCE.converterDosToModels(orderItemDos);
            orderItemList.forEach(item -> {
                IccItemDetailDTO itemDetailDTO = itemDetailDtoMap.get(item.getAcTwoLevelCigCode());
                Assert.notNull(itemDetailDTO, () -> new CustomException("商品中心未获取到" +
                        item.getAcTwoLevelCigCode() + "的二级牌号信息"));
                item.setAcTwoLevelCigName(itemDetailDTO.getAcTwoLevelCigName());
                item.setSeq(itemDetailDTO.getProductSeq());
            });
            order.setContOrderItemList(orderItemList);
            return order;
        });
    }


    /**
     * @param contOrderId 订单ID
     * @return ManageContOrderDTO
     * <AUTHOR> wangluhao01
     * @create_time : 2025-06-16 15:31:12
     * @description : 查询合同详情
     */
    @Override
    public ManageContOrder queryOrderDetail(String contOrderId) {

        Mc04IslmContOrderDO contOrderDo = mc04IslmContOrderService.lambdaQuery()
                .eq(Mc04IslmContOrderDO::getMc04ContOrderId, contOrderId)
                .eq(Mc04IslmContOrderDO::getIcomCode, IcomUtils.getIcomCode())
                .one();
        Assert.notNull(contOrderDo, () -> new CustomException("未获取到" + contOrderId + "的订单信息"));

        ManageContOrder order = Mc04IslmContOrderDOToManageContOrderConverter.INSTANCE
                .converterDoToModel(contOrderDo);

        //获取从表
        List<Mc04IslmContOrderItemDO> itemList = mc04IslmContOrderItemService.lambdaQuery()
                .eq(Mc04IslmContOrderItemDO::getMc04ContOrderId, contOrderId)
                .list();

        List<ManageContOrderItem> orderItemList =
                Mc04IslmContOrderItemDOToManageContOrderItemConverter.INSTANCE.converterDosToModels(itemList);


        //获取地市名称
        BusiComDto busiComDto = CustUtil.getBusiComDto(contOrderDo.getBaComOrgCode());
        Assert.notNull(busiComDto, () -> new CustomException("中心未获取到" +
                contOrderDo.getBaComOrgCode() + "的协议单位信息"));

        //获取卷烟名称
        List<String> acTwoLevelCigCodeList = itemList.stream()
                .map(Mc04IslmContOrderItemDO::getAcTwoLevelCigCode)
                .collect(Collectors.toList());
        Collection<IccItemDetailDTO> itemDetailDtos =
                CommodityUtil.getIccItemDetailDtos(acTwoLevelCigCodeList, CommodityUtil.LEVEL_CODE_3);
        Map<String, IccItemDetailDTO> itemDetailDtoMap = itemDetailDtos.stream()
                .collect(Collectors.toMap(IccItemDetailDTO::getAcThrLevelCigCode, Function.identity()));

        //获取发货仓库信息
        Mc04IslmcContDelivWhseDO contDelivWhseDo =
                mc04IslmcContDelivWhseService.getById(contOrderDo.getMd02CgtOutStorehouseCode());
        Assert.notNull(contDelivWhseDo, () -> new CustomException("未获取到" +
                contOrderDo.getMd02CgtOutStorehouseCode() + "的发货仓库信息"));
        order.setMd02CgtOutStorehouseShortName(contDelivWhseDo.getCbCbLogtWhseNameAbbrev());

        //获取到货仓库信息
        Mc04IslmcContReachWhseDO contReachWhseDo =
                mc04IslmcContReachWhseService.getById(contOrderDo.getMd02CgtInStorehouseCode());
        Assert.notNull(contReachWhseDo, () -> new CustomException("未获取到" +
                contOrderDo.getMd02CgtInStorehouseCode() + "的到货仓库信息"));
        order.setMd02CgtInStorehouseName(contReachWhseDo.getMd02CgtInStorehouseName());

        orderItemList.forEach(item -> {
            IccItemDetailDTO itemDetailDto = itemDetailDtoMap.get(item.getAcThrLevelCigCode());
            Assert.notNull(itemDetailDto, () -> new CustomException("商品中心未获取到" +
                    item.getAcTwoLevelCigCode() + "的三级牌号信息"));
            item.setAcThrLevelCigName(itemDetailDto.getAcThrLevelCigName());
            item.setAcTwoLevelCigName(itemDetailDto.getAcTwoLevelCigName());
        });

        if (busiComDto != null) {
            order.setBaComOrgName(busiComDto.getBaComOrgName());
            order.setMc04ComOrgShortName(busiComDto.getMc04ComOrgShortName());
        }
        order.setContOrderItemList(orderItemList);

        return order;
    }


    /**
     * 从中台侧获取合同列表
     *
     * @param statusList 订单状态
     * @return List<ManageContOrder>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 获取合同列表
     */
    @Override
    public List<ManageContOrder> getCenterContList(List<String> statusList) {
        Mc04IslmcContOrderREQ req = new Mc04IslmcContOrderREQ();
        req.setIcomCode(IcomUtils.getIcomCode());
        req.setMc04CgtTradeContStatusList(statusList);
        List<Mc04IslmcContOrderDTO> contOrderDtos =
                SaleResponseUtil.getCenterDTO("从中台侧获取合同列表", () -> contOrderApi.list(req));
        return ContOrderDTOToManageContOrderConverter.INSTANCE.converterDosToModels(contOrderDtos);
    }

    /**
     * 从中台侧获取合同列表
     *
     * @param param 参数
     * @return List<ManageContOrder>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 获取合同列表
     */
    @Override
    public List<ManageContOrder> getCenterContList(ManageContOrderGetCenterContParam param) {
        Mc04IslmcContOrderREQ req = new Mc04IslmcContOrderREQ();
        req.setIcomCode(IcomUtils.getIcomCode());
        req.setMc04ContOrderIdList(param.getMc04ContOrderIdList());
        req.setMc04CgtTradeContStatusList(param.getMc04CgtTradeContStatusList());
        List<Mc04IslmcContOrderDTO> contOrderDtos =
                SaleResponseUtil.getCenterDTO("从中台侧获取合同列表", () -> contOrderApi.list(req));
        return ContOrderDTOToManageContOrderConverter.INSTANCE.converterDosToModels(contOrderDtos);
    }

    /**
     * 获取合同列表
     *
     * @param statusList 订单状态
     * @return List<ManageContOrder>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 获取合同列表
     */
    @Override
    public List<ManageContOrder> getManageContList(List<String> statusList) {
        return getManageContList(statusList, new ArrayList<>());
    }

    /**
     * 获取合同列表
     *
     * @param statusList 订单状态
     * @param xyNoList   协议编号
     * @return List<ManageContOrder>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 获取合同列表
     */
    @Override
    public List<ManageContOrder> getManageContList(List<String> statusList, List<String> xyNoList) {
        ManageContOrderGetMangeContParam param = new ManageContOrderGetMangeContParam();
        param.setMc04ContOrderIdList(new ArrayList<>());
        param.setMd02CgtXyNoList(xyNoList);
        param.setMc04CgtTradeContStatusList(statusList);
        return getManageContList(param);
    }

    /**
     * 获取合同列表
     *
     * @param param 查询参数
     * @return List<ManageContOrder>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 获取合同列表
     */
    @Override
    public List<ManageContOrder> getManageContList(ManageContOrderGetMangeContParam param) {
        List<Mc04IslmContOrderDO> orderDoList = mc04IslmContOrderService.lambdaQuery()
                .in(CollUtil.isNotEmpty(param.getMc04ContOrderIdList()),
                        Mc04IslmContOrderDO::getMc04ContOrderId, param.getMc04ContOrderIdList())
                .eq(StrUtil.isNotBlank(param.getXyPeriodCode()),
                        Mc04IslmContOrderDO::getMc04CgtXyPeriodCode, param.getXyPeriodCode())
                .eq(StrUtil.isNotBlank(param.getProdTradeTypeCode()),
                        Mc04IslmContOrderDO::getMa02TobaProdTradeTypeCode, param.getProdTradeTypeCode())
                .in(CollUtil.isNotEmpty(param.getComCodeList()),
                        Mc04IslmContOrderDO::getBaComOrgCode, param.getComCodeList())
                .in(CollUtil.isNotEmpty(param.getMc04CgtTradeContStatusList()),
                        Mc04IslmContOrderDO::getMc04CgtTradeContStatus, param.getMc04CgtTradeContStatusList())
                .in(CollUtil.isNotEmpty(param.getMd02CgtXyNoList()),
                        Mc04IslmContOrderDO::getMd02CgtXyNo, param.getMd02CgtXyNoList())
                .in(CollUtil.isNotEmpty(param.getMc04ContZeroClockTypeList()),
                        Mc04IslmContOrderDO::getMc04ContZeroClockType, param.getMc04ContZeroClockTypeList())
                .in(CollUtil.isNotEmpty(param.getMc04CgtTradeContZeroStatusList()),
                        Mc04IslmContOrderDO::getMc04CgtTradeContZeroStatus, param.getMc04CgtTradeContZeroStatusList())
                .isNotNull(ObjectUtil.isNotNull(param.getIsHaveContractNumber())
                        && param.getIsHaveContractNumber(), Mc04IslmContOrderDO::getMc04CgtCpnContNo)
                .isNull(ObjectUtil.isNotNull(param.getIsHaveContractNumber())
                        && !param.getIsHaveContractNumber(), Mc04IslmContOrderDO::getMc04CgtCpnContNo)
                .isNotNull(ObjectUtil.isNotNull(param.getIsHaveTradeContStockTranNo())
                        && param.getIsHaveTradeContStockTranNo(), Mc04IslmContOrderDO::getMc04CgtTradeContStockTranNo)
                .eq(ObjectUtil.isNotNull(param.getIsSyncContractNumber()),
                        Mc04IslmContOrderDO::getMc04CgtTradeContVirtualStatus, param.getIsSyncContractNumber())
                .in(CollUtil.isNotEmpty(param.getDistOrderCodeList()),
                        Mc04IslmContOrderDO::getMc04CgtDistOrderCode, param.getDistOrderCodeList())
                .in(CollUtil.isNotEmpty(param.getMd02CgtTradeContNoList()),
                        Mc04IslmContOrderDO::getMd02CgtTradeContNo, param.getMd02CgtTradeContNoList())
                .eq(Mc04IslmContOrderDO::getIcomCode, IcomUtils.getIcomCode())
                .list();

        if (CollUtil.isEmpty(orderDoList)) {
            return Collections.emptyList();
        }
        List<ManageContOrder> orderList =
                Mc04IslmContOrderDOToManageContOrderConverter.INSTANCE.converterDosToModels(orderDoList);
        //获取从表
        List<String> orderIdList = orderList.stream()
                .map(ManageContOrder::getMc04ContOrderId).collect(Collectors.toList());
        List<Mc04IslmContOrderItemDO> itemDoList = mc04IslmContOrderItemService.lambdaQuery()
                .in(Mc04IslmContOrderItemDO::getMc04ContOrderId, orderIdList)
                .list();
        Assert.notEmpty(itemDoList, () -> new CustomException("合同丢失从表信息"));
        List<ManageContOrderItem> orderItemList =
                Mc04IslmContOrderItemDOToManageContOrderItemConverter.INSTANCE.converterDosToModels(itemDoList);

        //从表分组
        Map<String, List<ManageContOrderItem>> itemMap = orderItemList.stream()
                .collect(Collectors.groupingBy(ManageContOrderItem::getMc04ContOrderId));

        orderList.forEach(order -> {
            Assert.notEmpty(itemMap.get(order.getMc04ContOrderId()),
                    () -> new CustomException(order.getMc04ContOrderId() + "合同丢失从表信息"));
            order.setContOrderItemList(itemMap.get(order.getMc04ContOrderId()));
        });
        return orderList;
    }

    /**
     * 更新订单
     *
     * @param updateOrder 更新参数
     * @param oldOrder    旧订单
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 更新订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOrder(ManageContOrder updateOrder, ManageContOrder oldOrder) {
        Mc04IslmContOrderDO contOrderDo = Mc04IslmContOrderDOToManageContOrderConverter.INSTANCE
                .converterModelToDo(updateOrder);
        boolean flag = mc04IslmContOrderService.updateById(contOrderDo);

        Assert.isTrue(flag, () -> new CustomException("更新订单失败"));
        updateOrder.getContOrderItemList().forEach(item -> {
            Mc04IslmContOrderItemDO itemDo = Mc04IslmContOrderItemDOToManageContOrderItemConverter.INSTANCE
                    .converterModelToDo(item);
            boolean itemFlag = mc04IslmContOrderItemService.updateById(itemDo);
            Assert.isTrue(itemFlag, () -> new CustomException("更新订单从表失败"));
        });
        //记录日志
        LogUtil.saveUpdateLog(BusiActionLogBusiTypeEnum.CONT, updateOrder.getMc04ContOrderId(),
                JSON.toJSONString(oldOrder), JSON.toJSONString(updateOrder),
                Collections.singletonList("acTwoLevelCigCode"));
        return true;
    }

    /**
     * 删除订单
     *
     * @param contOrderId 订单ID
     * @param oldOrder    旧订单
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 删除订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteOrder(String contOrderId, ManageContOrder oldOrder) {
        boolean flag = mc04IslmContOrderService.removeById(contOrderId);
        Assert.isTrue(flag, () -> new CustomException("删除订单失败"));
        boolean remove = mc04IslmContOrderItemService.lambdaUpdate()
                .eq(Mc04IslmContOrderItemDO::getMc04ContOrderId, contOrderId)
                .remove();
        Assert.isTrue(remove, () -> new CustomException("删除订单从表失败"));
        //记录日志
        LogUtil.saveDeleteLog(BusiActionLogBusiTypeEnum.CONT, contOrderId, JSON.toJSONString(oldOrder));
        return true;
    }

    /**
     * 上传订单
     *
     * @param contIdList 订单主键列表
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 上传订单
     */
    @Override
    public List<UploadCont> getThirdUploadContList(List<String> contIdList) {
        return manageOrderManageMapper.getThirdUploadContList(contIdList, IcomUtils.getIcomCode());
    }

    /**
     * 更新订单状态
     *
     * @param uploadCont    订单信息
     * @param currentStatus 订单状态
     * @param nextStatus    下一个订单状态
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 更新订单状态
     */
    @Override
    public boolean updateOrderByUploadCont(UploadCont uploadCont, String currentStatus, String nextStatus) {
        Mc04IslmContOrderDO order = mc04IslmContOrderService.lambdaQuery()
                .eq(Mc04IslmContOrderDO::getMc04ContOrderCode, uploadCont.getItfpk())
                .one();
        Assert.notNull(order, () -> new CustomException("订单" + uploadCont.getItfpk() + "不存在"));

        Assert.isTrue(StrUtil.equals(currentStatus, order.getMc04CgtTradeContStatus()),
                () -> new CustomException("订单" + uploadCont.getItfpk() + "状态异常"));

        boolean flag = mc04IslmContOrderService.lambdaUpdate()
                .eq(Mc04IslmContOrderDO::getMc04ContOrderId, order.getMc04ContOrderId())
                .eq(Mc04IslmContOrderDO::getMc04CgtTradeContStatus, currentStatus)
                .set(Mc04IslmContOrderDO::getMc04CgtTradeContStatus, nextStatus)
                .set(Mc04IslmContOrderDO::getMd02CgtTradeContDelistartdate,
                        ISaleDateUtil.formatToInspurDate(uploadCont.getDeliStartDate()))
                .set(Mc04IslmContOrderDO::getMd02CgtTradeContDelienddate,
                        ISaleDateUtil.formatToInspurDate(uploadCont.getDeliEndDate()))
                .update();
        Assert.isTrue(flag, () -> new CustomException("更新订单" + uploadCont.getItfpk() + "状态失败"));
        Mc04IslmContOrderDO newOrder = mc04IslmContOrderService.lambdaQuery()
                .eq(Mc04IslmContOrderDO::getMc04ContOrderCode, order.getMc04ContOrderId())
                .one();
        //记录日志
        LogUtil.saveUpdateLog(BusiActionLogBusiTypeEnum.CONT, order.getMc04ContOrderId(),
                JSON.toJSONString(order), JSON.toJSONString(newOrder), new ArrayList<>());
        return flag;
    }

    /**
     * 批量更新订单状态
     *
     * @param needUpdateContOrderIdList 订单ID列表
     * @param currentStatus             订单状态
     * @param nextStatus                订单状态描述
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 批量更新订单状态
     */
    @Override
    public boolean batchUpdateOrderStatus(List<String> needUpdateContOrderIdList,
                                          String currentStatus, String nextStatus) {
        LambdaUpdateChainWrapper<Mc04IslmContOrderDO> updateWrapper = mc04IslmContOrderService.lambdaUpdate()
                .in(Mc04IslmContOrderDO::getMc04ContOrderId, needUpdateContOrderIdList)
                .eq(Mc04IslmContOrderDO::getMc04CgtTradeContStatus, currentStatus)
                .set(Mc04IslmContOrderDO::getMc04CgtTradeContStatus, nextStatus);
        String now = ISaleDateUtil.getNow();
        if (ContStatusEnum.CONT_STATUS_00.equals(currentStatus)
                && ContStatusEnum.CONT_STATUS_01.equals(nextStatus)) {
            updateWrapper.set(Mc04IslmContOrderDO::getMc04CgtTradeContSpreadNetTime, now);
        } else if (ContStatusEnum.CONT_STATUS_10.equals(currentStatus)
                && ContStatusEnum.CONT_STATUS_20.equals(nextStatus)) {
            updateWrapper.set(Mc04IslmContOrderDO::getMd04CgtOrderTime, now);
        } else if (ContStatusEnum.CONT_STATUS_20.equals(currentStatus)
                && ContStatusEnum.CONT_STATUS_10.equals(nextStatus)) {
            updateWrapper.set(Mc04IslmContOrderDO::getMd04CgtOrderTime, null);
        }
        updateWrapper.update();
        //记录日志
        needUpdateContOrderIdList.forEach(orderId -> {
            ManageContOrder order = new ManageContOrder();
            order.setMc04ContOrderId(orderId);
            order.setMc04CgtTradeContStatus(currentStatus);

            ManageContOrder newOrder = new ManageContOrder();
            newOrder.setMc04ContOrderId(orderId);
            newOrder.setMc04CgtTradeContStatus(nextStatus);
            LogUtil.saveUpdateLog(BusiActionLogBusiTypeEnum.CONT, orderId,
                    JSON.toJSONString(order), JSON.toJSONString(newOrder), new ArrayList<>());
        });
        return true;
    }

    /**
     * 更新中台侧数据
     *
     * @return boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-07 10:18:56
     * @description : 更新中台侧数据
     */
    @Override
    public boolean updateCenterOrder(ManageContOrder updateContOrder) {
        Mc04IslmcContOrderREQ contOrderReq = Mc04IslmcContOrderReqToManageContOrderConverter.INSTANCE
                .converterModelToDo(updateContOrder);
        Boolean success = SaleResponseUtil.getCenterDTO("更新合同数据",
                () -> contOrderApi.update(contOrderReq));
        if (!success) {
            log.error("更新合同数据失败");
            return false;
        }
        return true;
    }


    /**
     * 生成虚拟合同单号
     *
     * @param updateContOrder 订单信息
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 生成虚拟合同单号
     */
    @Override
    public boolean updateVirtualContNo(ManageContOrder updateContOrder) {
        mc04IslmContOrderService.lambdaUpdate()
                .eq(Mc04IslmContOrderDO::getMc04ContOrderId, updateContOrder.getMc04ContOrderId())
                .set(Mc04IslmContOrderDO::getMc04CgtTradeVirtualContNo, updateContOrder.getMc04CgtTradeVirtualContNo())
                .update();
        //记录日志
        ManageContOrder order = new ManageContOrder();
        order.setMc04ContOrderId(updateContOrder.getMc04ContOrderId());
        order.setMc04CgtTradeVirtualContNo(null);

        ManageContOrder newOrder = new ManageContOrder();
        newOrder.setMc04ContOrderId(updateContOrder.getMc04ContOrderId());
        newOrder.setMc04CgtTradeContStatus(updateContOrder.getMc04CgtTradeVirtualContNo());
        LogUtil.saveUpdateLog(BusiActionLogBusiTypeEnum.CONT, updateContOrder.getMc04ContOrderId(),
                JSON.toJSONString(order), JSON.toJSONString(newOrder), new ArrayList<>());
        return true;
    }

    /**
     * 批量更新订单状态
     *
     * @param needUpdateContOrderIdList 订单ID列表
     * @param hasContNo                 是否有合同号
     * @param hasSyncContNo             是否已经同步合同号
     * @param monthCode                 月份
     * @param currentStatus             订单状态
     * @param nextStatus                订单状态描述
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 批量更新订单状态
     */
    @Override
    public boolean batchUpdateBhykOrderStatus(List<String> needUpdateContOrderIdList,
                                              Boolean hasContNo, Boolean hasSyncContNo, String monthCode,
                                              String currentStatus, String nextStatus) {
        LambdaUpdateChainWrapper<Mc04IslmContOrderDO> updateWrapper = mc04IslmContOrderService.lambdaUpdate()
                .in(Mc04IslmContOrderDO::getMc04ContOrderId, needUpdateContOrderIdList)
                .eq(ObjectUtil.isNotNull(hasContNo) && hasContNo
                                || ObjectUtil.isNotNull(hasSyncContNo) && hasSyncContNo,
                        Mc04IslmContOrderDO::getMc04CgtTradeContStatus, currentStatus)
                .eq(Mc04IslmContOrderDO::getMc04CgtTradeContZeroStatus, currentStatus)
                .set(ObjectUtil.isNotNull(hasContNo) && hasContNo
                                || ObjectUtil.isNotNull(hasSyncContNo) && hasSyncContNo,
                        Mc04IslmContOrderDO::getMc04CgtTradeContStatus, nextStatus)
                .set(ObjectUtil.isNotNull(hasContNo) && hasContNo,
                        Mc04IslmContOrderDO::getMc04CgtTradeContVirtualStatus, CommonConstants.YES)
                .set(ObjectUtil.isNotNull(hasSyncContNo) && hasSyncContNo,
                        Mc04IslmContOrderDO::getMc04CgtTradeContVirtualStatus, CommonConstants.NO)
                .set(Mc04IslmContOrderDO::getMc04CgtTradeContZeroStatus, nextStatus);
        String now = monthCode.substring(0, 4) + "0101";
        if (ContStatusEnum.CONT_STATUS_10.equals(currentStatus)
                && ContStatusEnum.CONT_STATUS_20.equals(nextStatus)) {
            updateWrapper.set(Mc04IslmContOrderDO::getMd04CgtOrderTime, now);
        } else if (ContStatusEnum.CONT_STATUS_20.equals(currentStatus)
                && ContStatusEnum.CONT_STATUS_10.equals(nextStatus)) {
            updateWrapper.set(Mc04IslmContOrderDO::getMd04CgtOrderTime, null);
        }
        updateWrapper.update();
        //记录日志
        needUpdateContOrderIdList.forEach(orderId -> {
            ManageContOrder order = new ManageContOrder();
            order.setMc04ContOrderId(orderId);
            order.setMc04CgtTradeContStatus(currentStatus);

            ManageContOrder newOrder = new ManageContOrder();
            newOrder.setMc04ContOrderId(orderId);
            newOrder.setMc04CgtTradeContStatus(nextStatus);
            LogUtil.saveUpdateLog(BusiActionLogBusiTypeEnum.CONT, orderId,
                    JSON.toJSONString(order), JSON.toJSONString(newOrder), new ArrayList<>());
        });
        return true;
    }

        /**
         * 推送物流
         *
         * @param logisticsParam 推送物流参数
         * @return Map<Object>
         * <AUTHOR> wangluhao01
         * @create_time : 2025-08-19 19:50:10
         * @description : 推送物流
         */
        @Override
        public Map<String, Object> sendLogisticsContOrder(Map<String, Object> logisticsParam) {
            //请求物流传输数据
            Map<String, Object> data = new HashMap<>(2);
            data.put("serviceId", "HUB_T_CLMIS_PRODUCTORDER");
            data.put("data", logisticsParam);
            return threePartySysApi.push(data);
        }

    /**
     * 推送正式合同号到物流
     *
     * @param logisticsParam 参数
     * @return Map<Object>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-19 19:50:10
     * @description : 推送正式合同号到物流
     */
    @Override
    public Map<String, Object> sendContNoToLogistics(Map<String, Object> logisticsParam) {
        //请求物流传输数据
        Map<String, Object> data = new HashMap<>(2);
        data.put("serviceId", "HUB_SYNC_YX_BSTKD");
        data.put("data", logisticsParam);
        return threePartySysApi.push(data);
    }

    /**
     * 批量更新订单状态为零点合同状态
     *
     * @param contOrderIdList 订单ID
     * @return boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-19 19:50:10
     * @description : 批量更新订单状态为零点合同状态
     */
    @Override
    public boolean batchUpdateOrderStatusFromZeroStatus(List<String> contOrderIdList) {
        mc04IslmContOrderService.lambdaUpdate()
                .in(Mc04IslmContOrderDO::getMc04ContOrderId, contOrderIdList)
                .setSql("mc04_cgt_trade_cont_status = mc04_cgt_trade_cont_zero_status")
                .update();
        return true;
    }

    /**
     * 批量更新中台侧数据
     *
     * @param successContOrderIdList 订单ID
     * @param busiType               业务类型
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-19 19:50:10
     * @description : 批量更新中台侧数据
     */
    @Override
    public void batchUpdateCenterOrder(List<String> successContOrderIdList, InterfaceLogBusiTypeEnum busiType) {
        List<String> successContOrderIdListCopy = new ArrayList<>(successContOrderIdList);
        try {
            //先从中台查询数据
            ManageContOrderGetCenterContParam queryParam = new ManageContOrderGetCenterContParam();
            queryParam.setMc04ContOrderIdList(successContOrderIdList);
            List<ManageContOrder> orderList = this.getCenterContList(queryParam);
            Map<String, ManageContOrder> orderMap = orderList.stream()
                    .collect(Collectors.toMap(ManageContOrder::getMc04ContOrderId, Function.identity()));

            ManageContOrderGetMangeContParam manageQueryParam = new ManageContOrderGetMangeContParam();
            manageQueryParam.setMc04ContOrderIdList(successContOrderIdList);
            List<ManageContOrder> updateOrderList = this.getManageContList(manageQueryParam);
            Map<String, ManageContOrder> updateOrderMap = updateOrderList.stream()
                    .collect(Collectors.toMap(ManageContOrder::getMc04ContOrderId, Function.identity()));

            successContOrderIdList.forEach(contOrderId -> {
                //中台侧没有数据直接记录异常信息
                if (orderMap.containsKey(contOrderId)) {
                    InterfaceLogUtil.addErrorLog(InterfaceLogChannelEnum.ISALE_CENTER,
                            busiType, contOrderId, "", true);
                    return;
                }
                ManageContOrder contOrder = updateOrderMap.get(contOrderId);
                boolean success = this.updateCenterOrder(contOrder);
                if (!success) {
                    log.error("合同更新至中台侧失败");
                    InterfaceLogUtil.addErrorLog(InterfaceLogChannelEnum.ISALE_CENTER,
                            busiType, contOrderId, "", true);
                }
                successContOrderIdListCopy.remove(contOrderId);
                InterfaceLogUtil.addSuccessLog(InterfaceLogChannelEnum.ISALE_CENTER,
                        busiType, contOrderId, "");
            });
        } catch (Exception e) {
            log.error("更新中台数据失败");
            InterfaceLogUtil.addErrorLogList(InterfaceLogChannelEnum.ISALE_CENTER.getChannel(),
                    busiType.getType(), successContOrderIdListCopy, "", true);
        }
    }

    /**
     * 重试更新中台侧数据
     *
     * @param contOrderId 订单ID
     * @param busiType    业务类型
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-19 19:50:10
     * @description : 重试更新中台侧数据
     */
    @Override
    public Boolean retryUpdateCenterOrder(String contOrderId, InterfaceLogBusiTypeEnum busiType) {
        //先从中台查询数据
        ManageContOrderGetCenterContParam queryParam = new ManageContOrderGetCenterContParam();
        queryParam.setMc04ContOrderIdList(Collections.singletonList(contOrderId));
        List<ManageContOrder> orderList = this.getCenterContList(queryParam);
        //中台没有直接失败
        if (CollUtil.isEmpty(orderList)) {
            return false;
        }

        ManageContOrderGetMangeContParam manageQueryParam = new ManageContOrderGetMangeContParam();
        manageQueryParam.setMc04ContOrderIdList(Collections.singletonList(contOrderId));
        List<ManageContOrder> updateOrderList = this.getManageContList(manageQueryParam);
        ManageContOrder contOrder = updateOrderList.get(0);

        return this.updateCenterOrder(contOrder);
    }

    /**
     * 批量更新协议号到订单表
     *
     * @param contOrderIdList 订单ID
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-19 19:50:10
     * @description : 批量更新协议号到订单表
     */
    @Override
    public void updateAgreeNo(List<String> contOrderIdList) {
        manageOrderManageMapper.updateAgreeNo(contOrderIdList);
    }

    @Override
    public String getOrderDOId(ReturnBillDomain billDomain, String tradeTransType, String settlementModeCode, List<ReturnBillItemDomain> returnBillItemDOList) {
        String returnBillId = billDomain.getMc04CgtReturnBillId();

        Mc04IslmContOrderDO mc04IslmContOrderDO = mc04IslmContOrderService.lambdaQuery().eq(Mc04IslmContOrderDO::getMc04CgtDistOrderCode,returnBillId).one();
        if(mc04IslmContOrderDO!= null){
            return mc04IslmContOrderDO.getMc04ContOrderId();
        }
        Mc04IslmContOrderDO saveOrderDo = new Mc04IslmContOrderDO();
        saveOrderDo.setMc04CgtDistOrderCode(returnBillId);
        //todo查看原来怎么处理的
        saveOrderDo.setMc04ContOrderCode(String.format("%07d", new Random().nextInt(10000000)));
        String id = IcomUtils.getNextId(DistConstants.DIST_PARM_APPLY_KEY);
        saveOrderDo.setMc04ContOrderId(id);
        // 获取当前年月 YYYYMM 格式
        LocalDate now = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String currentYearMonth = now.format(formatter);
        //存入当前年+上半年 还是下半年
        saveOrderDo.setMc04CgtXyPeriodCode(ISaleDateUtil.getHalfYearFromMonthCode(currentYearMonth.substring(0,6)));
        //当前月份
        saveOrderDo.setMa02PlanMonth(currentYearMonth.substring(4,6));
        //调拨类型 10 常规类型
        saveOrderDo.setMc04MonthSalePlanType("10");
        //运输方式代码  2 联运
        saveOrderDo.setMd02TradeTransTypeCode("2");
        //运输方式 20
        saveOrderDo.setMc05LogtTransportTransType("20");
        //结算方式 1
        saveOrderDo.setZbSettlementModeCode("1");

        saveOrderDo.setMa02TobaProdTradeTypeCode(PROD_TRADE_TYPE_10.getStatus());
        saveOrderDo.setBaComOrgCode(billDomain.getBaComOrgCode());
        saveOrderDo.setMd02CgtOutStorehouseCode(billDomain.getMd02CgtOutStorehouseCode());
        saveOrderDo.setMd02CgtInStorehouseCode(billDomain.getMd02CgtInStorehouseCode());
        //实际发货仓库
        saveOrderDo.setMc04CgtPraOutStorehouseCode(billDomain.getMd02CgtOutStorehouseCode());
        //收货
        saveOrderDo.setMc04CgtPraInStorehouseCode(billDomain.getMd02CgtInStorehouseCode());

        //发货时间 当天
        saveOrderDo.setMc04ContExpecOutDate(currentYearMonth);
        //到货时间
        saveOrderDo.setMc04ContExpecArrivalDate(currentYearMonth);

        //todo 下单时间 暂时还没写 调完接口
        //saveOrderDo.setMd04CgtOrderTime();
        //默认 0 非备货
        saveOrderDo.setMc04ContZeroClockType("0");
        //开始时间 当前时间
        saveOrderDo.setMd02CgtTradeContDelistartdate(currentYearMonth);
        // 获取一个月后的日期
        LocalDate nowLater = LocalDate.now();
        LocalDate oneMonthLater = nowLater.plusMonths(1);
        DateTimeFormatter formatterLater = DateTimeFormatter.ofPattern("yyyyMMdd");
        String oneMonthLaterStr = oneMonthLater.format(formatterLater);
        //收货时间 往后延期一个月
        saveOrderDo.setMd02CgtTradeContDelienddate(oneMonthLaterStr);

        //计算总数量
        BigDecimal sum = returnBillItemDOList.stream()
                .map(ReturnBillItemDomain::getMc04CgtReturnBillSaleCenterAuditQty)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //总量
        saveOrderDo.setMd02CgtTradeContQty(sum);
        //金额
        saveOrderDo.setMd04CgtOrderSumWithtaxAmt(null);

        saveOrderDo.setMd02TradeTransTypeCode(tradeTransType);
        saveOrderDo.setZbSettlementModeCode(settlementModeCode);
        /**
         * todo 新增状态待修改
         * b)合同零点类型默认0非备货，物流运输方式2联运，卷烟交易合同类型40:退货合同，退货合同的协议、月计划、周计划相关属性可空。
         */
        saveOrderDo.setMc04CgtTradeContStatus(CONT_STATUS_00.getStatus());
        mc04IslmContOrderService.save(saveOrderDo);
        return id;
    }

    @Override
    public Boolean updateContOrder(String contOrderId, String returnBillId) {
        List<Mc04IslmContOrderItemDO> contOrderItemDos = mc04IslmContOrderItemService.lambdaQuery().eq(Mc04IslmContOrderItemDO::getMc04ContOrderId, contOrderId).list();
        BigDecimal tradeContAmtSum = contOrderItemDos.stream()
                .map(Mc04IslmContOrderItemDO::getMd02CgtTradeContAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return mc04IslmContOrderService.lambdaUpdate()
                .eq(Mc04IslmContOrderDO::getMc04ContOrderId, contOrderId)
                .set(Mc04IslmContOrderDO::getMc04CgtDayPlanCode, returnBillId)
                .set(Mc04IslmContOrderDO::getMd04CgtOrderSumWithtaxAmt, tradeContAmtSum)
                .update();
    }

    @Override
    public Boolean batchSaveContOrderItem(List<ContOrderItemDomain> contOrderItemDOList) {
        List<Mc04IslmContOrderItemDO> orderItemDos = ContOrderItemDomainToDoConverter.INSTANCE.converterModelsToDos(contOrderItemDOList);
        return mc04IslmContOrderItemService.saveBatch(orderItemDos);
    }

    @Override
    public List<ContOrderDomain> getListByStatus(ContManageDomain contManageDomain, String status, String type) {
        LambdaQueryChainWrapper<Mc04IslmContOrderDO> wrapper = mc04IslmContOrderService
                .lambdaQuery().eq(Mc04IslmContOrderDO::getMc04CgtTradeContStatus, status)
                .eq(Mc04IslmContOrderDO::getMa02TobaProdTradeTypeCode, type);
        if (contManageDomain.getMc04CgtReturnBillDateStartTime() != null && contManageDomain.getMc04CgtReturnBillDateEndTime() != null) {
            wrapper.ge(Mc04IslmContOrderDO::getMd04CgtOrderTime, contManageDomain.getMc04CgtReturnBillDateStartTime())
                    .le(Mc04IslmContOrderDO::getMd04CgtOrderTime, contManageDomain.getMc04CgtReturnBillDateEndTime());
        }
        if (contManageDomain.getBaComOrgCode() != null) {
            wrapper.eq(Mc04IslmContOrderDO::getBaComOrgCode, contManageDomain.getBaComOrgCode());
        }
        if (contManageDomain.getMc04CgtTradeContStatus() != null) {
            wrapper.eq(Mc04IslmContOrderDO::getMc04CgtTradeContStatus, contManageDomain.getMc04CgtTradeContStatus());
        }
        if (contManageDomain.getMc04CgtCpnContNo() != null) {
            wrapper.like(Mc04IslmContOrderDO::getMc04CgtCpnContNo, contManageDomain.getMc04CgtCpnContNo());
        }
        return ContOrderDomainToDoConverter.INSTANCE.converterDosToModels(wrapper.list());
    }

    @Override
    public List<ContOrderItemDomain> getContOrderItemList(List<String> contOrderIdList) {
        List<Mc04IslmContOrderItemDO> contOrderItemDos = mc04IslmContOrderItemService.lambdaQuery().in(Mc04IslmContOrderItemDO::getMc04ContOrderId, contOrderIdList).list();
        return ContOrderItemDomainToDoConverter.INSTANCE.converterDosToModels(contOrderItemDos);
    }


}