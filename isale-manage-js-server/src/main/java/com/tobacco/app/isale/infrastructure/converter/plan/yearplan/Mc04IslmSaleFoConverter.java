/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.yearplan;


import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSaleFo;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSaleFoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 品牌发展规划 数据转换器
 * <p>
 * Mc04IslmSaleFoConverter
 *
 * @Author: longxi
 * @Since: 2025-08-11
 */
@Mapper
public interface Mc04IslmSaleFoConverter extends StructureBaseConverter<Mc04IslmSaleFoDO, Mc04IslmSaleFo> {

    Mc04IslmSaleFoConverter INSTANCE = Mappers.getMapper(Mc04IslmSaleFoConverter.class);

}
