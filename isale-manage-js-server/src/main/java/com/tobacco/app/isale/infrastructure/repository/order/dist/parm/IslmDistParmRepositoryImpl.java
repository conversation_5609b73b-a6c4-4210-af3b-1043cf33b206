/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.order.dist.parm;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.constant.CommonConstants;
import com.tobacco.app.isale.domain.model.common.ComItem;
import com.tobacco.app.isale.domain.model.order.dist.parm.*;
import com.tobacco.app.isale.domain.repository.nation.subsys.ISaleNationSubsysRepository;
import com.tobacco.app.isale.domain.repository.order.dist.parm.IslmDistParmRepository;
import com.tobacco.app.isale.infrastructure.converter.dist.parm.*;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcDistParmApplyDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcDistParmApplyItemDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcDistParmApplySeasonDO;
import com.tobacco.app.isale.infrastructure.repository.common.ComItemRepositoryImpl;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcDistParmApplyItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcDistParmApplySeasonService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcDistParmApplyService;
import com.tobacco.app.isale.infrastructure.tunnel.database.order.dist.parm.IslmDistParmApplyMapper;
import com.tobacco.app.isale.tools.utils.DistUtils;
import com.tobacco.app.isalecenter.client.api.order.distParm.DistParmAPI;
import com.tobacco.app.isalecenter.client.dto.order.distParm.Mc04IslmcDistParmDTO;
import com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmREQ;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liuwancheng
 * @create_time : 2025/06/03 15:18
 * @description : 配货参数申请持久层服务实现
 */
@Slf4j
@Component("ISaleIslmmDistParmRepository")
public class IslmDistParmRepositoryImpl implements IslmDistParmRepository {
    @Resource
    private DistParmAPI distParmApi;
    @Resource
    private Mc04IslmcDistParmApplyService mc04IslmcDistParmApplyService;
    @Resource
    private Mc04IslmcDistParmApplyItemService mc04IslmcDistParmApplyItemService;
    @Resource
    private Mc04IslmcDistParmApplySeasonService mc04IslmcDistParmApplySeasonService;

    @Resource
    private ComItemRepositoryImpl comItemRepositoryImpl;

    @Resource
    private IslmDistParmApplyMapper islmDistParmApplyMapper;

    @Resource
    private ISaleNationSubsysRepository iSaleNationSubsysRepository;

    private <T, F> Page<F> getPage(Page<T> pageModel) {
        Page<F> page = new Page<>();
        page.setCurrent(pageModel.getCurrent());
        page.setSize(pageModel.getSize());
        if (CollectionUtil.isNotEmpty(pageModel.getOrders())) {
            List<OrderItem> orderItemList = pageModel.getOrders().stream().map(item -> {
                OrderItem orderItem = new OrderItem();
                orderItem.setColumn(item.getColumn());
                orderItem.setAsc(item.isAsc());
                return orderItem;
            }).collect(Collectors.toList());
            page.setOrders(orderItemList);
        }
        return page;
    }

    /**
     * 根据供方会员代码、需方会员代码以及网配订单发货地区代码、配货收货地区编码初始化配货参数的详细信息
     *
     * @param ma02CgtTradeReqMembCode   卷烟需方交易会员代码
     * @param ma02CgtTradeSuppMembCode  卷烟供方交易会员代码
     * @param md02CgtDistRegionCode     网配订单发货地区代码
     * @param md02DistReceiveregionCode 配货收货地区编码
     * @return 配货参数明细数据
     */
    private DistParm initDistParm(String ma02CgtTradeReqMembCode, String ma02CgtTradeSuppMembCode, String md02CgtDistRegionCode, String md02DistReceiveregionCode) {
        DistParm distParm = new DistParm();
        distParm.setMa02CgtTradeReqMembCode(ma02CgtTradeReqMembCode);
        distParm.setMa02CgtTradeSuppMembCode(ma02CgtTradeSuppMembCode);
        distParm.setMd02CgtDistRegionCode(md02CgtDistRegionCode);
        distParm.setMd02DistReceiveregionCode(md02DistReceiveregionCode);

        List<DistParmItem> items = new ArrayList<>();
        distParm.setItems(items);
        return distParm;
    }

    /**
     * 分页查询配货参数申请列表
     *
     * @param req 查询请求参数
     * @return 分页数据响应对象
     */
    @Override
    public Page<DistParmApply> queryDistParmApplyPage(DistParmApplyPage req) {
        Page<DistParmApply> page = getPage(req);
        return islmDistParmApplyMapper.queryDistParmApplyPage(page, req);
    }

    /**
     * 查询配货参数申请的详细信息
     *
     * @param req 配货参数申请查询
     * @return 配货参数申请明细数据
     */
    @Override
    public DistParmApply queryDistParmApplyDetail(DistParmApply req) {
        String mc04CgtDistParmApplyId = req.getMc04CgtDistParmApplyId();
        DistParmApply distParmApply;
        if (StrUtil.isNotBlank(mc04CgtDistParmApplyId)) {
            // 按配货参数申请编号查询明细
            distParmApply = getDistParmApplyById(mc04CgtDistParmApplyId);
        } else {
            // 按供方会员代码、需方会员代码以及网配订单发货地区代码、配货收货地区编码查询明细
            distParmApply = getDistParmApply(req.getMa02CgtTradeReqMembCode(), req.getMa02CgtTradeSuppMembCode(), req.getMd02CgtDistRegionCode(), req.getMd02DistReceiveregionCode());
        }
        return distParmApply;
    }


    /**
     * 查询本工业所有协议单位的配货参数申请的状态
     *
     * @param req 查询请求参数
     * @return 配货参数申请状态数据
     */
    @Override
    public List<DistParmApplyStatus> queryDistParmApplyStatus(DistParmApply req) {
        Assert.notNull(req.getMa02CgtTradeSuppMembCode(), () -> new CustomException("卷烟供方交易会员代码不能为空"));
        return islmDistParmApplyMapper.queryDistParmApplyStatus(req);
    }

    /**
     * 保存配货参数申请
     *
     * @param distParmApply 配货参数申请
     * @return 操作结果布尔值响应
     */
    @Override
    public DistParmApply saveDistParmApply(DistParmApply distParmApply) {
        Assert.notNull(distParmApply.getMc04CgtDistParmApplyCode(), () -> new CustomException("配货参数申请编码不能为空"));
        List<DistParmApplyItem> items = distParmApply.getItems();
        List<DistParmApplySeason> seasons = distParmApply.getSeasons();
        Assert.notEmpty(items, () -> new CustomException("配货参数卷烟列表不能为空"));

        // 配货参数申请保存
        boolean success = mc04IslmcDistParmApplyService.saveOrUpdate(Mc04IslmcDistParmApplyDOToDistParmApplyConverter.INSTANCE.converterModelToDo(distParmApply));
        // 配货参数申请卷烟批量保存   saveOrUpdateBatch太慢采用先删后批量插入
        if (success) {
            for (DistParmApplyItem item : items) {
                item.setMc04CgtDistParmApplyId(distParmApply.getMc04CgtDistParmApplyId());
            }
            mc04IslmcDistParmApplyItemService.remove(new LambdaQueryWrapper<Mc04IslmcDistParmApplyItemDO>().eq(Mc04IslmcDistParmApplyItemDO::getMc04CgtDistParmApplyId, distParmApply.getMc04CgtDistParmApplyId()));
            // 因mybatis  plus 批量插入单条处理 实际很快、可以使用
            success = mc04IslmcDistParmApplyItemService.saveBatch(Mc04IslmcDistParmApplyItemDOToDistParmApplyItemConverter.INSTANCE.converterModelsToDos(items));
        }

        // 配货参数申请季节因子 若有的话批量保存
        if (success) {
            mc04IslmcDistParmApplySeasonService.remove(new LambdaQueryWrapper<Mc04IslmcDistParmApplySeasonDO>().eq(Mc04IslmcDistParmApplySeasonDO::getMc04CgtDistParmApplyId, distParmApply.getMc04CgtDistParmApplyId()));
            if (ObjectUtil.isNotEmpty(seasons)) {
                for (DistParmApplySeason item : seasons) {
                    item.setMc04CgtDistParmApplyId(distParmApply.getMc04CgtDistParmApplyId());
                }
                // 因mybatis  plus 批量插入单条处理 实际很快、可以使用
                mc04IslmcDistParmApplySeasonService.saveBatch(Mc04IslmcDistParmApplySeasonDOToDistParmApplySeasonConverter.INSTANCE.converterModelsToDos(seasons));
            }
        }
        // 判断是否禁用发给行业子系统，默认否 ，否的话发送给行业子系统
        if (isSendToIndustry(distParmApply.getMc04CgtDistParmApplyStatus())) {
            boolean ok = iSaleNationSubsysRepository.pushDistparmToNation(distParmApply.getMc04CgtDistParmApplyCode());
            // 推送失败状态改回当前状态
            if (!ok) {
                distParmApply.setMc04CgtDistParmApplyStatus(distParmApply.getCurStatus());
            }
        }
        return distParmApply;
    }

    /**
     * 查询本工业所有协议单位的配货参数申请列表
     *
     * @param distParmApply 查询请求参数
     * @return 配货参数申请数据
     */
    @Override
    public List<DistParmApply> queryDistParmApplyList(DistParmApply distParmApply) {
        List<String> mc04CgtDistParmApplyIds = distParmApply.getMc04CgtDistParmApplyIds();
        List<String> ma02CgtTradeReqMembCodes = distParmApply.getMa02CgtTradeReqMembCodes();
        List<Mc04IslmcDistParmApplyDO> applys = mc04IslmcDistParmApplyService.lambdaQuery().eq(Mc04IslmcDistParmApplyDO::getMa02CgtTradeSuppMembCode, distParmApply.getMa02CgtTradeSuppMembCode()).eq(StrUtil.isNotBlank(distParmApply.getMc04CgtDistParmApplyStatus()), Mc04IslmcDistParmApplyDO::getMc04CgtDistParmApplyStatus, distParmApply.getMc04CgtDistParmApplyStatus()).in(CollectionUtil.isNotEmpty(mc04CgtDistParmApplyIds), Mc04IslmcDistParmApplyDO::getMc04CgtDistParmApplyId, mc04CgtDistParmApplyIds).in(CollectionUtil.isNotEmpty(ma02CgtTradeReqMembCodes), Mc04IslmcDistParmApplyDO::getMa02CgtTradeReqMembCode, ma02CgtTradeReqMembCodes).list();
        return Mc04IslmcDistParmApplyDOToDistParmApplyConverter.INSTANCE.converterDosToModels(applys);
    }

    /**
     * 批量更新配货参数申请状态
     *
     * @param distParmApplyStatus 配货参数申请状态信息
     * @return 处理后的配货参数申请状态列表
     */
    @Override
    public DistParmApplyStatus saveBatchDistParmApplyStatus(DistParmApplyStatus distParmApplyStatus) {
        Assert.notNull(distParmApplyStatus, () -> new CustomException("配货参数申请状态信息不能为空"));
        List<String> mc04CgtDistParmApplyIds = distParmApplyStatus.getMc04CgtDistParmApplyIds();
        Assert.notEmpty(mc04CgtDistParmApplyIds, () -> new CustomException("配货参数申请编码列表不能为空"));
        String mc04CgtDistParmApplyStatus = distParmApplyStatus.getMc04CgtDistParmApplyStatus();
        Assert.notBlank(mc04CgtDistParmApplyStatus, () -> new CustomException("配货参数申请状态不能为空"));
        // 查询配货参数申请单
        DistParmApply distParmApply = new DistParmApply();
        distParmApply.setMc04CgtDistParmApplyIds(mc04CgtDistParmApplyIds);
        distParmApply.setMa02CgtTradeSuppMembCode(distParmApplyStatus.getMa02CgtTradeSuppMembCode());
        List<DistParmApply> applys = queryDistParmApplyList(distParmApply);
        // 过滤当前状态的配货参数申请单
        applys = applys.stream().filter(apply -> apply.getMc04CgtDistParmApplyStatus().equals(distParmApplyStatus.getCurStatus())).collect(Collectors.toList());
        Assert.notEmpty(applys, () -> new CustomException("配货参数申请单不能为空"));
        // 成功和失败的配货参数申请编码列表
        List<String> successMc04CgtDistParmApplyIds = new ArrayList<>();
        List<String> successMc04CgtDistParmApplyCodes = new ArrayList<>();
        List<String> failMc04CgtDistParmApplyCodes = new ArrayList<>();
        // 判断是否禁用发给行业子系统，默认否 ，否的话发送给行业子系统
        if (isSendToIndustry(mc04CgtDistParmApplyStatus)) {
            for (DistParmApply apply : applys) {
                boolean ok = iSaleNationSubsysRepository.pushDistparmToNation(apply.getMc04CgtDistParmApplyCode());
                if (ok) {
                    successMc04CgtDistParmApplyIds.add(apply.getMc04CgtDistParmApplyId());
                    successMc04CgtDistParmApplyCodes.add(apply.getMc04CgtDistParmApplyCode());
                } else {
                    failMc04CgtDistParmApplyCodes.add(apply.getMc04CgtDistParmApplyCode());
                }
            }
        } else {
            successMc04CgtDistParmApplyIds = applys.stream().map(DistParmApply::getMc04CgtDistParmApplyId).collect(Collectors.toList());
        }
        Assert.notEmpty(successMc04CgtDistParmApplyIds, () -> new CustomException("配货参数申请批量审核失败"));
        // 批量更新调用行业成功地配货参数申请的状态
        mc04IslmcDistParmApplyService.update(new LambdaUpdateWrapper<Mc04IslmcDistParmApplyDO>().eq(Mc04IslmcDistParmApplyDO::getMa02CgtTradeSuppMembCode, distParmApplyStatus.getMa02CgtTradeSuppMembCode()).in(CollectionUtil.isNotEmpty(successMc04CgtDistParmApplyIds), Mc04IslmcDistParmApplyDO::getMc04CgtDistParmApplyId, successMc04CgtDistParmApplyIds).set(Mc04IslmcDistParmApplyDO::getMc04CgtDistParmApplyStatus, distParmApplyStatus.getMc04CgtDistParmApplyStatus()));
        // 返回成功和失败的配货参数申请状态列表
        distParmApplyStatus.setSuccessMc04CgtDistParmApplyCodes(successMc04CgtDistParmApplyCodes);
        distParmApplyStatus.setFailMc04CgtDistParmApplyCodes(failMc04CgtDistParmApplyCodes);
        return distParmApplyStatus;
    }

    /**
     * 查询配货参数的详细信息
     *
     * @param req 查询请求参数
     * @return 配货参数明细数据
     */
    @Override
    public DistParm queryDistParmDetail(DistParm req) {
        Mc04IslmcDistParmREQ mc04IslmcDistParmReq = new Mc04IslmcDistParmREQ();
        mc04IslmcDistParmReq.setMa02CgtTradeReqMembCode(req.getMa02CgtTradeReqMembCode());
        mc04IslmcDistParmReq.setMd02CgtDistRegionCode(req.getMd02CgtDistRegionCode());
        mc04IslmcDistParmReq.setMa02CgtTradeSuppMembCode(req.getMa02CgtTradeSuppMembCode());
        mc04IslmcDistParmReq.setMd02DistReceiveregionCode(req.getMd02DistReceiveregionCode());
        List<Mc04IslmcDistParmDTO> mc04IslmcDistParmDtos = distParmApi.list(mc04IslmcDistParmReq).getData();
        DistParm distParm;
        if (CollectionUtil.isNotEmpty(mc04IslmcDistParmDtos)) {
            Mc04IslmcDistParmDTO mc04IslmcDistParmDto = mc04IslmcDistParmDtos.get(0);
            distParm = DistParmToMc04IslmcDistParmDTOConverter.INSTANCE.converterDoToModel(mc04IslmcDistParmDto);
            distParm.setItems(DistParmItemToMc04IslmcDistParmItemDTOConverter.INSTANCE.converterDosToModels(mc04IslmcDistParmDto.getMc04IslmcDistParmItemList()));
            distParm.setSeasons(DistParmSeasonToMc04IslmcDistParmSeasonDTOConverter.INSTANCE.converterDosToModels(mc04IslmcDistParmDto.getMc04IslmcDistParmSeasonList()));
        } else {
            distParm = initDistParm(req.getMa02CgtTradeReqMembCode(), req.getMa02CgtTradeSuppMembCode(), req.getMd02CgtDistRegionCode(), req.getMd02DistReceiveregionCode());
        }
        List<DistParmItem> items = distParm.getItems();
        // 如果没有明细数据，则查询商业公司的卷烟列表
        if (CollectionUtil.isEmpty(items)) {
            items = new ArrayList<>();
            // 查询商业公司的卷烟列表
            List<ComItem> comItems = comItemRepositoryImpl.getComItemList("0", distParm.getMa02CgtTradeReqMembCode());
            Assert.notEmpty(comItems, () -> new CustomException("商业公司卷烟列表不能为空"));
            // 卷烟名称为空的话，查询商品中心取名称
            Map<String, String> productMap = DistUtils.getProductMap(DistUtils.getProducts(distParm.getMa02CgtTradeSuppMembCode()));

            for (ComItem comItem : comItems) {
                DistParmItem item = new DistParmItem();
                item.setAcCgtCartonCode(comItem.getAcCgtCartonCode());
                String acCgtName = productMap.get(item.getAcCgtCartonCode());
                // 可能取不到
                if (StrUtil.isNotBlank(acCgtName)) {
                    item.setAcCgtName(acCgtName);
                }
                items.add(item);
            }
            distParm.setItems(items);
        }
        return distParm;
    }

    /**
     * 查询配货参数的状态列表
     *
     * @param req 查询请求参数
     * @return 配货参数状态列表
     */
    @Override
    public List<DistParmStatus> queryDistParmStatus(DistParm req) {
        Mc04IslmcDistParmREQ mc04IslmcDistParmReq = new Mc04IslmcDistParmREQ();
        //mc04IslmcDistParmReq.setIsOnlyQueryMainTable(CommonConstants.YES);
        List<Mc04IslmcDistParmDTO> mc04IslmcDistParmDtos = distParmApi.list(mc04IslmcDistParmReq).getData();
        // 配货参数状态
        List<DistParmStatus> distParmStatuses = mc04IslmcDistParmDtos.stream().map(mc04IslmcDistParmDto -> {
            DistParmStatus distParmStatus = new DistParmStatus();
            distParmStatus.setMa02CgtTradeReqMembCode(mc04IslmcDistParmDto.getMa02CgtTradeReqMembCode());
            distParmStatus.setMc04CgtDistParmEffectStatus(mc04IslmcDistParmDto.getMc04CgtDistParmEffectStatus());
            return distParmStatus;
        }).collect(Collectors.toList());
        // 行业状态
        List<DistParmStatus> nationParmStatuses = islmDistParmApplyMapper.queryNationParmStatus(req);
        for (DistParmStatus distParmStatus : distParmStatuses) {
            for (DistParmStatus nationParmStatus : nationParmStatuses) {
                // 优先使用行业状态
                if (distParmStatus.getMa02CgtTradeReqMembCode().equals(nationParmStatus.getMa02CgtTradeReqMembCode())) {
                    distParmStatus.setMc04CgtDistParmEffectStatus(nationParmStatus.getMc04CgtDistParmEffectStatus());
                }
            }
        }
        return distParmStatuses;
    }

    /**
     * 配货参数申请状态-商业待确认
     */
    private static final String CGT_DIST_PARM_APPLY_STATUS_APPROVE = "50";

    /**
     * 是否发送给行业子系统
     *
     * @param mc04CgtDistParmApplyStatus 配货参数申请状态
     * @return 是否发送给行业子系统
     */
    private boolean isSendToIndustry(String mc04CgtDistParmApplyStatus) {
        return StrUtil.equals(mc04CgtDistParmApplyStatus, CGT_DIST_PARM_APPLY_STATUS_APPROVE);
    }

    /**
     * 根据供方会员代码、需方会员代码以及网配订单发货地区代码、配货收货地区编码查询配货参数申请的详细信息
     *
     * @param ma02CgtTradeReqMembCode   卷烟需方交易会员代码
     * @param ma02CgtTradeSuppMembCode  卷烟供方交易会员代码
     * @param md02CgtDistRegionCode     网配订单发货地区代码
     * @param md02DistReceiveregionCode 配货收货地区编码
     * @return 配货参数申请明细数据
     */
    private DistParmApply getDistParmApply(String ma02CgtTradeReqMembCode, String ma02CgtTradeSuppMembCode, String md02CgtDistRegionCode, String md02DistReceiveregionCode) {
        // 按供方会员代码、需方会员代码以及网配订单发货地区代码查询明细
        Assert.notNull(ma02CgtTradeReqMembCode, () -> new CustomException("卷烟需方交易会员代码不能为空"));
        Assert.notNull(ma02CgtTradeSuppMembCode, () -> new CustomException("卷烟供方交易会员代码不能为空"));
        // 按理同一个供方会员代码、需方会员代码的参数申请状态在60之前的只能有一个
        Mc04IslmcDistParmApplyDO mc04IslmcDistParmApplyDO = mc04IslmcDistParmApplyService.lambdaQuery().eq(Mc04IslmcDistParmApplyDO::getMa02CgtTradeReqMembCode, ma02CgtTradeReqMembCode).eq(Mc04IslmcDistParmApplyDO::getMa02CgtTradeSuppMembCode, ma02CgtTradeSuppMembCode).eq(StrUtil.isNotBlank(md02CgtDistRegionCode), Mc04IslmcDistParmApplyDO::getMd02CgtDistRegionCode, md02CgtDistRegionCode).eq(StrUtil.isNotBlank(md02DistReceiveregionCode), Mc04IslmcDistParmApplyDO::getMd02DistReceiveregionCode, md02DistReceiveregionCode).lt(Mc04IslmcDistParmApplyDO::getMc04CgtDistParmApplyStatus, "60").one();
        DistParmApply distParmApply;
        if (ObjectUtil.isNotNull(mc04IslmcDistParmApplyDO)) {
            // 如果有配货参数申请取配货参数申请
            distParmApply = Mc04IslmcDistParmApplyDOToDistParmApplyConverter.INSTANCE.converterDoToModel(mc04IslmcDistParmApplyDO);
            // 查询配货参数申请卷烟列表
            queryDistParmApplyItems(distParmApply);
        } else {
            // 查询销售中心配货参数
            DistParm dp = new DistParm();
            dp.setMa02CgtTradeReqMembCode(ma02CgtTradeReqMembCode);
            dp.setMa02CgtTradeSuppMembCode(ma02CgtTradeSuppMembCode);
            dp.setMd02CgtDistRegionCode(md02CgtDistRegionCode);
            dp.setMd02DistReceiveregionCode(md02DistReceiveregionCode);
            DistParm distParm = queryDistParmDetail(dp);
            distParmApply = DistParmToDistParmApplyConverter.INSTANCE.converterDoToModel(distParm);
        }
        List<DistParmApplyItem> items = distParmApply.getItems();
        // 查询商业公司的卷烟列表
        List<ComItem> comItems = comItemRepositoryImpl.getComItemList("0", ma02CgtTradeReqMembCode);
        Assert.notEmpty(comItems, () -> new CustomException("商业公司卷烟列表不能为空"));
        // 找到公司卷烟里有的 但是配货参数里没有的卷烟
        comItems = comItems.stream().filter(comItem -> items.stream().noneMatch(item -> item.getAcCgtCartonCode().equals(comItem.getAcCgtCartonCode()))).collect(Collectors.toList());
        // 添加商业公司卷烟里有但是配货参数里没有的烟
        if (CollectionUtil.isNotEmpty(comItems)) {
            // 卷烟名称为空的话，查询商品中心取名称
            Map<String, String> productMap = DistUtils.getProductMap(DistUtils.getProducts(ma02CgtTradeSuppMembCode));
            for (ComItem comItem : comItems) {
                DistParmApplyItem item = new DistParmApplyItem();
                item.setAcCgtCartonCode(comItem.getAcCgtCartonCode());
                String acCgtName = productMap.get(item.getAcCgtCartonCode());
                // 可能取不到
                if (StrUtil.isNotBlank(acCgtName)) {
                    item.setAcCgtName(acCgtName);
                }
                items.add(item);
            }
        }
        return distParmApply;
    }

    /**
     * 根据配货参数申请编号查询配货参数申请的详细信息
     *
     * @param mc04CgtDistParmApplyId 配货参数申请编号
     * @return 配货参数申请明细数据
     */
    private DistParmApply getDistParmApplyById(String mc04CgtDistParmApplyId) {
        // 按配货参数申请编号查询明细
        Assert.notNull(mc04CgtDistParmApplyId, () -> new CustomException("配货参数申请编号不能为空"));
        DistParmApply distParmApply = Mc04IslmcDistParmApplyDOToDistParmApplyConverter.INSTANCE.converterDoToModel(mc04IslmcDistParmApplyService.getById(mc04CgtDistParmApplyId));
        Assert.isTrue(ObjectUtil.isNotNull(distParmApply), () -> new CustomException(mc04CgtDistParmApplyId + "配货参数申请不存在"));
        // 查询配货参数申请卷烟列表
        queryDistParmApplyItems(distParmApply);
        return distParmApply;
    }

    /**
     * 查询配货参申请卷烟和季节因子
     *
     * @param distParmApply 配货参数申请
     */
    private void queryDistParmApplyItems(DistParmApply distParmApply) {
        String mc04CgtDistParmApplyId = distParmApply.getMc04CgtDistParmApplyId();
        // 查询配货参数申请卷烟列表
        List<Mc04IslmcDistParmApplyItemDO> items = mc04IslmcDistParmApplyItemService.lambdaQuery().eq(Mc04IslmcDistParmApplyItemDO::getMc04CgtDistParmApplyId, mc04CgtDistParmApplyId).list();
        Assert.notEmpty(items, () -> new CustomException("配货参数申请卷烟列表不能为空"));
        distParmApply.setItems(Mc04IslmcDistParmApplyItemDOToDistParmApplyItemConverter.INSTANCE.converterDosToModels(items));
        // 查询配货参数申请季节因子列表 可能为空
        List<Mc04IslmcDistParmApplySeasonDO> seasons = mc04IslmcDistParmApplySeasonService.lambdaQuery().eq(Mc04IslmcDistParmApplySeasonDO::getMc04CgtDistParmApplyId, mc04CgtDistParmApplyId).list();
        distParmApply.setSeasons(Mc04IslmcDistParmApplySeasonDOToDistParmApplySeasonConverter.INSTANCE.converterDosToModels(seasons));

    }

}
