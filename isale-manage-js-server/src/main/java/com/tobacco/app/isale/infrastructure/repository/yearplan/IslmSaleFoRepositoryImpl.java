/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.yearplan;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.util.IDUtils;
import com.tobacco.app.isale.domain.enums.plan.ycplan.LatestVersionEnum;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSaleFo;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSaleFoItem;
import com.tobacco.app.isale.domain.repository.plan.IslmSaleFoRepository;
import com.tobacco.app.isale.dto.plan.yearplan.Mc04IslmSaleFoDTO;
import com.tobacco.app.isale.dto.plan.yearplan.Mc04IslmSaleFoItemDTO;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmSaleFoConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmSaleFoItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSaleFoDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSaleFoItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmSaleFoItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmSaleFoService;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmSaleFoMapper;
import com.tobacco.app.isale.tools.utils.CommodityUtil;
import com.tobacco.sc.icommodity.dto.common.constant.dto.product.IccProductDetailDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: jinfuli
 * @Date: 2025/6/13
 * @Description:
 */
@Component("IslmSaleFoRepository")
public class IslmSaleFoRepositoryImpl implements IslmSaleFoRepository {

    @Autowired
    private IslmSaleFoMapper IslmSaleFoMapper;
    @Autowired
    private Mc04IslmSaleFoService mc04IslmSaleFoService;
    @Autowired
    private Mc04IslmSaleFoItemService mc04IslmSaleFoItemService;

    @Autowired
    private IslmSaleFoMapper islmSaleFoMapper;

    @Override
    public Page<Mc04IslmSaleFoDTO> getSaleFolist(Page page, String startYear, String endYear, String type, String status, String ma02TobaProdTradeTypeCode, String icomCode) {
        Page<Mc04IslmSaleFoDTO> getSaleFolist = IslmSaleFoMapper.getSaleFolist(page, startYear, endYear, type, status, ma02TobaProdTradeTypeCode, icomCode);
        return getSaleFolist;
    }

    @Override
    public void updateIslmSaleFoVersion(List<Mc04IslmSaleFoItem> mc04IslmSaleFoItemDOS, String saleId) {
        //重新导入功能，头表生成新记录，修改原记录的版本
        String mc04CgtSaleFoId = mc04IslmSaleFoItemDOS.get(0).getMc04CgtSaleFoId();
        UpdateWrapper<Mc04IslmSaleFoDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(Mc04IslmSaleFoDO::getMc04IsLastestVersion, 0).eq(Mc04IslmSaleFoDO::getMc04CgtSaleFoId, mc04CgtSaleFoId);
        mc04IslmSaleFoService.update(updateWrapper);
        Mc04IslmSaleFoDO byId = mc04IslmSaleFoService.getById(mc04CgtSaleFoId);
        byId.setMc04CgtSaleFoId(saleId);
        byId.setMc04IsLastestVersion("1");
        byId.setMc04CgtSaleFoVersion("PV" + System.currentTimeMillis());
        mc04IslmSaleFoService.save(byId);
    }

    @Override
    public void updateVersion(List<Mc04IslmSaleFoItem> mc04IslmSaleFoItemDOS, String mc04CgtSaleFoId) {
        //导入功能，更新版本号
        UpdateWrapper<Mc04IslmSaleFoDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(Mc04IslmSaleFoDO::getMc04CgtSaleFoVersion, "PV" + System.currentTimeMillis()).set(Mc04IslmSaleFoDO::getMc04PlanSubjectStatus, "1").eq(Mc04IslmSaleFoDO::getMc04CgtSaleFoId, mc04CgtSaleFoId);
        mc04IslmSaleFoService.update(updateWrapper);
    }

    @Override
    public Boolean batchSaveSaleFoItem(List<String> productList, List<Mc04IslmSaleFoItem> mc04IslmSaleFoItemDOS, String saleId) {
        //根据卷烟编码获取卷烟信息
        Collection<IccProductDetailDTO> iccProductDetailDtos = CommodityUtil.getIccProductDetailDtos(productList);

        for (Mc04IslmSaleFoItem mc04IslmSaleFoItemDO : mc04IslmSaleFoItemDOS) {
            String itemId = IDUtils.randomUUID(32);
            mc04IslmSaleFoItemDO.setMc04CgtSaleFoItemId(itemId);
            mc04IslmSaleFoItemDO.setMc04CgtSaleFoId(saleId);
            for (IccProductDetailDTO iccProductDetailDTO : iccProductDetailDtos) {
                if (iccProductDetailDTO.getProductCode().equals(mc04IslmSaleFoItemDO.getAcCgtCartonCode())) {
                    //如果查出来的卷烟编码相同，就是用商品树查询来的信息替换上传表格的信息
                    //因为商品中心和表字段不一致，所以需要手动赋值
                    mc04IslmSaleFoItemDO.setAcCgtName(iccProductDetailDTO.getProductName());
                    mc04IslmSaleFoItemDO.setAcCgtTaxAllotPrice(new BigDecimal(iccProductDetailDTO.getAcMateTaxTranPr()));
                    mc04IslmSaleFoItemDO.setAcCgtTradePrice(new BigDecimal(iccProductDetailDTO.getWholeSalePrice()));
                    mc04IslmSaleFoItemDO.setAcCgtMediumBranceFlag(iccProductDetailDTO.getIsMedium());
                    mc04IslmSaleFoItemDO.setAcCgtTinyFlag(iccProductDetailDTO.getIsTiny());
                    mc04IslmSaleFoItemDO.setAcCgtTarVal(new BigDecimal(iccProductDetailDTO.getTarQuantity()));
                    mc04IslmSaleFoItemDO.setAcCgtPriceSegmentCode(iccProductDetailDTO.getAcCigRangeCode());
                    mc04IslmSaleFoItemDO.setAcCgtPriceSegmentName(iccProductDetailDTO.getAcCigRangeName());
                }
            }
        }
        List<Mc04IslmSaleFoItemDO> mc04IslmSaleFoItemDOS1 = Mc04IslmSaleFoItemConverter.INSTANCE.converterModelsToDos(mc04IslmSaleFoItemDOS);
        return mc04IslmSaleFoItemService.saveBatch(mc04IslmSaleFoItemDOS1);
    }

    @Override
    public void getBeforeAndAfterDate(String currentYear, String icomCode, Map<String, Map<String, Map<String, BigDecimal>>> lastThreeYearsSales, String subjectType, List<Mc04IslmSaleFoItemDTO> mc04IslmPlanSubjectLineDTOS) {
        for (int i = 1; i <= 3; i++) {
            // 计算不同的年份
            String yearToFetch = String.valueOf(Integer.valueOf(currentYear) - i);
            // 获取对应年份的数据
            List<Map<String, Object>> lastThreeYearsData = islmSaleFoMapper.getLastThreeYearsData(yearToFetch, icomCode);
            Map<String, Map<String, BigDecimal>> yearData = new HashMap<>();
            for (Map<String, Object> data : lastThreeYearsData) {
                String carCode = data.get("acCgtCartonCode").toString();

                // 存储所有销量字段
                Map<String, BigDecimal> sales = new HashMap<>();
                sales.put("nationalSaleQty", (BigDecimal) data.get("nationalSaleQty"));
                sales.put("inSaleQty", (BigDecimal) data.get("inSaleQty"));
                sales.put("outSaleQty", (BigDecimal) data.get("outSaleQty"));

                //存储卷烟编码和各种属性值
                yearData.put(carCode, sales);
            }
            lastThreeYearsSales.put(yearToFetch, yearData);
        }

        String nextYear = String.valueOf(Integer.valueOf(currentYear) + 1);
        List<Map<String, Object>> nextYearData = islmSaleFoMapper.getNextYearData(nextYear, subjectType);
        //  获取明年的销量数据
        Map<String, Map<String, BigDecimal>> nextYearSales = new HashMap<>();

        for (Map<String, Object> data : nextYearData) {
            String carCode = data.get("acCgtCartonCode").toString();

            Map<String, BigDecimal> sales = new HashMap<>();
            sales.put("nationalSaleQty", (BigDecimal) data.get("nationalSaleQty"));
            sales.put("inSaleQty", (BigDecimal) data.get("inSaleQty"));
            sales.put("outSaleQty", (BigDecimal) data.get("outSaleQty"));
            nextYearSales.put(carCode, sales);
        }
        //遍历填充数据
        for (Mc04IslmSaleFoItemDTO dto : mc04IslmPlanSubjectLineDTOS) {
            String cigaretteCode = dto.getAcCgtCartonCode();

            // 填充过去3年数据
            for (int i = 1; i <= 3; i++) {
                String year = String.valueOf(Integer.parseInt(currentYear) - i);
                Map<String, Map<String, BigDecimal>> yearData = lastThreeYearsSales.get(year);

                if (yearData != null && yearData.containsKey(cigaretteCode)) {
                    Map<String, BigDecimal> sales = yearData.get(cigaretteCode);

                    switch (i) {
                        case 1:
                            dto.setLastYear1Mc04CgtInSaleFoSaleQty(sales.get("inSaleQty"));
                            dto.setLastYear1Mc04CgtSaleFoSaleQty(sales.get("nationalSaleQty"));
                            dto.setLastYear1Mc04CgtOutSaleFoSaleQty(sales.get("outSaleQty"));
                            break;
                        case 2:
                            dto.setLastYear2Mc04CgtInSaleFoSaleQty(sales.get("inSaleQty"));
                            dto.setLastYear2Mc04CgtSaleFoSaleQty(sales.get("nationalSaleQty"));
                            dto.setLastYear2Mc04CgtOutSaleFoSaleQty(sales.get("outSaleQty"));
                            break;
                        case 3:
                            dto.setLastYear3Mc04CgtInSaleFoSaleQty(sales.get("inSaleQty"));
                            dto.setLastYear3Mc04CgtSaleFoSaleQty(sales.get("nationalSaleQty"));
                            dto.setLastYear3Mc04CgtOutSaleFoSaleQty(sales.get("outSaleQty"));
                            break;
                    }
                }
            }

            // 填充明年数据
            if (nextYearSales.containsKey(cigaretteCode)) {
                Map<String, BigDecimal> sales = nextYearSales.get(cigaretteCode);
                dto.setNextYear1Mc04CgtInSaleFoSaleQty(sales.get("inSaleQty"));
                dto.setNextYear1Mc04CgtSaleFoSaleQty(sales.get("nationalSaleQty"));
                dto.setNextYear1Mc04CgtOutSaleFoSaleQty(sales.get("outSaleQty"));
            }

        }
    }

    @Override
    public Mc04IslmSaleFo latest(String zaOccurrenceYear) {
        LambdaQueryWrapper<Mc04IslmSaleFoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmSaleFoDO::getZaOccurrenceYear, zaOccurrenceYear).eq(Mc04IslmSaleFoDO::getMc04IsLastestVersion, LatestVersionEnum.CURRENT.getCode());
        List<Mc04IslmSaleFoDO> list = mc04IslmSaleFoService.list(queryWrapper);
        if (list.size() > 0) {
            Mc04IslmSaleFoDO mc04IslmSaleFoDO = list.get(0);
            return Mc04IslmSaleFoConverter.INSTANCE.converterDoToModel(mc04IslmSaleFoDO);
        }
        return null;
    }


}
