/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.agreement.basic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.bizworks.core.runtime.common.SingleResponse;
import com.alibaba.bizworks.core.specification.ddd.Repository;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.base.CustomPage;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.constants.agreement.basic.XyTypeConstants;
import com.tobacco.app.isale.domain.model.agreement.basic.IslmcXy;
import com.tobacco.app.isale.domain.model.agreement.basic.IslmcXyLine;
import com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcXy;
import com.tobacco.app.isale.domain.model.agreement.basic.QueryAgreementDomainREQ;
import com.tobacco.app.isale.domain.repository.agreement.basic.AgreementRepository;
import com.tobacco.app.isale.infrastructure.converter.agreement.basic.IslmcXyDtoToMc04IslmcXyConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcXyDO;
import com.tobacco.app.isale.infrastructure.entity.NationAgreementAdjustDO;
import com.tobacco.app.isale.infrastructure.entity.NationAgreementDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcXyService;
import com.tobacco.app.isale.infrastructure.service.api.NationAgreementAdjustService;
import com.tobacco.app.isale.infrastructure.service.api.NationAgreementService;
import com.tobacco.app.isale.infrastructure.tunnel.database.agreement.basic.AgreementMapper;
import com.tobacco.app.isale.tools.utils.ISalePermissionUtils;
import com.tobacco.app.isale.tools.utils.SaleResponseUtil;
import com.tobacco.app.isalecenter.client.api.xy.IslmcXyAdjustAPI;
import com.tobacco.app.isalecenter.client.api.xy.XyAPI;
import com.tobacco.app.isalecenter.client.dto.common.PageDTO;
import com.tobacco.app.isalecenter.client.dto.common.SaleSingleResponse;
import com.tobacco.app.isalecenter.client.dto.xy.Mc04IslmcXyAdjustDTO;
import com.tobacco.app.isalecenter.client.dto.xy.Mc04IslmcXyAdjustItemDTO;
import com.tobacco.app.isalecenter.client.dto.xy.Mc04IslmcXyDTO;
import com.tobacco.app.isalecenter.client.dto.xy.Mc04IslmcXyItemDTO;
import com.tobacco.app.isalecenter.client.req.xy.*;
import com.tobacco.app.isalecenter.common.enums.ProdTradeTypeCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> jinfuli
 * @Email : @inspur.com
 * @Create : 2025/05/09 08:27
 * @Description :
 */
@Repository
@Component
@Slf4j
public class AgreementRepositoryImpl implements AgreementRepository {
    private final Mc04IslmcXyService mc04IslmcYyService;
    private final NationAgreementService nationAgreementService;
    private final NationAgreementAdjustService nationAgreementAdjustService;
    private final IslmcXyAdjustAPI islmcXyAdjustAPI;

    public AgreementRepositoryImpl(Mc04IslmcXyService mc04IslmcYyService, NationAgreementService nationAgreementService, NationAgreementAdjustService nationAgreementAdjustService, IslmcXyAdjustAPI islmcXyAdjustAPI) {
        this.mc04IslmcYyService = mc04IslmcYyService;
        this.nationAgreementService = nationAgreementService;
        this.nationAgreementAdjustService = nationAgreementAdjustService;
        this.islmcXyAdjustAPI = islmcXyAdjustAPI;
    }

    private AgreementMapper agreementMapper;

    @Resource
    public void setAgreementMapper(AgreementMapper agreementMapper) {
        this.agreementMapper = agreementMapper;
    }

    private XyAPI xyApi;

    @Autowired
    public void setxyApi(XyAPI xyApi) {
        this.xyApi = xyApi;
    }


    /**
     * 查询协议详情（协议编号）
     */
    public Map<String, NationAgreementDO> queryAgreement(List<IslmcXy> list) {
        // 提取协议编号和需方编码
        List<String> xyNoList = new ArrayList<>();
        List<String> comCodeList = new ArrayList<>();
        for (IslmcXy xy : list) {
            xyNoList.add(xy.getMd02CgtXyNo());
            comCodeList.add(xy.getReqmemberCode());
        }
        if (CollUtil.isEmpty(xyNoList)) {
            return new HashMap<>();
        }
        QueryWrapper<NationAgreementDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(NationAgreementDO::getProtocolno, xyNoList)
                .in(NationAgreementDO::getReqmemberCode, comCodeList);
        List<NationAgreementDO> agreementDOList = nationAgreementService.list(queryWrapper);
        Map<String, NationAgreementDO> agreementMap = new HashMap<>();
        for (NationAgreementDO agreementDO : agreementDOList) {
            agreementMap.put(agreementDO.getProtocolno() + "_" + agreementDO.getReqmemberCode(), agreementDO);
        }
        return agreementMap;
    }

    /**
     * 查询协议详情（协议编号）
     */
    public Map<String, NationAgreementAdjustDO> queryAgreementAdjust(List<IslmcXy> list) {
        // 提取协议编号和需方编码
        List<String> nationIdList = new ArrayList<>();
        List<String> comCodeList = new ArrayList<>();
        for (IslmcXy xy : list) {
            nationIdList.add(xy.getMc04NationXyAdjustId());
            comCodeList.add(xy.getReqmemberCode());
        }
        if (CollUtil.isEmpty(nationIdList) || CollUtil.isEmpty(comCodeList)) {
            return new HashMap<>();
        }
        QueryWrapper<NationAgreementAdjustDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(NationAgreementAdjustDO::getPk, nationIdList)
                .in(NationAgreementAdjustDO::getReqmemberCode, comCodeList);
        List<NationAgreementAdjustDO> agreementDOList = nationAgreementAdjustService.list(queryWrapper);
        Map<String, NationAgreementAdjustDO> agreementMap = new HashMap<>();
        for (NationAgreementAdjustDO agreementDO : agreementDOList) {
            agreementMap.put(agreementDO.getProtocolno() + "_" + agreementDO.getReqmemberCode(), agreementDO);
        }
        return agreementMap;
    }


    /**
     * @param xyNoList 协议编号
     * @param type     类型 暂定 1为配货单 2为合同
     * @param status   状态
     * @return List<IslmcXy>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-22 14:57:09
     * @description : 获取协议剩余量
     */
    @Override
    public List<Mc04IslmcXy> getRemainAgreement(List<String> xyNoList, String type, String status) {
        if (REMAIN_AGREEMENT_CONT_TYPE.equals(type)) {
            return this.getRemainAgreementContType(xyNoList, status);
        } else if (REMAIN_AGREEMENT_DIST_TYPE.equals(type)) {
            //todo 先空着,后续大橙子可能要用
            return Collections.emptyList();
        } else {
            throw new CustomException("类型错误");
        }
    }

    /**
     * 获取协议量(只会查询有效协议)
     *
     * @param xyNo 协议编码
     * @return Mc04IslmcXy
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-22 14:57:09
     * @description : 获取协议量(只会查询有效协议)
     */
    @Override
    public Mc04IslmcXy getAgreement(String xyNo) {
        //查协议
        Mc04IslmcXyQueryREQ req = new Mc04IslmcXyQueryREQ();
        req.setMd02CgtXyNo(xyNo);
        req.setIcomCode(IcomUtils.getIcomCode());
        List<Mc04IslmcXyDTO> list = SaleResponseUtil.getCenterDTO("中心获取协议", () -> xyApi.list(req));

        if (CollUtil.isEmpty(list)) {
            return null;
        }

        return IslmcXyDtoToMc04IslmcXyConverter.INSTANCE.converterDoToModel(list.get(0));
    }

    /**
     * 查询协议
     *
     * @param xyPeriodCode      协议区间
     * @param comCode           地市编码
     * @param prodTradeTypeCode 卷烟交易类型 {@link ProdTradeTypeCodeEnum}
     * @return Mc04IslmcXy
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-22 14:57:09
     * @description : 获取协议量(只会查询有效协议)
     */
    @Override
    public Mc04IslmcXy getAgreement(String xyPeriodCode, String comCode, String prodTradeTypeCode) {
        //查协议
        Mc04IslmcXyQueryREQ req = new Mc04IslmcXyQueryREQ();
        req.setMc04CgtXyPeriodCode(xyPeriodCode);
        req.setBaComOrgCode(comCode);
        req.setMc04CgtXyPeriodCode(prodTradeTypeCode);
        req.setIcomCode(IcomUtils.getIcomCode());
        List<Mc04IslmcXyDTO> list = SaleResponseUtil.getCenterDTO("中心获取协议", () -> xyApi.list(req));

        if (CollUtil.isEmpty(list)) {
            return null;
        }

        return IslmcXyDtoToMc04IslmcXyConverter.INSTANCE.converterDoToModel(list.get(0));
    }


    /**
     * @param xyPeriodCode      协议周期编码
     * @param comCodeList       协议单位
     * @param prodTradeTypeCode 卷烟交易类型 {@link ProdTradeTypeCodeEnum}
     * @param type              类型 暂定 1为配货单 2为合同
     * @param status            状态
     * @return List<IslmcXy>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-22 14:57:09
     * @description : 获取协议剩余量
     */
    @Override
    public List<Mc04IslmcXy> getRemainAgreement(String xyPeriodCode, List<String> comCodeList,
                                                String prodTradeTypeCode, String type, String status) {
        Assert.notNull(xyPeriodCode, () -> new CustomException("协议周期编码不能为空"));
        Assert.notNull(comCodeList, () -> new CustomException("协议单位不能为空"));
        List<Mc04IslmcXyDO> xyDOList = mc04IslmcYyService.lambdaQuery()
                .eq(Mc04IslmcXyDO::getMc04CgtXyPeriodCode, xyPeriodCode)
                .in(Mc04IslmcXyDO::getBaComOrgCode, comCodeList)
                .eq(StrUtil.isNotBlank(prodTradeTypeCode),
                        Mc04IslmcXyDO::getMa02TobaProdTradeTypeCode, prodTradeTypeCode)
                .eq(Mc04IslmcXyDO::getMc04CgtXyStatus, "1")
                .list();
        if (CollUtil.isEmpty(xyDOList)) {
            return Collections.emptyList();
        }
        List<String> xyNoList = xyDOList.stream().map(Mc04IslmcXyDO::getMd02CgtXyNo).collect(Collectors.toList());
        return getRemainAgreement(xyNoList, type, status);
    }

    /**
     * 获取协议列表
     *
     * @param prodTradeType 卷烟交易类型 {@link ProdTradeTypeCodeEnum}
     * @param xyPeriodCode 协议周期编码
     * @return List<IslmcXy>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-22 14:57:09
     * @description : 获取协议列表
     */
    @Override
    public List<Mc04IslmcXy> queryAgreementList(String prodTradeType, String xyPeriodCode) {
        //查协议
        Mc04IslmcXyQueryREQ req = new Mc04IslmcXyQueryREQ();
        req.setMa02TobaProdTradeTypeCode(prodTradeType);
        req.setMc04CgtXyPeriodCode(xyPeriodCode);
        req.setIcomCode(IcomUtils.getIcomCode());
        List<Mc04IslmcXyDTO> list =
                SaleResponseUtil.getCenterDTO("中心获取协议", () -> xyApi.list(req));
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return IslmcXyDtoToMc04IslmcXyConverter.INSTANCE.converterDosToModels(list);
    }


    @Override
    public CustomPage<IslmcXy> queryAgreementPage(QueryAgreementDomainREQ req) {

        req.setIcomCode(IcomUtils.getIcomCode());
        if (XyTypeConstants.XY_TYPE_XY.equals(req.getDataType())) {
            return pageXy(req);
        } else {
            return pageXyAdjust(req);
        }
    }

    /**
     * 获取协议或协议调整详情
     *
     * @param xyId     协议ID
     * @param dataType 数据类型 10 协议 20 协议调整
     * @return 协议详情
     */
    @Override
    public IslmcXy getAgreementDetail(String xyId, String dataType) {
        if (XyTypeConstants.XY_TYPE_XY.equals(dataType)) {
            SaleSingleResponse<Mc04IslmcXyDTO> detail = xyApi.detail(xyId);
            Assert.isTrue(detail.isSuccess(), () -> new CustomException("协议详情查询失败"));
            Mc04IslmcXyDTO data = detail.getData();
            IslmcXy xy = BeanUtil.copyProperties(data, IslmcXy.class);
            List<IslmcXyLine> collect = data.getXyItems().stream().map(xyItem -> {
                IslmcXyLine xyLine = new IslmcXyLine();
                xyLine.setMd02CgtXyOriginalQty(xyItem.getMd02CgtXyAdjustedQty());
                xyLine.setAcCgtCartonCode(xyItem.getAcCgtCartonCode());
                xyLine.setQty(xyItem.getMd02CgtXyAdjustedQty());
                return xyLine;
            }).collect(Collectors.toList());
            xy.setIslmcXyLineList(collect);
            return xy;
        } else {
            SaleSingleResponse<Mc04IslmcXyAdjustDTO> detail = islmcXyAdjustAPI.detail(xyId);
            Assert.isTrue(detail.isSuccess(), () -> new CustomException("协议详情查询失败"));
            Mc04IslmcXyAdjustDTO data = detail.getData();
            IslmcXy xy = BeanUtil.copyProperties(data, IslmcXy.class);
            List<IslmcXyLine> collect = data.getItems().stream().map(xyItem -> {
                IslmcXyLine xyLine = new IslmcXyLine();
                xyLine.setMd02CgtXyOriginalQty(xyItem.getMd02CgtXyAdjustQty());
                xyLine.setAcCgtCartonCode(xyItem.getAcCgtCartonCode());
                xyLine.setQty(xyItem.getMd02CgtXyAdjustQty());
                return xyLine;
            }).collect(Collectors.toList());
            xy.setIslmcXyLineList(collect);
            return xy;
        }
    }

    /**
     * 修改协议状态
     *
     * @param xyId     协议ID
     * @param xyStatus 协议状态
     * @return 是否成功
     */
    @Override
    public Boolean updateAgreementStatus(String xyId, String xyStatus) {
        SaleSingleResponse<Mc04IslmcXyDTO> detail = xyApi.detail(xyId);
        Assert.isTrue(detail.isSuccess(), () -> new CustomException("协议详情查询失败"));
        Mc04IslmcXyDTO data = detail.getData();
        List<Mc04IslmcXyItemDTO> xyItems = data.getXyItems();
        List<Mc04IslmcXyItemREQ> mc04IslmcXyItemREQS = BeanUtil.copyToList(xyItems, Mc04IslmcXyItemREQ.class);
        Mc04IslmcXyREQ mc04IslmcXyREQ = BeanUtil.copyProperties(data, Mc04IslmcXyREQ.class);
        mc04IslmcXyREQ.setXyItems(mc04IslmcXyItemREQS);
        mc04IslmcXyREQ.setMc04CgtXyStatus(XyTypeConstants.XY_STATUS_XY_STATUS_ACTIVE);
        SingleResponse<Boolean> update = xyApi.update(mc04IslmcXyREQ);
        Assert.isTrue(update.isSuccess(), () -> new CustomException("协议状态修改失败"));
        return update.getData();
    }

    private List<Mc04IslmcXy> getRemainAgreementContType(List<String> xyNoList, String status) {

        return agreementMapper.getRemainAgreementContType(xyNoList, status);
    }

    /**
     * @param queryAgreementDomainRE
     * @return
     * @create_time : 2025-05-22 14:57:09
     * @description : 协议调整分页查询
     */
    private CustomPage<IslmcXy> pageXyAdjust(QueryAgreementDomainREQ queryAgreementDomainRE) {
        Mc04IslmcXyAdjustPageREQ req = new Mc04IslmcXyAdjustPageREQ();
        req.setIcomCode(IcomUtils.getIcomCode());
        req.setSize(queryAgreementDomainRE.getLimit());
        req.setCurrent(queryAgreementDomainRE.getOffset());
        req.setMc04CgtXyDownloadDateBegin(queryAgreementDomainRE.getDownBeginDate());
        req.setMc04CgtXyDownloadDateEnd(queryAgreementDomainRE.getDownEndDate());
        req.setMa02TobaProdTradeTypeCode(queryAgreementDomainRE.getCgtType());
        req.setCurrent(queryAgreementDomainRE.getOffset() / queryAgreementDomainRE.getLimit() + 1);
        req.setSize(queryAgreementDomainRE.getLimit());
        req.setMc04CgtXyPeriodCode(queryAgreementDomainRE.getDateCode());
        String comIds = queryAgreementDomainRE.getComIds();
        List<String> permissionComList = ISalePermissionUtils.getPermissionComList(StrUtil.isBlank(comIds) ? new ArrayList<>() : StrUtil.split(comIds, ","));
        req.setBaComOrgCodes(permissionComList);
        SaleSingleResponse<PageDTO<Mc04IslmcXyAdjustDTO>> page = islmcXyAdjustAPI.page(req);
        Assert.isTrue(page.isSuccess() && page.getData() != null, () -> new CustomException("获取协议调整失败"));
        List<Mc04IslmcXyAdjustDTO> records = page.getData().getRecords();
        List<IslmcXy> collect = records.stream().map(item -> {
            IslmcXy xy = new IslmcXy();
            xy.setXyId(item.getMc04XyAdjustId());
            xy.setMd02CgtXyNo(item.getMd02CgtXyNo());
            BigDecimal adjustQty = item.getItems().stream().map(Mc04IslmcXyAdjustItemDTO::getMd02CgtXyAdjustQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            xy.setAdjustQty(adjustQty);
            xy.setQty(adjustQty);
            xy.setMc04CgtXyType(XyTypeConstants.XY_TYPE_XY_ADJUST);
            xy.setReqmemberCode(item.getBaComOrgCode());
            xy.setMc04CgtXyDownloadDate(item.getMc04CgtXyDownloadDate());
            xy.setMc04CgtXyPeriodCode(item.getMc04CgtXyPeriodCode());
            return xy;
        }).collect(Collectors.toList());
        Map<String, NationAgreementAdjustDO> stringNationAgreementAdjustDoMap = queryAgreementAdjust(collect);

        collect.forEach(xy -> {
            NationAgreementAdjustDO nationAgreementAdjustDO = stringNationAgreementAdjustDoMap.get(xy.getMd02CgtXyNo() + "_" + xy.getReqmemberCode());
            if (nationAgreementAdjustDO != null) {
                xy.setReqmemberName(nationAgreementAdjustDO.getReqmemberName());
                xy.setSupmemberCode(nationAgreementAdjustDO.getSupmemberCode());
                xy.setSupmemberName(nationAgreementAdjustDO.getSupmemberName());
            }
        });
        CustomPage<IslmcXy> pageXy = new CustomPage<>();
        pageXy.setRecords(collect);
        pageXy.setTotal(page.getData().getTotal());
        pageXy.setCurrent(page.getData().getCurrent());
        pageXy.setSize(page.getData().getSize());
        return pageXy;
    }

    /**
     * @param queryAgreementDomainRE
     * @return
     * @create_time : 2025-05-22 14:57:09
     * @description : 协议分页查询
     */
    private CustomPage<IslmcXy> pageXy(QueryAgreementDomainREQ queryAgreementDomainRE) {
        Mc04IslmcXyPageREQ req = new Mc04IslmcXyPageREQ();
        req.setIcomCode(IcomUtils.getIcomCode());
        req.setSize(queryAgreementDomainRE.getLimit());
        req.setCurrent(queryAgreementDomainRE.getOffset());
        req.setMc04CgtXyDownloadDateBegin(queryAgreementDomainRE.getDownBeginDate());
        req.setMc04CgtXyDownloadDateEnd(queryAgreementDomainRE.getDownEndDate());
        req.setMc04CgtXyStatuses(Arrays.asList(XyTypeConstants.XY_STATUS_XY_STATUS_ACTIVE, XyTypeConstants.XY_STATUS_XY_STATUS_DRAFT));
        req.setMc04CgtXyPeriodCode(queryAgreementDomainRE.getDateCode());
        req.setCurrent(queryAgreementDomainRE.getOffset() / queryAgreementDomainRE.getLimit() + 1);
        req.setSize(queryAgreementDomainRE.getLimit());
        req.setMa02TobaProdTradeTypeCode(queryAgreementDomainRE.getCgtType());
        String comIds = queryAgreementDomainRE.getComIds();
        List<String> permissionComList = ISalePermissionUtils.getPermissionComList(StrUtil.isBlank(comIds) ? new ArrayList<>() : StrUtil.split(comIds, ","));
        req.setBaComOrgCodes(permissionComList);
        SaleSingleResponse<PageDTO<Mc04IslmcXyDTO>> page = xyApi.page(req);
        Assert.isTrue(page.isSuccess() && page.getData() != null, () -> new CustomException("获取协议调整失败"));
        List<Mc04IslmcXyDTO> records = page.getData().getRecords();
        List<IslmcXy> collect = records.stream().map(item -> {
            IslmcXy xy = new IslmcXy();
            xy.setXyId(item.getMc04CgtXyId());
            xy.setMd02CgtXyNo(item.getMd02CgtXyNo());
            BigDecimal adjustQty = item.getXyItems().stream().map(Mc04IslmcXyItemDTO::getMd02CgtXyAdjustedQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            xy.setAdjustQty(adjustQty);
            xy.setQty(adjustQty);
            xy.setMc04CgtXyType(XyTypeConstants.XY_TYPE_XY);
            xy.setReqmemberCode(item.getBaComOrgCode());
            xy.setMc04CgtXyDownloadDate(item.getMc04CgtXyDownloadDate());
            xy.setMc04CgtXyStatus(item.getMc04CgtXyStatus());
            xy.setMc04CgtXyPeriodCode(item.getMc04CgtXyPeriodCode());
            return xy;
        }).collect(Collectors.toList());
        Map<String, NationAgreementDO> stringNationAgreementDoMap = queryAgreement(collect);
        collect.forEach(xy -> {
            NationAgreementDO nationAgreementDO = stringNationAgreementDoMap.get(xy.getMd02CgtXyNo() + "_" + xy.getReqmemberCode());
            if (nationAgreementDO != null) {
                xy.setReqmemberName(nationAgreementDO.getReqmemberName());
                xy.setSupmemberCode(nationAgreementDO.getSupmemberCode());
                xy.setReqmemberName(nationAgreementDO.getReqmemberName());
            }
        });
        CustomPage<IslmcXy> pageXy = new CustomPage<>();
        pageXy.setRecords(collect);
        pageXy.setTotal(page.getData().getTotal());
        pageXy.setCurrent(page.getData().getCurrent());
        pageXy.setSize(page.getData().getSize());
        return pageXy;
    }


}
