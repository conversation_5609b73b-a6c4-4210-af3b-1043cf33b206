/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tobacco.app.isale.domain.model.agreement.sign.ManageIndXyOrgCgtPurchSaleStkDay;
import com.tobacco.app.isale.infrastructure.entity.Mc04IndXyOrgCgtPurchSaleStkDayDO;

import java.util.List;


/**
 * <AUTHOR> wangluhao01
 * @description : 服务类
 * @email : <EMAIL>
 * @create_time : 2025-05-13
 * @since : 2025-05-13
 */
public interface Mc04IndXyOrgCgtPurchSaleStkDayService extends IService<Mc04IndXyOrgCgtPurchSaleStkDayDO> {

    /**
     * @param baComOrgCode 地市编码
     * @param periodCode   卷烟协议周期
     * @return List<ManageIndXyOrgCgtPurchSaleStkDay>
     * <AUTHOR> wanglu<PERSON>01
     * @create_time : 2025-05-13 19:11:42
     * @description : 获取前半年销售量
     */

    List<ManageIndXyOrgCgtPurchSaleStkDay> getFirstHalfYearSalesGroupByProduct(String baComOrgCode, String periodCode);
}