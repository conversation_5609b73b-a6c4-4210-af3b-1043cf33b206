/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.basic;

import com.tobacco.app.isale.domain.model.basic.ManageIslmcComItemContDelivWhse;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcComItemContDelivWhseDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: zhangshch01
 * @Create: 2025-07-28
 */

@Mapper
public interface Mc04IslmcComItemContDelivWhseDOToManageIslmcComItemContDelivWhseConverter
        extends StructureBaseConverter<Mc04IslmcComItemContDelivWhseDO, ManageIslmcComItemContDelivWhse> {

    Mc04IslmcComItemContDelivWhseDOToManageIslmcComItemContDelivWhseConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcComItemContDelivWhseDOToManageIslmcComItemContDelivWhseConverter.class);

}