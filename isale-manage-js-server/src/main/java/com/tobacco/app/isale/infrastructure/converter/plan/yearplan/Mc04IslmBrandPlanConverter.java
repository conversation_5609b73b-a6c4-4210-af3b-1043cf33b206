/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.yearplan;

import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmBrandPlanModel;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmBrandPlanDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 品牌发展规划 数据转换器
 * <p>
 * IslmBrandPlanConverter
 *
 * @Author: longxi
 * @Since: 2025-08-07
 */

@Mapper
public interface Mc04IslmBrandPlanConverter extends StructureBaseConverter<Mc04IslmBrandPlanDO, Mc04IslmBrandPlanModel> {

    Mc04IslmBrandPlanConverter INSTANCE = Mappers.getMapper(Mc04IslmBrandPlanConverter.class);

}