/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.agreement.adjust;

import com.tobacco.app.isale.domain.model.adjust.IslmcXyAdjustApply;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcXyAdjustApplyDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcXyAdjustApplyItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: jinfuli
 * @Date: 2025/7/23
 * @Description:
 */
@Mapper
public interface XyAdjustApplyConverter {
    XyAdjustApplyConverter INSTANCE = Mappers.getMapper(XyAdjustApplyConverter.class);

    @Mappings({})
    IslmcXyAdjustApply mc04IslmcXyAdjustApplyDOToXyAdjustApply(Mc04IslmcXyAdjustApplyDO mc04IslmcXyAdjustApplyDO);
    @Mappings({})
    IslmcXyAdjustApply mc04IslmcXyAdjustApplyDOToXyAdjustApply(Mc04IslmcXyAdjustApplyItemDO mc04IslmcXyAdjustApplyItemDO);

    @Mappings({})
    Mc04IslmcXyAdjustApplyDO xyAdjustApplyToMc04IslmcXyAdjustApplyDO(IslmcXyAdjustApply islmcXyAdjustApply);
    @Mappings({})
    List<Mc04IslmcXyAdjustApplyItemDO> xyAdjustApplyListToMc04IslmcXyAdjustApplyItemDOList(List<IslmcXyAdjustApply> islmcXyAdjustApply);
    @Mappings({})
    List<Mc04IslmcXyAdjustApplyDO> xyAdjustApplyListToMc04IslmcXyAdjustApplyDOList(List<IslmcXyAdjustApply> islmcXyAdjustApply);

    @Mappings({})
    List<IslmcXyAdjustApply> covertDosToModels(List<Mc04IslmcXyAdjustApplyDO> mc04IslmcXyAdjustApplyDOList);
}
