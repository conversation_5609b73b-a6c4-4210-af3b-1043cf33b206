/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.domain.service.plan.yearplan;

import cn.hutool.core.lang.Assert;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.ddd.DomainService;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.base.CustomPage;
import com.inspur.ind.icom.IcomUtils;
import com.inspur.ind.util.IDUtils;
import com.tobacco.app.isale.app.converter.plan.yearplan.allot.IslmSalePlanConverter;
import com.tobacco.app.isale.domain.enums.common.ProdTradeTypeCodeEnum;
import com.tobacco.app.isale.domain.enums.plan.yearplan.*;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanItemModel;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanModel;
import com.tobacco.app.isale.domain.model.plan.yearplan.yearplanallot.YearPlanAllotProvince;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmSalePlanAllotProvinceRepository;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmSalePlanItemRepository;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmSalePlanRepository;
import com.tobacco.app.isale.dto.common.TreeStatus;
import com.tobacco.app.isale.dto.plan.yearplan.allot.IslmSalePlanDto;
import com.tobacco.sc.icommodity.dto.common.constant.client.api.product.ProductServiceApi;
import com.tobacco.sc.icommodity.dto.common.constant.dto.common.IccMultiDataDTO;
import com.tobacco.sc.icommodity.dto.common.constant.dto.common.IccMultiResponse;
import com.tobacco.sc.icommodity.dto.common.constant.dto.product.IccProductDetailDTO;
import com.tobacco.sc.icommodity.dto.common.constant.req.product.IccGetProductListRequest;
import com.tobacco.sc.icust.client.api.com.ComServiceAPI;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import com.tobacco.sc.icust.dto.common.KCMultiResponse;
import com.tobacco.sc.icust.req.com.GetBusiComListREQ;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0.0
 * 创建时间：2025/8/12 14:33
 */
@Slf4j
@Service
@Component("IslmSalePlanAllotProvinceDS")
@DomainService(name = "年度计划分解(省)明细服务", desc = "年度计划分解(省)明细服务")
public class IslmYearPlanAllotProvinceDomainService {

    private IslmSalePlanRepository salePlanRepository;
    private IslmSalePlanItemRepository salePlanItemRepository;
    private IslmSalePlanAllotProvinceRepository  allotProvinceRepository;
    @Resource
    private ComServiceAPI comServiceAPI;
    @Resource
    private ProductServiceApi productServiceApi;

    @Autowired
    public void setAllDependencies(IslmSalePlanRepository salePlanRepository,IslmSalePlanItemRepository salePlanItemRepository, IslmSalePlanAllotProvinceRepository  allotProvinceRepository) {
        this.salePlanRepository = salePlanRepository;
        this.salePlanItemRepository = salePlanItemRepository;
        this.allotProvinceRepository = allotProvinceRepository;
    }

    public Page<IslmSalePlanDto> page(@Parameter(name = "查询参数" , required = true) CustomPage<?> customPage,
                                                   String startYear, String endYear, String type, String status, String icomCode) {
        Page<Mc04IslmSalePlanModel> doPage = salePlanRepository.page(customPage, startYear, endYear, type, status,
                YearPlanOrgTypeEnum.PROVINCE.getKind(), icomCode, YearPlanPeriodTypeEnum.YEAR.getCode(), null);
        List<Mc04IslmSalePlanModel> records = doPage.getRecords();
        Page<IslmSalePlanDto> page = Page.of(doPage.getCurrent(), doPage.getSize(), doPage.getTotal());
        List<IslmSalePlanDto> islmSalePlanDtos = IslmSalePlanConverter.INSTANCE.converterModelsToDtos(records);
        page.setRecords(islmSalePlanDtos);
        return page;
    }

    /**
     * 获取省份（含下属所有地市）分解计划详情数据（按季度）
     * @param mc04SalePlanId 分解计划ID
     * @param cgtSaleFoPeriodCode 分解计划周期
     * @return YearPlanAllotProvince
     */
    @ReturnValue(desc = "获取年度计划分解（省）地市季度分解数据")
    @Method(desc = "获取年度计划分解（省）地市季度分解数据")
    public YearPlanAllotProvince getCityAllotQuarterData(@Parameter(name = "分解计划ID" , required = true) String mc04SalePlanId, @Parameter(name = "分解计划周期" , required = true) String cgtSaleFoPeriodCode) {
        Mc04IslmSalePlanModel salePlanModel = salePlanRepository.getById(mc04SalePlanId);
        // 获取省及省下所有地市
        List<BusiComDto> citysInProvince = getCitysInProvince(salePlanModel.getMc04OrgTypeCode(), true);
        // 获取当前省当前季度计划主体
//        List<Mc04IslmSalePlanModel> provinceQuarterPlan = salePlanRepository.getAllotPlanDataByMultiParam(mc04SalePlanId, "22", citysInProvince, cgtSaleFoPeriodCode, YearPlanPeriodTypeEnum.QUARTER.getCode(), YearPlanOrgTypeEnum.PROVINCE.getKind());
        List<Mc04IslmSalePlanModel> provinceQuarterPlan = salePlanRepository.getAllotPlanDataByMultiParam(mc04SalePlanId, YearPlanAllotProvinceStatusEnum.CITY_PENDING.getCode(), citysInProvince, cgtSaleFoPeriodCode, YearPlanPeriodTypeEnum.QUARTER.getCode(), YearPlanOrgTypeEnum.PROVINCE.getKind());
        // 省分解计划编码，地市代码、季度信息获取省以及下属所有地市分解计划
        List<Mc04IslmSalePlanModel> allQuarterData = salePlanRepository.getAllotPlanDataByMultiParam(mc04SalePlanId, YearPlanAllotProvinceStatusEnum.CITY_PENDING.getCode(), citysInProvince, cgtSaleFoPeriodCode, YearPlanPeriodTypeEnum.QUARTER.getCode(), YearPlanOrgTypeEnum.CITY.getKind());
        provinceQuarterPlan.addAll(allQuarterData);
        List<Map<String, Object>> resultData;

        //获取所有品规所有地市n-1年度的期末库存并分组
        List<String> cityFoYearStk = citysInProvince.stream()
                .map(BusiComDto::getBaComOrgCode)
                .filter(cityCode -> !salePlanModel.getMc04OrgTypeCode().equals(cityCode))
                .collect(Collectors.toList());
        List<BusiComDto> cityList = citysInProvince.stream()
                .filter(city -> !salePlanModel.getMc04OrgTypeCode().equals(city.getBaComOrgCode()))
                .collect(Collectors.toList());
        String year = salePlanModel.getZaOccurrenceYear();
        String previousYear = String.valueOf(Integer.parseInt(year) - 1);
        String periodCode = cgtSaleFoPeriodCode.substring(4, 6);
        List<YearPlanAllotProvince> allProductIncludeCityYearEndStk = null;
        // 只有1季度需要取上一年的期末库存作为初始库存，其他季度取上一季度的季末库存作为初始库存进行判断
        if ("Q1".equals(periodCode)){
            allProductIncludeCityYearEndStk = allotProvinceRepository.getAllProductIncludeCityYearEndStk(previousYear, cityFoYearStk);
        } else {
            // cgtSaleFoPeriodCode
            String currentYear = cgtSaleFoPeriodCode.substring(0, 4);
            String previousQuarterCode = getPreviousQuarterCode(periodCode);
            String previousQuarterPeriod = currentYear + previousQuarterCode;

            // 调用独立方法获取上一季度库存数据
            allProductIncludeCityYearEndStk = getPreviousQuarterCityStockData(
                    mc04SalePlanId, cityList, previousQuarterPeriod);
        }
        // 构建产品-地市-库存映射
        Map<String, Map<String, BigDecimal>> productCityStockMap = buildProductCityStockMap(allProductIncludeCityYearEndStk);

        // 获取所有商品信息, 无论有无数据，都需要根据商品构造数据体
        Collection<IccProductDetailDTO> productList = getProductList();

        if (!allQuarterData.isEmpty()) {
            // 遍历计划集合，提出计划id，根据id获取所有计划明细数据
            List<String> cityPlanIds = provinceQuarterPlan.stream()
                    .map(Mc04IslmSalePlanModel::getMc04SalePlanId)
                    .collect(Collectors.toList());
            List<Mc04IslmSalePlanItemModel> allotProvinceDetailData = salePlanItemRepository.getAllotProvinceDetailData(cityPlanIds);
            // 组装已有数据
            resultData = assembleExistingData(provinceQuarterPlan, allotProvinceDetailData, citysInProvince, productList, productCityStockMap);
        } else { // 计划为空需人工构造表头及数据进行返回
            resultData = constructEmptyData(salePlanModel, citysInProvince, productList, cgtSaleFoPeriodCode, productCityStockMap);
        }
        YearPlanAllotProvince yearPlanAllotProvince = new YearPlanAllotProvince();
        yearPlanAllotProvince.setDataList(resultData);
        return yearPlanAllotProvince;
    }

    /**
     * 保存年度分解（省）分解数据
     * @param mc04SalePlanId 分解计划ID
     * @param dataList 待保存数据
     * @return 保存结果
     */
    @ReturnValue(desc = "保存年度分解（省）地市详情数据")
    @Method(desc = "保存年度分解（省）地市详情数据")
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCityPlanData(String mc04SalePlanId, List<Map<String, Object>> dataList, String cgtSaleFoPeriodCode) {
        boolean saveStatus = true;
        Mc04IslmSalePlanModel salePlanModel = salePlanRepository.getById(mc04SalePlanId);
        // 从key中截取所有地市编码
        Set<String> cityCodes = dataList.stream()
                .flatMap(dataMap -> dataMap.keySet().stream())
                .filter(key -> key.length() > 8)
                .map(key -> key.substring(key.length() - 8))
                .filter(cityCode -> cityCode.matches("\\d{8}"))
                .collect(Collectors.toSet());

        // 获取省及地市信息
        List<BusiComDto> cityList = getCitysInProvince(salePlanModel.getMc04OrgTypeCode(), false);
        Map<String, BusiComDto> cityMap = cityList.stream()
                .collect(Collectors.toMap(BusiComDto::getBaComOrgCode, city -> city));

        // 获取已存在的地市计划
        List<Mc04IslmSalePlanModel> existingCityPlans = salePlanRepository.getAllotPlanDataByMultiParam(
                mc04SalePlanId, YearPlanAllotProvinceStatusEnum.CITY_PENDING.getCode(), cityList, cgtSaleFoPeriodCode, YearPlanPeriodTypeEnum.QUARTER.getCode(), YearPlanOrgTypeEnum.CITY.getKind());

        // 为每个地市创建计划主表记录 (仅创建不存在的)
        List<Mc04IslmSalePlanModel> cityPlanModels = new ArrayList<>();
        Map<String, String> cityPlanIdMap = new HashMap<>();
        // 如果已存在计划，如果是空计划，在第一次插入时就会把所有地市插入，所以不会存在空缺，有就全有
        if (!existingCityPlans.isEmpty()) {
            // 先将已存在的计划ID放入映射中
            for (Mc04IslmSalePlanModel existingPlan : existingCityPlans) {
                cityPlanIdMap.put(existingPlan.getMc04OrgTypeCode(), existingPlan.getMc04SalePlanId());
            }
        } else { // 空计划就新增
            buildCityPlanDatas(cityCodes, salePlanModel, cityMap, cgtSaleFoPeriodCode, cityPlanModels, cityPlanIdMap);
            // 批量保存地市计划主表
            saveStatus = saveCityQuarterPlanModels(cityPlanModels, cityPlanIdMap);
        }
        // 获取需要更新详情的所有计划ID
        List<String> allCityPlanIds = new ArrayList<>(cityPlanIdMap.values());
        // 删除已存在的计划详情数据
        if (!allCityPlanIds.isEmpty()) {
            salePlanItemRepository.deleteBatchBySalePlanId(allCityPlanIds);
        }

        // 获取产品数据
        Collection<IccProductDetailDTO> productList = getProductList();
        List<Mc04IslmSalePlanItemModel> planItems = new ArrayList<>();
        // 收集所有数据，按产品和地市分组
        Map<String, Map<String, Map<String, Object>>> productCityDataMap = new HashMap<>();
        collectProductCityData(dataList, productCityDataMap);
        // 构建地市计划明细数据
        buildCityPlanItemDatas(productCityDataMap, planItems, cityPlanIdMap, productList);
        // 批量保存计划明细
        if (!planItems.isEmpty()) {
            saveStatus = saveStatus && saveCityPlanItems(planItems);
        }
        return saveStatus;
    }

    public boolean saveCityPlanItems(List<Mc04IslmSalePlanItemModel> planItems) {
        if (!planItems.isEmpty()) {
            return salePlanItemRepository.saveBatch(planItems);
        }
        return true;
    }

    /**
     * 获取地市上一季度期末库存数据
     * @param mc04SalePlanId 分解计划ID
     * @param cityList 地市列表
     * @param previousQuarterPeriod 上一季度周期代码（如：2025Q1）
     * @return 上一季度的地市计划明细数据列表
     */
    private List<YearPlanAllotProvince> getPreviousQuarterCityStockData(
            String mc04SalePlanId,
            List<BusiComDto> cityList,
            String previousQuarterPeriod) {

        List<YearPlanAllotProvince> result = new ArrayList<>();

        // 获取上一季度的计划数据
        List<Mc04IslmSalePlanModel> preQuarterCityPlans = salePlanRepository.getAllotPlanDataByMultiParam(
                mc04SalePlanId,
                YearPlanAllotProvinceStatusEnum.CITY_PENDING.getCode(),
                cityList,
                previousQuarterPeriod,
                YearPlanPeriodTypeEnum.QUARTER.getCode(),
                YearPlanOrgTypeEnum.CITY.getKind());

        if (!preQuarterCityPlans.isEmpty()) {
            // 获取计划ID列表
            List<String> preQuarterPlanIds = preQuarterCityPlans.stream()
                    .map(Mc04IslmSalePlanModel::getMc04SalePlanId)
                    .collect(Collectors.toList());

            // 获取上一季度的计划明细数据
            List<Mc04IslmSalePlanItemModel> preQuarterCitySalePlanItemModels =
                    salePlanItemRepository.getAllotProvinceDetailData(preQuarterPlanIds);

            // 建立计划ID到计划实体的映射
            Map<String, Mc04IslmSalePlanModel> planIdToPlanMap = preQuarterCityPlans.stream()
                    .collect(Collectors.toMap(Mc04IslmSalePlanModel::getMc04SalePlanId, p -> p));

            // 转换为YearPlanAllotProvince类型
            result = convertPlanItemsToYearPlanAllotProvince(
                    preQuarterCitySalePlanItemModels, planIdToPlanMap);
        }

        return result;
    }

    /**
     * 将上一季度的计划明细数据转换为YearPlanAllotProvince列表（完整版）
     * @param preQuarterCitySalePlanItemModels 上一季度的地市计划明细数据
     * @param planIdToPlanMap 计划ID到计划实体的映射
     * @return List<YearPlanAllotProvince>
     */
    private List<YearPlanAllotProvince> convertPlanItemsToYearPlanAllotProvince(
            List<Mc04IslmSalePlanItemModel> preQuarterCitySalePlanItemModels,
            Map<String, Mc04IslmSalePlanModel> planIdToPlanMap) {

        List<YearPlanAllotProvince> result = new ArrayList<>();

        if (preQuarterCitySalePlanItemModels != null && !preQuarterCitySalePlanItemModels.isEmpty()) {
            for (Mc04IslmSalePlanItemModel item : preQuarterCitySalePlanItemModels) {
                String planId = item.getMc04SalePlanId();
                Mc04IslmSalePlanModel planModel = planIdToPlanMap.get(planId);

                if (planModel != null) {
                    YearPlanAllotProvince allotProvince = new YearPlanAllotProvince();
                    allotProvince.setProductCode(item.getAcCgtCartonCode());
                    allotProvince.setBusiComCode(planModel.getMc04OrgTypeCode());
                    allotProvince.setYearEndStock(item.getMd03Cgt10thComEndStkQty());

                    result.add(allotProvince);
                }
            }
        }

        return result;
    }

    // 辅助方法：获取上一季度的代码
    private String getPreviousQuarterCode(String currentQuarter) {
        switch (currentQuarter) {
            case "Q2":
                return "Q1";
            case "Q3":
                return "Q2";
            case "Q4":
                return "Q3";
            default:
                return "Q4";
        }
    }

    /**
     * 构建产品-地市-期末库存映射关系
     * @param allProductIncludeCityYearEndStk 包含产品、地市和期末库存信息的列表
     * @return Map<产品code, Map<地市code, 期末库存>>
     */
    private Map<String, Map<String, BigDecimal>> buildProductCityStockMap(List<YearPlanAllotProvince> allProductIncludeCityYearEndStk) {
        Map<String, Map<String, BigDecimal>> productCityStockMap = new HashMap<>();

        if (allProductIncludeCityYearEndStk != null && !allProductIncludeCityYearEndStk.isEmpty()) {
            for (YearPlanAllotProvince item : allProductIncludeCityYearEndStk) {
                String productCode = item.getProductCode();
                String cityCode = item.getBusiComCode();
                BigDecimal endStock = item.getYearEndStock();

                // 初始化产品层级映射
                productCityStockMap.computeIfAbsent(productCode, k -> new HashMap<>());

                // 设置地市和库存的映射关系
                productCityStockMap.get(productCode).put(cityCode, endStock);
            }
        }

        return productCityStockMap;
    }

    /**
     * 获取地市各品规上下半年协议全表数据
     * @param mc04SalePlanId 分解计划ID
     * @param cgtSaleFoPeriodType 周期类型
     * @return 地市各品规上下半年协议全表数据
     */
    @ReturnValue(desc = "获取地市各品规上下半年协议全表数据")
    @Method(desc = "获取地市各品规上下半年协议全表数据")
    public YearPlanAllotProvince getAllotProvinceAgreementData(String mc04SalePlanId, String cgtSaleFoPeriodType) {
        Mc04IslmSalePlanModel salePlanModel = salePlanRepository.getById(mc04SalePlanId);
        // 获取省下所有地市
        List<BusiComDto> citysInProvince = getCitysInProvince(salePlanModel.getMc04OrgTypeCode(), true);
        // 省分解计划编码，地市代码、季度信息获取省以及下属所有地市分解计划
        List<Mc04IslmSalePlanModel> cityPlanAgreementData = salePlanRepository.getAllotPlanDataByMultiParam(mc04SalePlanId, YearPlanAllotProvinceStatusEnum.CITY_PENDING.getCode(), citysInProvince, "", cgtSaleFoPeriodType, YearPlanOrgTypeEnum.CITY.getKind());
        List<Mc04IslmSalePlanModel> provincePlanAgreementData = salePlanRepository.getAllotPlanDataByMultiParam(mc04SalePlanId, YearPlanAllotProvinceStatusEnum.CITY_PENDING.getCode(), citysInProvince, "", cgtSaleFoPeriodType, YearPlanOrgTypeEnum.PROVINCE.getKind());
//        List<Mc04IslmSalePlanModel> provincePlanAgreementData = salePlanRepository.getAllotPlanDataByMultiParam(mc04SalePlanId, "22", citysInProvince, "", cgtSaleFoPeriodType, YearPlanOrgTypeEnum.PROVINCE.getKind());
        Map<String, String> provinceAgreePlanMap = provincePlanAgreementData.stream()
                .collect(Collectors.toMap(
                        Mc04IslmSalePlanModel::getMc04SalePlanId,
                        Mc04IslmSalePlanModel::getMc04CgtSaleFoPeriodCode
                ));
        provincePlanAgreementData.addAll(cityPlanAgreementData);
        // 回参 数据
        List<Map<String, Object>> resultData;
        // 获取所有商品信息, 无论有无数据，都需要根据商品构造数据体
        Collection<IccProductDetailDTO> productList = getProductList();
        if (!cityPlanAgreementData.isEmpty()) { // 如果有数据继续查询详细数据
            // 如果有数据继续查询详细数据
            List<String> cityPlanIds = provincePlanAgreementData.stream()
                    .map(Mc04IslmSalePlanModel::getMc04SalePlanId)
                    .collect(Collectors.toList());
            List<Mc04IslmSalePlanItemModel> allotProvinceDetailData = salePlanItemRepository.getAllotProvinceDetailData(cityPlanIds);
            // 组装已有数据
            resultData = assembleAgreementData(provincePlanAgreementData, allotProvinceDetailData, citysInProvince, productList);
        } else { // 计划为空需人工构造表头及数据进行返回
            resultData = constructEmptyAgreementData(salePlanModel, citysInProvince, productList, provinceAgreePlanMap);
        }
        YearPlanAllotProvince yearPlanAllotProvince = new YearPlanAllotProvince();
        yearPlanAllotProvince.setDataList(resultData);
        return yearPlanAllotProvince;
    }

        /**
         *  保存各地市各品规上下半年协议数据
         * @param mc04SalePlanId 分解计划ID
         * @param dataList  数据
         * @param isSubmit 是否提交
         * @return 保存结果
         */
    @ReturnValue(desc = "保存各地市各品规上下半年协议数据")
    @Method(name = "保存各地市各品规上下半年协议数据")
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCityPlanAgreementData(String mc04SalePlanId, List<Map<String, Object>> dataList, String isSubmit) {
        Mc04IslmSalePlanModel salePlanModel = salePlanRepository.getById(mc04SalePlanId);
        String zaOccurrenceYear = salePlanModel.getZaOccurrenceYear();
        // 获取所有地市编码
        Set<String> cityCodes = new HashSet<>();
        dataList.stream()
                .flatMap(dataMap -> dataMap.keySet().stream())
                .filter(key -> key.length() > 8)
                .map(key -> key.substring(key.length() - 8))
                .filter(cityCode -> cityCode.matches("\\d{8}"))
                .forEach(cityCodes::add);

        // 获取地市信息不包含省
        List<BusiComDto> cityList = getCitysInProvince(salePlanModel.getMc04OrgTypeCode(), false);
        Map<String, BusiComDto> cityMap = cityList.stream()
                .collect(Collectors.toMap(BusiComDto::getBaComOrgCode, city -> city));

        // 收集各地市对应的计划id
        Map<String, String> cityPlanIdMap = new HashMap<>();
        // 收集地市季度及全能计划id用于明细的先删后插
        Map<String, String> cityPlanIdWithDeleteMap = new HashMap<>();

        // 获取地市上下半年的协议计划
        List<Mc04IslmSalePlanModel> existingCityHalfYearPlans = salePlanRepository.getAllotPlanDataByMultiParam(
                mc04SalePlanId, YearPlanAllotProvinceStatusEnum.CITY_PENDING.getCode(), cityList, "", YearPlanPeriodTypeEnum.HALF_YEAR.getCode(), YearPlanOrgTypeEnum.CITY.getKind());

        List<String> cityWithT01PlanIds = new ArrayList<>();

        // 各地市上下半年协议计划实体 地市全年 + 地市上下半年
        if (existingCityHalfYearPlans.isEmpty()) {
            List<Mc04IslmSalePlanModel> cityPlanModels = new ArrayList<>();
            // 创建并保存地市上下半年及全年计划数据
            buildCityPlanHalfYearDatas(cityCodes, salePlanModel, cityMap, cityPlanModels, cityPlanIdMap, isSubmit, cityWithT01PlanIds);
            // 批量保存地市计划主表
            saveCityPlanModels(cityPlanModels, cityPlanIdMap, cityWithT01PlanIds);
        } else { // 如果非空直接查询所有的地市上下半年协议计划id
            // 获取所有地市上下半年计划并存入地市计划map集合（包含地市全年、上、下半年）
            existingCityHalfYearPlans.forEach(cityPlan -> {
                String key = cityPlan.getMc04OrgTypeCode() + cityPlan.getMc04CgtSaleFoPeriodCode().substring(4);
                cityPlanIdMap.put(key, cityPlan.getMc04SalePlanId());
                cityPlanIdWithDeleteMap.put(key, cityPlan.getMc04SalePlanId());
            });
            List<Mc04IslmSalePlanModel> CityPlansYear = salePlanRepository.getAllotPlanDataByMultiParam(
                    mc04SalePlanId, YearPlanAllotProvinceStatusEnum.CITY_PENDING.getCode(), cityList, zaOccurrenceYear, YearPlanPeriodTypeEnum.YEAR.getCode(), YearPlanOrgTypeEnum.CITY.getKind());
            cityWithT01PlanIds = CityPlansYear.stream().map(Mc04IslmSalePlanModel::getMc04SalePlanId).collect(Collectors.toList());
            CityPlansYear.forEach(cityPlan -> 
                cityPlanIdWithDeleteMap.put(
                    cityPlan.getMc04OrgTypeCode() + cityPlan.getMc04CgtSaleFoPeriodCode().substring(4), 
                    cityPlan.getMc04SalePlanId()
                )
            );
        }

        // 按产品、地市和半年度分组处理数据 productCityPeriodDataMap: <productCode,<cityCode+H1,<下转协议、销售、调拨、库存, value>>>
        Map<String, Map<String, Map<String, Object>>> productCityPeriodDataMap = new HashMap<>();
        processHalfYearAgreementData(dataList, productCityPeriodDataMap);

        // 保存省及各地市上下半年协议详情数据
        List<Mc04IslmSalePlanItemModel> planItems = new ArrayList<>();
        Collection<IccProductDetailDTO> productList = getProductList();

        // 用于存储地市上下半年数据的Map，key为"cityCode_productId"
        Map<String, Map<String, Mc04IslmSalePlanItemModel>> cityHalfYearDataMap = new HashMap<>();

        // 根据分组后的数据创建地市上下半年计划明细项
        // todo 地市上下半年协议数据校验: a)(N-1年末下转协议)+(N年上半年协议)>=(N年上半年调拨预计)；b)(N-1年末下转协议)+(N年上半年协议)-(N年上半年调拨预计)=(N年上半年下转协议)；c)	(N年上半年下转协议)+(N年下半年协议)>=(N年下半年调拨预计)；
        for (Map.Entry<String, Map<String, Map<String, Object>>> productEntry : productCityPeriodDataMap.entrySet()) {
            String productCode = productEntry.getKey();
            Map<String, Map<String, Object>> cityPeriodDataMap = productEntry.getValue();
            // 查找对应的产品信息
            IccProductDetailDTO productDto = productList.stream()
                    .filter(product -> product.getProductCode().equals(productCode))
                    .findFirst()
                    .orElse(null);
            if (productDto == null) {
                log.warn("未找到产品信息，产品代码: {}", productCode);
                continue;
            }

            // 处理地市周期数据
            processCityPeriodData(productCode, productDto, cityPeriodDataMap, cityPlanIdMap, planItems, cityHalfYearDataMap);
        }

        // 获取需要更新详情的所有计划ID
        List<String> allCityPlanIds = new ArrayList<>(cityPlanIdWithDeleteMap.values());
        // 批量保存计划明细，先删后插；删的时候也需要同步把地市全年计划详情明细删除，因为后面需要合计地市全年的计划明细保存
        if (!planItems.isEmpty()) {
            // 根据计划Id先删数据
            salePlanItemRepository.deleteBatchBySalePlanId(allCityPlanIds);
            // 保存地市上下半年计划明细
            salePlanItemRepository.saveBatch(planItems);
        }

        // 创建并保存地市全年维度的计划详情数据 供明细与地市分解使用
        List<Mc04IslmSalePlanItemModel> cityAllYearPlanItems = processCityAnnualPlans(cityWithT01PlanIds, cityHalfYearDataMap);

        // 保存地市全年维度计划明细数据
        if (!cityAllYearPlanItems.isEmpty()) {
            salePlanItemRepository.saveBatch(cityAllYearPlanItems);
            log.info("成功为 {} 个地市计划创建年度明细数据", cityAllYearPlanItems.size());
        }

        // 如果是提交操作，则修改各地市年度计划状态为：31:待分解到月份
        if ("1".equals(isSubmit)) {
            List<BusiComDto> waitUpdateCityAndProvinceList = getCitysInProvince(salePlanModel.getMc04OrgTypeCode(), true);
            // 获取所有的地市按季度、上下半年、全年划分的计划
            List<Mc04IslmSalePlanModel> allotProvinceData = salePlanRepository.getAllotPlanDataByMultiParam(mc04SalePlanId, YearPlanAllotProvinceStatusEnum.CITY_PENDING.getCode(), waitUpdateCityAndProvinceList, "", "", "");
            // 将所有返回结果的salePlanStatus统一设置为31
            for (Mc04IslmSalePlanModel planModel : allotProvinceData) {
                planModel.setMc04SalePlanStatus(YearPlanAllotProvinceStatusEnum.DRAFT.getCode());
            }
            salePlanRepository.updateBatchById(allotProvinceData);
        }
        return true;
    }

    /**
     * 获取地市分解计划详情
     * @param mc04SalePlanId 省计划ID
     * @return 年度计划分解（省）明细
     */
    @ReturnValue(desc = "获取年度计划分解（省）明细")
    @Method(name = "获取年度计划分解（省）明细")
    public YearPlanAllotProvince getAllotProvinceDetailData(String mc04SalePlanId){
        // 1. 获取省计划主体
        Mc04IslmSalePlanModel provincePlan = salePlanRepository.getById(mc04SalePlanId);
        Assert.notNull(provincePlan, "省计划数据为空，请联系管理员！");

        // 2. 获取省下所有地市
        List<BusiComDto> cityList = getCitysInProvince(provincePlan.getMc04OrgTypeCode(), true);

        // 3. 获取季度计划数据 (T03)
        List<Mc04IslmSalePlanModel> quarterlyPlans = salePlanRepository.getAllotPlanDataByMultiParam(
                mc04SalePlanId, "30", cityList, "", YearPlanPeriodTypeEnum.QUARTER.getCode(), "");

        // 4. 获取上下半年计划数据 (T02)
        List<Mc04IslmSalePlanModel> halfYearPlans = salePlanRepository.getAllotPlanDataByMultiParam(
                mc04SalePlanId, "30", cityList, "", YearPlanPeriodTypeEnum.HALF_YEAR.getCode(), "");

        // 5. 获取全年计划数据
        List<Mc04IslmSalePlanModel> yearPlans = salePlanRepository.getAllotPlanDataByMultiParam(
                mc04SalePlanId, "30", cityList, "", YearPlanPeriodTypeEnum.YEAR.getCode(), "");

        // 收集季度计划ID
        List<String> allPlanIds = quarterlyPlans.stream()
                .map(Mc04IslmSalePlanModel::getMc04SalePlanId).collect(Collectors.toList());

        // 收集上下半年计划ID
        List<String> halfYearPlanIds = halfYearPlans.stream()
                .map(Mc04IslmSalePlanModel::getMc04SalePlanId)
                .collect(Collectors.toList());
        allPlanIds.addAll(halfYearPlanIds);

        List<String> yearPlanIds = yearPlans.stream()
                .map(Mc04IslmSalePlanModel::getMc04SalePlanId)
                .collect(Collectors.toList());
        allPlanIds.addAll(yearPlanIds);

        // 6. 如果没有计划数据，返回空Map
        if (allPlanIds.isEmpty()) {
            return null;
        }

        // 7. 根据全国及地市的季度、上下半年、全年计划ID获取所有计划详情数据
        List<Mc04IslmSalePlanItemModel> planItems = salePlanItemRepository.getAllotProvinceDetailData(allPlanIds);

        // 8. 建立计划ID到计划信息的映射
        Map<String, Mc04IslmSalePlanModel> planIdToPlanMap = new HashMap<>();

        // 添加季度计划映射
        for (Mc04IslmSalePlanModel plan : quarterlyPlans) {
            planIdToPlanMap.put(plan.getMc04SalePlanId(), plan);
        }

        // 添加上下半年计划映射
        for (Mc04IslmSalePlanModel plan : halfYearPlans) {
            planIdToPlanMap.put(plan.getMc04SalePlanId(), plan);
        }

        // 添加上下半年计划映射
        for (Mc04IslmSalePlanModel plan : yearPlans) {
            planIdToPlanMap.put(plan.getMc04SalePlanId(), plan);
        }

        // 9. 按地市代码重组数据
        Map<String, Map<String, Object>> result = new HashMap<>();

        // 初始化每个地市的数据结构
        for (BusiComDto city : cityList) {
            result.put(city.getBaCityOrgCode(), new HashMap<>());
        }

        // 处理每个计划详情项
        processPlanItems(planItems, planIdToPlanMap, result);

        // 获取所有商品信息
        Collection<IccProductDetailDTO> productList = getProductList();

        // 构造最终返回结果
        List<Map<String, Object>> finalResultData = buildAllotDetailTableData(productList, cityList, result);

        YearPlanAllotProvince returnResult = new YearPlanAllotProvince();
        returnResult.setDataList(finalResultData);

        return returnResult;
    }

    /**
     * 处理计划详情项并按地市重组数据
     * @param planItems 计划详情项列表
     * @param planIdToPlanMap 计划ID到计划实体的映射
     * @param result 按地市重组的结果数据
     */
    private void processPlanItems(List<Mc04IslmSalePlanItemModel> planItems,
                                  Map<String, Mc04IslmSalePlanModel> planIdToPlanMap,
                                  Map<String, Map<String, Object>> result) {
        for (Mc04IslmSalePlanItemModel item : planItems) {
            processPlanItem(item, planIdToPlanMap, result);
        }
    }

    /**
     * 处理单个计划详情项
     * @param item 计划详情项
     * @param planIdToPlanMap 计划ID到计划实体的映射
     * @param result 按地市重组的结果数据
     */
    private void processPlanItem(Mc04IslmSalePlanItemModel item,
                                 Map<String, Mc04IslmSalePlanModel> planIdToPlanMap,
                                 Map<String, Map<String, Object>> result) {
        String planId = item.getMc04SalePlanId();
        Mc04IslmSalePlanModel plan = planIdToPlanMap.get(planId);

        if (plan == null) {
            return;
        }

        String cityCode = plan.getMc04OrgTypeCode();
        String productCode = item.getAcCgtCartonCode();
        String periodType = plan.getMc04CgtSaleFoPeriodType();
        String periodCode = plan.getMc04CgtSaleFoPeriodCode();

        Map<String, Object> cityData = result.computeIfAbsent(cityCode, k -> new HashMap<>());

        String prefix = productCode + "_";

        // 根据周期类型处理数据
        processItemByPeriodType(item, periodType, periodCode, cityData, prefix);
    }

    /**
     * 根据周期类型处理计划详情项
     * @param item 计划详情项
     * @param periodType 周期类型
     * @param periodCode 周期代码
     * @param cityData 地市数据
     */
    private void processItemByPeriodType(Mc04IslmSalePlanItemModel item,
                                                    String periodType,
                                                    String periodCode,
                                                    Map<String, Object> cityData,
                                                    String prefix) {
        if (YearPlanPeriodTypeEnum.QUARTER.getCode().equals(periodType)) {
            // 季度数据处理时加上前缀
            processQuarterlyData(cityData, item, periodCode, prefix);
        } else if (YearPlanPeriodTypeEnum.HALF_YEAR.getCode().equals(periodType)) {
            // 上下半年数据处理时加上前缀
            processHalfYearData(cityData, item, periodCode, prefix);
        } else if (YearPlanPeriodTypeEnum.YEAR.getCode().equals(periodType)) {
            // 全年数据处理时加上前缀
            processYearData(cityData, item, prefix);
        }
    }
    /**
     * 处理季度计划详情数据
     * @param cityData 地市数据Map
     * @param item 计划详情项
     * @param periodCode 周期代码
     */
    private void processQuarterlyData(Map<String, Object> cityData, Mc04IslmSalePlanItemModel item, String periodCode, String prefix) {
        String quarterCode = periodCode.substring(periodCode.length() - 2);

        // 销售计划
        cityData.put(prefix + "mc04CgtSalePlanAdjustedQty" + quarterCode, item.getMc04CgtSalePlanAdjustedQty());

        // 调拨计划
        cityData.put(prefix + "ma02CgtPlAdjustedQty" + quarterCode, item.getMa02CgtPlAdjustedQty());

    }

    /**
     * 处理上下半年计划详情数据
     * @param cityData 地市数据Map
     * @param item 计划详情项
     * @param periodCode 周期代码
     */
    private void processHalfYearData(Map<String, Object> cityData, Mc04IslmSalePlanItemModel item, String periodCode, String prefix) {
        String halfYearCode = periodCode.substring(periodCode.length() - 2);

        switch (halfYearCode) {
            case "H1":
                cityData.put(prefix + "H1mc04CgtXyCarryOverQty", item.getMc04CgtXyCarryOverQty());
                cityData.put(prefix + "H1md02CgtXyOriginalQty", item.getMd02CgtXyOriginalQty());
                cityData.put(prefix + "H1mc04CgtSalePlanAdjustedQty", item.getMc04CgtSalePlanAdjustedQty());
                cityData.put(prefix + "H1ma02CgtPlAdjustedQty", item.getMa02CgtPlAdjustedQty());
                cityData.put(prefix + "H1md03Cgt10thComEndStkQty", item.getMd03Cgt10thComEndStkQty());
                break;
            case "H2":
                cityData.put(prefix + "H2mc04CgtXyCarryOverQty", item.getMc04CgtXyCarryOverQty());
                cityData.put(prefix + "H2md02CgtXyOriginalQty", item.getMd02CgtXyOriginalQty());
                cityData.put(prefix + "H2mc04CgtSalePlanAdjustedQty", item.getMc04CgtSalePlanAdjustedQty());
                cityData.put(prefix + "H2ma02CgtPlAdjustedQty", item.getMa02CgtPlAdjustedQty());
                break;
        }
    }

    /**
     * 处理全年计划详情数据
     * @param cityData 地市数据Map
     * @param item 计划详情项
     */
    private void processYearData(Map<String, Object> cityData, Mc04IslmSalePlanItemModel item, String prefix) {

        // N年底库存
        cityData.put(prefix + "md03Cgt10thComEndStkQty", item.getMd03Cgt10thComEndStkQty());
        // N+1年度销售计划
        cityData.put(prefix + "mc04CgtSalePlanAdjustedQty", item.getMc04CgtSalePlanAdjustedQty());
        // N+1年度调拨计划
        cityData.put(prefix + "ma02CgtPlAdjustedQty", item.getMa02CgtPlAdjustedQty());
        // N+1年预计库存
        cityData.put(prefix + "md03Cgt10thComInitStkQty", item.getMd03Cgt10thComInitStkQty());
    }

    /**
     * 构建地市分解计划详情数据
     * @param productList 产品列表
     * @param cityList 地市列表
     * @param result 地市数据映射
     * @return 构建后的结果数据列表
     */
    private List<Map<String, Object>> buildAllotDetailTableData(
            Collection<IccProductDetailDTO> productList,
            List<BusiComDto> cityList,
            Map<String, Map<String, Object>> result) {

        List<Map<String, Object>> finalResultData = new ArrayList<>();

        // 为每个商品创建一行数据
        for (IccProductDetailDTO product : productList) {
            Map<String, Object> rowData = buildProductRowData(product, cityList, result);
            finalResultData.add(rowData);
        }

        return finalResultData;
    }

    /**
     * 构建单个产品的行数据
     * @param product 产品信息
     * @param cityList 地市列表
     * @param result 地市数据映射
     * @return 产品行数据
     */
    private Map<String, Object> buildProductRowData(
            IccProductDetailDTO product,
            List<BusiComDto> cityList,
            Map<String, Map<String, Object>> result) {

        Map<String, Object> rowData = new HashMap<>();
        String productCode = product.getProductCode();
        String prefix = productCode + "_";

        rowData.put("acCgtCode", product.getProductCode());
        rowData.put("acCgtName", product.getProductName());

        BigDecimal taxAllotPrice = new BigDecimal(product.getAcMateTaxTranPr())
                .divide(new BigDecimal(product.getPackageQty2()), 6, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("0"));
        rowData.put("acCgtTaxAllotPrice", taxAllotPrice);

        BigDecimal cgtTradePrice = new BigDecimal(product.getWholeSalePrice())
                .divide(new BigDecimal(product.getPackageQty2()), 6, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("0"));
        rowData.put("acCgtTradePrice", cgtTradePrice);

        // 为每个地市设置相关数据
        for (BusiComDto city : cityList) {
            buildCityColumnData(rowData, result, city, prefix);
        }
        return rowData;
    }

    /**
     * 构建地市列数据
     * @param rowData 行数据
     * @param result 地市数据映射
     * @param city 地市信息
     */
    private void buildCityColumnData(
            Map<String, Object> rowData,
            Map<String, Map<String, Object>> result,
            BusiComDto city,
            String prefix) {

        Map<String, Object> cityData = result.get(city.getBaCityOrgCode());
        String cityCode = city.getBaCityOrgCode();

        if (cityData != null) {
            // 年度合计数据
            rowData.put("md03Cgt10thComEndStkQty" + cityCode,
                    cityData.getOrDefault(prefix + "md03Cgt10thComEndStkQty", ""));
            rowData.put("mc04CgtSalePlanAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "mc04CgtSalePlanAdjustedQty", ""));
            rowData.put("ma02CgtPlAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "ma02CgtPlAdjustedQty", ""));
            rowData.put("md03Cgt10thComInitStkQty" + cityCode,
                    cityData.getOrDefault(prefix + "md03Cgt10thComInitStkQty", ""));

            // 上半年数据
            rowData.put("H1mc04CgtXyCarryOverQty" + cityCode,
                    cityData.getOrDefault(prefix + "H1mc04CgtXyCarryOverQty", ""));
            rowData.put("H1md02CgtXyOriginalQty" + cityCode,
                    cityData.getOrDefault(prefix + "H1md02CgtXyOriginalQty", ""));
            rowData.put("H1mc04CgtSalePlanAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "H1mc04CgtSalePlanAdjustedQty", ""));
            rowData.put("H1ma02CgtPlAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "H1ma02CgtPlAdjustedQty", ""));
            rowData.put("H1md03Cgt10thComEndStkQty" + cityCode,
                    cityData.getOrDefault(prefix + "H1md03Cgt10thComEndStkQty", ""));

            // 下半年数据
            rowData.put("H2mc04CgtXyCarryOverQty" + cityCode,
                    cityData.getOrDefault(prefix + "H2mc04CgtXyCarryOverQty", ""));
            rowData.put("H2mc04CgtSalePlanAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "H2mc04CgtSalePlanAdjustedQty", ""));
            rowData.put("H2ma02CgtPlAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "H2ma02CgtPlAdjustedQty", ""));
            rowData.put("H2md02CgtXyOriginalQty" + cityCode,
                    cityData.getOrDefault(prefix + "H2md02CgtXyOriginalQty", ""));

            // 季度数据
            rowData.put("Q1mc04CgtSalePlanAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "mc04CgtSalePlanAdjustedQtyQ1", ""));
            rowData.put("Q1ma02CgtPlAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "ma02CgtPlAdjustedQtyQ1", ""));
            rowData.put("Q2mc04CgtSalePlanAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "mc04CgtSalePlanAdjustedQtyQ2", ""));
            rowData.put("Q2ma02CgtPlAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "ma02CgtPlAdjustedQtyQ2", ""));
            rowData.put("Q3mc04CgtSalePlanAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "mc04CgtSalePlanAdjustedQtyQ3", ""));
            rowData.put("Q3ma02CgtPlAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "ma02CgtPlAdjustedQtyQ3", ""));
            rowData.put("Q4mc04CgtSalePlanAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "mc04CgtSalePlanAdjustedQtyQ4", ""));
            rowData.put("Q4ma02CgtPlAdjustedQty" + cityCode,
                    cityData.getOrDefault(prefix + "ma02CgtPlAdjustedQtyQ4", ""));
        } else {
            // 如果没有数据，设置默认空值
            for (String fieldSuffix : Arrays.asList(
                    "md03Cgt10thComEndStkQty", "mc04CgtSalePlanAdjustedQty", "ma02CgtPlAdjustedQty",
                    "md03Cgt10thComInitStkQty", "mc04CgtXyCarryOverQty", "H1md02CgtXyOriginalQty",
                    "H1mc04CgtSalePlanAdjustedQty", "H1ma02CgtPlAdjustedQty", "H1md03Cgt10thComEndStkQty",
                    "H2mc04CgtXyCarryOverQty", "H2mc04CgtSalePlanAdjustedQty", "H2ma02CgtPlAdjustedQty",
                    "H2md02CgtXyOriginalQty", "Q1mc04CgtSalePlanAdjustedQty", "Q1ma02CgtPlAdjustedQty",
                    "Q2mc04CgtSalePlanAdjustedQty", "Q2ma02CgtPlAdjustedQty", "Q3mc04CgtSalePlanAdjustedQty",
                    "Q3ma02CgtPlAdjustedQty", "Q4mc04CgtSalePlanAdjustedQty", "Q4ma02CgtPlAdjustedQty"
            )) {
                rowData.put(fieldSuffix + cityCode, "");
            }
        }
    }

    @ReturnValue(name = "提交年度计划地市审核结果")
    @Method(name = "提交年度计划地市审核分解")
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitCityAgreeData(@Parameter(name = "年度计划分解(省)编制对象" , required = true) Mc04IslmSalePlanModel salePlanModel,
                          @Parameter(name = "分解(省)编制明细" , required = true) List<Mc04IslmSalePlanItemModel> salePlanItemModelList) {
        boolean saveStatus = salePlanItemRepository.saveBatch(salePlanItemModelList);
        salePlanModel.setMc04SalePlanStatus(YearPlanAllotProvinceStatusEnum.PROVINCIAL_PENDING.getCode());
        boolean updateStatus = salePlanRepository.updateById(salePlanModel);
        // 提交时需要同步修改地市对应季度与上下半年计划状态，保持状态同步
        List<Mc04IslmSalePlanModel> allotCityData = salePlanRepository.getAllotPlanDataByMultiParam(salePlanModel.getMc04SalePlanId(), YearPlanAllotProvinceStatusEnum.DRAFT.getCode(), null, "", "", YearPlanOrgTypeEnum.CITY.getKind());
        for (Mc04IslmSalePlanModel cityPlan : allotCityData){
            cityPlan.setMc04SalePlanStatus(YearPlanAllotProvinceStatusEnum.PROVINCIAL_PENDING.getCode());
        }
        return saveStatus && updateStatus && salePlanRepository.updateBatchById(allotCityData);
    }

    @ReturnValue(name = "提交年度计划地市审核驳回")
    @Method(name = "提交年度计划地市审核驳回")
    @Transactional(rollbackFor = Exception.class)
    public Boolean rejectCityAgreeData(@Parameter(name = "年度计划分解(省)编制对象" , required = true) Mc04IslmSalePlanModel salePlanModel,
                                   @Parameter(name = "分解(省)编制明细" , required = true) List<Mc04IslmSalePlanItemModel> salePlanItemModelList) {
        boolean saveStatus = salePlanItemRepository.saveBatch(salePlanItemModelList);
        salePlanModel.setMc04SalePlanStatus(YearPlanAllotProvinceStatusEnum.DRAFT.getCode());
        return saveStatus && salePlanRepository.updateById(salePlanModel);
    }

    @ReturnValue(name = "保存年度计划（省） 地市分解审核详情结果")
    @Method(name = "保存年度计划（省）地市分解审核详情")
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCityAgreeData(@Parameter(name = "分解(省)编制审核明细" , required = true) List<Mc04IslmSalePlanItemModel> salePlanItemModelList) {
        return salePlanItemRepository.saveBatch(salePlanItemModelList);
    }

    @ReturnValue(name = "获取省下地市分解计划")
    @Method(name = "获取省下地市分解计划")
    public List<IslmSalePlanDto> getCityAllotPlanByProvincePlanId(@Parameter(name = "省分解计划Id" , required = true) String  salePlanId) {
        Mc04IslmSalePlanModel provincePlan = salePlanRepository.getById(salePlanId);
        List<BusiComDto> citysInProvince = getCitysInProvince(provincePlan.getMc04OrgTypeCode(), false);
        List<Mc04IslmSalePlanModel> cityPlansInProvince = salePlanRepository.getAllotPlanDataByMultiParam(salePlanId, "", citysInProvince, "", YearPlanPeriodTypeEnum.YEAR.getCode(), YearPlanOrgTypeEnum.CITY.getKind());
        return IslmSalePlanConverter.INSTANCE.converterModelsToDtos(cityPlansInProvince);
    }


    @ReturnValue(name = "获取地市分解审核状态")
    @Method(name = "获取地市分解审核状态")
    public List<TreeStatus> getCityAllotPlanStatusList(@Parameter(name = "省分解计划Id" , required = true) String planId, @Parameter(name = "业务年份" , required = true) String zaOccurrenceYear){
        List<Mc04IslmSalePlanModel> allotProvinceData = salePlanRepository.getAllotPlanDataByMultiParam(planId, "", null, zaOccurrenceYear, YearPlanPeriodTypeEnum.YEAR.getCode(), YearPlanOrgTypeEnum.CITY.getKind());
        return allotProvinceData.stream().map(plan -> new TreeStatus(plan.getMc04OrgTypeCode(), plan.getMc04SalePlanStatus())).collect(Collectors.toList());
    }

    /**
     * 根据省code获取下属所有地市
     * @param provinceCode 省code
     * @param isIncludeProvince 是否包含省
     * @return List<BusiComDto>
     */
    public List<BusiComDto> getCitysInProvince(String provinceCode, boolean isIncludeProvince) {
        List<String> provinceCodes = new ArrayList<>();
        provinceCodes.add(provinceCode);
        GetBusiComListREQ q = new GetBusiComListREQ();
        if (!isIncludeProvince) {
            q.setMc04ComOrgLevel("02");
        }
        q.setPcomCodeArr(provinceCodes);
        q.setIcomCode(IcomUtils.getIcomCode());
        KCMultiResponse<BusiComDto> comList = comServiceAPI.getBusiComList(q);
        return new ArrayList<>(comList.getData().getItems());
    }

    /**
     * 获取所有商品信息
     * @return Collection<IccProductDetailDTO>
     */
    private Collection<IccProductDetailDTO> getProductList() {
        IccGetProductListRequest iccGetProductListRequest = new IccGetProductListRequest();
        String icomCode = IcomUtils.getIcomCode();
        iccGetProductListRequest.setProviderCustomerId(Collections.singletonList(icomCode));
        iccGetProductListRequest.setAcOneLevelClassTypeCode(Collections.singletonList("01"));
        iccGetProductListRequest.setAcCigSalFlag(Collections.singletonList("1"));
        iccGetProductListRequest.setAcCigBusiTypeCode("A");
        IccMultiResponse<IccProductDetailDTO> productListResponse =
                productServiceApi.getProductList(iccGetProductListRequest);
        Assert.notNull(productListResponse, () -> new CustomException("商品中心获取商品信息失败"));
        Assert.isTrue(productListResponse.isSuccess(), () -> {
            log.error("商品中心获取商品信息失败, 商品中心返回失败信息:{}", productListResponse.getMessage());
            return new CustomException("商品中心获取商品信息失败");
        });
        IccMultiDataDTO<IccProductDetailDTO> data = productListResponse.getData();
        Assert.notNull(data, () -> new CustomException("商品中心获取商品信息失败"));
        return data.getItems();
    }

    /**
     * 组装各品规各地市按季度分解的数据体
     */
    private List<Map<String, Object>> assembleExistingData(
            List<Mc04IslmSalePlanModel> allotProvinceData,
            List<Mc04IslmSalePlanItemModel> allotProvinceDetailData,
            List<BusiComDto> citysInProvince,
            Collection<IccProductDetailDTO> productList,
            Map<String, Map<String, BigDecimal>> productCityStockMap) {

        List<Map<String, Object>> resultData = new ArrayList<>();

        // 按地市分组主计划数据
        Map<String, Mc04IslmSalePlanModel> planByCity = allotProvinceData.stream()
                .collect(Collectors.toMap(Mc04IslmSalePlanModel::getMc04OrgTypeCode, m -> m));

        // 按商品分组数据，key为商品名，value为该商品在各地市的计划数据
        Map<String, Map<String, Mc04IslmSalePlanItemModel>> productItemMap = new HashMap<>();
        for (Mc04IslmSalePlanItemModel item : allotProvinceDetailData) {
            String productCode= item.getAcCgtCartonCode();
            String planId = item.getMc04SalePlanId();

            productItemMap.computeIfAbsent(productCode, k -> new HashMap<>());
            productItemMap.get(productCode).put(planId, item);
        }

        // todo 已过完季度取实际发生量，如何获取实际值？
        // 为每个商品创建一行数据
        for (IccProductDetailDTO product : productList) {
            Map<String, Object> rowData = new HashMap<>();
            rowData.put("acCgtCode", product.getProductCode());
            rowData.put("acCgtName", product.getProductName());
            BigDecimal taxAllotPrice = new BigDecimal(product.getAcMateTaxTranPr())
                    .divide(new BigDecimal(product.getPackageQty2()), 6, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("200"));
            rowData.put("acCgtTaxAllotPrice", taxAllotPrice);
            BigDecimal cgtTradePrice = new BigDecimal(product.getWholeSalePrice())
                    .divide(new BigDecimal(product.getPackageQty2()), 6, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("200"));
            rowData.put("acCgtTradePrice", cgtTradePrice);

            // 获取该商品在各地市的期初库存量
            Map<String, BigDecimal> cityInitStk = productCityStockMap.get(product.getProductCode());

            // 为每个地市设置计划量和调拨量
            for (BusiComDto city : citysInProvince) {
                Mc04IslmSalePlanModel plan = planByCity.get(city.getBaComOrgCode());
                Map<String, Mc04IslmSalePlanItemModel> itemsByPlan = productItemMap.get(product.getProductCode());
                fillCityData(rowData, city, plan, itemsByPlan, cityInitStk);
//                if (plan != null) {
//                    String planId = plan.getMc04SalePlanId();
//                    Map<String, Mc04IslmSalePlanItemModel> itemsByPlan = productItemMap.get(product.getProductCode());
//
//                    // 提前写入期初库存
//                    if (cityInitStk != null && cityInitStk.containsKey(city.getBaComOrgCode())) {
//                        BigDecimal bigDecimal = cityInitStk.get(city.getBaComOrgCode()) != null ? cityInitStk.get(city.getBaComOrgCode()) : BigDecimal.ZERO;
//                        rowData.put("md03Cgt10thComInitStkQty" + city.getBaComOrgCode(), bigDecimal);
//                    } else {
//                        rowData.put("md03Cgt10thComInitStkQty" + city.getBaComOrgCode(), "0");
//                    }
//
//                    if (itemsByPlan != null && itemsByPlan.containsKey(planId)) {
//                        Mc04IslmSalePlanItemModel item = itemsByPlan.get(planId);
//                        rowData.put("mc04CgtSalePlanAdjustedQty" + city.getBaComOrgCode(), item.getMc04CgtSalePlanAdjustedQty());
//                        rowData.put("ma02CgtPlAdjustedQty" + city.getBaComOrgCode(), item.getMa02CgtPlAdjustedQty());
//                    } else {
//                        rowData.put("mc04CgtSalePlanAdjustedQty" + city.getBaComOrgCode(), "0");
//                        rowData.put("ma02CgtPlAdjustedQty" + city.getBaComOrgCode(), "0");
//                    }
//                } else {
//                    rowData.put("mc04CgtSalePlanAdjustedQty" + city.getBaComOrgCode(), "0");
//                    rowData.put("ma02CgtPlAdjustedQty" + city.getBaComOrgCode(), "0");
//                    rowData.put("md03Cgt10thComInitStkQty" + city.getBaComOrgCode(), "0");
//                }
            }

            resultData.add(rowData);
        }

        return resultData;
    }

    /**
     * 填充地市季度销调、库存实际数据
     * @param rowData 行数据
     * @param city 地市
     * @param plan 计划
     * @param itemsByPlan 地市计划数据
     * @param cityInitStk 地市期初库存
     */
    private void fillCityData(
            Map<String, Object> rowData,
            BusiComDto city,
            Mc04IslmSalePlanModel plan,
            Map<String, Mc04IslmSalePlanItemModel> itemsByPlan,
            Map<String, BigDecimal> cityInitStk) {

        String cityCode = city.getBaComOrgCode();

        // 填充期初库存
        BigDecimal initStk = (cityInitStk != null && cityInitStk.containsKey(cityCode))
                ? cityInitStk.getOrDefault(cityCode, BigDecimal.ZERO)
                : BigDecimal.ZERO;
        rowData.put("md03Cgt10thComInitStkQty" + cityCode, initStk);

        // 填充销售与调拨计划
        if (plan != null && itemsByPlan != null && itemsByPlan.containsKey(plan.getMc04SalePlanId())) {
            Mc04IslmSalePlanItemModel item = itemsByPlan.get(plan.getMc04SalePlanId());
            rowData.put("mc04CgtSalePlanAdjustedQty" + cityCode, item.getMc04CgtSalePlanAdjustedQty());
            rowData.put("ma02CgtPlAdjustedQty" + cityCode, item.getMa02CgtPlAdjustedQty());
        } else {
            rowData.put("mc04CgtSalePlanAdjustedQty" + cityCode, BigDecimal.ZERO);
            rowData.put("ma02CgtPlAdjustedQty" + cityCode, BigDecimal.ZERO);
        }
    }

    /**
     * 构造空数据结构
     */
    private List<Map<String, Object>> constructEmptyData(
            Mc04IslmSalePlanModel salePlanModel,
            List<BusiComDto> citysInProvince,
            Collection<IccProductDetailDTO> productList,
            String quarter,
            Map<String, Map<String, BigDecimal>> productCityStockMap) {

        List<Map<String, Object>> yearPlanAllotProvinceItemList = new ArrayList<>();

        // 一次性获取省级各品规的季度分解明细
        Map<String, Mc04IslmSalePlanItemModel> productProvinceQuarterData = getProductProvinceQuarterData(salePlanModel, citysInProvince, quarter);

        // 为每个商品创建一行空数据
        for (IccProductDetailDTO product : productList) {
            Map<String, Object> rowData = new HashMap<>();
            rowData.put("acCgtCode", product.getProductCode());
            rowData.put("acCgtName", product.getProductName());
            BigDecimal taxAllotPrice = new BigDecimal(product.getAcMateTaxTranPr())
                    .divide(new BigDecimal(product.getPackageQty2()), 6, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("200"));
            rowData.put("acCgtTaxAllotPrice", taxAllotPrice);
            BigDecimal cgtTradePrice = new BigDecimal(product.getWholeSalePrice())
                    .divide(new BigDecimal(product.getPackageQty2()), 6, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("200"));
            rowData.put("acCgtTradePrice", cgtTradePrice);

            // 获取该商品在各地市的期初库存量
            Map<String, BigDecimal> cityInitStk = productCityStockMap.get(product.getProductCode());

            // 组装该省的总计数据 todo 待省份季度分解做完后放开注释，对于省份x季度的数据是可以直接获取的
            assert productProvinceQuarterData != null;
            Mc04IslmSalePlanItemModel item = productProvinceQuarterData.get(product.getProductCode());
            if (item != null) {
                rowData.put("mc04CgtSalePlanAdjustedQty" + salePlanModel.getMc04OrgTypeCode(), item.getMc04CgtSalePlanAdjustedQty());
                rowData.put("ma02CgtPlAdjustedQty" + salePlanModel.getMc04OrgTypeCode(), item.getMa02CgtPlAdjustedQty());
                rowData.put("md03Cgt10thComInitStkQty" + salePlanModel.getMc04OrgTypeCode(), "0");
            } else {
                rowData.put("mc04CgtSalePlanAdjustedQty" + salePlanModel.getMc04OrgTypeCode(), "");
                rowData.put("ma02CgtPlAdjustedQty" + salePlanModel.getMc04OrgTypeCode(), "");
                rowData.put("md03Cgt10thComInitStkQty" + salePlanModel.getMc04OrgTypeCode(), "0");
            }

            // 遍历省下各地市，组装地市销售计划与调拨计划
            for (BusiComDto city : citysInProvince) {
//                if (city.getBaComOrgCode().equals(salePlanModel.getMc04OrgTypeCode())){
//                    continue;
//                }
//                // 提前写入期初库存
//                if (cityInitStk != null && cityInitStk.containsKey(city.getBaComOrgCode())) {
//                    BigDecimal bigDecimal = cityInitStk.get(city.getBaComOrgCode()) != null ? cityInitStk.get(city.getBaComOrgCode()) : BigDecimal.ZERO;
//                    rowData.put("md03Cgt10thComInitStkQty" + city.getBaComOrgCode(), bigDecimal);
//                } else {
//                    rowData.put("md03Cgt10thComInitStkQty" + city.getBaComOrgCode(), "0");
//                }
//                rowData.put("mc04CgtSalePlanAdjustedQty" + ("".equals(city.getBaCityOrgCode()) ? city.getBaComOrgCode() : city.getBaCityOrgCode()), "");
//                rowData.put("ma02CgtPlAdjustedQty" + ("".equals(city.getBaCityOrgCode()) ? city.getBaComOrgCode() : city.getBaCityOrgCode()), "");

                // 优化后
                String cityCode = city.getBaComOrgCode();

                // 跳过省份本身
                if (cityCode.equals(salePlanModel.getMc04OrgTypeCode())) {
                    continue;
                }

                // 填充期初库存
                BigDecimal initStk = BigDecimal.ZERO;
                if (cityInitStk != null && cityInitStk.containsKey(cityCode)) {
                    initStk = cityInitStk.getOrDefault(cityCode, BigDecimal.ZERO);
                }
                rowData.put("md03Cgt10thComInitStkQty" + cityCode, initStk);

                // 确定目标地市编码
                String targetCityCode = city.getBaCityOrgCode();
                if ("".equals(targetCityCode)) {
                    targetCityCode = cityCode;
                }

                // 填充销售计划和调拨计划的空值
                rowData.put("mc04CgtSalePlanAdjustedQty" + targetCityCode, "");
                rowData.put("ma02CgtPlAdjustedQty" + targetCityCode, "");
            }

            yearPlanAllotProvinceItemList.add(rowData);
        }

        return yearPlanAllotProvinceItemList;
    }

    /**
     * 获取省级各品规的季度分解明细
     * @param salePlanModel 分解计划
     * @param citysInProvince 地市
     * @param quarter 季度
     * @return 省级各品规的季度分解
     */
    private Map<String, Mc04IslmSalePlanItemModel> getProductProvinceQuarterData(Mc04IslmSalePlanModel salePlanModel, List<BusiComDto> citysInProvince, String quarter) {
        // 一次性获取所有省份季度数据（移到循环外面）
        List<Mc04IslmSalePlanModel> provinceQuarterData = salePlanRepository.getAllotPlanDataByMultiParam(
                salePlanModel.getMc04SalePlanId(),
                salePlanModel.getMc04SalePlanStatus(),
                citysInProvince,
                quarter,
                YearPlanPeriodTypeEnum.QUARTER.getCode(),
                salePlanModel.getMc04OrgTypeKind());

        // 如果查询结果异常，提前处理
        if (provinceQuarterData.size() != 1) {
            Assert.notNull(provinceQuarterData, () -> new CustomException("省份当前季度计划数据查询异常，请联系管理员！"));
        }

        // 如果有数据，一次性获取所有计划详情
        List<Mc04IslmSalePlanItemModel> mc04IslmSalePlanItemModels = new ArrayList<>();
        if (!provinceQuarterData.isEmpty()) {
            mc04IslmSalePlanItemModels = salePlanItemRepository.listByPlanId(provinceQuarterData.get(0).getMc04SalePlanId());
        }

        // 按产品代码建立映射，提高查找效率
        Map<String, Mc04IslmSalePlanItemModel> productItemMap = new HashMap<>();
        for (Mc04IslmSalePlanItemModel item : mc04IslmSalePlanItemModels) {
            productItemMap.put(item.getAcCgtCartonCode(), item);
        }
        return productItemMap;
    }

    /**
     * 收集所有数据，按产品和地市分组
     * @param dataList 原始数据列表
     * @param productCityDataMap 结果映射
     */
    private void collectProductCityData(List<Map<String, Object>> dataList,
                                        Map<String, Map<String, Map<String, Object>>> productCityDataMap) {
        for (Map<String, Object> dataMap : dataList) {
            String productCode = (String) dataMap.get("acCgtCode");
            collectDataMapEntries(dataMap, productCode, productCityDataMap);
        }
    }

    /**
     * 收集单个数据映射中的条目
     * @param dataMap 数据映射
     * @param productCode 产品代码
     * @param productCityDataMap 结果映射
     */
    private void collectDataMapEntries(Map<String, Object> dataMap, String productCode,
                                       Map<String, Map<String, Map<String, Object>>> productCityDataMap) {
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 标准化值，将空值、空字符串、空格等转换为"0"
            String normalizedValue = "0";
            if (value != null && !value.toString().isEmpty() && !value.toString().trim().isEmpty() &&
                    !"null".equalsIgnoreCase(value.toString().trim())) {
                normalizedValue = value.toString();
            }

            if (key.length() > 8 && key.matches(".*\\d{8}")) {
                String fieldName = key.substring(0, key.length() - 8);
                String cityCode = key.substring(key.length() - 8);
                // 初始化嵌套Map结构
                productCityDataMap.computeIfAbsent(productCode, k -> new HashMap<>());
                productCityDataMap.get(productCode).computeIfAbsent(cityCode, k -> new HashMap<>());
                productCityDataMap.get(productCode).get(cityCode).put(fieldName, normalizedValue);
            }
        }
    }

    /**
     * 保存地市计划模型并更新映射关系
     * @param cityPlanModels 地市计划模型列表
     * @param cityPlanIdMap 地市计划ID映射
     */
    public boolean saveCityQuarterPlanModels(List<Mc04IslmSalePlanModel> cityPlanModels,
                                              Map<String, String> cityPlanIdMap) {
        if (!cityPlanModels.isEmpty()) {
            boolean saveResult = salePlanRepository.saveBatch(cityPlanModels);
            if (saveResult) {
                // 清空原来的映射
                cityPlanIdMap.clear();
                // 重新建立映射关系
                for (Mc04IslmSalePlanModel savedPlan : cityPlanModels) {
                    cityPlanIdMap.put(savedPlan.getMc04OrgTypeCode(), savedPlan.getMc04SalePlanId());
                }
            }
            return saveResult;
        }
        return false;
    }

    /**
     * 构建地市计划明细数据
     * @param productCityDataMap 产品地市数据映射
     * @param planItems 计划明细项列表
     * @param cityPlanIdMap 地市计划ID映射
     * @param productList 产品列表
     */
    private void buildCityPlanItemDatas(Map<String, Map<String, Map<String, Object>>> productCityDataMap,
                                        List<Mc04IslmSalePlanItemModel> planItems,
                                        Map<String, String> cityPlanIdMap,
                                        Collection<IccProductDetailDTO> productList){
        // 根据分组后的数据创建计划明细项
        for (Map.Entry<String, Map<String, Map<String, Object>>> productEntry : productCityDataMap.entrySet()) {
            processProductEntry(productEntry, planItems, cityPlanIdMap, productList);
        }
    }

    /**
     * 用于封装累计值的简单数据类
     */
    private static class PlanTotals {
        BigDecimal productCitySaleTotal = BigDecimal.ZERO;
        BigDecimal productCityAllotTotal = BigDecimal.ZERO;
        BigDecimal provinceSalePlanQty = BigDecimal.ZERO;
        BigDecimal provinceAllotPlanQty = BigDecimal.ZERO;
    }

    /**
     * 处理单个产品条目
     * @param productEntry 产品条目
     * @param planItems 计划明细项列表
     * @param cityPlanIdMap 地市计划ID映射
     * @param productList 产品列表
     */
    private void processProductEntry(Map.Entry<String, Map<String, Map<String, Object>>> productEntry,
                                     List<Mc04IslmSalePlanItemModel> planItems,
                                     Map<String, String> cityPlanIdMap,
                                     Collection<IccProductDetailDTO> productList) {
        String productCode = productEntry.getKey();
        Map<String, Map<String, Object>> cityDataMap = productEntry.getValue();

        IccProductDetailDTO productDto = productList.stream()
                .filter(product -> product.getProductCode().equals(productCode))
                .findFirst()
                .orElse(null);
        Assert.notNull(productDto, "没有找到对应的卷烟信息: " + productCode);

        // 为每个产品统计地市总计
        PlanTotals totals = new PlanTotals();

        // 处理城市数据
        for (Map.Entry<String, Map<String, Object>> cityEntry : cityDataMap.entrySet()) {
            processCityEntry(cityEntry, planItems, cityPlanIdMap, productDto, totals);
        }

        // todo 验证地市计划总量是否等于省计划总量，校验期末库存不为负数（期初+调拨-销售=期末）
        // if (totals.productCitySaleTotal.compareTo(totals.provinceSalePlanQty) != 0) {
        //     log.warn("产品 {} 销售计划合计量({})与省计划量({})不一致", productCode, totals.productCitySaleTotal, totals.provinceSalePlanQty);
        // }
        // if (totals.productCityAllotTotal.compareTo(totals.provinceAllotPlanQty) != 0) {
        //     log.warn("产品 {} 调拨计划合计量({})与省计划量({})不一致", productCode, totals.productCityAllotTotal, totals.provinceAllotPlanQty);
        // }
    }

    /**
     * 处理单个城市条目
     */
    private void processCityEntry(Map.Entry<String, Map<String, Object>> cityEntry,
                                  List<Mc04IslmSalePlanItemModel> planItems,
                                  Map<String, String> cityPlanIdMap,
                                  IccProductDetailDTO productDto,
                                  PlanTotals totals) {
        String cityCode = cityEntry.getKey();
        Map<String, Object> fieldDataMap = cityEntry.getValue();

        String cityPlanId = cityPlanIdMap.get(cityCode);

        if (cityPlanId != null && !cityPlanId.isEmpty()) {
            // 处理地市计划数据
            processCityPlanData(cityPlanId, fieldDataMap, planItems, productDto, totals, cityCode);
        } else {
            // 处理省计划数据
            processProvincePlanData(fieldDataMap, totals);
        }
    }

    /**
     * 处理地市计划数据
     */
    private void processCityPlanData(String cityPlanId,
                                     Map<String, Object> fieldDataMap,
                                     List<Mc04IslmSalePlanItemModel> planItems,
                                     IccProductDetailDTO productDto,
                                     PlanTotals totals,
                                     String cityCode) {
        Mc04IslmSalePlanItemModel item = new Mc04IslmSalePlanItemModel();
        String planItemId = IDUtils.randomUUID(32);
        item.setMc04SalePlanItemId(planItemId);
        item.setMc04SalePlanId(cityPlanId);
        item.setAcCgtName(productDto.getProductName());
        item.setAcCgtCartonCode(productDto.getProductCode());
        item.setAcCgtPriceSegmentCode(productDto.getAcCigSegCode());
        item.setAcCgtPriceSegmentName(productDto.getAcCigSegName());
        item.setAcCgtTaxAllotPrice(new BigDecimal(productDto.getAcMateTaxTranPr()));
        item.setAcCgtTradePrice(new BigDecimal(productDto.getWholeSalePrice()));
        item.setAcCgtMediumBranceFlag(productDto.getIsMedium());
        item.setAcCgtTinyFlag(productDto.getIsTiny());
        item.setAcCgtTarVal(new BigDecimal(productDto.getTarQuantity()));

        // 设置销售计划、调拨计划、期初期末库存数据
        Object salePlanValue = fieldDataMap.get("mc04CgtSalePlanAdjustedQty");
        Object allotPlanValue = fieldDataMap.get("ma02CgtPlAdjustedQty");
        Object comInitStkValue = fieldDataMap.get("md03Cgt10thComInitStkQty");

        BigDecimal salePlanQty = BigDecimal.ZERO;
        BigDecimal allotPlanQty = BigDecimal.ZERO;
        BigDecimal comInitStk = BigDecimal.ZERO;

        if (salePlanValue != null && !salePlanValue.toString().isEmpty()) {
            salePlanQty = new BigDecimal(salePlanValue.toString());
            item.setMc04CgtSalePlanAdjustedQty(salePlanQty);
        }
        if (allotPlanValue != null && !allotPlanValue.toString().isEmpty()) {
            allotPlanQty = new BigDecimal(allotPlanValue.toString());
            item.setMa02CgtPlAdjustedQty(allotPlanQty);
        }

        // 期末库存校验: 期初库存 + 调拨 - 销售 >= 0
        if (comInitStkValue != null && !comInitStkValue.toString().isEmpty()) {
            comInitStk = new BigDecimal(comInitStkValue.toString());
            BigDecimal currentCityEndStk = comInitStk.add(allotPlanQty).subtract(salePlanQty);
            if (currentCityEndStk.compareTo(BigDecimal.ZERO) < 0) {
                throw new CustomException(String.format(
                        "保存失败：卷烟[%s]在地市[%s]的期末库存不能为负数！期初库存：%s，计算期末库存：%s",
                        productDto.getProductName(),
                        cityCode,
                        comInitStk,
                        currentCityEndStk
                ));
            } else {
                item.setMd03Cgt10thComInitStkQty(comInitStk);
                item.setMd03Cgt10thComEndStkQty(currentCityEndStk);
            }
            item.setMa02CgtPlAdjustedQty(allotPlanQty);
        }

        planItems.add(item);

        // 累加到产品地市总量统计中
        totals.productCitySaleTotal = totals.productCitySaleTotal.add(salePlanQty);
        totals.productCityAllotTotal = totals.productCityAllotTotal.add(allotPlanQty);
    }

    /**
     * 处理省计划数据
     */
    private void processProvincePlanData(Map<String, Object> fieldDataMap,
                                         PlanTotals totals) {
        // 如果cityPlanId为空，说明这是省计划的数据
        Object salePlanValue = fieldDataMap.get("mc04CgtSalePlanAdjustedQty");
        Object allotPlanValue = fieldDataMap.get("ma02CgtPlAdjustedQty");

        if (salePlanValue != null && !salePlanValue.toString().isEmpty()) {
            totals.provinceSalePlanQty = new BigDecimal(salePlanValue.toString());
        }
        if (allotPlanValue != null && !allotPlanValue.toString().isEmpty()) {
            totals.provinceAllotPlanQty = new BigDecimal(allotPlanValue.toString());
        }
    }

    /**
     *  创建地市计划数据
     * @param cityCodes 地市code
     * @param salePlanModel 父级计划
     * @param cityMap 地市信息
     * @param cgtSaleFoPeriodCode 周期
     * @param cityPlanModels 地市计划
     * @param cityPlanIdMap 地市计划ID
     */
    private void buildCityPlanDatas(Set<String> cityCodes, Mc04IslmSalePlanModel salePlanModel,
                                    Map<String, BusiComDto> cityMap,
                                    String cgtSaleFoPeriodCode,
                                    List<Mc04IslmSalePlanModel> cityPlanModels,
                                    Map<String, String> cityPlanIdMap){
        for (String cityCode : cityCodes) {
            // 跳过省份
            if (salePlanModel.getMc04OrgTypeCode().equals(cityCode)) {
                continue;
            }

            Mc04IslmSalePlanModel cityPlan = new Mc04IslmSalePlanModel();
            BusiComDto cityInfo = cityMap.get(cityCode);
            Assert.notNull(cityInfo, "城市信息不存在!");

            // 设置地市计划基本信息(含省一级总计计划)
            String planId = IDUtils.randomUUID(32);
            cityPlan.setMc04SalePlanId(planId);
            cityPlan.setMc04OrgTypeCode(cityInfo.getBaComOrgCode());
            cityPlan.setMc04OrgTypeName(cityInfo.getMc04ComOrgShortName());
            cityPlan.setZaOccurrenceYear(salePlanModel.getZaOccurrenceYear());
            cityPlan.setMc04SalePlanStatus(YearPlanAllotProvinceStatusEnum.CITY_PENDING.getCode());
            cityPlan.setMa02TobaProdTradeTypeCode(ProdTradeTypeCodeEnum.PROD_TRADE_TYPE_CODE_0.getCode());
            cityPlan.setMc04CgtSalePlanVersion(salePlanModel.getMc04CgtSalePlanVersion());
            cityPlan.setMc04IsLastestVersion("1");
            cityPlan.setMc04PlanSubjectType(salePlanModel.getMc04PlanSubjectType());
            cityPlan.setMc04PlanSubjectName(salePlanModel.getMc04PlanSubjectName());
            cityPlan.setMc04PlanSubjectBeginDate(salePlanModel.getMc04PlanSubjectBeginDate());
            cityPlan.setMc04PlanSubjectEndDate(salePlanModel.getMc04PlanSubjectEndDate());
            cityPlan.setMc04CgtSaleFoPeriodType(YearPlanPeriodTypeEnum.QUARTER.getCode());
            cityPlan.setMc04CgtSaleFoPeriodCode(cgtSaleFoPeriodCode);
            cityPlan.setMc04OrgTypeKind(YearPlanOrgTypeEnum.CITY.getKind());
            cityPlanModels.add(cityPlan);
            cityPlanIdMap.put(cityCode, cityPlan.getMc04SalePlanId());
        }
    }

    /**
     * 组装各品规各地市上下半年协议数据
     * @param allotProvinceData 省分解计划
     * @param allotProvinceDetailData 省分解计划明细
     * @param citysInProvince 地市
     * @param productList 品规
     * @return 列表
     */
    private List<Map<String, Object>> assembleAgreementData(
            List<Mc04IslmSalePlanModel> allotProvinceData,
            List<Mc04IslmSalePlanItemModel> allotProvinceDetailData,
            List<BusiComDto> citysInProvince,
            Collection<IccProductDetailDTO> productList) {

        List<Map<String, Object>> resultData = new ArrayList<>();

        // 获取业务年份
        String zaOccurrenceYear = allotProvinceData.get(0).getZaOccurrenceYear();

        // 按地市分组主计划数据，但是key需要复合用地市代码加周期代码，因为一个地市有两个计划（上/下半年）(planModel.orgTypeCode + planModel.periodCode -> planModel)
        Map<String, Mc04IslmSalePlanModel> planByCity = allotProvinceData.stream()
                .collect(Collectors.toMap(
                        m -> m.getMc04OrgTypeCode() + "_" + m.getMc04CgtSaleFoPeriodCode(),
                        m -> m
                ));

        // 按卷烟代码分组数据，key为卷烟代码，value为该卷烟在各地市的计划数据
        Map<String, Map<String, Mc04IslmSalePlanItemModel>> productItemMap =  buildProductItemMap(allotProvinceDetailData);

        // 为每个商品创建一行数据
        for (IccProductDetailDTO product : productList) {
            Map<String, Object> rowData = createProductBaseRowData(product);
            fillCityAgreementData(rowData, product, citysInProvince, planByCity, productItemMap, zaOccurrenceYear);
            resultData.add(rowData);
        }

        return resultData;
    }

    /**
     * 构建产品项映射
     */
    private Map<String, Map<String, Mc04IslmSalePlanItemModel>> buildProductItemMap(List<Mc04IslmSalePlanItemModel> allotProvinceDetailData) {
        Map<String, Map<String, Mc04IslmSalePlanItemModel>> productItemMap = new HashMap<>();
        for (Mc04IslmSalePlanItemModel item : allotProvinceDetailData) {
            String productCode = item.getAcCgtCartonCode();
            String planId = item.getMc04SalePlanId();

            productItemMap.computeIfAbsent(productCode, k -> new HashMap<>());
            productItemMap.get(productCode).put(planId, item);
        }
        return productItemMap;
    }

    /**
     * 创建产品基础行数据
     */
    private Map<String, Object> createProductBaseRowData(IccProductDetailDTO product) {
        Map<String, Object> rowData = new HashMap<>();
        rowData.put("acCgtCode", product.getProductCode());
        rowData.put("acCgtName", product.getProductName());
        BigDecimal taxAllotPrice = new BigDecimal(product.getAcMateTaxTranPr())
                .divide(new BigDecimal(product.getPackageQty2()), 6, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("200"));
        rowData.put("acCgtTaxAllotPrice", taxAllotPrice);
        BigDecimal cgtTradePrice = new BigDecimal(product.getWholeSalePrice())
                .divide(new BigDecimal(product.getPackageQty2()), 6, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("200"));
        rowData.put("acCgtTradePrice", cgtTradePrice);
        return rowData;
    }

    /**
     * 填充地市协议数据
     */
    private void fillCityAgreementData(Map<String, Object> rowData,
                                       IccProductDetailDTO product,
                                       List<BusiComDto> citysInProvince,
                                       Map<String, Mc04IslmSalePlanModel> planByCity,
                                       Map<String, Map<String, Mc04IslmSalePlanItemModel>> productItemMap,
                                       String zaOccurrenceYear) {
        // 为每个地市设置相关协议数据
        for (BusiComDto city : citysInProvince) {
            // 地市代码 + 上下半年协议code才能确定一条计划主体
            String[] periodCode = new String[]{"H1", "H2"};
            for (String period : periodCode) {
                Mc04IslmSalePlanModel plan = planByCity.get(city.getBaComOrgCode() + "_" + zaOccurrenceYear + period);
                if (plan != null) {
                    String planId = plan.getMc04SalePlanId();
                    Map<String, Mc04IslmSalePlanItemModel> itemsByPlan = productItemMap.get(product.getProductCode());
                    if (itemsByPlan != null && itemsByPlan.containsKey(planId)) {
                        Mc04IslmSalePlanItemModel item = itemsByPlan.get(planId);
                        rowData.put(period + "mc04CgtXyCarryOverQty" + city.getBaComOrgCode(), item.getMc04CgtXyCarryOverQty());
                        rowData.put(period + "md02CgtXyOriginalQty" + city.getBaComOrgCode(), item.getMd02CgtXyOriginalQty());
                        rowData.put(period + "mc04CgtSalePlanAdjustedQty" + city.getBaComOrgCode(), item.getMc04CgtSalePlanAdjustedQty());
                        rowData.put(period + "ma02CgtPlAdjustedQty" + city.getBaComOrgCode(), item.getMa02CgtPlAdjustedQty());
                        rowData.put(period + "md03Cgt10thComEndStkQty" + city.getBaComOrgCode(), item.getMd03Cgt10thComEndStkQty());
                    }
                }
            }
        }
    }

    /**
     * 构造地市上下半年协议空数据结构
     */
    private List<Map<String, Object>> constructEmptyAgreementData(
            Mc04IslmSalePlanModel salePlanModel,
            List<BusiComDto> citysInProvince,
            Collection<IccProductDetailDTO> productList,
            Map<String, String> provinceAgreePlanMap) {

        List<Map<String, Object>> yearPlanAllotProvinceAgreementItemList = new ArrayList<>();

        // 获取省计划详情数据
        Map<String, Mc04IslmSalePlanItemModel> provincePlanItemMap = new HashMap<>();
        List<String> planIds = new ArrayList<>(provinceAgreePlanMap.keySet());
        List<Mc04IslmSalePlanItemModel> provincePlanItems = salePlanItemRepository.listByPlanIds(planIds);
        // 按产品代码分组省计划详情数据
        for (Mc04IslmSalePlanItemModel item : provincePlanItems) {
            provincePlanItemMap.put(item.getAcCgtCartonCode() + "_" + provinceAgreePlanMap.get(item.getMc04SalePlanId()), item);
        }
        // 获取地市季度分解计划详情,提取销售计划与调拨计划数据
        List<Mc04IslmSalePlanModel> cityQuarterAllotPlanData = salePlanRepository.getAllotPlanDataByMultiParam(salePlanModel.getMc04SalePlanId(), YearPlanAllotProvinceStatusEnum.CITY_PENDING.getCode(), citysInProvince, "", YearPlanPeriodTypeEnum.QUARTER.getCode(), YearPlanOrgTypeEnum.CITY.getKind());
        List<String> cityPlanQuarterIds = cityQuarterAllotPlanData.stream().map(Mc04IslmSalePlanModel::getMc04SalePlanId).collect(Collectors.toList());
        List<Mc04IslmSalePlanItemModel> cityPlanQuarterItems = salePlanItemRepository.listByPlanIds(cityPlanQuarterIds);
        Map<String, Map<String, Map<String, Mc04IslmSalePlanItemModel>>> productCityQuarterMap = new HashMap<>();

        // 获取n年末商业库存
        List<String> cityFoYearStk = citysInProvince.stream()
                .map(BusiComDto::getBaComOrgCode)
                .filter(cityCode -> !salePlanModel.getMc04OrgTypeCode().equals(cityCode))
                .collect(Collectors.toList());
        String year = salePlanModel.getZaOccurrenceYear();
        String previousYear = String.valueOf(Integer.parseInt(year) - 1);
        List<YearPlanAllotProvince> allProductIncludeCityYearEndStk = null;
        // 只有1季度需要取上一年的期末库存作为初始库存，其他季度取上一季度的季末库存作为初始库存进行判断
        allProductIncludeCityYearEndStk = allotProvinceRepository.getAllProductIncludeCityYearEndStk(previousYear, cityFoYearStk);
        // 构建产品-地市-库存映射
        Map<String, Map<String, BigDecimal>> productCityStockMap = buildProductCityStockMap(allProductIncludeCityYearEndStk);


        for (Mc04IslmSalePlanItemModel item : cityPlanQuarterItems) {
            // 通过planId找到对应的父计划，获取地市代码和季度信息
            String planId = item.getMc04SalePlanId();
            Mc04IslmSalePlanModel parentPlan = cityQuarterAllotPlanData.stream()
                    .filter(plan -> plan.getMc04SalePlanId().equals(planId))
                    .findFirst()
                    .orElse(null);

            if (parentPlan != null) {
                String productCode = item.getAcCgtCartonCode();
                String cityCode = parentPlan.getMc04OrgTypeCode();
                String quarterCode = parentPlan.getMc04CgtSaleFoPeriodCode().substring(4);

                // 初始化三层Map结构
                productCityQuarterMap.computeIfAbsent(productCode, k -> new HashMap<>());
                productCityQuarterMap.get(productCode).computeIfAbsent(cityCode, k -> new HashMap<>());

                // 存储计划详情项
                productCityQuarterMap.get(productCode).get(cityCode).put(quarterCode, item);
            }
        }
        // 为每个商品创建一行空数据
        for (IccProductDetailDTO product : productList) {
            Map<String, Object> rowData = new HashMap<>();
            rowData.put("acCgtCode", product.getProductCode());
            rowData.put("acCgtName", product.getProductName());
            BigDecimal taxAllotPrice = new BigDecimal(product.getAcMateTaxTranPr())
                    .divide(new BigDecimal(product.getPackageQty2()), 6, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("200"));
            rowData.put("acCgtTaxAllotPrice", taxAllotPrice);
            BigDecimal cgtTradePrice = new BigDecimal(product.getWholeSalePrice())
                    .divide(new BigDecimal(product.getPackageQty2()), 6, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("200"));
            rowData.put("acCgtTradePrice", cgtTradePrice);

            // 当前产品地市上年末库存对象
            Map<String, BigDecimal> stringBigDecimalMap = productCityStockMap.get(product.getProductCode());

            // 遍历各地市，组装各地市上下半年协议量并自动计算销售计划与调拨计划
            buildProductAgreeRowData(product.getProductCode(), salePlanModel.getMc04OrgTypeCode(), salePlanModel.getZaOccurrenceYear(), provincePlanItemMap, citysInProvince, rowData, productCityQuarterMap, stringBigDecimalMap);

            yearPlanAllotProvinceAgreementItemList.add(rowData);
        }

        return yearPlanAllotProvinceAgreementItemList;
    }

    /**
     * 构造地市各品规上下半年协议数据
     */
    private void buildProductAgreeRowData(String productCode, String provinceCode, String zaOccurrenceYear, Map<String, Mc04IslmSalePlanItemModel> provincePlanItemMap, List<BusiComDto> citysInProvince, Map<String, Object> rowData, Map<String, Map<String, Map<String, Mc04IslmSalePlanItemModel>>> productCityQuarterMap, Map<String, BigDecimal> stringBigDecimalMap){
        for (BusiComDto city : citysInProvince) {
            String cityCode = city.getBaComOrgCode();
            if (city.getBaComOrgCode().equals(provinceCode)) {
                Mc04IslmSalePlanItemModel mc04IslmSalePlanItemModelH1 = provincePlanItemMap.get(productCode + "_" + zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H1.getCode());
                rowData.put("H1mc04CgtXyCarryOverQty" + city.getBaComOrgCode(), mc04IslmSalePlanItemModelH1 != null ? (mc04IslmSalePlanItemModelH1.getMc04CgtXyCarryOverQty() != null ? mc04IslmSalePlanItemModelH1.getMc04CgtXyCarryOverQty() : BigDecimal.ZERO) : BigDecimal.ZERO);
                rowData.put("H1md02CgtXyOriginalQty" + city.getBaComOrgCode(), mc04IslmSalePlanItemModelH1 != null ? (mc04IslmSalePlanItemModelH1.getMd02CgtXyOriginalQty() != null ? mc04IslmSalePlanItemModelH1.getMd02CgtXyOriginalQty() : BigDecimal.ZERO) : BigDecimal.ZERO);
                rowData.put("H1mc04CgtSalePlanAdjustedQty" + city.getBaComOrgCode(), mc04IslmSalePlanItemModelH1 != null ? (mc04IslmSalePlanItemModelH1.getMc04CgtSalePlanAdjustedQty() != null ? mc04IslmSalePlanItemModelH1.getMc04CgtSalePlanAdjustedQty() : BigDecimal.ZERO) : BigDecimal.ZERO);
                rowData.put("H1ma02CgtPlAdjustedQty" + city.getBaComOrgCode(), mc04IslmSalePlanItemModelH1 != null ? (mc04IslmSalePlanItemModelH1.getMa02CgtPlAdjustedQty() != null ? mc04IslmSalePlanItemModelH1.getMa02CgtPlAdjustedQty() : BigDecimal.ZERO) : BigDecimal.ZERO);
                rowData.put("H1md03Cgt10thComEndStkQty" + city.getBaComOrgCode(), mc04IslmSalePlanItemModelH1 != null ? (mc04IslmSalePlanItemModelH1.getMd03Cgt10thComEndStkQty() != null ? mc04IslmSalePlanItemModelH1.getMd03Cgt10thComEndStkQty() : BigDecimal.ZERO) : BigDecimal.ZERO);
                Mc04IslmSalePlanItemModel mc04IslmSalePlanItemModelH2 = provincePlanItemMap.get(productCode + "_" + zaOccurrenceYear + YearPlanPeriodHalfYearEnum.HALF_YEAR_H2.getCode());
                rowData.put("H2mc04CgtXyCarryOverQty" + city.getBaComOrgCode(), mc04IslmSalePlanItemModelH2 != null ? (mc04IslmSalePlanItemModelH2.getMc04CgtXyCarryOverQty() != null ? mc04IslmSalePlanItemModelH2.getMc04CgtXyCarryOverQty() : BigDecimal.ZERO) : BigDecimal.ZERO);
                rowData.put("H2md02CgtXyOriginalQty" + city.getBaComOrgCode(), mc04IslmSalePlanItemModelH2 != null ? (mc04IslmSalePlanItemModelH2.getMd02CgtXyOriginalQty() != null ? mc04IslmSalePlanItemModelH2.getMd02CgtXyOriginalQty() : BigDecimal.ZERO) : BigDecimal.ZERO);
                rowData.put("H2mc04CgtSalePlanAdjustedQty" + city.getBaComOrgCode(), mc04IslmSalePlanItemModelH2 != null ? (mc04IslmSalePlanItemModelH2.getMc04CgtSalePlanAdjustedQty() != null ? mc04IslmSalePlanItemModelH2.getMc04CgtSalePlanAdjustedQty() : BigDecimal.ZERO) : BigDecimal.ZERO);
                rowData.put("H2ma02CgtPlAdjustedQty" + city.getBaComOrgCode(),  mc04IslmSalePlanItemModelH2 != null ? (mc04IslmSalePlanItemModelH2.getMa02CgtPlAdjustedQty() != null ? mc04IslmSalePlanItemModelH2.getMa02CgtPlAdjustedQty() : BigDecimal.ZERO) : BigDecimal.ZERO);
                rowData.put("H2md03Cgt10thComEndStkQty" + city.getBaComOrgCode(), mc04IslmSalePlanItemModelH2 != null ? (mc04IslmSalePlanItemModelH2.getMd03Cgt10thComEndStkQty() != null ? mc04IslmSalePlanItemModelH2.getMd03Cgt10thComEndStkQty() : BigDecimal.ZERO) : BigDecimal.ZERO);
            } else {
                BigDecimal cityH1EndStockQty = BigDecimal.ZERO;
                if (stringBigDecimalMap != null && stringBigDecimalMap.containsKey(cityCode)) {
                    cityH1EndStockQty = stringBigDecimalMap.get(cityCode) != null ? stringBigDecimalMap.get(cityCode) : BigDecimal.ZERO;
                 }
                // 构造地市协议数据，需从地市季度数据中合计销售与调拨值
                buildCityQuarterFoAgreeData(productCode, cityCode, rowData, productCityQuarterMap, cityH1EndStockQty);
            }
        }
    }

    /**
     * 构造地市协议并合并计算季度销售与调拨值
     * @param productCode 商品编码
     * @param cityCode 地市编码
     * @param rowData 行数据
     * @param productCityQuarterMap 地市各品规各季度数据
     */
    private void buildCityQuarterFoAgreeData(String productCode, String cityCode, Map<String, Object> rowData,
                                             Map<String, Map<String, Map<String, Mc04IslmSalePlanItemModel>>> productCityQuarterMap, BigDecimal cityH1EndStockQty) {
        Map<String, Map<String, Mc04IslmSalePlanItemModel>> cityQuarterMap = productCityQuarterMap.get(productCode);

        if (cityQuarterMap != null && cityQuarterMap.containsKey(cityCode)) {
            Map<String, Mc04IslmSalePlanItemModel> quarterMap = cityQuarterMap.get(cityCode);

            // 计算上半年合计 (Q1 + Q2)
            BigDecimal firstHalfSaleTotal = BigDecimal.ZERO;
            BigDecimal firstHalfAllotTotal = BigDecimal.ZERO;

            for (String quarter : Arrays.asList("Q1", "Q2")) {
                Mc04IslmSalePlanItemModel item = quarterMap.get(quarter);
                if (item != null) {
                    firstHalfSaleTotal = firstHalfSaleTotal.add(item.getMc04CgtSalePlanAdjustedQty());
                    firstHalfAllotTotal = firstHalfAllotTotal.add(item.getMa02CgtPlAdjustedQty());
                }
            }

            // 计算下半年合计 (Q3 + Q4)
            BigDecimal secondHalfSaleTotal = BigDecimal.ZERO;
            BigDecimal secondHalfAllotTotal = BigDecimal.ZERO;

            for (String quarter : Arrays.asList("Q3", "Q4")) {
                Mc04IslmSalePlanItemModel item = quarterMap.get(quarter);
                if (item != null) {
                    secondHalfSaleTotal = secondHalfSaleTotal.add(item.getMc04CgtSalePlanAdjustedQty());
                    secondHalfAllotTotal = secondHalfAllotTotal.add(item.getMa02CgtPlAdjustedQty());
                }
            }

            // 设置上半年数据
            rowData.put("H1mc04CgtXyCarryOverQty" + cityCode, "0");
            rowData.put("H1md02CgtXyOriginalQty" + cityCode, "0");
            rowData.put("H1mc04CgtSalePlanAdjustedQty" + cityCode, firstHalfSaleTotal.toString());
            rowData.put("H1ma02CgtPlAdjustedQty" + cityCode, firstHalfAllotTotal.toString());
            BigDecimal h1EndStockQty = cityH1EndStockQty.add(firstHalfAllotTotal).subtract(firstHalfSaleTotal);
            rowData.put("H1md03Cgt10thComEndStkQty" + cityCode, h1EndStockQty);

            // 设置下半年数据
            rowData.put("H2mc04CgtXyCarryOverQty" + cityCode, "0");
            rowData.put("H2md02CgtXyOriginalQty" + cityCode, "0");
            rowData.put("H2mc04CgtSalePlanAdjustedQty" + cityCode, secondHalfSaleTotal.toString());
            rowData.put("H2ma02CgtPlAdjustedQty" + cityCode, secondHalfAllotTotal.toString());
            rowData.put("H2md03Cgt10thComEndStkQty" + cityCode, h1EndStockQty.add(secondHalfAllotTotal).subtract(secondHalfSaleTotal));
        } else {
            // 如果没有找到季度数据，使用默认值
            rowData.put("H1mc04CgtXyCarryOverQty" + cityCode, "0");
            rowData.put("H1md02CgtXyOriginalQty" + cityCode, "0");
            rowData.put("H1mc04CgtSalePlanAdjustedQty" + cityCode, "20");
            rowData.put("H1ma02CgtPlAdjustedQty" + cityCode, "100");
            rowData.put("H1md03Cgt10thComEndStkQty" + cityCode, "0");
            rowData.put("H2mc04CgtXyCarryOverQty" + cityCode, "0");
            rowData.put("H2md02CgtXyOriginalQty" + cityCode, "0");
            rowData.put("H2mc04CgtSalePlanAdjustedQty" + cityCode, "20");
            rowData.put("H2ma02CgtPlAdjustedQty" + cityCode, "100");
            rowData.put("H2md03Cgt10thComEndStkQty" + cityCode, "0");
        }
    }

    private boolean saveCityPlanModels(List<Mc04IslmSalePlanModel> cityPlanModels,
                                       Map<String, String> cityPlanIdMap,
                                       List<String> cityWithT01PlanIds) {
        if (!cityPlanModels.isEmpty()) {
            boolean saveResult = salePlanRepository.saveBatch(cityPlanModels);
            if (saveResult) {
                rebuildCityPlanMappings(cityPlanModels, cityPlanIdMap, cityWithT01PlanIds);
            }
            return saveResult;
        }
        return false;
    }

    private void rebuildCityPlanMappings(List<Mc04IslmSalePlanModel> cityPlanModels,
                                         Map<String, String> cityPlanIdMap,
                                         List<String> cityWithT01PlanIds) {
        // 清空原来的映射
        cityPlanIdMap.clear();
        cityWithT01PlanIds.clear();
        // 重新建立映射关系
        for (Mc04IslmSalePlanModel savedPlan : cityPlanModels) {
            if (YearPlanPeriodTypeEnum.YEAR.getCode().equals(savedPlan.getMc04CgtSaleFoPeriodType())) {
                cityWithT01PlanIds.add(savedPlan.getMc04SalePlanId());
            } else {
                cityPlanIdMap.put(savedPlan.getMc04OrgTypeCode() + savedPlan.getMc04CgtSaleFoPeriodCode().substring(4),
                        savedPlan.getMc04SalePlanId());
            }
        }
    }

    /**
     * 处理地市上下半年协议数据项
     * @param productCode 产品代码
     * @param productDto 产品信息
     * @param cityPeriodDataMap 地市周期数据映射
     * @param cityPlanIdMap 地市计划ID映射
     * @param planItems 计划项列表
     * @param cityHalfYearDataMap 地市上下半年数据映射
     */
    private void processCityPeriodData(String productCode,
                                       IccProductDetailDTO productDto,
                                       Map<String, Map<String, Object>> cityPeriodDataMap,
                                       Map<String, String> cityPlanIdMap,
                                       List<Mc04IslmSalePlanItemModel> planItems,
                                       Map<String, Map<String, Mc04IslmSalePlanItemModel>> cityHalfYearDataMap) {
        // 遍历每个地市和周期的数据
        for (Map.Entry<String, Map<String, Object>> cityPeriodEntry : cityPeriodDataMap.entrySet()) {
            String cityPeriodKey = cityPeriodEntry.getKey(); // 格式：cityCode + periodCode
            Map<String, Object> fieldDataMap = cityPeriodEntry.getValue();

            // 从key中提取地市代码和周期代码
            String cityCode = cityPeriodKey.substring(0, 8);
            String periodCode = cityPeriodKey.substring(8);

            // 获取对应的计划ID
            String cityPlanId = cityPlanIdMap.get(cityPeriodKey);
            if (cityPlanId == null) {
                log.warn("未找到地市计划ID，地市代码: {}，周期代码: {}", cityCode, periodCode);
                continue;
            }

            // 创建每个产品对应每个地市上、下半年计划明细项
            Mc04IslmSalePlanItemModel item = buildPlanItem(productDto, cityPlanId, fieldDataMap);

            planItems.add(item);

            // 存储上下半年数据到Map中，key为"<cityCode_productCode，<periodCode, Mc04IslmSalePlanItemModel>>"
            String cityProductKey = cityCode + "_" + productCode;
            Map<String, Mc04IslmSalePlanItemModel> halfYearData = cityHalfYearDataMap.computeIfAbsent(cityProductKey, k -> new HashMap<>());
            halfYearData.put(periodCode, item); // 以periodCode(H1/H2)为key存储对应的item
        }
    }

    /**
     * 构建计划明细项
     * @param productDto 产品信息
     * @param cityPlanId 地市计划ID
     * @param fieldDataMap 字段数据映射
     * @return 计划明细项
     */
    private Mc04IslmSalePlanItemModel buildPlanItem(IccProductDetailDTO productDto,
                                                    String cityPlanId,
                                                    Map<String, Object> fieldDataMap) {
        Mc04IslmSalePlanItemModel item = new Mc04IslmSalePlanItemModel();
        String planId = IDUtils.randomUUID(32);
        item.setMc04SalePlanItemId(planId);
        item.setMc04SalePlanId(cityPlanId);
        item.setAcCgtName(productDto.getProductName());
        item.setAcCgtCartonCode(productDto.getProductCode());
        item.setAcCgtPriceSegmentCode(productDto.getAcCigSegCode());
        item.setAcCgtPriceSegmentName(productDto.getAcCigSegName());
        item.setAcCgtTaxAllotPrice(new BigDecimal(productDto.getAcMateTaxTranPr()));
        item.setAcCgtTradePrice(new BigDecimal(productDto.getWholeSalePrice()));
        item.setAcCgtMediumBranceFlag(productDto.getIsMedium());
        item.setAcCgtTinyFlag(productDto.getIsTiny());
        item.setAcCgtTarVal(new BigDecimal(productDto.getTarQuantity()));
        item.setMc04CgtSalePlanAdjustedQty(getBigDecimalValue(fieldDataMap,"mc04CgtSalePlanAdjustedQty"));
        item.setMa02CgtPlAdjustedQty(getBigDecimalValue(fieldDataMap,"ma02CgtPlAdjustedQty"));
        item.setMd03Cgt10thComEndStkQty(getBigDecimalValue(fieldDataMap, "md03Cgt10thComEndStkQty"));

        // 设置各个字段的值，默认为0
        item.setMc04CgtXyCarryOverQty(getBigDecimalValue(fieldDataMap, "mc04CgtXyCarryOverQty"));
        item.setMd02CgtXyOriginalQty(getBigDecimalValue(fieldDataMap, "md02CgtXyOriginalQty"));

        return item;
    }

    /**
     * 处理并分组上下半年协议数据
     * @param dataList 原始数据列表
     * @param productCityPeriodDataMap 用于存储分组后的数据
     */
    private void processHalfYearAgreementData(List<Map<String, Object>> dataList,
                                              Map<String, Map<String, Map<String, Object>>> productCityPeriodDataMap) {
        for (Map<String, Object> dataMap : dataList) {
            String productCode = (String) dataMap.get("acCgtCode");

            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                processAgreementDataEntry(entry, productCode, productCityPeriodDataMap);
            }
        }
    }

    /**
     * 处理单个协议数据项
     * @param entry 数据项
     * @param productCode 产品代码
     * @param productCityPeriodDataMap 数据存储Map
     */
    private void processAgreementDataEntry(Map.Entry<String, Object> entry,
                                           String productCode,
                                           Map<String, Map<String, Map<String, Object>>> productCityPeriodDataMap) {
        String key = entry.getKey();
        Object value = entry.getValue();

        // 跳过产品名称字段
        if (isProductInfoField(key)) {
            return;
        }

        // 检查key的格式是否符合要求
        if (!isValidAgreementKey(key)) {
            return;
        }

        try {
            // 解析key的各个部分
            Map<String, String> keyInfo = parseAgreementKey(key);
            if (keyInfo == null) {
                return;
            }

            // 验证地市代码和周期代码
            if (!isValidCityCode(keyInfo.get("cityCode")) || !isValidPeriodCode(keyInfo.get("periodCode"))) {
                return;
            }

            // 初始化嵌套Map结构：产品 -> (地市+周期) -> 字段
            initializeDataMapStructure(productCode, keyInfo, productCityPeriodDataMap);

            // 存储字段值
            String fieldValue = getFieldValue(value);
            productCityPeriodDataMap.get(productCode)
                    .get(keyInfo.get("cityCode") + keyInfo.get("periodCode"))
                    .put(keyInfo.get("fieldName"), fieldValue);

        } catch (StringIndexOutOfBoundsException e) {
            // 如果key格式不正确，跳过该条数据
            log.warn("Key格式不正确，跳过处理: {}", key);
        }
    }

    /**
     * 判断是否为产品信息字段
     * @param key 字段名
     * @return 是否为产品信息字段
     */
    private boolean isProductInfoField(String key) {
        return "acCgtName".equals(key) || "acCgtCode".equals(key)
                || "acCgtTaxAllotPrice".equals(key) || "acCgtTradePrice".equals(key);
    }

    /**
     * 判断协议key是否有效
     * @param key 字段名
     * @return 是否有效
     */
    private boolean isValidAgreementKey(String key) {
        // 检查key的格式是否符合要求（至少需要11位：8位地市代码+至少1位字段名+2位半年度标识）
        return key.length() >= 11;
    }


    /**
     * 判断地市代码是否有效
     * @param cityCode 地市代码
     * @return 是否有效
     */
    private boolean isValidCityCode(String cityCode) {
        return cityCode.matches("\\d{8}");
    }

    /**
     * 判断周期代码是否有效
     * @param periodCode 周期代码
     * @return 是否有效
     */
    private boolean isValidPeriodCode(String periodCode) {
        return "H1".equals(periodCode) || "H2".equals(periodCode);
    }

    /**
     * 解析协议key
     * @param key 字段名
     * @return 包含解析信息的Map，包含cityCode、fieldName、periodCode三个键
     */
    private Map<String, String> parseAgreementKey(String key) {
        try {
            Map<String, String> keyInfo = new HashMap<>();
            // 拆分key的三部分
            keyInfo.put("cityCode", key.substring(key.length() - 8));
            keyInfo.put("fieldName", key.substring(2, key.length() - 8));
            keyInfo.put("periodCode", key.substring(0, 2));
            return keyInfo;
        } catch (StringIndexOutOfBoundsException e) {
            return null;
        }
    }

    /**
     * 初始化数据Map结构
     * @param productCode 产品代码
     * @param keyInfo key信息Map
     * @param productCityPeriodDataMap 数据存储Map
     */
    private void initializeDataMapStructure(String productCode,
                                            Map<String, String> keyInfo,
                                            Map<String, Map<String, Map<String, Object>>> productCityPeriodDataMap) {
        productCityPeriodDataMap.computeIfAbsent(productCode, k -> new HashMap<>());
        productCityPeriodDataMap.get(productCode).computeIfAbsent(
                keyInfo.get("cityCode") + keyInfo.get("periodCode"), k -> new HashMap<>());
    }

    /**
     * 获取字段值，处理空值情况
     * @param value 原始值
     * @return 处理后的字段值
     */
    private String getFieldValue(Object value) {
        // 如果value为空，则默认赋值为0
        if (value == null || value.toString().isEmpty()) {
            return "0";
        } else {
            return value.toString();
        }
    }

    /**
     * 创建并保存地市上下半年维度计划数据
     * @param cityCodes 地市代码
     * @param salePlanModel 源计划数据
     * @param cityMap 地市信息
     * @param cityPlanModels 地市计划数据
     * @param cityPlanIdMap 地市计划ID
     * @param isSubmit 是否提交
     */
    private void buildCityPlanHalfYearDatas(Set<String> cityCodes, Mc04IslmSalePlanModel salePlanModel,
                                            Map<String, BusiComDto> cityMap,
                                            List<Mc04IslmSalePlanModel> cityPlanModels,
                                            Map<String, String> cityPlanIdMap,
                                            String isSubmit,
                                            List<String> cityWithT01PlanIds){
        // 创建地市上下半年协议计划
        for (String cityCode : cityCodes) {
            BusiComDto cityInfo = cityMap.get(cityCode);
            if (cityInfo == null) {
                continue;
            }

            // 为每个地市创建两个计划：上半年和下半年
            for (YearPlanPeriodHalfYearEnum halfYear : Arrays.asList(YearPlanPeriodHalfYearEnum.HALF_YEAR_H1, YearPlanPeriodHalfYearEnum.HALF_YEAR_H2)) {
                Mc04IslmSalePlanModel cityPlan = new Mc04IslmSalePlanModel();
                // 设置地市计划基本信息
                String planId = IDUtils.randomUUID(32);
                cityPlan.setMc04SalePlanId(planId);
                cityPlan.setMc04OrgTypeCode(cityInfo.getBaComOrgCode());
                cityPlan.setMc04OrgTypeName(cityInfo.getMc04ComOrgShortName());
                cityPlan.setZaOccurrenceYear(salePlanModel.getZaOccurrenceYear());
                if ("1".equals(isSubmit)) {
                    cityPlan.setMc04SalePlanStatus(YearPlanAllotProvinceStatusEnum.DRAFT.getCode());
                } else {
                    cityPlan.setMc04SalePlanStatus(YearPlanAllotProvinceStatusEnum.CITY_PENDING.getCode());
                }
                cityPlan.setMa02TobaProdTradeTypeCode(ProdTradeTypeCodeEnum.PROD_TRADE_TYPE_CODE_0.getCode());
                cityPlan.setMc04CgtSalePlanVersion(salePlanModel.getMc04CgtSalePlanVersion());
                cityPlan.setMc04IsLastestVersion("1");
                cityPlan.setMc04PlanSubjectType(salePlanModel.getMc04PlanSubjectType());
                cityPlan.setMc04PlanSubjectName(salePlanModel.getMc04PlanSubjectName());
                cityPlan.setMc04PlanSubjectBeginDate(salePlanModel.getMc04PlanSubjectBeginDate());
                cityPlan.setMc04PlanSubjectEndDate(salePlanModel.getMc04PlanSubjectEndDate());
                cityPlan.setMc04CgtSaleFoPeriodType(YearPlanPeriodTypeEnum.HALF_YEAR.getCode());
                cityPlan.setMc04CgtSaleFoPeriodCode(salePlanModel.getZaOccurrenceYear() + halfYear.getCode());
                cityPlan.setMc04OrgTypeKind(YearPlanOrgTypeEnum.CITY.getKind());

                cityPlanModels.add(cityPlan);
                // 使用城市代码+周期代码作为key，确保唯一性
                cityPlanIdMap.put(cityCode + halfYear.getCode(), planId);
            }

            // 每个地市还需要创建一个全年的计划
            Mc04IslmSalePlanModel cityPlan = new Mc04IslmSalePlanModel();
            // 设置地市计划基本信息
            String planId = IDUtils.randomUUID(32);
            cityPlan.setMc04SalePlanId(planId);
            cityPlan.setMc04OrgTypeCode(cityInfo.getBaComOrgCode());
            cityPlan.setMc04OrgTypeName(cityInfo.getMc04ComOrgShortName());
            cityPlan.setZaOccurrenceYear(salePlanModel.getZaOccurrenceYear());
            if ("1".equals(isSubmit)) {
                cityPlan.setMc04SalePlanStatus(YearPlanAllotProvinceStatusEnum.DRAFT.getCode());
            } else {
                cityPlan.setMc04SalePlanStatus(YearPlanAllotProvinceStatusEnum.CITY_PENDING.getCode());
            }
            cityPlan.setMa02TobaProdTradeTypeCode(ProdTradeTypeCodeEnum.PROD_TRADE_TYPE_CODE_0.getCode());
            cityPlan.setMc04CgtSalePlanVersion(salePlanModel.getMc04CgtSalePlanVersion());
            cityPlan.setMc04IsLastestVersion("1");
            cityPlan.setMc04PlanSubjectType(salePlanModel.getMc04PlanSubjectType());
            cityPlan.setMc04PlanSubjectName(salePlanModel.getMc04PlanSubjectName());
            cityPlan.setMc04PlanSubjectBeginDate(salePlanModel.getMc04PlanSubjectBeginDate());
            cityPlan.setMc04PlanSubjectEndDate(salePlanModel.getMc04PlanSubjectEndDate());
            cityPlan.setMc04CgtSaleFoPeriodType(YearPlanPeriodTypeEnum.YEAR.getCode());
            cityPlan.setMc04CgtSaleFoPeriodCode(salePlanModel.getZaOccurrenceYear());
            cityPlan.setMc04OrgTypeKind(YearPlanOrgTypeEnum.CITY.getKind());

            cityPlanModels.add(cityPlan);
            cityWithT01PlanIds.add(planId);
            // 使用城市代码+周期代码作为key，确保唯一性
//                cityPlanIdMap.put(cityCode + halfYear.getCode(), planId);
        }
    }

    // 辅助方法：安全获取BigDecimal值
    private BigDecimal getBigDecimalValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null || value.toString().isEmpty()) {
            return new BigDecimal("0");
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return new BigDecimal("0");
        }
    }

    /**
     * 为地市年度计划创建明细数据，数据来源于上下半年计划数据的汇总
     *
     * @param cityHalfYearDataMap 上下半年计划数据Map，格式为 Map<cityCode_productCode, Map<periodCode, Mc04IslmSalePlanItemModel>>
     * @param cityPlanIdMap 地市年度计划ID映射，格式为 Map<cityCode, planId>
     * @param productList 产品列表
     * @return 年度计划明细项列表
     */
    private List<Mc04IslmSalePlanItemModel> createAnnualPlanItemsFromHalfYearData(
            Map<String, Map<String, Mc04IslmSalePlanItemModel>> cityHalfYearDataMap,
            Map<String, String> cityPlanIdMap,
            Collection<IccProductDetailDTO> productList) {

        // 回参集合
        List<Mc04IslmSalePlanItemModel> annualPlanItems = new ArrayList<>();

        // 遍历所有地市和产品的上下半年数据
        for (Map.Entry<String, Map<String, Mc04IslmSalePlanItemModel>> entry : cityHalfYearDataMap.entrySet()) {
            String cityProductKey = entry.getKey();
            Map<String, Mc04IslmSalePlanItemModel> halfYearData = entry.getValue();

            // 解析cityCode和productCode
            String[] parts = cityProductKey.split("_");
            if (parts.length != 2) {
                continue;
            }
            String cityCode = parts[0];
            String productCode = parts[1];

            // 获取对应的年度计划ID
            String annualPlanId = cityPlanIdMap.get(cityCode);
            if (annualPlanId == null) {
                log.warn("未找到地市 {} 的年度计划ID", cityCode);
                continue;
            }

            // 获取上半年(H1)和下半年(H2)的数据
            Mc04IslmSalePlanItemModel firstHalfItem = halfYearData.get("H1");
            Mc04IslmSalePlanItemModel secondHalfItem = halfYearData.get("H2");

            // 只有当上下半年数据都存在时才创建年度数据
            if (firstHalfItem != null && secondHalfItem != null) {
                // 查找对应的产品信息
                IccProductDetailDTO productDto = productList.stream()
                        .filter(product -> product.getProductCode().equals(productCode))
                        .findFirst()
                        .orElse(null);

                if (productDto == null) {
                    log.warn("未找到产品信息，产品代码: {}", productCode);
                    continue;
                }

                // 创建年度计划明细项
                Mc04IslmSalePlanItemModel annualItem = new Mc04IslmSalePlanItemModel();
                annualItem.setMc04SalePlanItemId(String.valueOf(IdWorker.getId()));
                annualItem.setMc04SalePlanId(annualPlanId);
                annualItem.setAcCgtName(firstHalfItem.getAcCgtName());
                annualItem.setAcCgtCartonCode(firstHalfItem.getAcCgtCartonCode());
                annualItem.setAcCgtPriceSegmentCode(firstHalfItem.getAcCgtPriceSegmentCode());
                annualItem.setAcCgtPriceSegmentName(firstHalfItem.getAcCgtPriceSegmentName());
                annualItem.setAcCgtTaxAllotPrice(firstHalfItem.getAcCgtTaxAllotPrice());
                annualItem.setAcCgtTradePrice(firstHalfItem.getAcCgtTradePrice());
                annualItem.setAcCgtMediumBranceFlag(firstHalfItem.getAcCgtMediumBranceFlag());
                annualItem.setAcCgtTinyFlag(firstHalfItem.getAcCgtTinyFlag());
                annualItem.setAcCgtTarVal(firstHalfItem.getAcCgtTarVal());

                // 计算年度数据（将上下半年数据相加）
                // todo: 地市其他计算属性是否需要统计待定（N年末下转协议、N年底库存、N+1年预计库存）
                annualItem.setMc04CgtSalePlanAdjustedQty(
                        firstHalfItem.getMc04CgtSalePlanAdjustedQty().add(secondHalfItem.getMc04CgtSalePlanAdjustedQty()));
                annualItem.setMa02CgtPlAdjustedQty(
                        firstHalfItem.getMa02CgtPlAdjustedQty().add(secondHalfItem.getMa02CgtPlAdjustedQty()));
                annualItem.setMc04CgtXyCarryOverQty(firstHalfItem.getMc04CgtXyCarryOverQty());
                annualItem.setMd02CgtXyOriginalQty(firstHalfItem.getMd02CgtXyOriginalQty().add(secondHalfItem.getMd02CgtXyOriginalQty()));
                annualItem.setMd03Cgt10thComEndStkQty(secondHalfItem.getMd03Cgt10thComEndStkQty());
                annualPlanItems.add(annualItem);

            }
        }
        return annualPlanItems;
    }

    /**
     * 查找并处理地市年度计划
     *
     * @param cityWithT01PlanIds 省级计划ID
     * @param cityHalfYearDataMap 上下半年计划数据Map
     */
    private List<Mc04IslmSalePlanItemModel> processCityAnnualPlans(List<String> cityWithT01PlanIds,
                                                                   Map<String, Map<String, Mc04IslmSalePlanItemModel>> cityHalfYearDataMap) {

        // 获取所有地市全年维度的分解计划实体
        List<Mc04IslmSalePlanModel> cityAnnualPlans = salePlanRepository.listByPlanIds(cityWithT01PlanIds);

        // 创建地市年度计划ID映射
        Map<String, String> cityPlanIdMap = new HashMap<>();
        for (Mc04IslmSalePlanModel plan : cityAnnualPlans) {
            cityPlanIdMap.put(plan.getMc04OrgTypeCode(), plan.getMc04SalePlanId());
        }

        // 获取产品列表
        Collection<IccProductDetailDTO> productList = getProductList();

        // 为地市年度计划创建明细数据
        // cityHalfYearData: Map<地市代码_卷烟代码, Map<halfYear, Mc04IslmSalePlanItemModel>>
        // cityPlanIdMap: Map<地市代码，地市计划id>

        return createAnnualPlanItemsFromHalfYearData(
                cityHalfYearDataMap, cityPlanIdMap, productList);
    }
}
