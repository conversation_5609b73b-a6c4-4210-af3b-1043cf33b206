/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.order.ret.apply;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tobacco.app.isale.domain.model.order.ret.apply.Mc04IslmContOrderItem;
import com.tobacco.app.isale.domain.repository.order.ret.apply.IslmContOrderItemRepository;
import com.tobacco.app.isale.infrastructure.converter.order.ret.apply.Mc04IslmContOrderItemDOToMc04IslmContOrderItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmContOrderItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: liuwancheng
 * @Since: 2025-07-25
 */
@Component("ISaleMc04IslmContOrderItemDORepository")
public class Mc04IslmContOrderItemRepositoryImpl implements IslmContOrderItemRepository{

    @Autowired
    private final Mc04IslmContOrderItemService mc04IslmContOrderItemService;

    public Mc04IslmContOrderItemRepositoryImpl(Mc04IslmContOrderItemService mc04IslmContOrderItemService) {
        this.mc04IslmContOrderItemService = mc04IslmContOrderItemService;
    }

    @Override
    public List<Mc04IslmContOrderItem> listContOrderItem(Mc04IslmContOrderItem mc04IslmContOrderItem) {
        // 构建查询条件
        LambdaQueryWrapper<Mc04IslmContOrderItemDO> queryWrapper = new LambdaQueryWrapper<>();

        if (mc04IslmContOrderItem.getMc04ContOrderId() == null) {
            throw new RuntimeException("mc04ContOrderId不能为空");
        }
        queryWrapper.eq(Mc04IslmContOrderItemDO::getMc04ContOrderId, mc04IslmContOrderItem.getMc04ContOrderId());

        List<Mc04IslmContOrderItemDO> doList = mc04IslmContOrderItemService.list( queryWrapper);

        // 转换记录列表
        List<Mc04IslmContOrderItem> mc04IslmContOrderItemList = Mc04IslmContOrderItemDOToMc04IslmContOrderItemConverter.INSTANCE.converterDosToModels(doList);

        return mc04IslmContOrderItemList;
    }

    @Override
    public List<Mc04IslmContOrderItem> listByContOrderId(String mc04ContOrderId) {

        LambdaQueryWrapper<Mc04IslmContOrderItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmContOrderItemDO::getMc04ContOrderId, mc04ContOrderId);
        List<Mc04IslmContOrderItemDO> mc04IslmContOrderItemDOS = mc04IslmContOrderItemService.list(queryWrapper);
        return Mc04IslmContOrderItemDOToMc04IslmContOrderItemConverter.INSTANCE.converterDosToModels(mc04IslmContOrderItemDOS);
    }

    @Override
    public List<Mc04IslmContOrderItem> listQuery(Mc04IslmContOrderItem mc04IslmContOrderItem) {
        LambdaQueryWrapper<Mc04IslmContOrderItemDO> queryWrapper = new LambdaQueryWrapper<>();
        if (mc04IslmContOrderItem.getAcTwoLevelCigCode() != null) {
            queryWrapper.eq(Mc04IslmContOrderItemDO::getAcTwoLevelCigCode, mc04IslmContOrderItem.getAcTwoLevelCigCode());
            List<Mc04IslmContOrderItemDO> mc04IslmContOrderItemDOS = mc04IslmContOrderItemService.list(queryWrapper);
            return Mc04IslmContOrderItemDOToMc04IslmContOrderItemConverter.INSTANCE.converterDosToModels(mc04IslmContOrderItemDOS);
        }

        if (mc04IslmContOrderItem.getAcThrLevelCigCode() != null) {
            queryWrapper.eq(Mc04IslmContOrderItemDO::getAcThrLevelCigCode, mc04IslmContOrderItem.getAcThrLevelCigCode());
            List<Mc04IslmContOrderItemDO> mc04IslmContOrderItemDOS = mc04IslmContOrderItemService.list(queryWrapper);
            return Mc04IslmContOrderItemDOToMc04IslmContOrderItemConverter.INSTANCE.converterDosToModels(mc04IslmContOrderItemDOS);
        }
        return null;
    }

}