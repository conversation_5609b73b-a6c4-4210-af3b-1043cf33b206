/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.order.preshipment.demand;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.constant.CommonConstants;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcBatch;
import com.tobacco.app.isale.domain.model.agreement.collect.ManageIndDemandFo;
import com.tobacco.app.isale.domain.model.agreement.collect.ManageIndDemandFoItem;
import com.tobacco.app.isale.domain.model.order.preshipment.demand.Mc04IslmcNcStockMoveDemand;
import com.tobacco.app.isale.domain.model.order.preshipment.demand.Mc04IslmcNcStockMoveDemandItem;
import com.tobacco.app.isale.domain.repository.order.preshipment.demand.IslmcNcStockMoveDemandRepository;
import com.tobacco.app.isale.infrastructure.converter.agreement.basic.Mc04IslmcBatchDOToMc04IslmcBatchConverter;
import com.tobacco.app.isale.infrastructure.converter.agreement.collect.Mc04IslmcIndDemandFoDoToManageIndDemandFoConverter;
import com.tobacco.app.isale.infrastructure.converter.agreement.collect.Mc04IslmcIndDemandFoItemDOToManageIndDemandFoItemConverter;
import com.tobacco.app.isale.infrastructure.converter.order.preshipment.demand.Mc04IslmcNcStockMoveDemandDOToMc04IslmcNcStockMoveDemandConverter;
import com.tobacco.app.isale.infrastructure.converter.order.preshipment.demand.Mc04IslmcNcStockMoveDemandItemDOToMc04IslmcNcStockMoveDemandItemConverter;
import com.tobacco.app.isale.infrastructure.entity.*;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcBatchService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcNcStockMoveDemandItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcNcStockMoveDemandService;
import com.tobacco.app.isale.req.order.preshipment.demand.NcStockMoveDemandReq;
import com.tobacco.app.isalecenter.common.constants.SaleCenterConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/04/22 10:42
 * @description : 销售管理-协议管理(卷烟)-意向采集-意向上报(市场填报) infrastructureRepository
 */
@Slf4j
@Component("IslmcNcStockMoveDemandRepository")
@RequiredArgsConstructor
public class IslmcNcStockMoveDemandRepositoryImpl implements IslmcNcStockMoveDemandRepository {

    private final Mc04IslmcNcStockMoveDemandService mc04IslmcNcStockMoveDemandService;
    private final Mc04IslmcNcStockMoveDemandItemService mc04IslmcNcStockMoveDemandItemService;
    private final IslmcNcStockMoveDemandItemRepositoryImpl islmcNcStockMoveDemandItemRepository;

    private final Mc04IslmcBatchService mc04IslmcBatchService;

    @Override
    public Mc04IslmcNcStockMoveDemand queryNcStockMove(NcStockMoveDemandReq req) {
        LambdaQueryWrapper<Mc04IslmcNcStockMoveDemandDO> queryWrapper = Wrappers.lambdaQuery(Mc04IslmcNcStockMoveDemandDO.class)
                .eq(StrUtil.isNotBlank(req.getCgtType()), Mc04IslmcNcStockMoveDemandDO::getMa02TobaProdTradeTypeCode, req.getCgtType())
                .in(CollUtil.isNotEmpty(req.getComIdList()), Mc04IslmcNcStockMoveDemandDO::getBaComOrgCode, req.getComIdList())
                .eq(StrUtil.isNotBlank(req.getDateCode()), Mc04IslmcNcStockMoveDemandDO::getZaOccurrenceYear, req.getDateCode())
                .eq(StrUtil.isNotBlank(req.getBatchNo()), Mc04IslmcNcStockMoveDemandDO::getMc04BatchNo, req.getBatchNo());

        queryWrapper.last("limit 1");
        Mc04IslmcNcStockMoveDemandDO mc04IslmcNcStockMoveDemandDO = mc04IslmcNcStockMoveDemandService.getOne(queryWrapper);

        if (ObjectUtil.isEmpty(mc04IslmcNcStockMoveDemandDO)){
            return null;
        }

        List<Mc04IslmcNcStockMoveDemandItem> list = islmcNcStockMoveDemandItemRepository.list(mc04IslmcNcStockMoveDemandDO.getMc04NcStockMoveDemandId());
        Mc04IslmcNcStockMoveDemand ncStockMoveDemand = Mc04IslmcNcStockMoveDemandDOToMc04IslmcNcStockMoveDemandConverter.INSTANCE.converterDoToModel(mc04IslmcNcStockMoveDemandDO);
        ncStockMoveDemand.setItems(list);
        return ncStockMoveDemand;

    }

    @Override
    public List<Mc04IslmcNcStockMoveDemand> queryNcStockMoveList(NcStockMoveDemandReq req) {
        LambdaQueryWrapper<Mc04IslmcNcStockMoveDemandDO> queryWrapper = Wrappers.lambdaQuery(Mc04IslmcNcStockMoveDemandDO.class)
                .eq(StrUtil.isNotBlank(req.getCgtType()), Mc04IslmcNcStockMoveDemandDO::getMa02TobaProdTradeTypeCode, req.getCgtType())
                .in(CollUtil.isNotEmpty(req.getComIdList()), Mc04IslmcNcStockMoveDemandDO::getBaComOrgCode, req.getComIdList())
                .eq(StrUtil.isNotBlank(req.getDateCode()), Mc04IslmcNcStockMoveDemandDO::getZaOccurrenceYear, req.getDateCode())
                .eq(StrUtil.isNotBlank(req.getBatchNo()), Mc04IslmcNcStockMoveDemandDO::getMc04BatchNo, req.getBatchNo());

        List<Mc04IslmcNcStockMoveDemandDO> mc04IslmcNcStockMoveDemandDOList = mc04IslmcNcStockMoveDemandService.list(queryWrapper);

        if (CollUtil.isEmpty(mc04IslmcNcStockMoveDemandDOList)){
            return null;
        }

        List<String> ids = mc04IslmcNcStockMoveDemandDOList.stream().map(Mc04IslmcNcStockMoveDemandDO::getMc04NcStockMoveDemandId).collect(Collectors.toList());
        Map<String, List<Mc04IslmcNcStockMoveDemandItem>> itemMap = islmcNcStockMoveDemandItemRepository.list(ids);

        List<Mc04IslmcNcStockMoveDemand> ncStockMoveDemandList = Mc04IslmcNcStockMoveDemandDOToMc04IslmcNcStockMoveDemandConverter.INSTANCE.converterDosToModels(mc04IslmcNcStockMoveDemandDOList);
        ncStockMoveDemandList.forEach(ncStockMoveDemand -> {
            ncStockMoveDemand.setItems(itemMap.get(ncStockMoveDemand.getMc04NcStockMoveDemandId()));
        });

        return ncStockMoveDemandList;
    }

    @Override
    public List<Mc04IslmcNcStockMoveDemand> list(Mc04IslmcNcStockMoveDemand mc04IslmcNcStockMoveDemand) {

        LambdaQueryWrapper<Mc04IslmcNcStockMoveDemandDO> queryWrapper = Wrappers.lambdaQuery(Mc04IslmcNcStockMoveDemandDO.class)
                .eq(StrUtil.isNotBlank(mc04IslmcNcStockMoveDemand.getMc04NcStockMoveDemandId()), Mc04IslmcNcStockMoveDemandDO::getMc04NcStockMoveDemandId, mc04IslmcNcStockMoveDemand.getMc04NcStockMoveDemandId())
                .eq(StrUtil.isNotBlank(mc04IslmcNcStockMoveDemand.getMa02TobaProdTradeTypeCode()), Mc04IslmcNcStockMoveDemandDO::getMa02TobaProdTradeTypeCode, mc04IslmcNcStockMoveDemand.getMa02TobaProdTradeTypeCode())
                .eq(StrUtil.isNotBlank(mc04IslmcNcStockMoveDemand.getMc04BatchNo()), Mc04IslmcNcStockMoveDemandDO::getMc04BatchNo, mc04IslmcNcStockMoveDemand.getMc04BatchNo())
                .eq(StrUtil.isNotBlank(mc04IslmcNcStockMoveDemand.getZaOccurrenceYear()), Mc04IslmcNcStockMoveDemandDO::getZaOccurrenceYear, mc04IslmcNcStockMoveDemand.getZaOccurrenceYear())
                .eq(StrUtil.isNotBlank(mc04IslmcNcStockMoveDemand.getBaComOrgCode()), Mc04IslmcNcStockMoveDemandDO::getBaComOrgCode, mc04IslmcNcStockMoveDemand.getBaComOrgCode())
                .eq(StrUtil.isNotBlank(mc04IslmcNcStockMoveDemand.getMc04NcStockMoveDemandStatus()), Mc04IslmcNcStockMoveDemandDO::getMc04NcStockMoveDemandStatus, mc04IslmcNcStockMoveDemand.getMc04NcStockMoveDemandStatus());


        return Mc04IslmcNcStockMoveDemandDOToMc04IslmcNcStockMoveDemandConverter.INSTANCE.converterDosToModels(mc04IslmcNcStockMoveDemandService.list(queryWrapper));
    }

//    @Override
//    public Mc04IslmcNcStockMoveDemand queryNcStockMove(Mc04IslmcNcStockMoveDemand mc04IslmcNcStockMoveDemand) {
//        LambdaQueryWrapper<Mc04IslmcNcStockMoveDemandDO> queryWrapper = Wrappers.lambdaQuery(Mc04IslmcNcStockMoveDemandDO.class)
//                .eq(StrUtil.isNotBlank(mc04IslmcNcStockMoveDemand.getMc04NcStockMoveDemandId()), Mc04IslmcNcStockMoveDemandDO::getMc04NcStockMoveDemandId, mc04IslmcNcStockMoveDemand.getMc04NcStockMoveDemandId())
//                .eq(StrUtil.isNotBlank(mc04IslmcNcStockMoveDemand.getMa02TobaProdTradeTypeCode()), Mc04IslmcNcStockMoveDemandDO::getMa02TobaProdTradeTypeCode, mc04IslmcNcStockMoveDemand.getMa02TobaProdTradeTypeCode())
//                .eq(StrUtil.isNotBlank(mc04IslmcNcStockMoveDemand.getMc04BatchNo()), Mc04IslmcNcStockMoveDemandDO::getMc04BatchNo, mc04IslmcNcStockMoveDemand.getMc04BatchNo())
//                .eq(StrUtil.isNotBlank(mc04IslmcNcStockMoveDemand.getZaOccurrenceYear()), Mc04IslmcNcStockMoveDemandDO::getZaOccurrenceYear, mc04IslmcNcStockMoveDemand.getZaOccurrenceYear())
//                .eq(StrUtil.isNotBlank(mc04IslmcNcStockMoveDemand.getBaComOrgCode()), Mc04IslmcNcStockMoveDemandDO::getBaComOrgCode, mc04IslmcNcStockMoveDemand.getBaComOrgCode())
//                .eq(StrUtil.isNotBlank(mc04IslmcNcStockMoveDemand.getMc04NcStockMoveDemandStatus()), Mc04IslmcNcStockMoveDemandDO::getMc04NcStockMoveDemandStatus, mc04IslmcNcStockMoveDemand.getMc04NcStockMoveDemandStatus());
//
//        queryWrapper.last("limit 1");
//        Mc04IslmcNcStockMoveDemandDO mc04IslmcNcStockMoveDemandDO = mc04IslmcNcStockMoveDemandService.getOne(queryWrapper);
//
//        return Mc04IslmcNcStockMoveDemandDOToMc04IslmcNcStockMoveDemandConverter.INSTANCE.converterDoToModel(mc04IslmcNcStockMoveDemandDO);
//    }

    @Override
    public Mc04IslmcNcStockMoveDemand listById(String mc04NcStockMoveDemandId) {
        return Mc04IslmcNcStockMoveDemandDOToMc04IslmcNcStockMoveDemandConverter.INSTANCE.converterDoToModel(mc04IslmcNcStockMoveDemandService.getById(mc04NcStockMoveDemandId));
    }

    @Override
    public Boolean create(Mc04IslmcNcStockMoveDemand mc04IslmcNcStockMoveDemand) {
        return mc04IslmcNcStockMoveDemandService.save(Mc04IslmcNcStockMoveDemandDOToMc04IslmcNcStockMoveDemandConverter.INSTANCE.converterModelToDo(mc04IslmcNcStockMoveDemand));
    }

    @Override
    public Boolean update(Mc04IslmcNcStockMoveDemand mc04IslmcNcStockMoveDemand) {
        return mc04IslmcNcStockMoveDemandService.updateById(Mc04IslmcNcStockMoveDemandDOToMc04IslmcNcStockMoveDemandConverter.INSTANCE.converterModelToDo(mc04IslmcNcStockMoveDemand));
    }

    @Override
    public Boolean delete(String mc04NcStockMoveDemandId) {
        return mc04IslmcNcStockMoveDemandService.removeById(mc04NcStockMoveDemandId);
    }

    /**
     * @param batchType 批次类型
     * @param cgtType   业务类型
     * @param dateCode  周期
     * @return List<IslmcBatch>
     * <AUTHOR> hujiarong
     * @create_time : 2025-08-28 10:32:21
     * @description : 批次列表查询
     */
    @Override
    public List<Mc04IslmcBatch> queryBatchList(String batchType, String cgtType, String dateCode) {
        List<Mc04IslmcBatchDO> batchDOList = mc04IslmcBatchService.lambdaQuery()
                .eq(StrUtil.isNotBlank(batchType) ,Mc04IslmcBatchDO::getMa02TobaProdTradeTypeCode, cgtType)
                .eq(Mc04IslmcBatchDO::getMc04DatePeriodCode, dateCode)
                .eq(StrUtil.isNotBlank(batchType) ,Mc04IslmcBatchDO::getMc04BatchType, batchType)
                .eq(Mc04IslmcBatchDO::getZaEnableStatus, CommonConstants.YES)
                .select(Mc04IslmcBatchDO::getMc04BatchNo, Mc04IslmcBatchDO::getMc04BatchName)
                .orderByAsc(Mc04IslmcBatchDO::getCreateTime)
                .list();

        return Mc04IslmcBatchDOToMc04IslmcBatchConverter.INSTANCE.converterDosToModels(batchDOList);
    }

    @Override
    public Mc04IslmcBatch queryBatchById(String mc04BatchNo) {
        Mc04IslmcBatchDO batchDO = mc04IslmcBatchService.lambdaQuery()
                .eq(Mc04IslmcBatchDO::getMc04BatchNo, mc04BatchNo)
                .eq(Mc04IslmcBatchDO::getZaEnableStatus, CommonConstants.YES)
                .orderByAsc(Mc04IslmcBatchDO::getCreateTime)
                .one();
        return Mc04IslmcBatchDOToMc04IslmcBatchConverter.INSTANCE.converterDoToModel(batchDO);
    }

    @Override
    public List<Mc04IslmcNcStockMoveDemand> queryNcStockMoveStatusApi(String cgtType, String dateCode, String batchNo, List<String> comIdList) {
        //查询工业预测主表
        List<Mc04IslmcNcStockMoveDemand> ncStockMoveDemands = queryNcStockMoveStatusApi(cgtType, comIdList, dateCode,
                batchNo, SaleCenterConstants.NO);
        if (CollUtil.isEmpty(ncStockMoveDemands)) {
            return Collections.emptyList();
        }
        return ncStockMoveDemands;
    }



    private List<Mc04IslmcNcStockMoveDemand> queryNcStockMoveStatusApi(String cgtType, List<String> comIdList,
                                                         String dateCode, String batchNo,
                                                         String isContainItems) {
        //查询主表
        List<Mc04IslmcNcStockMoveDemandDO> list = mc04IslmcNcStockMoveDemandService.lambdaQuery()
                .eq(Mc04IslmcNcStockMoveDemandDO::getMa02TobaProdTradeTypeCode, cgtType)
                .eq(StrUtil.isNotBlank(batchNo),
                        Mc04IslmcNcStockMoveDemandDO::getMc04BatchNo, batchNo)
                .eq(Mc04IslmcNcStockMoveDemandDO::getZaOccurrenceYear, dateCode)
                .eq(Mc04IslmcNcStockMoveDemandDO::getIcomCode, IcomUtils.getIcomCode())
                .in(CollUtil.isNotEmpty(comIdList), Mc04IslmcNcStockMoveDemandDO::getBaComOrgCode, comIdList)
                .list();
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        if (SaleCenterConstants.NO.equals(isContainItems)) {
            return Mc04IslmcNcStockMoveDemandDOToMc04IslmcNcStockMoveDemandConverter.INSTANCE
                    .converterDosToModels(list);
        }
        //主表有值，获取从表数据
        List<String> ids = list.stream()
                .map(Mc04IslmcNcStockMoveDemandDO::getMc04NcStockMoveDemandId)
                .collect(Collectors.toList());
        List<Mc04IslmcNcStockMoveDemandItemDO> itemDOList = mc04IslmcNcStockMoveDemandItemService.lambdaQuery()
                .in(Mc04IslmcNcStockMoveDemandItemDO::getMc04NcStockMoveDemandId, ids)
                .list();
        Assert.notEmpty(itemDOList, () -> new CustomException("存在异常数据," +
                CollectionUtil.join(comIdList, ",") + "有主表数据,但无明细数据"));
        List<Mc04IslmcNcStockMoveDemandItem> itemList =
                Mc04IslmcNcStockMoveDemandItemDOToMc04IslmcNcStockMoveDemandItemConverter.INSTANCE
                        .converterDosToModels(itemDOList);
        Map<String, List<Mc04IslmcNcStockMoveDemandItem>> foItemMap = itemList.stream()
                .collect(Collectors.groupingBy(Mc04IslmcNcStockMoveDemandItem::getMc04NcStockMoveDemandId));
        return list.stream()
                .map(ncStockMoveDemandDO -> {
                    Mc04IslmcNcStockMoveDemand mc04IslmcNcStockMoveDemand = Mc04IslmcNcStockMoveDemandDOToMc04IslmcNcStockMoveDemandConverter.INSTANCE
                            .converterDoToModel(ncStockMoveDemandDO);
                    Assert.isTrue(foItemMap.containsKey(ncStockMoveDemandDO.getMc04NcStockMoveDemandId()),
                            () -> new CustomException("存在异常数据," + ncStockMoveDemandDO.getBaComOrgCode() + "有意向数据,但无明细数据"));
                    mc04IslmcNcStockMoveDemand.setItems(
                            foItemMap.get(ncStockMoveDemandDO.getMc04NcStockMoveDemandId()));
                    return mc04IslmcNcStockMoveDemand;
                })
//                .sorted(Comparator.comparing(Mc04IslmcNcStockMoveDemand::getCreateId))
                .collect(Collectors.toList());
    }
}
