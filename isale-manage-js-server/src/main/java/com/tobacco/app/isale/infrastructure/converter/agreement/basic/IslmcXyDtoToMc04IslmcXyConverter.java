package com.tobacco.app.isale.infrastructure.converter.agreement.basic;

import com.tobacco.app.isale.domain.model.agreement.basic.Mc04IslmcXy;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isalecenter.client.dto.xy.Mc04IslmcXyDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**

 * @description : 数据转化
 */
@Mapper
public interface IslmcXyDtoToMc04IslmcXyConverter
        extends StructureBaseConverter<Mc04IslmcXyDTO, Mc04IslmcXy> {
    IslmcXyDtoToMc04IslmcXyConverter INSTANCE =
            Mappers.getMapper(IslmcXyDtoToMc04IslmcXyConverter.class);

    @Override
    @Mappings(@Mapping(source = "xyItems", target = "mc04IslmcXyItemList"))
    Mc04IslmcXy converterDoToModel(Mc04IslmcXyDTO xyDto);
}
