package com.tobacco.app.isale.infrastructure.repository.order.ret.apply.utils;

import cn.hutool.core.util.StrUtil;
import com.inspur.ind.base.BaseFactor;
import com.inspur.ind.icom.IcomUtils;
import com.inspur.ind.uc.user.UserUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


public class ApplyUtils {

    public static <T extends BaseFactor> void processBaseFactorProperties(T dto) {
        String loginName = UserUtils.getCurrentLoginName();
        String displayName = UserUtils.getCurrentDisplayName();
        // 创建人为空时，使用系统用户
        if (StrUtil.isBlank(dto.getCreateId())) {
            dto.setCreateId(loginName);
            dto.setCreateName(displayName);
        }
        dto.setUpdateId(loginName);
        dto.setUpdateName(displayName);
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        dto.setCreateTime(currentTime);
        dto.setUpdateTime(currentTime);
        dto.setIcomCode(IcomUtils.getIcomCode());
    }
}
