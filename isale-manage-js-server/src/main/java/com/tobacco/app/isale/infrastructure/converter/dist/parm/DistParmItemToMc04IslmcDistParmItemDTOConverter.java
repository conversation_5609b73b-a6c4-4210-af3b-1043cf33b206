/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.parm;

import com.tobacco.app.isale.domain.model.order.dist.parm.DistParmItem;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isalecenter.client.dto.order.distParm.Mc04IslmcDistParmItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> liu<PERSON><PERSON>
 * @create_time : 2025/06/03 19:18
 * @description : 配货参数数据转化
 */
@Mapper
public interface DistParmItemToMc04IslmcDistParmItemDTOConverter extends StructureBaseConverter<Mc04IslmcDistParmItemDTO, DistParmItem> {
    DistParmItemToMc04IslmcDistParmItemDTOConverter INSTANCE = Mappers.getMapper(DistParmItemToMc04IslmcDistParmItemDTOConverter.class);
}
