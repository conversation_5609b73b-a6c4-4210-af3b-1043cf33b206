package com.tobacco.app.isale.infrastructure.repository.order.inventory.realtimeinventory;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.inspur.ind.icom.IcomUtils;
import com.lamboegg.common.base.BaseResultConstant;
import com.tobacco.app.isale.domain.model.order.inventory.realtimeinventory.Mc04IslmcCgtStock;
import com.tobacco.app.isale.domain.repository.order.inventory.realtimeinventory.CgtStockRepository;
import com.tobacco.app.isale.infrastructure.converter.order.inventory.realtimeinventory.Mc04IslmcCgtStockDOToMc04IslmcCgtStockConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtStockDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcCgtStockService;
import com.tobacco.app.isale.tools.utils.InterHttpUtils;
import com.tobacco.app.isalecenter.common.exception.CustomException;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: hujiarong
 * @Since: 2025-07-22
 */
@Component("ISaleManageMc04IslmcCgtStockDORepository")
@RequiredArgsConstructor
public class Mc04IslmcCgtStockRepositoryImpl implements CgtStockRepository {

    private final InterHttpUtils interHttpUtils;
    private final Mc04IslmcCgtStockService mc04IslmcCgtStockService;

    @Override
    public List<Map<String,Object>> getRealTimeInventory(HashMap<String, Object> params) throws CustomException {
        String msgid = "T_YX_KCCX";
        Map<String, Object> data = interHttpUtils.createData(msgid, "实时库存查询接口", params);
        Map<String, Object> result = interHttpUtils.postPull("HUB_" + msgid, data);
        return (List<Map<String,Object>>)result.get("row");
    }

    /**
     * 获取实时库存
     *
     * @return List<Mc04IslmcCgtStock>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-07-22 16:46:52
     * @description : 获取实时库存
     */
    @Override
    public List<Mc04IslmcCgtStock> getStockList() {
        List<Mc04IslmcCgtStockDO> stockDOList = mc04IslmcCgtStockService.list();
        if (CollUtil.isEmpty(stockDOList)) {
            return Collections.emptyList();
        }
        return Mc04IslmcCgtStockDOToMc04IslmcCgtStockConverter.INSTANCE.converterDosToModels(stockDOList);
    }
}