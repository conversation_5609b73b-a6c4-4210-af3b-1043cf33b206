/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.basic;

import com.tobacco.app.isale.domain.model.basic.IslmcComItem;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcComItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: zhangshch01
 * @Create: 2025-07-28
 */

@Mapper
public interface Mc04IslmcComItemDOToIslmcComItemConverter
        extends StructureBaseConverter<Mc04IslmcComItemDO, IslmcComItem> {

    Mc04IslmcComItemDOToIslmcComItemConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcComItemDOToIslmcComItemConverter.class);

}