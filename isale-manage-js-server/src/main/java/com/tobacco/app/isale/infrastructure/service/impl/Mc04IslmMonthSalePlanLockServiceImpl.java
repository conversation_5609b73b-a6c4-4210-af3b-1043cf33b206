/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanLockDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmMonthSalePlanLockMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanLockService;
import org.springframework.stereotype.Service;

/**
 * @Description: 服务实现类
 *
 * @Author: jinfuli
 * @Since: 2025-06-09
 * @Email: <EMAIL>
 * @Create: 2025-06-09
 */
@Service
public class Mc04IslmMonthSalePlanLockServiceImpl extends ServiceImpl<Mc04IslmMonthSalePlanLockMapper, Mc04IslmMonthSalePlanLockDO> implements Mc04IslmMonthSalePlanLockService {

}