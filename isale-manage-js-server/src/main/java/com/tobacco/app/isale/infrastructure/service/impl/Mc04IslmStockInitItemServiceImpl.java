/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmStockInitItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmStockInitItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmStockInitItemService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: loongxi
 * @Since: 2025-08-06
 * @Email: <EMAIL>
 * @Create: 2025-08-06
 */
@Service
public class Mc04IslmStockInitItemServiceImpl extends ServiceImpl<Mc04IslmStockInitItemMapper, Mc04IslmStockInitItemDO> implements Mc04IslmStockInitItemService {

}