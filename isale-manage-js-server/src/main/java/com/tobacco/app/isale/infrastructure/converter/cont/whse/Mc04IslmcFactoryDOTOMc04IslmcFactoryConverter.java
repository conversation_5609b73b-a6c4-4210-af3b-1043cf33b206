package com.tobacco.app.isale.infrastructure.converter.cont.whse;

import com.tobacco.app.isale.domain.model.cont.whse.Mc04IslmcFactory;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcFactoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
@Mapper
public interface Mc04IslmcFactoryDOTOMc04IslmcFactoryConverter
        extends StructureBaseConverter<Mc04IslmcFactoryDO, Mc04IslmcFactory> {
    Mc04IslmcFactoryDOTOMc04IslmcFactoryConverter INSTANCE = Mappers.getMapper(Mc04IslmcFactoryDOTOMc04IslmcFactoryConverter.class);
}
