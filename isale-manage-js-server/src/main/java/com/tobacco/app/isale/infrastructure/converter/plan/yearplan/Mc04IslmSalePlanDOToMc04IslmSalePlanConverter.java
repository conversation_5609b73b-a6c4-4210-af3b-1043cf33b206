package com.tobacco.app.isale.infrastructure.converter.plan.yearplan;

import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlan;
import com.tobacco.app.isale.dto.plan.yearplan.Mc04SalePlanDto;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSalePlanDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version v1.0.0
 * 创建时间：2025/8/12 15:08
 */
@Mapper
public interface Mc04IslmSalePlanDOToMc04IslmSalePlanConverter extends StructureBaseConverter<Mc04IslmSalePlanDO, Mc04IslmSalePlan> {

    Mc04IslmSalePlanDOToMc04IslmSalePlanConverter INSTANCE =
            Mappers.getMapper(Mc04IslmSalePlanDOToMc04IslmSalePlanConverter.class);
}
