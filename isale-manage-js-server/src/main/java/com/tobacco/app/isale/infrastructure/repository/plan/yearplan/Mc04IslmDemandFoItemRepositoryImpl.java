/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.yearplan;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tobacco.app.isale.domain.enums.plan.yearplan.DemandFoDataTypeEnum;
import com.tobacco.app.isale.domain.enums.plan.yearplan.DemandFoItemPeriodTypeEnum;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmDemandFoItem;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmDemandFoItemRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmDemandFoItemDOToMc04IslmDemandFoItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmDemandFoItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmDemandFoItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmDemandFoItemService;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmDemandFoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: loongxi
 * @Since: 2025-08-07
 */
@Component("ISaleMc04IslmDemandFoItemDORepository")
public class Mc04IslmDemandFoItemRepositoryImpl implements IslmDemandFoItemRepository {
    @Resource
    Mc04IslmDemandFoItemService demandFoItemService;

    @Autowired
    private Mc04IslmDemandFoItemMapper demandFoItemMapper;

    @Autowired
    private IslmDemandFoMapper myDemandFoBusinessMapper;

    @Autowired
    private Mc04IslmDemandFoItemService mc04IslmDemandFoItemService;

    @Override
    public List<Mc04IslmDemandFoItemDO> listDemandFoItemByWrapper(QueryWrapper<Mc04IslmDemandFoItemDO> queryWrapper) {
        return demandFoItemMapper.selectList(queryWrapper);
    }

    @Override
    public Boolean updateDemandFoItem(Mc04IslmDemandFoItemDO demandFoItemDO) {
        return demandFoItemService.updateById(demandFoItemDO);
    }

    @Override
    public Boolean saveDemandFoItemList(List<Mc04IslmDemandFoItemDO> list) {
        return demandFoItemService.saveBatch(list);
    }

    @Override
    public Boolean updateDemandFoItemList(List<Mc04IslmDemandFoItemDO> list) {
        return demandFoItemService.updateBatchById(list);
    }

    //删除
    @Override
    public Boolean deleteDemandFoItem(List<Mc04IslmDemandFoItemDO> list) {
        return demandFoItemService.removeBatchByIds(list);
    }

    @Override
    public Map<String, String> allSalesByMonthAndBamCode(String bamCode, String month) {
        List<Map<String, Object>> resultList = myDemandFoBusinessMapper.allSalesByMonthAndBamCode(bamCode, month);
        Map<String, String> resultMap = new HashMap<>();

        if (resultList != null && !resultList.isEmpty()) {
            for (Map<String, Object> row : resultList) {
                String cartonCode = (String) row.get("cartonCode");
                Object saleObj = row.get("sale");
                String sale = saleObj != null ? saleObj.toString() : null;
                resultMap.put(cartonCode, sale);
            }
        }

        return resultMap;
    }

    @Override
    public Map<String, String> selectCartonCodeAndAdjustedQtyByYearAndSubjectType(String zaOccurrenceYear, String planSubjectType) {
        List<Map<String, Object>> resultList = myDemandFoBusinessMapper.selectCartonCodeAndAdjustedQtyByYearAndSubjectType(zaOccurrenceYear, planSubjectType);
        Map<String, String> resultMap = new HashMap<>();

        if (resultList != null && !resultList.isEmpty()) {
            for (Map<String, Object> row : resultList) {
                String cartonCode = (String) row.get("cartonCode");
                Object saleObj = row.get("adjustedQty");
                String sale = saleObj != null ? saleObj.toString() : null;
                resultMap.put(cartonCode, sale);
            }
        }
        return resultMap;
    }

    @Override
    public List<Mc04IslmDemandFoItem> getYcItemListByDemandFoIdList(List<String> demandFoIdList) {
        LambdaQueryWrapper<Mc04IslmDemandFoItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Mc04IslmDemandFoItemDO::getMc04DemandFoId, demandFoIdList);
        queryWrapper.eq(Mc04IslmDemandFoItemDO::getMc04SaleFoDataType, DemandFoDataTypeEnum.PREDICT_SALES.getStatus());
        List<String> periodTypeList = new ArrayList<>();
        periodTypeList.add(DemandFoItemPeriodTypeEnum.DATE_PERIOD_TYPE_CODE_YC.getCode());
        periodTypeList.add(DemandFoItemPeriodTypeEnum.DATE_PERIOD_TYPE_CODE_T04.getCode());
        queryWrapper.in(Mc04IslmDemandFoItemDO::getMc04CgtSaleFoPeriodType, periodTypeList);
        List<String> periodCodeList = new ArrayList<>();
        periodCodeList.add(DemandFoItemPeriodTypeEnum.DATE_PERIOD_CODE_01Month.getCode());
        periodCodeList.add(DemandFoItemPeriodTypeEnum.DATE_PERIOD_CODE_02Month.getCode());
        queryWrapper.in(Mc04IslmDemandFoItemDO::getMc04CgtSaleFoPeriodCode, periodCodeList);
        return Mc04IslmDemandFoItemDOToMc04IslmDemandFoItemConverter.INSTANCE.converterDosToModels(demandFoItemMapper.selectList(queryWrapper));
    }

    @Override
    public List<Mc04IslmDemandFoItem> listByDemandFoIds(List<String> mc04DemandFoIds) {
        LambdaQueryWrapper<Mc04IslmDemandFoItemDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtil.isNotEmpty(mc04DemandFoIds)) {
            queryWrapper.in(Mc04IslmDemandFoItemDO::getMc04DemandFoId, mc04DemandFoIds);
            return Mc04IslmDemandFoItemDOToMc04IslmDemandFoItemConverter.INSTANCE.converterDosToModels(mc04IslmDemandFoItemService.list(queryWrapper));
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<Mc04IslmDemandFoItem> listByDemandFoId(String demandFoId) {
        LambdaQueryWrapper<Mc04IslmDemandFoItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmDemandFoItemDO::getMc04DemandFoId, demandFoId);
        return Mc04IslmDemandFoItemDOToMc04IslmDemandFoItemConverter.INSTANCE.converterDosToModels(mc04IslmDemandFoItemService.list(queryWrapper));
    }
}