/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmWaybillLineDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmWaybillLineMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmWaybillLineService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: wang<PERSON><PERSON>
 * @Since: 2025-08-26
 * @Email: <EMAIL>
 * @Create: 2025-08-26
 */
@Service
public class Mc04IslmWaybillLineServiceImpl extends ServiceImpl<Mc04IslmWaybillLineMapper, Mc04IslmWaybillLineDO> implements Mc04IslmWaybillLineService {

}