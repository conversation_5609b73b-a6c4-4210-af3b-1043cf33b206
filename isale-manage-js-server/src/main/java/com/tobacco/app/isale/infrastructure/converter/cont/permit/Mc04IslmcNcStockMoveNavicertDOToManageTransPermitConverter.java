package com.tobacco.app.isale.infrastructure.converter.cont.permit;


import com.tobacco.app.isale.domain.model.cont.permit.ManageTransPermit;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcNcStockMoveNavicertDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 备货移库准运证表 应用服务
 * </p>
 *
 * @Author: wangluhao01
 * @Since: 2025-09-05
 */
@Mapper
public interface Mc04IslmcNcStockMoveNavicertDOToManageTransPermitConverter
        extends StructureBaseConverter<Mc04IslmcNcStockMoveNavicertDO, ManageTransPermit> {
    Mc04IslmcNcStockMoveNavicertDOToManageTransPermitConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcNcStockMoveNavicertDOToManageTransPermitConverter.class);

}
