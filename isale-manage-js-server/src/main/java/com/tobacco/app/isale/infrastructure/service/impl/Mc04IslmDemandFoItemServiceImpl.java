/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.tobacco.app.isale.infrastructure.entity.Mc04IslmDemandFoItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmDemandFoItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmDemandFoItemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: loongxi
 * @Since: 2025-08-07
 * @Email: <EMAIL>
 * @Create: 2025-08-07
 */
@Service
public class Mc04IslmDemandFoItemServiceImpl extends ServiceImpl<Mc04IslmDemandFoItemMapper, Mc04IslmDemandFoItemDO> implements Mc04IslmDemandFoItemService {

}