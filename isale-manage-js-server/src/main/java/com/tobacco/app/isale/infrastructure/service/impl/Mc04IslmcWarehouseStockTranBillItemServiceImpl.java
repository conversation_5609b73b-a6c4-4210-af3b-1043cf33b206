/*
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 *  版权所有 (C) 浪潮软件股份有限公司
 */

package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcWarehouseStockTranBillItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcWarehouseStockTranBillItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcWarehouseStockTranBillItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: renyonghui
 * @Since: 2025-05-21
 * @Email: <EMAIL>
 * @Create: 2025-05-21
 */
@Service
public class Mc04IslmcWarehouseStockTranBillItemServiceImpl extends ServiceImpl<Mc04IslmcWarehouseStockTranBillItemMapper, Mc04IslmcWarehouseStockTranBillItemDO> implements Mc04IslmcWarehouseStockTranBillItemService {

    @Autowired
    Mc04IslmcWarehouseStockTranBillItemMapper billItemMapper;

    @Override
    public void removeItem(String billId, String icomCode) {
        billItemMapper.removeItem(billId, icomCode);
    }
}