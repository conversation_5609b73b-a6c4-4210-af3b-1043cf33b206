/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.common;

import com.tobacco.app.isale.domain.model.product.IccProductDetail;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.sc.icommodity.dto.common.constant.dto.product.IccProductDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/04/28 16:17
 * @description : IccProductDetailDTOToIccProductDetailConverter
 */
@Mapper
public interface IccProductDetailDTOToIccProductDetailConverter
        extends StructureBaseConverter<IccProductDetailDTO, IccProductDetail> {
    IccProductDetailDTOToIccProductDetailConverter INSTANCE =
            Mappers.getMapper(IccProductDetailDTOToIccProductDetailConverter.class);
}
