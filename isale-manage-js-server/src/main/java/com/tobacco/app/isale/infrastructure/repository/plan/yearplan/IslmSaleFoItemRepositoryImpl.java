/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.yearplan;


import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSaleFoItem;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmSaleFoItemRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmSaleFoItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSaleFoItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmSaleFoItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 年度销售预测明细领域仓库实现
 *
 * @Author: longxi
 * @Since: 2025-08-07
 */
@Component("IslmSaleFoItemRepository")
public class IslmSaleFoItemRepositoryImpl implements IslmSaleFoItemRepository {

    @Autowired
    private Mc04IslmSaleFoItemService mc04IslmSaleFoItemService;

    @Override
    public List<Mc04IslmSaleFoItem> listBySaleFoId(String mc04CgtSaleFoId) {
        LambdaQueryChainWrapper<Mc04IslmSaleFoItemDO> lambdaQuery = mc04IslmSaleFoItemService.lambdaQuery();
        List<Mc04IslmSaleFoItemDO> doList = lambdaQuery.eq(Mc04IslmSaleFoItemDO::getMc04CgtSaleFoId, mc04CgtSaleFoId).list();
        return Mc04IslmSaleFoItemConverter.INSTANCE.converterDosToModels(doList);
    }
}
