/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.weekplan.weekplanadjust;

import com.tobacco.app.isale.domain.model.plan.weekplan.weekplanadjust.IslmWeekPlanAdjAdd;
import com.tobacco.app.isale.domain.model.plan.weekplan.weekplansubmit.WeekPlanSubmitAdd;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmWeekSalePlanAdjDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmWeekSalePlanDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> jxy
 * @Email : @inspur.com
 * @Create : 2025/08/14
 * @Description : 类型转化
 */
@Mapper
public interface Mc04IslmWeekSalePlanAdjConverter extends StructureBaseConverter<Mc04IslmWeekSalePlanAdjDO, IslmWeekPlanAdjAdd> {
    Mc04IslmWeekSalePlanAdjConverter INSTANCE = Mappers.getMapper(Mc04IslmWeekSalePlanAdjConverter.class);
}
