package com.tobacco.app.isale.domain.model.order.ret.apply;


import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DomainObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <p>
 * 合同表
 * </p>
 *
 * @Author: liuwancheng
 * @Since: 2025-07-30
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@DomainObject(isAggregateRoot = false,name = "合同表", desc = "合同表")
public class Mc04IslmContOrderPage implements Serializable {

    private static final long serialVersionUID = 1L;

  @ApiModelProperty(value = "分页开始位置")
  private int offset = 0;

  @ApiModelProperty(value = "分页大小")
  private int limit = 10;

  @ApiModelProperty(value = "排序方式")
  private String sort = "create_time";

  @ApiModelProperty(value = "排序方式")
  private String order = "desc";


    /**
     * 卷烟收货仓库名称
     */
    @Field(value = "卷烟收货仓库名称", name = "卷烟收货仓库名称")
    private String md02CgtInStorehouseName;

    /**
     * 卷烟发货仓库名称
     */
    @Field(value = "卷烟发货仓库名称", name = "卷烟发货仓库名称")
    private String md02CgtOutStorehouseName;
  /**
     * 合同订单主键编码
     */
    @Field(value = "合同订单主键编码", name = "合同订单主键编码")
    private String mc04ContOrderId;

  /**
     * 合同订单代码
     */
    @Field(value = "合同订单代码", name = "合同订单代码")
    private String mc04ContOrderCode;

  /**
     * 商业公司编码
     */
    @Field(value = "商业公司编码", name = "商业公司编码")
    private String baComOrgCode;

  /**
     * 卷烟交易合同代码
     */
    @Field(value = "卷烟交易合同代码", name = "卷烟交易合同代码")
    private String md02CgtTradeContNo;

  /**
     * 网配订单代码
     */
    @Field(value = "网配订单代码", name = "网配订单代码")
    private String mc04CgtDistOrderCode;

  /**
     * 卷烟日计划编码
     */
    @Field(value = "卷烟日计划编码", name = "卷烟日计划编码")
    private String mc04CgtDayPlanCode;

  /**
     * 卷烟协议周期
     */
    @Field(value = "卷烟协议周期", name = "卷烟协议周期")
    private String mc04CgtXyPeriodCode;

  /**
     * 计划月份
     */
    @Field(value = "计划月份", name = "计划月份")
    private String ma02PlanMonth;

  /**
     * 调拨计划类型
     */
    @Field(value = "调拨计划类型", name = "调拨计划类型")
    private String mc04MonthSalePlanType;

  /**
     * 时间周期代码
     */
    @Field(value = "时间周期代码", name = "时间周期代码")
    private String mc04DatePeriodCode;

  /**
     * 卷烟协议编号
     */
    @Field(value = "卷烟协议编号", name = "卷烟协议编号")
    private String md02CgtXyNo;

  /**
     * 运输方式代码
     */
    @Field(value = "运输方式代码", name = "运输方式代码")
    private String md02TradeTransTypeCode;

  /**
     * 物流卷烟运输运输方式
     */
    @Field(value = "物流卷烟运输运输方式", name = "物流卷烟运输运输方式")
    private String mc05LogtTransportTransType;

  /**
     * 结算方式代码
     */
    @Field(value = "结算方式代码", name = "结算方式代码")
    private String zbSettlementModeCode;

  /**
     * 卷烟发货仓库代码
     */
    @Field(value = "卷烟发货仓库代码", name = "卷烟发货仓库代码")
    private String md02CgtOutStorehouseCode;

  /**
     * 卷烟收货仓库代码
     */
    @Field(value = "卷烟收货仓库代码", name = "卷烟收货仓库代码")
    private String md02CgtInStorehouseCode;

  /**
     * 实际发货仓库
     */
    @Field(value = "实际发货仓库", name = "实际发货仓库")
    private String mc04CgtPraOutStorehouseCode;

  /**
     * 卷烟交易合同交货站点编码
     */
    @Field(value = "卷烟交易合同交货站点编码", name = "卷烟交易合同交货站点编码")
    private String mc04CgtPraInStorehouseCode;

  /**
     * 合同预计发货日期
     */
    @Field(value = "合同预计发货日期", name = "合同预计发货日期")
    private String mc04ContExpecOutDate;

  /**
     * 合同预计到货日期
     */
    @Field(value = "合同预计到货日期", name = "合同预计到货日期")
    private String mc04ContExpecArrivalDate;

  /**
     * 卷烟交易合同状态
     */
    @Field(value = "卷烟交易合同状态", name = "卷烟交易合同状态")
    private String mc04CgtTradeContStatus;

  /**
     * 卷烟联营加工类型
     */
    @Field(value = "卷烟联营加工类型", name = "卷烟联营加工类型")
    private String mc04CgtAssoType;

  /**
     * 卷烟合作生产正式合同号
     */
    @Field(value = "卷烟合作生产正式合同号", name = "卷烟合作生产正式合同号")
    private String mc04CgtCpnContNo;

  /**
     * 卷烟合作生产准运证号
     */
    @Field(value = "卷烟合作生产准运证号", name = "卷烟合作生产准运证号")
    private String mc04CgtCpnPermNum;

  /**
     * 卷烟合作生产发票号
     */
    @Field(value = "卷烟合作生产发票号", name = "卷烟合作生产发票号")
    private String mc04CgtCpnInvoiceNo;

  /**
     * 卷烟订单下单时间
     */
    @Field(value = "卷烟订单下单时间", name = "卷烟订单下单时间")
    private String md04CgtOrderTime;

  /**
     * 烟草制品交易业务类型代码,0:国产卷烟,1:国产雪茄烟,2:合作生产,5:国产卷烟退货,6:国产雪茄烟退货
     */
    @Field(value = "烟草制品交易业务类型代码,0:国产卷烟,1:国产雪茄烟,2:合作生产,5:国产卷烟退货,6:国产雪茄烟退货", name = "烟草制品交易业务类型代码,0:国产卷烟,1:国产雪茄烟,2:合作生产,5:国产卷烟退货,6:国产雪茄烟退货")
    private String ma02TobaProdTradeTypeCode;

  /**
     * 合同零点类型
     */
    @Field(value = "合同零点类型", name = "合同零点类型")
    private String mc04ContZeroClockType;

  /**
     * 卷烟交易合同开票状态
     */
    @Field(value = "卷烟交易合同开票状态", name = "卷烟交易合同开票状态")
    private String mc04CgtTradeContInvStatus;

  /**
     * 卷烟交易合同开票时间
     */
    @Field(value = "卷烟交易合同开票时间", name = "卷烟交易合同开票时间")
    private String mc04CgtTradeContInvTime;

  /**
     * 卷烟订单发票号码
     */
    @Field(value = "卷烟订单发票号码", name = "卷烟订单发票号码")
    private String md04CgtOrderInvoiceNo;

  /**
     * 税票金额
     */
    @Field(value = "税票金额", name = "税票金额")
    private BigDecimal mc04InvoiceTaxAmt;

  /**
     * 卷烟交易合同交货起始日期
     */
    @Field(value = "卷烟交易合同交货起始日期", name = "卷烟交易合同交货起始日期")
    private String md02CgtTradeContDelistartdate;

  /**
     * 卷烟交易合同交货截止日期
     */
    @Field(value = "卷烟交易合同交货截止日期", name = "卷烟交易合同交货截止日期")
    private String md02CgtTradeContDelienddate;

  /**
     * 卷烟交易合同挂网时间
     */
    @Field(value = "卷烟交易合同挂网时间", name = "卷烟交易合同挂网时间")
    private String mc04CgtTradeContSpreadNetTime;

  /**
     * 卷烟交易合同鉴章时间
     */
    @Field(value = "卷烟交易合同鉴章时间", name = "卷烟交易合同鉴章时间")
    private String ma02CgtTradeContAuditTime;

  /**
     * 卷烟交易合同解除时间
     */
    @Field(value = "卷烟交易合同解除时间", name = "卷烟交易合同解除时间")
    private String md02CgtTradeContCancelTime;

  /**
     * 卷烟交易合同鉴章人代码
     */
    @Field(value = "卷烟交易合同鉴章人代码", name = "卷烟交易合同鉴章人代码")
    private String ma02CgtTradeContAuditoperatCode;

  /**
     * 准运证号码
     */
    @Field(value = "准运证号码", name = "准运证号码")
    private String mz04NavicertNo;

  /**
     * 准运证制证日期
     */
    @Field(value = "准运证制证日期", name = "准运证制证日期")
    private String mf01NavicertPermDate;

  /**
     * 准运证确认日期
     */
    @Field(value = "准运证确认日期", name = "准运证确认日期")
    private String mc04NavicertConfirmDate;

  /**
     * 卷烟交易合同交易量
     */
    @Field(value = "卷烟交易合同交易量", name = "卷烟交易合同交易量")
    private BigDecimal md02CgtTradeContQty;

  /**
     * 卷烟订单含税金额合计
     */
    @Field(value = "卷烟订单含税金额合计", name = "卷烟订单含税金额合计")
    private BigDecimal md04CgtOrderSumWithtaxAmt;

  /**
     * 卷烟交易合同备注
     */
    @Field(value = "卷烟交易合同备注", name = "卷烟交易合同备注")
    private String md02CgtTradeContRemark;

  /**
     * 发货建议
     */
    @Field(value = "发货建议", name = "发货建议")
    private String zaRemark;

  /**
     * 物流托盘联运托盘类型
     */
    @Field(value = "物流托盘联运托盘类型", name = "物流托盘联运托盘类型")
    private String md03LogtTrayCombTspTrayType;

  /**
     * 卷烟交易虚拟合同号
     */
    @Field(value = "卷烟交易虚拟合同号", name = "卷烟交易虚拟合同号")
    private String mc04CgtTradeVirtualContNo;

  /**
     * 工业公司code
     */
    @Field(value = "工业公司code", name = "工业公司code")
    private String icomCode;

  /**
     * 创建人ID
     */
    @Field(value = "创建人ID", name = "创建人ID")
    private String createId;

  /**
     * 创建人名称
     */
    @Field(value = "创建人名称", name = "创建人名称")
    private String createName;

  /**
     * 创建时间
     */
    @Field(value = "创建时间", name = "创建时间")
    private String createTime;

  /**
     * 修改人ID
     */
    @Field(value = "修改人ID", name = "修改人ID")
    private String updateId;

  /**
     * 修改人名称
     */
    @Field(value = "修改人名称", name = "修改人名称")
    private String updateName;

  /**
     * 修改时间
     */
    @Field(value = "修改时间", name = "修改时间")
    private String updateTime;


}
