/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.cont.order;

import com.tobacco.app.isale.domain.model.cont.order.ManageContOrder;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/06/12 17:11
 * @description : 订单DTO转换成订单
 */
@Mapper
public interface Mc04IslmContOrderDOToManageContOrderConverter
        extends StructureBaseConverter<Mc04IslmContOrderDO, ManageContOrder> {

    Mc04IslmContOrderDOToManageContOrderConverter INSTANCE = Mappers.getMapper(Mc04IslmContOrderDOToManageContOrderConverter.class);

}
