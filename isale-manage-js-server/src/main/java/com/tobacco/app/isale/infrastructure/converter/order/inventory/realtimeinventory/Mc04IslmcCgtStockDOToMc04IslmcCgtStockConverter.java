package com.tobacco.app.isale.infrastructure.converter.order.inventory.realtimeinventory;
/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */

import com.tobacco.app.isale.domain.model.order.inventory.realtimeinventory.Mc04IslmcCgtStock;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtStockDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/06/12 17:11
 * @description : 订单DTO转换成订单
 */
@Mapper
public interface Mc04IslmcCgtStockDOToMc04IslmcCgtStockConverter
        extends StructureBaseConverter<Mc04IslmcCgtStockDO, Mc04IslmcCgtStock> {

    Mc04IslmcCgtStockDOToMc04IslmcCgtStockConverter INSTANCE = Mappers.getMapper(Mc04IslmcCgtStockDOToMc04IslmcCgtStockConverter.class);

}
