/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.monthplan.monthplanadjreview;

import com.tobacco.app.isale.app.converter.BaseConverter;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadjreview.Mc04IslmMonthSalePlanAdj;
import com.tobacco.app.isale.dto.plan.monthplan.monthplanadjreview.Mc04IslmMonthSalePlanAdjDTO;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanAdjDO;
import com.tobacco.app.isale.req.plan.monthplan.monthplanadjreview.IslmMonthSalePlanAdjREQ;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> guosong02
 * @email : <EMAIL>
 * @create_time : 2025/08/06 10:28
 * @description : 批次DTO转换成批次model
 */
@Mapper
public interface MonthSalePlanAdjDOToMc04IslmMonthSalePlanAdjConverter extends StructureBaseConverter<Mc04IslmMonthSalePlanAdjDO, Mc04IslmMonthSalePlanAdj> {

    MonthSalePlanAdjDOToMc04IslmMonthSalePlanAdjConverter INSTANCE =
        Mappers.getMapper(MonthSalePlanAdjDOToMc04IslmMonthSalePlanAdjConverter.class);

}
