/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.basic;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tobacco.app.isale.domain.model.basic.IslmcFactory;
import com.tobacco.app.isale.domain.model.cont.whse.IslmcContDelivWhse;
import com.tobacco.app.isale.domain.repository.basic.IslmcFactoryRepository;
import com.tobacco.app.isale.infrastructure.converter.basic.IslmcFactoryDOTOMc04IslmcFactoryConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcFactoryDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcFactoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 工业卷烟厂（生产点） 应用服务实现类
 *
 * @Author: wmd
 * @Since: 2025-08-07
 * @Email: <EMAIL>
 * @Create: 2025-08-07
 */
@Slf4j
@Component("IslmcFactoryRepositoryImplRepositoryImplDORepository")
public class IslmcFactoryRepositoryImpl implements IslmcFactoryRepository {

    @Autowired
    private Mc04IslmcFactoryService mc04IslmcFactoryService;

    /**
     * 查询合同仓库分页数据
     * @param islmcFactory 查询参数
     * @return Page<IslmcContDelivWhseDTO>
     * <AUTHOR> wmd
     */
    @Override
    public Page<IslmcFactory> page(IslmcFactory islmcFactory){
        QueryWrapper<Mc04IslmcFactoryDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .like(StrUtil.isNotBlank(islmcFactory.getBaFactOrgCode()), Mc04IslmcFactoryDO::getBaFactOrgCode, islmcFactory.getBaFactOrgCode())
                .like(StrUtil.isNotBlank(islmcFactory.getBaFactOrgName()), Mc04IslmcFactoryDO::getBaFactOrgName, islmcFactory.getBaFactOrgName());
        wrapper.orderBy(StrUtil.isNotBlank(islmcFactory.getSort()) && StrUtil.isNotBlank(islmcFactory.getOrder()),
                "asc".equals(islmcFactory.getOrder()), StrUtil.toUnderlineCase(islmcFactory.getSort()));
        Page<Mc04IslmcFactoryDO> page = new Page<>(
                islmcFactory.getOffset()/islmcFactory.getLimit()+1,
                islmcFactory.getLimit()
        );
         Page<Mc04IslmcFactoryDO> ismFactoryPage = mc04IslmcFactoryService.page(page, wrapper);
        return (Page<IslmcFactory>) ismFactoryPage.convert(page1 ->{
            IslmcFactory islmcFactory1 =
                    IslmcFactoryDOTOMc04IslmcFactoryConverter.INSTANCE
                            .converterDoToModel(page1);
            return islmcFactory1;
        });
    }
    /**
     * 查询详情
     * @param islmcFactory 仓库代码
     * @return IslmcContDelivWhse
     * <AUTHOR> wmd
     */
    @Override
    public IslmcFactory detail(IslmcFactory islmcFactory) {
        QueryWrapper<Mc04IslmcFactoryDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(StrUtil.isNotBlank(islmcFactory.getBaFactOrgCode()),Mc04IslmcFactoryDO::getBaFactOrgCode,islmcFactory.getBaFactOrgCode());
        Mc04IslmcFactoryDO mc04IslmcFactoryDO = mc04IslmcFactoryService.getOne(wrapper);

        return IslmcFactoryDOTOMc04IslmcFactoryConverter.INSTANCE
                .converterDoToModel(mc04IslmcFactoryDO);
    }
    /**
     * 新增
     * @param islmcFactory 新增参数
     * @return IslmcContDelivWhse
     * <AUTHOR> wmd
     */
    @Override
    public Boolean create(IslmcFactory islmcFactory) {
        Mc04IslmcFactoryDO mc04IslmcFactoryDO = IslmcFactoryDOTOMc04IslmcFactoryConverter.INSTANCE
                .converterModelToDo(islmcFactory);
        Boolean b= mc04IslmcFactoryService.save(mc04IslmcFactoryDO);
        return b;
    }
    /**
     * 删除
     * @param islmcFactory 仓库代码
     * @return Boolean
     * <AUTHOR> wmd
     */
    @Override
    public Boolean delete(IslmcFactory islmcFactory) {
        QueryWrapper<Mc04IslmcFactoryDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(StrUtil.isNotBlank(islmcFactory.getBaFactOrgCode()),Mc04IslmcFactoryDO::getBaFactOrgCode ,islmcFactory.getBaFactOrgCode());
        Boolean b = mc04IslmcFactoryService.remove(wrapper);
        return b;
    }
    /**
     * 更新
     * @param islmcFactory 更新参数
     * @return Boolean
     * <AUTHOR> wmd
     */
    @Override
    public Boolean update(IslmcFactory islmcFactory) {
        QueryWrapper<Mc04IslmcFactoryDO> wrapper = new QueryWrapper<>();
        Boolean b =  mc04IslmcFactoryService.lambdaUpdate()
                .eq(Mc04IslmcFactoryDO::getBaFactOrgCode, islmcFactory.getBaFactOrgCode())
                .set(Mc04IslmcFactoryDO::getBaFactOrgName, islmcFactory.getBaFactOrgName())
                .set(Mc04IslmcFactoryDO::getBaFactIndustryCode,islmcFactory.getBaFactIndustryCode())
                .set(Mc04IslmcFactoryDO::getBaFactShortname,islmcFactory.getBaFactShortname())
                .set(Mc04IslmcFactoryDO::getIsUse,islmcFactory.getIsUse())
                .set(Mc04IslmcFactoryDO::getSeq,islmcFactory.getSeq())
                .update();
        return null;
    }

    /**
     * 查询
     *
     * @param factOrgCodeList 工厂编码
     * @return IslmcFactory
     * <AUTHOR> wmd
     */
    @Override
    public List<IslmcFactory> queryFactOrgListByPk(List<String> factOrgCodeList) {
        return mc04IslmcFactoryService.listByIds(factOrgCodeList)
                .stream()
                .map(IslmcFactoryDOTOMc04IslmcFactoryConverter.INSTANCE::converterDoToModel)
                .collect(Collectors.toList());
    }


    /**
     * 校验是否已存在
     * @param islmcFactory 卷烟厂代码
     * @return Boolean
     * <AUTHOR> wmd
     */
    @Override
    public Boolean checkBaFactOrgCode(IslmcFactory islmcFactory){
        String baFactOrgCode = islmcFactory.getBaFactOrgCode();
        QueryWrapper<Mc04IslmcFactoryDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(StrUtil.isNotBlank(baFactOrgCode), Mc04IslmcFactoryDO::getBaFactOrgCode, baFactOrgCode);
        List<Mc04IslmcFactoryDO> list = mc04IslmcFactoryService.list(wrapper);
        if(CollectionUtil.isNotEmpty(list)){
            return false;
        }else {
            return true;
        }
    }
}
