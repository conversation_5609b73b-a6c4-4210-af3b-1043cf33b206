package com.tobacco.app.isale.infrastructure.converter.cont.whse;


import com.tobacco.app.isale.domain.model.cont.whse.IslmcContDelivWhse;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
@Mapper
public interface Mc04IslmcContDelivWhseDOToIslmcContDelivWhseConverter
        extends StructureBaseConverter<Mc04IslmcContDelivWhseDO, IslmcContDelivWhse> {
    Mc04IslmcContDelivWhseDOToIslmcContDelivWhseConverter INSTANCE = Mappers.getMapper(Mc04IslmcContDelivWhseDOToIslmcContDelivWhseConverter.class);
}
