/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.basic;

import com.tobacco.app.isale.domain.model.basic.BusiActionLog;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmBusiActionLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: wlh
 * @Create: 2025-07-22
 */

@Mapper
public interface Mc04IslmBusiActionLogDOToMc04IslmBusiActionLogConverter
        extends StructureBaseConverter<Mc04IslmBusiActionLogDO, BusiActionLog> {

    Mc04IslmBusiActionLogDOToMc04IslmBusiActionLogConverter INSTANCE =
            Mappers.getMapper(Mc04IslmBusiActionLogDOToMc04IslmBusiActionLogConverter.class);

}