/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.monthplan.monthplanadjreview;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.tobacco.app.isale.domain.enums.plan.monthplan.MonthSalePlanStatusEnum;
import com.tobacco.app.isale.domain.enums.plan.monthplan.TobaProdTradeTypeCodeEnum;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadj.MonthPlanAdjItemAdd;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadjreview.Mc04IslmMonthSalePlanAdj;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadjreview.Mc04MonthPlanAdjReviewQuery;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadjreview.Mc04MonthPlanAdjReviewQueryResult;
import com.tobacco.app.isale.domain.repository.plan.monthplan.monthplanadjreview.IslmMonthPlanAdjReviewRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.monthplanadjreview.MonthPlanAdjReviewFlowConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.monthplanadjreview.MonthSalePlanAdjDOToMc04IslmMonthSalePlanAdjConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanAdjDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanAdjService;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan.monthplanadjreview.IslmMonthPlanAdjReviewMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Author: jiaxiyang
 * @Date: 2025/8/18
 * @Description:
 */
@Component("IslmMonthPlanAdjReviewRepository")
public class IslmMonthPlanAdjReviewRepositoryImpl implements IslmMonthPlanAdjReviewRepository {

    @Autowired
    IslmMonthPlanAdjReviewMapper islmMonthPlanAdjReviewMapper;

    @Autowired
    private Mc04IslmMonthSalePlanAdjService mc04IslmMonthSalePlanAdjService;

    /**
     * 查询卷烟树状态
     *
     * @param ma02PlanMonth
     * @param icomCode
     * @return
     */
    @Override
    public List<Map<String, Object>> queryCgtStatus(String ma02PlanMonth, String icomCode) {
        return islmMonthPlanAdjReviewMapper.queryCgtStatus(ma02PlanMonth, icomCode);
    }

    @Override
    public Boolean batchUpdate(List<Mc04IslmMonthSalePlanAdj> mc04IslmMonthSalePlanAdjs) {
        List<Mc04IslmMonthSalePlanAdjDO> mc04IslmMonthSalePlanAdjDos = MonthSalePlanAdjDOToMc04IslmMonthSalePlanAdjConverter.INSTANCE.converterModelsToDos(mc04IslmMonthSalePlanAdjs);
        return mc04IslmMonthSalePlanAdjService.updateBatchById(mc04IslmMonthSalePlanAdjDos);
    }


    @Override
    public List<Mc04IslmMonthSalePlanAdj> queryListByIds(List<String> monthSalePlanAdjItemIds) {
        List<Mc04IslmMonthSalePlanAdjDO> mc04IslmMonthSalePlanAdjDos = mc04IslmMonthSalePlanAdjService.listByIds(monthSalePlanAdjItemIds);
        return MonthSalePlanAdjDOToMc04IslmMonthSalePlanAdjConverter.INSTANCE.converterDosToModels(mc04IslmMonthSalePlanAdjDos);
    }

    @Override
    public boolean batchUpdateStatus(List<String> mc04MonthSalePlanAdjItemIds, MonthSalePlanStatusEnum monthSalePlanStatusEnum) {
        UpdateWrapper<Mc04IslmMonthSalePlanAdjDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(Mc04IslmMonthSalePlanAdjDO::getMc04MonthSalePlanAdjItemId, mc04MonthSalePlanAdjItemIds)
                .set(Mc04IslmMonthSalePlanAdjDO:: getMc04MonthSalePlanStatus, monthSalePlanStatusEnum.getCode());
        return mc04IslmMonthSalePlanAdjService.update(updateWrapper);
    }

    @Override
    public Boolean updateBatchById(List<MonthPlanAdjItemAdd> monthSalePlanAdjs) {
        List<Mc04IslmMonthSalePlanAdjDO> monthSalePlanDOList = MonthPlanAdjReviewFlowConverter.INSTANCE.converterModelsToDos(monthSalePlanAdjs);
        return mc04IslmMonthSalePlanAdjService.updateBatchById(monthSalePlanDOList);
    }

    @Override
    public List<MonthPlanAdjItemAdd> selectList(List<String> baComOrgCodes, String ma02PlanMonth, String status) {
        QueryWrapper<Mc04IslmMonthSalePlanAdjDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmMonthSalePlanAdjDO::getMa02PlanMonth, ma02PlanMonth)
                .eq(Mc04IslmMonthSalePlanAdjDO::getMc04MonthSalePlanStatus, status)
                .in(Mc04IslmMonthSalePlanAdjDO::getBaComOrgCode, baComOrgCodes);
        return MonthPlanAdjReviewFlowConverter.INSTANCE.converterDosToModels(mc04IslmMonthSalePlanAdjService.list(queryWrapper));
    }

    @Override
    public String getPlanSubjectType(String year, String tobaProdTradeTypeCode) {
        return islmMonthPlanAdjReviewMapper.getPlanSubjectType(year, tobaProdTradeTypeCode);
    }

    /**
     * 获取数据日期
     *
     * @return
     */
    @Override
    public String getDataDate() {
        return islmMonthPlanAdjReviewMapper.getDataDate();
    }

    @Override
    public List<Map<String, Object>> getSaleStkDayList(String acCgtCartonCode, String dataDate) {
        return islmMonthPlanAdjReviewMapper.getSaleStkDayList(acCgtCartonCode, dataDate);
    }

    /**
     * 获取列表
     *
     */
    @Override
    public List<Mc04MonthPlanAdjReviewQueryResult> getMonthPlanAdjBrandList(Mc04MonthPlanAdjReviewQuery condition) {
        return islmMonthPlanAdjReviewMapper.getMonthPlanAdjBrandList(condition);
    }

    /**
     * 查询行情价格
     *
     * @param acCgtCartonCode 卷烟编码
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @return
     */
    @Override
    public List<Map<String, Object>> getMarketPrice(String acCgtCartonCode, String startTime, String endTime) {
        return islmMonthPlanAdjReviewMapper.getMarketPrice(acCgtCartonCode, startTime, endTime);
    }

    /**
     * 获取列表
     *
     * @param mc04MonthSalePlanItemIds
     * @return
     */
    @Override
    public List<MonthPlanAdjItemAdd> selectBatchIds(List<String> mc04MonthSalePlanItemIds) {
        return MonthPlanAdjReviewFlowConverter.INSTANCE.converterDosToModels(mc04IslmMonthSalePlanAdjService.listByIds(mc04MonthSalePlanItemIds));
    }

    @Override
    public List<Mc04IslmMonthSalePlanAdj> queryListByPlanIds(List<String> planIds) {
        QueryWrapper<Mc04IslmMonthSalePlanAdjDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(Mc04IslmMonthSalePlanAdjDO::getMc04MonthSalePlanAdjId,planIds).
                eq(Mc04IslmMonthSalePlanAdjDO::getMa02TobaProdTradeTypeCode, TobaProdTradeTypeCodeEnum.CIGARETTE.getCode());

        List<Mc04IslmMonthSalePlanAdjDO> list = mc04IslmMonthSalePlanAdjService.list(queryWrapper);
        return MonthSalePlanAdjDOToMc04IslmMonthSalePlanAdjConverter.INSTANCE.converterDosToModels(list);
    }
}
