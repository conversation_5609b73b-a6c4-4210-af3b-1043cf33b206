/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.order.ret.bill;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomException;
import com.tobacco.app.isale.domain.model.order.ret.bill.ReturnBillDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.apply.ApplyDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.apply.ApplyItemDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.areaconfirm.AreaConfirmDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.ReturnBillItemDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.arriveconfirm.ArriveConfirmDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.centeraudit.CenterAuditDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.contstart.ContStartDomain;
import com.tobacco.app.isale.domain.model.order.ret.bill.contstart.ContStartItem;
import com.tobacco.app.isale.domain.model.order.ret.bill.transstart.TransStartDomain;
import com.tobacco.app.isale.domain.repository.order.ret.bill.ReturnBillItemRepository;
import com.tobacco.app.isale.infrastructure.converter.order.ret.bill.ReturnBillItemDomainToDoConverter;
import com.tobacco.app.isale.infrastructure.converter.order.ret.bill.apply.ReturnBillItemDOToModelConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtReturnBillDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtReturnBillItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcCgtReturnBillItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcCgtReturnBillItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcCgtReturnBillService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: qifengyu
 * @Since: 2025-07-25
 */
@Component("ISaleMc04IslmcCgtReturnBillItemDORepository")
public class Mc04IslmcCgtReturnBillItemRepositoryImpl implements ReturnBillItemRepository {

    private Mc04IslmcCgtReturnBillItemService billItemService;

    private Mc04IslmcCgtReturnBillService billService;

    @Autowired
    public void setBillService(Mc04IslmcCgtReturnBillService billService) {
        this.billService = billService;
    }

    @Autowired
    public void setBillItemService(Mc04IslmcCgtReturnBillItemService billItemService) {
        this.billItemService = billItemService;
    }

    /**
     * 查询退货单详情
     *
     * @param returnBillIdList 退货单
     */
    @Override
    public List<ReturnBillItemDomain> queryReturnBillItemList(List<String> returnBillIdList) {

        LambdaQueryChainWrapper<Mc04IslmcCgtReturnBillItemDO> queryChainWrapper = billItemService.lambdaQuery();
        if (returnBillIdList != null && returnBillIdList.size() > 0) {
            queryChainWrapper.in(Mc04IslmcCgtReturnBillItemDO::getMc04CgtReturnBillId, returnBillIdList);
        }
        List<Mc04IslmcCgtReturnBillItemDO> returnBillItemDOList = queryChainWrapper.list();
        return ReturnBillItemDomainToDoConverter.INSTANCE.converterDosToModels(returnBillItemDOList);
    }

    /**
     * 更新退货单和详情信息
     *
     * @param applyDomain 要更新的列表
     */
    @Override
    public Boolean updateBillAndItem(ApplyDomain applyDomain) {
        List<ApplyItemDomain> applyItemDomainList = applyDomain.getApplyItemDomainList();
        //如果id不为空 说明是修改
        Mc04IslmcCgtReturnBillDO mc04IslmcCgtReturnBillDO = new Mc04IslmcCgtReturnBillDO();
        BeanUtils.copyProperties(applyDomain, mc04IslmcCgtReturnBillDO);
        billService.updateById(mc04IslmcCgtReturnBillDO);
        List<Mc04IslmcCgtReturnBillItemDO> billItemList = ReturnBillItemDOToModelConverter.INSTANCE.converterModelsToDos(applyItemDomainList);
        billItemList.forEach(billItemDO -> billItemDO.setMc04CgtReturnBillId(applyDomain.getMc04CgtReturnBillId()));
        return billItemService.saveOrUpdateBatch(billItemList);
    }

    /**
     * 校验提交数量是否符合规则
     *
     * @param qtyList 查询参数
     * @return Boolean
     * @create_time : 2025-07-23 09:14:49
     * @description : 校验提交数量是否符合规则
     */
    @Override
    public Boolean verifySubmitQty(List<BigDecimal>  qtyList) {
        if(qtyList.stream().filter(Objects::nonNull).anyMatch(qty -> qty.compareTo(BigDecimal.ZERO) > 0)){
            Assert.isTrue(false, () -> new CustomException("退货量不能大于0！"));
        }
        // 校验退货到货量合计不能等于0
        BigDecimal totalQty = qtyList.stream().filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalQty.compareTo(BigDecimal.ZERO) == 0) {
            Assert.isTrue(false, () -> new CustomException("退货量合计不能等于0！"));
        }
        // 校验单规格的退货到货量不能大于0（必须小于等于0）
        return true;
    }

    /**
     * 根据ID更新退货单
     *
     * @param areaConfirmSubmitREQList 传入的REQ
     * @return Boolean
     * @create_time : 2025-07-23 09:14:49
     * @description : 校验提交数量是否符合规则
     */
    @Override
    public Boolean updateAuditQty(List<AreaConfirmDomain> areaConfirmSubmitREQList) {
        Map<String, BigDecimal> map = areaConfirmSubmitREQList.stream().collect(Collectors.toMap(AreaConfirmDomain::getMc04CgtReturnBillItemId, AreaConfirmDomain::getMc04CgtReturnBillSaleAreaAuditQty));
        List<Mc04IslmcCgtReturnBillItemDO> list = billItemService.lambdaQuery().in(Mc04IslmcCgtReturnBillItemDO::getMc04CgtReturnBillItemId, map.keySet()).list();
        for (Mc04IslmcCgtReturnBillItemDO itemDO : list) {
            itemDO.setMc04CgtReturnBillSaleAreaAuditQty(map.get(itemDO.getMc04CgtReturnBillItemId()));
        }
        return billItemService.updateBatchById(list);
    }

    /**
     * 根据ID列表查询退货订单详情列
     *
     * @param returnBillIdList 查询参数
     * @return Boolean
     * @create_time : 2025-07-23 09:14:49
     * @description : 校验提交数量是否符合规则
     */
    @Override
    public List<ReturnBillItemDomain> listTransStartReturnBillItemByIdList(List<String> returnBillIdList) {
        if (CollectionUtil.isNotEmpty(returnBillIdList)) {
            LambdaQueryChainWrapper<Mc04IslmcCgtReturnBillItemDO> lambdaQueryChainWrapper = billItemService.lambdaQuery();
            lambdaQueryChainWrapper.in(Mc04IslmcCgtReturnBillItemDO::getMc04CgtReturnBillId, returnBillIdList);
            return ReturnBillItemDomainToDoConverter.INSTANCE.converterDosToModels(lambdaQueryChainWrapper.list());
        }
        return new ArrayList<>();
    }
    /**
     * 先更新子表的数量 确认不能大于0，如果都不大于0，则更新子表；子表更新成功，则更新主表，如果失败则退出并回滚已更新的
     *
     * @param centerAuditDomainList 子表的数据
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean centerAuditBatchItem(List<CenterAuditDomain> centerAuditDomainList) {
        if (centerAuditDomainList == null || centerAuditDomainList.isEmpty()) {
            return false;
        }
        // 先验证所有数据是否符合要求
        for (CenterAuditDomain item : centerAuditDomainList) {
            if (item.getMc04CgtReturnBillSaleCenterAuditQty().compareTo(BigDecimal.ZERO) > 0) {
                return false;
            }
        }
        // 已经确认数据符合要求，根据传入的参数匹配id，批量更新子表的审核数量
        try {
            List<Mc04IslmcCgtReturnBillItemDO> itemDos = centerAuditDomainList.stream()
                    .filter(item -> item.getMc04CgtReturnBillItemId() != null)
                    .map(item -> {
                        Mc04IslmcCgtReturnBillItemDO itemDO = new Mc04IslmcCgtReturnBillItemDO();
                        itemDO.setMc04CgtReturnBillItemId(item.getMc04CgtReturnBillItemId());
                        itemDO.setMc04CgtReturnBillSaleCenterAuditQty(item.getMc04CgtReturnBillSaleCenterAuditQty());
                        return itemDO;
                    })
                    .collect(Collectors.toList());

            // 批量更新审核数量
            return billItemService.updateBatchById(itemDos);
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("提交审核失败，请联系管理员");
        }
    }

    /**
     * 批量更新订单运输单号
     * @param transStartDomainList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateTransTransCertNo(List<TransStartDomain> transStartDomainList){
        try {
            List<Mc04IslmcCgtReturnBillItemDO> itemDos = transStartDomainList.stream()
                    .filter(item -> item.getMc04CgtReturnBillItemId() != null)
                    .map(item -> {
                        Mc04IslmcCgtReturnBillItemDO itemDO = new Mc04IslmcCgtReturnBillItemDO();
                        itemDO.setMc04CgtReturnBillItemId(item.getMc04CgtReturnBillItemId());
                        itemDO.setMd02CgtTransTranscertNo(item.getMd02CgtTransTranscertNo());
                        return itemDO;
                    })
                    .collect(Collectors.toList());

            // 批量更新审核数量
            return billItemService.updateBatchById(itemDos);
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("提交审核失败，请联系管理员");
        }

    }
    /**
     * 批量更新订单运输单号
     * @param arriveConfirmDomainList
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateArriveConfirm(List<ArriveConfirmDomain> arriveConfirmDomainList) {
        try {
            List<Mc04IslmcCgtReturnBillItemDO> itemDos = arriveConfirmDomainList.stream()
                    .filter(item -> item.getMc04CgtReturnBillItemId() != null)
                    .map(item -> {
                        Mc04IslmcCgtReturnBillItemDO itemDO = new Mc04IslmcCgtReturnBillItemDO();
                        itemDO.setMc04CgtReturnBillItemId(item.getMc04CgtReturnBillItemId());
                        itemDO.setMc05Cgt10thIndRebackInQty(item.getMc05Cgt10thIndRebackInQty());
                        return itemDO;
                    })
                    .collect(Collectors.toList());
            return billItemService.updateBatchById(itemDos);
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("提交审核失败，请联系管理员");
        }
    }

    /**
     * 获取中心审核信息
     * @param billDO
     * @return ContStartDomain
     */
    @Override
    public ContStartDomain getCenterAuditInfo(ReturnBillDomain billDO) {
        List<Mc04IslmcCgtReturnBillItemDO> billItemList = billItemService.lambdaQuery().eq(Mc04IslmcCgtReturnBillItemDO::getMc04CgtReturnBillId, billDO.getMc04CgtReturnBillId()).list();
        if(CollectionUtil.isEmpty(billItemList)){
            return null;
        }
        List<ContStartItem> returnBillSpecList = new ArrayList<>();
        for (Mc04IslmcCgtReturnBillItemDO mc04IslmcCgtReturnBillItemDO : billItemList) {
            ContStartItem contStartItem = new ContStartItem();
            BeanUtils.copyProperties(mc04IslmcCgtReturnBillItemDO, contStartItem);
            returnBillSpecList.add(contStartItem);
        }
        BigDecimal InQtySum = billItemList.stream()
                .map(Mc04IslmcCgtReturnBillItemDO::getMc04CgtReturnBillSaleCenterAuditQty)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        ContStartDomain contStartDomain = new ContStartDomain();
        BeanUtils.copyProperties(billDO, contStartDomain);
        contStartDomain.setMc04CgtReturnBillSpec(returnBillSpecList);
        contStartDomain.setBaComOrgName(billDO.getBaComOrgCode());
        contStartDomain.setRebackInQtySum(InQtySum);
        return contStartDomain;
    }
}