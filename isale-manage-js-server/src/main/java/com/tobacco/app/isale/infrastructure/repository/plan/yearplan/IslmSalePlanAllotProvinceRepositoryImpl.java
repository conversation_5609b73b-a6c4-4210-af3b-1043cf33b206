/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.yearplan;

import com.tobacco.app.isale.domain.model.plan.PlanQty;
import com.tobacco.app.isale.domain.model.plan.yearplan.yearplanallot.YearPlanAllotProvince;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmSalePlanAllotProvinceRepository;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmSalePlanMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 销量初始化 领域仓库实现
 *
 * @Author: longxi
 * @Since: 2025-08-07
 */
@Component("IslmSalePlanAllotProvinceRepository")
public class IslmSalePlanAllotProvinceRepositoryImpl implements IslmSalePlanAllotProvinceRepository {

    @Autowired
    private IslmSalePlanMapper islmSalePlanMapper;

    @Override
    public List<YearPlanAllotProvince> getAllProductIncludeCityYearEndStk(String year, List<String> cityList) {
        List<PlanQty> planQtyList = islmSalePlanMapper.getAllProductIncludeCityYearEndStk(year, cityList);
        List<YearPlanAllotProvince> result = new ArrayList<>();

        for (PlanQty planQty : planQtyList) {
            YearPlanAllotProvince province = new YearPlanAllotProvince();
            province.setProductCode(planQty.getProductCode());
            province.setBusiComCode(planQty.getBusiComCode());
            province.setYearEndStock(planQty.getYearEndStock());
            result.add(province);
        }

        return result;
    }
}
