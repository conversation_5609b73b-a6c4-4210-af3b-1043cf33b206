package com.tobacco.app.isale.infrastructure.repository.supply.psc.allocdemand.manage;

import cn.hutool.core.lang.Assert;
import com.alibaba.druid.util.StringUtils;
import com.inspur.ind.base.CustomException;
import com.tobacco.app.isale.domain.enums.supply.psc.allocdemand.IsaleSupplyDemandAjStatusEnum;
import com.tobacco.app.isale.domain.model.supply.psc.allocdemand.TranDemandAdjItemDomain;
import com.tobacco.app.isale.domain.repository.supply.psc.allocdemand.manage.TranDemandAdjItemRepository;
import com.tobacco.app.isale.infrastructure.converter.supply.psc.allocdemand.TranDemandAdjItemDomainToDoConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtTranDemandAdjDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtTranDemandAdjItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmCgtTranDemandAdjItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmCgtTranDemandAdjService;
import com.tobacco.app.isale.req.supply.psc.allocdemand.adjustaudit.AllocdemandAdjustauditIdListReq;
import com.tobacco.app.isale.req.supply.psc.allocdemand.adjustaudit.AllocdemandAdjustauditIdReq;
import com.tobacco.app.isale.req.supply.psc.allocdemand.adjustaudit.AllocdemandAdjustauditReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.tobacco.app.isale.domain.enums.supply.psc.allocdemand.IsaleSupplyDemandAjStatusEnum.*;

/**
 * <AUTHOR>
 */
@Component("ISaleMc04IslmCgtTranDemandAdjItemRepository")
public class Mc04IslmCgtTranDemandAdjItemRepositoryImpl implements TranDemandAdjItemRepository {

    private Mc04IslmCgtTranDemandAdjItemService mc04IslmCgtTranDemandAdjItemService;

    private Mc04IslmCgtTranDemandAdjService mc04IslmCgtTranDemandAdjService;

    @Autowired
    public void setMc04IslmCgtTranDemandAdjItemService(Mc04IslmCgtTranDemandAdjItemService mc04IslmCgtTranDemandAdjItemService) {
        this.mc04IslmCgtTranDemandAdjItemService = mc04IslmCgtTranDemandAdjItemService;
    }

    @Autowired
    public void setMc04IslmCgtTranDemandAdjService(Mc04IslmCgtTranDemandAdjService mc04IslmCgtTranDemandAdjService) {
        this.mc04IslmCgtTranDemandAdjService = mc04IslmCgtTranDemandAdjService;
    }

    @Override
    public Boolean batchUpdateData(List<TranDemandAdjItemDomain> tranDemandAdjItemDomainList) {
        List<Mc04IslmCgtTranDemandAdjItemDO> list = TranDemandAdjItemDomainToDoConverter.INSTANCE.converterModelsToDos(tranDemandAdjItemDomainList);
        return mc04IslmCgtTranDemandAdjItemService.updateBatchById(list);
    }

    /**
     * 更新状态
     * @param adjIdList 调拨列表
     * @param preStatus 前置条件
     * @param status 变更状态
     * @return Boolean
     */
    @Override
    public Boolean updateStatus(List<String> adjIdList, IsaleSupplyDemandAjStatusEnum preStatus, IsaleSupplyDemandAjStatusEnum status) {
        //判断前置状态

        List<Mc04IslmCgtTranDemandAdjDO> demandAdjDOList = mc04IslmCgtTranDemandAdjService.lambdaQuery().in(Mc04IslmCgtTranDemandAdjDO::getMc04MonthSalePlanAdjId, adjIdList).list();
        boolean hasInvalidStatus = demandAdjDOList.stream()
                .anyMatch(demandAdjDO -> !StringUtils.equals(preStatus.getStatus(), demandAdjDO.getMc04CgtTranDemandAdjStatus()));
        Assert.isTrue(!hasInvalidStatus, () -> new CustomException("提交失败，存在非"+preStatus.getDesc()+"状态调拨，请检查后重新提交！"));

        return mc04IslmCgtTranDemandAdjService.lambdaUpdate()
                .in(Mc04IslmCgtTranDemandAdjDO::getMc04MonthSalePlanAdjId, adjIdList)
                .set(Mc04IslmCgtTranDemandAdjDO::getMc04CgtTranDemandAdjStatus, status.getStatus())
                .update();
    }

    /**
     * 保存调拨单详情
     * @param demandAdjItemDomainList 调拨单详情
     * @return Boolean
     */
    @Override
    public Boolean saveBatch(List<TranDemandAdjItemDomain> demandAdjItemDomainList) {
        List<Mc04IslmCgtTranDemandAdjItemDO> list = TranDemandAdjItemDomainToDoConverter.INSTANCE.converterModelsToDos(demandAdjItemDomainList);
        return mc04IslmCgtTranDemandAdjItemService.saveBatch(list);
    }

    /**
     * 根据调拨单ID查询调拨单详情
     * @param mc04MonthSalePlanAdjId 根据调拨单ID
     * @return  List<TranDemandAdjItemDomain>
     */
    @Override
    public List<TranDemandAdjItemDomain> getAdjItemById(String mc04MonthSalePlanAdjId) {
        Mc04IslmCgtTranDemandAdjDO mc04IslmCgtTranDemandAdjDO = mc04IslmCgtTranDemandAdjService.lambdaQuery().eq(Mc04IslmCgtTranDemandAdjDO::getMc04MonthSalePlanAdjId, mc04MonthSalePlanAdjId).one();
        Assert.notNull(mc04IslmCgtTranDemandAdjDO, () -> new CustomException("传入查询参数有误，请检查后重新操作！"));
        List<Mc04IslmCgtTranDemandAdjItemDO> list = mc04IslmCgtTranDemandAdjItemService.lambdaQuery().eq(Mc04IslmCgtTranDemandAdjItemDO::getMc04MonthSalePlanAdjId, mc04MonthSalePlanAdjId).list();
        return TranDemandAdjItemDomainToDoConverter.INSTANCE.converterDosToModels(list);
    }

}
