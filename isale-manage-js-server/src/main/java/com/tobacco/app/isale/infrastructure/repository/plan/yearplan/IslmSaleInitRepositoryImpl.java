/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.yearplan;

import com.inspur.ind.util.IDUtils;
import com.tobacco.app.isale.domain.model.plan.yearplan.IslmSaleInitModel;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmSaleInitRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmSaleInitDoConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSaleInitDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmSaleInitService;
import com.tobacco.app.isale.tools.utils.OrderedIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 销量初始化 领域仓库实现
 *
 * @Author: longxi
 * @Since: 2025-08-07
 */
@Component("IslmSaleInitRepository")
public class IslmSaleInitRepositoryImpl implements IslmSaleInitRepository {

    @Autowired
    private Mc04IslmSaleInitService islmSaleInitService;

    @Override
    public IslmSaleInitModel getById(String mc04SaleInitId) {
        Mc04IslmSaleInitDO saleInitDo = islmSaleInitService.getById(mc04SaleInitId);
        return Mc04IslmSaleInitDoConverter.INSTANCE.converterDoToModel(saleInitDo);
    }

    @Override
    public boolean save(IslmSaleInitModel saleInitModel) {
        if (saleInitModel != null) {
            saleInitModel.setMc04SaleInitId(OrderedIdGenerator.generateOrderedId32());
        }
        Mc04IslmSaleInitDO mc04IslmSaleInitDO = Mc04IslmSaleInitDoConverter.INSTANCE.converterModelToDo(saleInitModel);
        return islmSaleInitService.save(mc04IslmSaleInitDO);
    }

    @Override
    public boolean updateById(IslmSaleInitModel saleInitModel) {
        Mc04IslmSaleInitDO mc04IslmSaleInitDO = Mc04IslmSaleInitDoConverter.INSTANCE.converterModelToDo(saleInitModel);
        return islmSaleInitService.updateById(mc04IslmSaleInitDO);
    }
}
