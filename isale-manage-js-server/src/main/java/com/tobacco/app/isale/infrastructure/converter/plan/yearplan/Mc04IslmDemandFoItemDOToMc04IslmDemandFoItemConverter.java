/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.yearplan;

import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmDemandFoItem;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmDemandFoItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: loongxi
 * @Email: <EMAIL>
 * @Create: 2025-08-07
 */

@Mapper

public interface Mc04IslmDemandFoItemDOToMc04IslmDemandFoItemConverter extends StructureBaseConverter<Mc04IslmDemandFoItemDO, Mc04IslmDemandFoItem> {

        Mc04IslmDemandFoItemDOToMc04IslmDemandFoItemConverter INSTANCE =
            Mappers.getMapper(Mc04IslmDemandFoItemDOToMc04IslmDemandFoItemConverter.class);

}