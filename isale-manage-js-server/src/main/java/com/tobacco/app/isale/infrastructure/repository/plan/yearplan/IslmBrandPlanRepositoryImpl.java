/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.yearplan;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomPage;
import com.tobacco.app.imarket.dto.infocoll.eval.rule.ImmInfoCollEvalRuleDTO;
import com.tobacco.app.isale.domain.enums.plan.ycplan.OrgTypeEnum;
import com.tobacco.app.isale.domain.enums.plan.yearplan.BrandPlanSubjectStatusEnum;
import com.tobacco.app.isale.domain.enums.plan.yearplan.YearPlanOrgTypeEnum;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmBrandPlanModel;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmBrandPlanRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmBrandPlanConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmBrandPlanDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmBrandPlanService;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmBrandPlanMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 品牌发展规划领域仓库实现
 *
 * @Author: longxi
 * @Since: 2025-08-07
 */
@Component("IslmBrandPlanRepository")
public class IslmBrandPlanRepositoryImpl implements IslmBrandPlanRepository {

    @Autowired
    private IslmBrandPlanMapper mc04IslmBrandPlanMapper;

    @Autowired
    private Mc04IslmBrandPlanService mc04IslmBrandPlanService;

    @Override
    public List<Mc04IslmBrandPlanModel> query(LambdaQueryWrapper<Mc04IslmBrandPlanDO> queryWrapper) {
        List<Mc04IslmBrandPlanDO> list = mc04IslmBrandPlanService.list(queryWrapper);
        List<Mc04IslmBrandPlanModel> modelList = Mc04IslmBrandPlanConverter.INSTANCE.converterDosToModels(list);
        return modelList;
    }

    @Override
    public Mc04IslmBrandPlanModel getById(String mc04BrandPlanId) {
        Mc04IslmBrandPlanDO byId = mc04IslmBrandPlanService.getById(mc04BrandPlanId);
        Mc04IslmBrandPlanModel model = Mc04IslmBrandPlanConverter.INSTANCE.converterDoToModel(byId);
        return model;
    }

    @Override
    public List<Mc04IslmBrandPlanModel> getSameBrandPlan(String version) {
        List<Mc04IslmBrandPlanDO> list = mc04IslmBrandPlanService.list(
                new LambdaQueryWrapper<Mc04IslmBrandPlanDO>()
                        .eq(Mc04IslmBrandPlanDO::getMc04PlanSubjectStatus, BrandPlanSubjectStatusEnum.PUBLISH.getStatus())
                        .eq(Mc04IslmBrandPlanDO::getMc04BrandPlanVersion, version));
        List<Mc04IslmBrandPlanModel> modelList = Mc04IslmBrandPlanConverter.INSTANCE.converterDosToModels(list);
        return modelList;
    }

    @Override
    public List<Mc04IslmBrandPlanModel> submit() {
        return null;
    }

    @Override
    public Page<Mc04IslmBrandPlanModel> page(CustomPage<ImmInfoCollEvalRuleDTO> customPage,
                                             String startYear, String endYear, String type, String ma02TobaProdTradeTypeCode,
                                             String status, String icomCode, String isLastestVersion, String zaOccurrenceYear) {
        LambdaQueryWrapper<Mc04IslmBrandPlanDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(icomCode)) {
            queryWrapper.eq(Mc04IslmBrandPlanDO::getIcomCode, icomCode);
        }
//todo 弃用 queryWrapper.eq(Mc04IslmBrandPlanDO::getMz10IndexItemDataType, datatype)
        if (StrUtil.isNotBlank(isLastestVersion)) {
            queryWrapper.eq(Mc04IslmBrandPlanDO::getMc04IsLastestVersion, isLastestVersion);
        }
        if (StrUtil.isNotBlank(type)) {
            queryWrapper.eq(Mc04IslmBrandPlanDO::getMc04PlanSubjectType, type);
        }
        if (StrUtil.isNotBlank(ma02TobaProdTradeTypeCode)) {
            queryWrapper.eq(Mc04IslmBrandPlanDO::getMa02TobaProdTradeTypeCode, ma02TobaProdTradeTypeCode);
        }
        if (StrUtil.isNotBlank(status)) {
            queryWrapper.eq(Mc04IslmBrandPlanDO::getMc04PlanSubjectStatus, status);
        }
        if (StrUtil.isNotBlank(startYear) && StrUtil.isNotBlank(endYear)) {
            queryWrapper.ge(Mc04IslmBrandPlanDO::getZaOccurrenceYear, startYear)
                    .le(Mc04IslmBrandPlanDO::getZaOccurrenceYear, endYear);

        }
        if (StrUtil.isNotBlank(zaOccurrenceYear)) {
            queryWrapper.eq(Mc04IslmBrandPlanDO::getZaOccurrenceYear, zaOccurrenceYear);
        }
        queryWrapper.eq(Mc04IslmBrandPlanDO::getMc04OrgTypeKind, OrgTypeEnum.QG.getKind());
        long size = customPage.getSize();
        long current = customPage.getCurrent();
        Page<Mc04IslmBrandPlanDO> page = new Page<>(current, size);
        Page<Mc04IslmBrandPlanDO> doPage = mc04IslmBrandPlanService.page(page, queryWrapper);
        List<Mc04IslmBrandPlanModel> modelList = Mc04IslmBrandPlanConverter.INSTANCE.converterDosToModels(doPage.getRecords());
        Page<Mc04IslmBrandPlanModel> modelPage = new Page<>(current, size, doPage.getTotal());
        modelPage.setRecords(modelList);
//        mc04IslmBrandPlanMapper.page(page, startYear, endYear, type, ma02TobaProdTradeTypeCode, status, icomCode, isLastestVersion, zaOccurrenceYear)
        return modelPage;
    }

    @Override
    public Mc04IslmBrandPlanModel latest(String zaOccurrenceYear) {
        LambdaQueryWrapper<Mc04IslmBrandPlanDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmBrandPlanDO::getZaOccurrenceYear, zaOccurrenceYear)
                .eq(Mc04IslmBrandPlanDO::getMc04OrgTypeKind, YearPlanOrgTypeEnum.QG.getKind())
                .eq(Mc04IslmBrandPlanDO::getMc04OrgTypeCode, YearPlanOrgTypeEnum.QG.getCode())
                .orderByDesc(Mc04IslmBrandPlanDO::getCreateTime);
        List<Mc04IslmBrandPlanDO> list = mc04IslmBrandPlanService.list(queryWrapper);
        if (list.size() > 0) {
            Mc04IslmBrandPlanDO mc04IslmBrandPlanDO = list.get(0);
            return Mc04IslmBrandPlanConverter.INSTANCE.converterDoToModel(mc04IslmBrandPlanDO);
        }
        return null;
    }

    @Override
    public void save(Mc04IslmBrandPlanDO snBrandPlan) {
        mc04IslmBrandPlanService.save(snBrandPlan);
    }

    @Override
    public void update(Mc04IslmBrandPlanDO qgBrandPlan) {
        mc04IslmBrandPlanService.updateById(qgBrandPlan);
    }

    @Override
    public void updateBatch(List<Mc04IslmBrandPlanDO> listByVersion) {
        mc04IslmBrandPlanService.updateBatchById(listByVersion);
    }
}
