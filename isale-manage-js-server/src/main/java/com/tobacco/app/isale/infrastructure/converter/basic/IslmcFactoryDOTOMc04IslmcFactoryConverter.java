/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.basic;

import com.tobacco.app.isale.domain.model.basic.IslmcFactory;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcFactoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface IslmcFactoryDOTOMc04IslmcFactoryConverter
        extends StructureBaseConverter<Mc04IslmcFactoryDO,IslmcFactory> {
    IslmcFactoryDOTOMc04IslmcFactoryConverter INSTANCE=
            Mappers.getMapper(IslmcFactoryDOTOMc04IslmcFactoryConverter.class);
}
