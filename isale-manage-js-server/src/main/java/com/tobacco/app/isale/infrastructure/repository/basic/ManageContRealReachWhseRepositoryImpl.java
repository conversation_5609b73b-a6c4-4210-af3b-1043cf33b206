/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.basic;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.model.basic.ContRealReachWhse;
import com.tobacco.app.isale.domain.repository.basic.ManageContRealReachWhseRepository;
import com.tobacco.app.isale.infrastructure.converter.dist.order.ContRealReachWhseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmDelivSiteDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContRealReachWhseDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmDelivSiteService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContRealReachWhseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/08/19 17:30
 * @description : 仓库实收仓库信息
 */
@Slf4j
@Component("ISaleManageManageContRealReachWhseDORepository")
public class ManageContRealReachWhseRepositoryImpl implements ManageContRealReachWhseRepository {

    private Mc04IslmcContRealReachWhseService mc04IslmcContRealReachWhseService;


    @Autowired
    public void setMc04IslmcContRealReachWhseService(Mc04IslmcContRealReachWhseService mc04IslmcContRealReachWhseService) {
        this.mc04IslmcContRealReachWhseService = mc04IslmcContRealReachWhseService;
    }

    @Resource
    private Mc04IslmDelivSiteService mc04IslmDelivSiteService;

    /**
     * 公司实际到货仓库
     *
     * @param contRealReachWhse 公司实际到货仓库
     * @return 公司实际到货仓库
     */
    @Override
    public List<ContRealReachWhse> queryContRealReachWhse(ContRealReachWhse contRealReachWhse) {
        QueryWrapper<Mc04IslmcContRealReachWhseDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Mc04IslmcContRealReachWhseDO::getIcomCode, IcomUtils.getIcomCode())
                .eq(Mc04IslmcContRealReachWhseDO::getBaComOrgCode, contRealReachWhse.getBaComOrgCode())
                .eq(Mc04IslmcContRealReachWhseDO::getIsUse, "1");
        List<Mc04IslmcContRealReachWhseDO> list = mc04IslmcContRealReachWhseService.list(queryWrapper);
        // 获取交货站点名称
        List<String> delivCodes = list.stream().map(Mc04IslmcContRealReachWhseDO::getBaDelivSiteCode).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(delivCodes)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Mc04IslmDelivSiteDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Mc04IslmDelivSiteDO::getBaDelivSiteCode, delivCodes)
                .eq(Mc04IslmDelivSiteDO::getIcomCode, IcomUtils.getIcomCode())
                .eq(Mc04IslmDelivSiteDO::getIsUse, "1");
        List<Mc04IslmDelivSiteDO> delivList = mc04IslmDelivSiteService.list(wrapper);
        Map<String, String> map = delivList.stream().collect(Collectors.toMap(Mc04IslmDelivSiteDO::getBaDelivSiteCode, Mc04IslmDelivSiteDO::getBaDelivSiteName, (v1, v2) -> v1));
        List<ContRealReachWhse> contRealReachWhses = ContRealReachWhseConverter.INSTANCE.converterDosToModels(list);
        contRealReachWhses.forEach(item -> {
            item.setMc04CgtPraInStorehouseName(map.getOrDefault(item.getBaDelivSiteCode(), ""));
        });
        return contRealReachWhses;
    }


    /**
     * 查询实际到货仓库列表
     *
     * @param praInStorehouseCodeList 实际到货仓库编码
     * @return 实际到货仓库列表
     */
    @Override
    public List<ContRealReachWhse> queryContRealReachWhseListByPk(List<String> praInStorehouseCodeList) {
        return mc04IslmcContRealReachWhseService.listByIds(praInStorehouseCodeList)
                .stream()
                .map(ContRealReachWhseConverter.INSTANCE::converterDoToModel)
                .collect(Collectors.toList());
    }
}