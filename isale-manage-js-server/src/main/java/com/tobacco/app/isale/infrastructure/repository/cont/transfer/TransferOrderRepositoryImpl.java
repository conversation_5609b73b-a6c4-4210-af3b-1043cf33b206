/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */

package com.tobacco.app.isale.infrastructure.repository.cont.transfer;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomException;
import com.inspur.x1.ac.rule.utils.AcRuleUtil;
import com.tobacco.app.isale.domain.model.collect.TransferOrderSaveCenterQuery;
import com.tobacco.app.isale.domain.model.cont.transfer.*;
import com.tobacco.app.isale.domain.model.item.IccItemDetail;
import com.tobacco.app.isale.domain.model.product.IccProductDetail;
import com.tobacco.app.isale.domain.repository.cont.transfer.TransferOrderRepository;
import com.tobacco.app.isale.dto.cont.transfer.TransferOrderPalletDTO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcWarehouseStockTranBillDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcWarehouseStockTranBillItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcWarehouseStockTranBillItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcWarehouseStockTranBillService;
import com.tobacco.app.isale.infrastructure.tunnel.database.cont.transfer.TransferOrderMapper;
import com.tobacco.app.isale.tools.utils.CustUtil;
import com.tobacco.app.isale.tools.utils.ICommodityUtil;
import com.tobacco.app.isale.tools.utils.InterHttpUtils;
import com.tobacco.app.isale.tools.utils.SaleResponseUtil;
import com.tobacco.app.isalecenter.client.api.plan.monthplan.MonthSalePlanServiceAPI;
import com.tobacco.app.isalecenter.client.api.xy.XyAPI;
import com.tobacco.app.isalecenter.client.dto.plan.monthplan.MonthSalePlanDTO;
import com.tobacco.app.isalecenter.client.dto.xy.Mc04IslmcXyDTO;
import com.tobacco.app.isalecenter.client.dto.xy.Mc04IslmcXyItemDTO;
import com.tobacco.app.isalecenter.client.req.plan.monthplan.MonthSalePlanQueryREQ;
import com.tobacco.app.isalecenter.client.req.xy.Mc04IslmcXyQueryREQ;
import com.tobacco.sc.icommodity.dto.common.constant.dto.product.IccProductDetailDTO;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/04/22 10:42
 * @description : 销售管理-协议管理(卷烟)-意向采集-意向上报(市场填报) infrastructureRepository
 */
@Slf4j
@Component("TransferOrderRepositoryImplDORepository")
public class TransferOrderRepositoryImpl implements TransferOrderRepository {


    @Autowired
    private XyAPI xyAPI;

    @Autowired
    private MonthSalePlanServiceAPI monthSalePlanServiceAPI;


    @Autowired
    private TransferOrderMapper transferOrderMapper;

    @Autowired
    private Mc04IslmcWarehouseStockTranBillService tranBillService;

    @Autowired
    private Mc04IslmcWarehouseStockTranBillItemService tranBillItemService;

    @Resource
    public void setTransferOrderMapper(TransferOrderMapper transferOrderMapper) {
        this.transferOrderMapper = transferOrderMapper;
    }

    /**
     * 前置库移库单查询已经保存的数据
     *
     * @param orderPage 前置库移库单数据
     * @return List<Map < String, Object>>
     */
    @Override
    public Page<WarehouseStockTranBill> queryTransferData(TransferOrderBillPage orderPage) {
        Integer offset = orderPage.getOffset();
        Integer limit = orderPage.getLimit();
        Page<WarehouseStockTranBill> page = new Page<>(offset, limit);
        Page<WarehouseStockTranBill> pageResult = transferOrderMapper.queryTransferData(page, orderPage);
        List<WarehouseStockTranBill> records = pageResult.getRecords();
        Map<String, WarehouseStockTranBill> summaryMap = new HashMap<>();
        // 使用distinct()方法对Stream中的元素进行去重
        List<String> acCgtCartonCodeList = records.stream()
                .map(WarehouseStockTranBill::getAcCgtCartonCode)
                .distinct()  // 去除重复的acCgtCartonCode
                .collect(Collectors.toList());
        List<String> baComOrgCodeList = records.stream()
                .map(WarehouseStockTranBill::getBaComOrgCode)
                .distinct()  // 去除重复的acCgtCartonCode
                .collect(Collectors.toList());
        // 基于去重后的列表获取产品详情映射
        Map<String, IccProductDetailDTO> iccProductDetailMap =
                ICommodityUtil.getIccProductDetailDTOMap(acCgtCartonCodeList);
        Map<String, BusiComDto> busiComDtoMap = CustUtil.getBusiComDtoMap(baComOrgCodeList);
        for (WarehouseStockTranBill bill : records) {
            String mainOrderId = bill.getMc04WarehouseStockTranBillId();

            // 处理空值，确保数量和金额不为null
            BigDecimal quantity = bill.getQty();
            BigDecimal price = bill.getAcCgtTaxAllotPrice() != null ? bill.getAcCgtTaxAllotPrice() : BigDecimal.ZERO;
            BigDecimal amount = price.multiply(quantity
                    .multiply(BigDecimal.valueOf(10000))
                    .divide(new BigDecimal(iccProductDetailMap.get(bill.getAcCgtCartonCode()).getPackageQty2()),6,RoundingMode.HALF_UP)
                    .setScale(2, RoundingMode.HALF_UP));
            if (summaryMap.containsKey(mainOrderId)) {
                // 已存在的订单，累加数量和金额
                WarehouseStockTranBill summary = summaryMap.get(mainOrderId);
                summary.setQty(summary.getQty().add(quantity));

                summary.setTotalAmount(summary.getTotalAmount().add(amount));
            } else {
                // 新订单，创建汇总对象
                WarehouseStockTranBill summary = new WarehouseStockTranBill();
                summary.setMc04WarehouseStockTranBillId(mainOrderId);
                summary.setMa02TobaProdTradeTypeCode(bill.getMa02TobaProdTradeTypeCode());
                summary.setBaComOrgCode(bill.getBaComOrgCode());
                summary.setBaComOrgName(busiComDtoMap.get(bill.getBaComOrgCode()).getBaComOrgName());
                summary.setMa02PlanMonth(bill.getMa02PlanMonth());
                summary.setMc04DatePeriodCode(bill.getMc04DatePeriodCode());
                summary.setMc04AskComeDate(bill.getMc04AskComeDate());
                summary.setMd02CgtOutStorehouseCode(bill.getMd02CgtOutStorehouseCode());
                summary.setMd02CgtInStorehouseCode(bill.getMd02CgtInStorehouseCode());
                summary.setMd02CgtOutStorehouseName(bill.getMd02CgtOutStorehouseName());
                summary.setMd02CgtInStorehouseName(bill.getMd02CgtInStorehouseName());
                summary.setMc05PreposMovePlMoveDate(bill.getMc05PreposMovePlMoveDate());
                summary.setCreateName(bill.getCreateName());
                summary.setCreateTime(bill.getCreateTime());
                summary.setMc04WarehouseStockTranBillStatus(bill.getMc04WarehouseStockTranBillStatus());
                summary.setQty(quantity);
                summary.setTotalAmount(amount);
                summaryMap.put(mainOrderId, summary);
            }
        }
        // 3. 转换为列表并保持分页信息
        List<WarehouseStockTranBill> summaryList = new ArrayList<>(summaryMap.values());

        // 4. 创建新的分页对象并设置汇总结果
        Page<WarehouseStockTranBill> summaryPage = new Page<>(pageResult.getCurrent(),
                pageResult.getSize(),
                pageResult.getTotal());
        summaryPage.setRecords(summaryList);
        return summaryPage;
    }

    /**
     * 前置库月度计划查询已经保存的数据
     *
     * @param orderPage 前置库移库单数据
     * @return List<Map < String, Object>>
     */
    @Override
    public Page<TransferMonthPlan> queryMonthPlanData(TransferOrderMonthPlanPage orderPage) {
        Integer offset = orderPage.getOffset();
        Integer limit = orderPage.getLimit();
        Page<TransferMonthPlan> page = new Page<>(offset, limit);
        Page<TransferMonthPlan> pageResult = transferOrderMapper.queryMonthPlanData(page, orderPage);
        return pageResult;
    }



    /**
     * 前置库库存查询库存数据
     *
     * @param orderPage 请求参数
     * @return Page<Stock>
     * <AUTHOR> Wangwenpeng
     * @create_time : 2025/8/15
     * @description :
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Page<Stock> queryStockData(TransferOrderStockPage orderPage) {
        try {
            Integer offset = orderPage.getOffset();
            Integer limit = orderPage.getLimit();
            Page<Stock> page = new Page<>(offset, limit);
            String businessDate = orderPage.getBusinessDate();
            String monthBeginDate = businessDate.substring(0, 6) + "01";
            String monthEndDate = businessDate.substring(0, 6) + "31";
            String yearBeginDate = businessDate.substring(0, 4) + "0101";
            String yearEndDate = businessDate.substring(0, 4) + "1231";
            orderPage.setMonthBeginDate(monthBeginDate);
            orderPage.setMonthEndDate(monthEndDate);
            orderPage.setYearBeginDate(yearBeginDate);
            orderPage.setYearEndDate(yearEndDate);
            Page<Stock> pageResult = transferOrderMapper.queryStockData(page, orderPage);
            //获取123级卷烟编码名称以及含税调拨价
            // 调用商品中心获得卷烟名
            List<Stock> records = pageResult.getRecords();
            List<String> acCgtCartonCodeList = records != null ? records
                    .stream().map(Stock::getAcCgtCartonCode).collect(Collectors.toList())
                    : Collections.emptyList();
            Map<String, IccItemDetail> iccItemDetailMap =
                    ICommodityUtil.getIccItemDetailMap(acCgtCartonCodeList);
            switch (orderPage.getCountType()) {
                case "10":
                    assert records != null;
                    records.forEach(item -> {
                        Assert.notNull(iccItemDetailMap, () -> new CustomException("商品中心未获取到" +
                                item.getAcCgtCartonCode() + "的明细信息"));
                        IccItemDetail iccItemDetail = iccItemDetailMap.get(item.getAcCgtCartonCode());
                        item.setCigName(iccItemDetail.getAcOneLevelCigName());
                        getTaxAllotPrice(item, iccItemDetail);
                    });
                    break;
                case "20":
                    pageResult.getRecords().forEach(item -> {
                        IccItemDetail iccItemDetail = iccItemDetailMap.get(item.getAcCgtCartonCode());
                        item.setCigName(iccItemDetail.getAcTwoLevelCigName());
                        getTaxAllotPrice(item, iccItemDetail);
                    });
                    break;
                case "30":
                    pageResult.getRecords().forEach(item -> {
                        IccItemDetail iccItemDetail = iccItemDetailMap.get(item.getAcCgtCartonCode());
                        item.setCigName(iccItemDetail.getAcThrLevelCigName());
                        getTaxAllotPrice(item, iccItemDetail);
                    });
                    break;
            }
            return pageResult;
        } catch (Exception e) {
            log.error("查询库存信息失败：{}", e.getMessage());
            throw new RuntimeException("查询库存信息失败：{}" + e.getMessage());
        }
    }

    /**
     * 新建和保存前置库移库单
     *
     * @param saveCenterQuery 新建和保存前置库移库单参数
     * @return boolean
     * <AUTHOR> Wangwenpeng
     * @create_time : 2025/8/15
     * @description :
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createOrUpdate(TransferOrderSaveCenterQuery saveCenterQuery) {
        try {

            String billId = saveCenterQuery.getMc04WarehouseStockTranBillId();
            String icomCode = saveCenterQuery.getIcomCode();
            Mc04IslmcWarehouseStockTranBillDO billDO = getMc04IslmcWarehouseStockTranBillDO(saveCenterQuery, billId, icomCode);
            //更新或新建
            boolean save = tranBillService.saveOrUpdate(billDO);
            //查询从表，新建为空，更新则移除后新增
            List<Mc04IslmcWarehouseStockTranBillItemDO> oldList = tranBillItemService.lambdaQuery()
                    .eq(Mc04IslmcWarehouseStockTranBillItemDO::getMc04WarehouseStockTranBillId, billId)
                    .list();
            if (oldList != null && !oldList.isEmpty()) {
                tranBillItemService.removeItem(billId, icomCode);
            }
            if (save) {
                List<TransferOrderDetail> dataList = saveCenterQuery.getDataList();
                List<Mc04IslmcWarehouseStockTranBillItemDO> itemDOList = new ArrayList<>();
                // 调用商品中心获得卷烟名
                List<String> acCgtCartonCodeList = dataList != null
                        ? dataList.stream()
                        .map(TransferOrderDetail::getAcCgtCartonCode)
                        .collect(Collectors.toList())
                        : Collections.emptyList();
                Map<String, IccProductDetail> iccProductDetailMap =
                        ICommodityUtil.getIccProductDetailMap(acCgtCartonCodeList);
                assert dataList != null;
                for (TransferOrderDetail transferOrderDetail : dataList) {
                    Mc04IslmcWarehouseStockTranBillItemDO billItemDO = new Mc04IslmcWarehouseStockTranBillItemDO();
                    billItemDO.setMc04WarehouseStockTranBillItemId(IdUtil.fastSimpleUUID());
                    billItemDO.setMc04WarehouseStockTranBillId(billId);
                    billItemDO.setAcCgtCartonCode(transferOrderDetail.getAcCgtCartonCode());
                    Assert.notNull(iccProductDetailMap, () -> new CustomException("商品中心未获取到" +
                            transferOrderDetail.getAcCgtCartonCode() + "的明细信息"));
                    billItemDO.setAcCgtName(iccProductDetailMap.get(transferOrderDetail.getAcCgtCartonCode()).getProductName());
                    billItemDO.setAcCgtTaxAllotPrice(transferOrderDetail.getAcCgtTaxAllotPrice());
                    billItemDO.setAcTwoLevelCigCode(transferOrderDetail.getAcTwoLevelCigCode());
                    billItemDO.setAcTwoLevelCigName(transferOrderDetail.getAcTwoLevelCigName());
                    billItemDO.setAcCgtTaxAllotPrice(transferOrderDetail.getAcCgtTaxAllotPrice());
                    billItemDO.setMd03LogtTrayCombTspIcRlcQty(BigDecimal.valueOf(Long.parseLong(transferOrderDetail.getTransferQty())));
                    billItemDO.setIcomCode(icomCode);
                    itemDOList.add(billItemDO);
                }
                tranBillItemService.saveBatch(itemDOList);
            }
            return true;
        } catch (Exception e) {
            log.error("新增移库单失败：{}", e.getMessage());
            throw new RuntimeException("新增移库单失败：{}" + e.getMessage());
        }
    }

    private static Mc04IslmcWarehouseStockTranBillDO getMc04IslmcWarehouseStockTranBillDO(TransferOrderSaveCenterQuery saveCenterQuery, String billId, String icomCode) {
        Mc04IslmcWarehouseStockTranBillDO billDO = new Mc04IslmcWarehouseStockTranBillDO();
        billDO.setMc04WarehouseStockTranBillId(billId);
        billDO.setMa02TobaProdTradeTypeCode("0");
        billDO.setBaComOrgCode(saveCenterQuery.getBaComOrgCode());
        billDO.setMa02PlanMonth(saveCenterQuery.getMa02PlanMonth());
        billDO.setMc04DatePeriodCode(saveCenterQuery.getMc04DatePeriodCode());
        billDO.setMc05PreposMovePlMoveDate(saveCenterQuery.getMc05PreposMovePlMoveDate());
        billDO.setMc04AskComeDate(saveCenterQuery.getMc04AskComeDate());
        billDO.setMd02CgtOutStorehouseCode(saveCenterQuery.getMd02CgtOutStorehouseCode());
        billDO.setMd02CgtInStorehouseCode(saveCenterQuery.getMd02CgtInStorehouseCode());
        billDO.setMc04WarehouseStockTranBillStatus(saveCenterQuery.getMc04WarehouseStockTranBillStatus());
        billDO.setZaRemark(saveCenterQuery.getZaRemark());
        billDO.setIcomCode(icomCode);
        billDO.setIsDelete("0");
        return billDO;
    }

    /**
     * 获取前置库移库单详情
     *
     * @param req 请求参数
     * @return List<TransferOrderDetail>
     * <AUTHOR> Wangwenpeng
     * @create_time : 2025/8/15
     * @description :
     */
    @Override
    public List<TransferOrderDetail> getTransferOrderDetail(TransferOrderDetail req) {
        try {
            // 1. 构建查询参数
            TransferOrderDetailQuery query = buildTransferOrderQuery(req);
            Mc04IslmcXyQueryREQ mc04IslmcXyREQ = buildMc04IslmcXyQueryREQ(req);
            //根据halfList的长度来判断是否是1月和7月特殊月
            // 2. 处理半年列表逻辑
            List<TransferOrderDetail> result = handleHalfListLogic(query, mc04IslmcXyREQ, req);
            // 3. 统一设置公共属性并返回
            setCommonProperties(result);
            return result;
        } catch (Exception e) {
            log.error("获取前置库移库单明细失败：{}", e.getMessage());
            throw new RuntimeException("333获取前置库移库单明细失败:" + e.getMessage());
        }
    }


    /**
     * 获取前置库移库单明细
     *
     * @param req 请求参数
     * @return List<TransferOrderDetail>
     * <AUTHOR> Wangwenpeng
     * @create_time : 2025/8/15
     * @description :
     */
    @Override
    public List<TransferOrderDetail> getTransferDetail(TransferOrderDetail req) {
        try {

            List<Mc04IslmcWarehouseStockTranBillItemDO> list = tranBillItemService.lambdaQuery()
                    .eq(Mc04IslmcWarehouseStockTranBillItemDO::getMc04WarehouseStockTranBillId, req.getMc04WarehouseStockTranBillId())
                    .list();
            List<TransferOrderDetail> transferOrderDetailList = this.getTransferOrderDetail(req);
            transferOrderDetailList.forEach(transferOrderDetail -> {
                list.forEach(mc04IslmcWarehouseStockTranBillItemDO -> {
                    if (transferOrderDetail.getAcCgtCartonCode().equals(mc04IslmcWarehouseStockTranBillItemDO.getAcCgtCartonCode())) {
                        transferOrderDetail.setTransferQty(mc04IslmcWarehouseStockTranBillItemDO.getMd03LogtTrayCombTspIcRlcQty().toString());
                    }
                });
            });
            return transferOrderDetailList;
        } catch (Exception e) {
            log.error("获取前置库移库单详情失败：{}", e.getMessage());
            throw new RuntimeException("获取前置库移库单详情失败:" + e.getMessage());
        }
    }

    /**
     * 删除单据
     *
     * @param billId   移库单id
     * @param icomCode 公司编码
     * @return Boolean
     * <AUTHOR> Wangwenpeng
     * @create_time : 2025/8/15
     * @description :
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delBill(String billId, String icomCode) {
        if (StringUtils.isBlank(billId)) {
            log.error("删除单据失败，单据ID不能为空，公司编码：{}", icomCode);
            throw new CustomException("单据ID不能为空");
        }
        log.info("开始删除单据，单据ID：{}，公司编码：{}", billId, icomCode);
        // 删除主单据
        Mc04IslmcWarehouseStockTranBillDO mc04IslmcWarehouseStockTranBillDO = new Mc04IslmcWarehouseStockTranBillDO();
        mc04IslmcWarehouseStockTranBillDO.setIsDelete("1");
        mc04IslmcWarehouseStockTranBillDO.setMc04WarehouseStockTranBillId(billId);
        boolean mainBillDeleted = tranBillService.updateById(mc04IslmcWarehouseStockTranBillDO);
        if (!mainBillDeleted) {
            log.warn("单据主表记录删除失败，单据ID：{}", billId);
            // 主单据删除失败，直接抛出异常触发回滚
            throw new CustomException("单据主记录删除失败");
        }
        return true;
    }

    /**
     * 提交单据
     *
     * @param transferOrderSubmit 提交单据参数
     * @return Boolean
     * <AUTHOR> Wangwenpeng
     * @create_time : 2025/8/15
     * @description :
     */
    @Override
    public TransferSubmitMessage submit(TransferOrderSubmit transferOrderSubmit) {
        final String message = "20".equals(transferOrderSubmit.getStatus()) ? "撤回" : "递交";
        try {
            TransferOrderDetail transferOrderDetail = getTransferOrderDetail(transferOrderSubmit);
            List<TransferOrderDetail> transferDetail = getTransferDetail(transferOrderDetail);
            TransferSubmitMessage transferSubmitMessage = new TransferSubmitMessage(true, new ArrayList<>());
            if ("30".equals(transferOrderSubmit.getStatus())){
                transferSubmitMessage = validateStockControlForTransferQty(transferDetail, transferOrderSubmit.getIcomCode());
                if (!transferSubmitMessage.getSuccess()) {
                    return transferSubmitMessage;
                }

            }
            InterHttpUtils interHttpUtils = new InterHttpUtils();
            List<Object> dataList = transferDetail.stream()
                    .map(item -> (Object) item)
                    .collect(Collectors.toList());
            Map<String, Object> params = interHttpUtils.createDataList("HB_CLMIS_SALE_0003", "移库单递交/撤销接口", dataList);
            Map<String, Object> responseData = interHttpUtils.post("serviceId", "/ddd-inter-server/api/threepartysys/push", params);

            Map<String, Object> resultData = (Map<String, Object>) responseData.get("data");
            // 示例：获取 resultData 中的某个字段（如 "result" 表示成功与否）
            Object result = resultData.get("result");
            if ("1".equals(result)) {
                // 处理成功逻辑
                transferOrderMapper.submit(transferOrderSubmit.getMc04WarehouseStockTranBillId(),
                        transferOrderSubmit.getStatus());
                log.info("前置库移库单递交成功！");
                transferSubmitMessage.setSuccess(true);
            } else {
                // 处理失败逻辑（例如记录错误信息）
                log.error("前置库移库单远程递交失败：远程接口返回错误！");
                transferSubmitMessage.setSuccess(false);
                transferSubmitMessage.getMessage().add("前置库移库单远程递交失败！");
            }
            return transferSubmitMessage;
        } catch (Exception e) {
            log.error("前置库移库单{}失败：{}", message, e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    private static TransferOrderDetail getTransferOrderDetail(TransferOrderSubmit transferOrderSubmit) {
        TransferOrderDetail transferOrderDetail = new TransferOrderDetail();
        transferOrderDetail.setMc04WarehouseStockTranBillId(transferOrderSubmit
                .getMc04WarehouseStockTranBillId());
        transferOrderDetail.setIcomCode(transferOrderSubmit.getIcomCode());
        transferOrderDetail.setMa02PlanMonth(transferOrderSubmit.getMa02PlanMonth());
        transferOrderDetail.setBaComOrgCode(transferOrderSubmit.getBaComOrgCode());
        transferOrderDetail.setMd02CgtOutStorehouseCode(transferOrderSubmit
                .getMd02CgtOutStorehouseCode());
        transferOrderDetail.setMd02CgtInStorehouseCode(transferOrderSubmit
                .getMd02CgtInStorehouseCode());
        transferOrderDetail.setMc04DatePeriodCode(transferOrderSubmit
                .getMc04DatePeriodCode());
        return transferOrderDetail;
    }

    private TransferSubmitMessage validateStockControlForTransferQty(
            List<TransferOrderDetail> orderDetailList, String icomCode) {

        TransferSubmitMessage transferSubmitMessage = new TransferSubmitMessage(true, new ArrayList<>());

        // 1.业务规则ISALE_ORDER_TRANS_CTR_AGREE是否控制协议剩余量
        validateTransferQtyAgainstLimit(
                orderDetailList,
                TransferOrderDetail::getSurplXyQty,
                "ISALE_ORDER_TRANS_CTR_AGREE",
                icomCode,
                "协议剩余量不足",
                transferSubmitMessage
        );

        // 2.业务规则ISALE_ORDER_TRANS_CTR_MONTH是否控制月计划剩余量
        validateTransferQtyAgainstLimit(
                orderDetailList,
                TransferOrderDetail::getMa02CgtPlQty,
                "ISALE_ORDER_TRANS_CTR_MONTH",
                icomCode,
                "月计划剩余量不足",
                transferSubmitMessage
        );

        // 3.ISALE_ORDER_TRANS_CTR_WEEK
        validateTransferQtyAgainstLimit(
                orderDetailList,
                TransferOrderDetail::getWeekMa02CgtPlQty,
                "ISALE_ORDER_TRANS_CTR_WEEK",
                icomCode,
                "周计划剩余量不足",
                transferSubmitMessage
        );

        // 4.业务规则ISALE_ORDER_TRANS_CTR_STOCK是否控制移出仓库(集并库)的库存量
        validateTransferQtyAgainstLimit(
                orderDetailList,
                TransferOrderDetail::getJbkQty,
                "ISALE_ORDER_TRANS_CTR_STOCK",
                icomCode,
                "集并库库存不足",
                transferSubmitMessage
        );

        if (!transferSubmitMessage.getMessage().isEmpty()) {
            transferSubmitMessage.setSuccess(false);
        }
        return transferSubmitMessage;
    }
    /**
     * 验证移库数量是否超过指定限制
     *
     * @param orderDetailList 订单详情列表
     * @param limitGetter 限制值获取函数
     * @param ruleCode 业务规则代码
     * @param icomCode 公司编码
     * @param errorMessage 错误信息模板
     * @param transferSubmitMessage 返回的验证结果
     */
    private void validateTransferQtyAgainstLimit(
            List<TransferOrderDetail> orderDetailList,
            java.util.function.Function<TransferOrderDetail, String> limitGetter,
            String ruleCode,
            String icomCode,
            String errorMessage,
            TransferSubmitMessage transferSubmitMessage) {

        if ("1".equals(AcRuleUtil.getRuleValue(ruleCode, icomCode))) {
            for (TransferOrderDetail orderDetail : orderDetailList) {
                // 空值处理
                String transferQty = normalizeQtyValue(orderDetail.getTransferQty());
                String limitValue = normalizeQtyValue(limitGetter.apply(orderDetail));

                if (new BigDecimal(limitValue)
                        .compareTo(new BigDecimal(transferQty)) < 0) {
                    transferSubmitMessage.getMessage().add(
                            orderDetail.getAcTwoLevelCigName() + errorMessage);
                }
            }
        }
    }

    /**
     * 标准化数量值，处理空值情况
     *
     * @param qtyValue 原始数量值
     * @return 标准化后的数量值
     */
    private String normalizeQtyValue(String qtyValue) {
        return (qtyValue == null || qtyValue.trim().isEmpty()) ? "0" : qtyValue;
    }


    /**
     * 查询托盘
     *
     * @param baComOrgCode 商业编码
     * @param icomCode     工业编码
     * @return List<TransferOrderPallet>
     * <AUTHOR> Wangwenpeng
     * @create_time : 2025/8/15
     * @description :
     */

    @Override
    public List<TransferOrderPallet> queryPallet(String baComOrgCode, String icomCode) {
        try {
            List<TransferOrderPallet> transferOrderPallets = transferOrderMapper.queryPallet(baComOrgCode, icomCode);
            if (transferOrderPallets.isEmpty()) {
                return transferOrderPallets;
            }
            List<String> acTwoTwoLevelCigCodeList = transferOrderPallets.stream().map(TransferOrderPallet::getAcTwoLevelCigCode).collect(Collectors.toList());
            Map<String, IccItemDetail> iccItemDetailMap = ICommodityUtil.getIccItemDetailMap(acTwoTwoLevelCigCodeList);
            for (TransferOrderPallet transferOrderPallet : transferOrderPallets) {
                IccItemDetail iccItemDetail = iccItemDetailMap.get(transferOrderPallet.getAcTwoLevelCigCode());
                transferOrderPallet.setAcTwoLevelCigName(iccItemDetail.getAcTwoLevelCigName());
            }
            return transferOrderPallets;
        } catch (Exception e) {
            log.error("获取托盘数设置失败：{}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 设置托盘数
     *
     * @param req 请求参数
     * @return Boolean
     * <AUTHOR> Wangwenpeng
     * @create_time : 2025/8/15
     * @description :
     */
    @Override
    public Boolean setPalletEnabled(TransferOrderPallet req) {
        try {
            List<TransferOrderPalletDTO> palletList = req.getPalletList();
            return transferOrderMapper.setPalletEnabled(palletList, req.getBaComOrgCode(), req.getIcomCode()) > 0;
        } catch (Exception e) {
            log.error("设置托盘数失败：{}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 查询周计划
     *
     * @param transferOrderWeekSelect 查询周计划参数
     * @return List<TransferOrderWeekSelect>
     * <AUTHOR> Wangwenpeng
     * @create_time : 2025/8/15
     * @description :
     */
    @Override
    public List<TransferOrderWeekSelect> queryWeekSelect(TransferOrderWeekSelect transferOrderWeekSelect) {
        return transferOrderMapper.queryWeekSelect(transferOrderWeekSelect);
    }


    private void getTaxAllotPrice(Stock item, IccItemDetail iccItemDetail) {
        BigDecimal packageQty = new BigDecimal(iccItemDetail.getPackageQty2());
        if (packageQty.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal price = iccItemDetail.getAcMateTaxTranPr()
                    .divide(packageQty, 2, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(5000));
            item.setAcCgtTaxAllotPrice(price.toString());
        }
        item.setAcCgtTaxAllotPrice(iccItemDetail.getAcMateTaxTranPr().toString());
    }

    private void getHalfList(TransferOrderDetailQuery query) {
        String ma02PlanMonth = query.getMa02PlanMonth();
        String year = ma02PlanMonth.substring(0, 4);
        String month = ma02PlanMonth.substring(4, 6);
        List<String> halfList = new ArrayList<>();
        if ("01".equals(month) || "07".equals(month)) {
            if ("01".equals(month)) {
                query.setBeginMonth((Integer.parseInt(year) - 1) + "07");
                query.setEndMonth((Integer.parseInt(year) - 1) + "12");
                halfList.add((Integer.parseInt(year) - 1) + "H2");
                query.setHalf(year + "H1");
                halfList.add(year + "H1");
            } else {
                query.setHalf(year + "H2");
                query.setBeginMonth(year + "01");
                query.setEndMonth(year + "06");
                halfList.add(year + "H1");
                halfList.add(year + "H2");
            }
            query.setHalfList(halfList);
        } else {
            String half;
            if (Integer.parseInt(month) >= 7) {
                query.setBeginMonth(year + "07");
                half = year + "H2";
            } else {
                query.setBeginMonth(year + "01");
                half = year + "H1";
            }
            query.setEndMonth(ma02PlanMonth);
            query.setHalf(half);
            halfList.add(half);
        }
        query.setHalfList(halfList);
    }

    private List<TransferOrderDetail> setMonthPlanAndSurplTranQty(Mc04IslmcXyQueryREQ mc04IslmcXyREQ, TransferOrderDetailQuery query) {

        List<Mc04IslmcXyDTO> xyDtoS = SaleResponseUtil.getCenterDTO("从中台获取协议量：{}",
                () -> xyAPI.list(mc04IslmcXyREQ));
        List<TransferOrderDetail> transferOrderDetail = transferOrderMapper.getTransferOrderDetailInAdd(query);
        // 2. 处理协议量相关逻辑
        processXyQuantities(xyDtoS, transferOrderDetail);
        // 3. 处理月计划相关逻辑
        processMonthPlanQuantities(query, transferOrderDetail);
        return transferOrderDetail;
    }

    /**
     * 处理协议量和剩余协议量
     */
    private void processXyQuantities(List<Mc04IslmcXyDTO> xyDtoS, List<TransferOrderDetail> transferOrderDetails) {
        for (Mc04IslmcXyDTO xyItem : xyDtoS) {
            List<Mc04IslmcXyItemDTO> xyItems = xyItem.getXyItems();
            for (TransferOrderDetail orderDetail : transferOrderDetails) {
                // 计算周计划量
                orderDetail.calculateWeekTransferQty();
                // 匹配协议项并设置相关数量
                xyItems.forEach(xyItemDetail -> {
                    if (xyItemDetail.getAcCgtCartonCode().equals(orderDetail.getAcCgtCartonCode())) {
                        // 设置半年协议量
                        orderDetail.setXyQty(xyItemDetail.getMd02CgtXyAdjustedQty() + "");

                        // 计算并设置剩余协议量
                        BigDecimal halfYearTranQty = new BigDecimal(orderDetail.getHalfYearTranQty());
                        BigDecimal surplXyQty = xyItemDetail.getMd02CgtXyAdjustedQty().subtract(halfYearTranQty);
                        orderDetail.setSurplXyQty(surplXyQty.toString());
                    }
                });
            }
        }
    }
    /**
     * 处理月计划量和剩余移库量
     */
    private void processMonthPlanQuantities(TransferOrderDetailQuery query, List<TransferOrderDetail> transferOrderDetails) {
        // 构建月计划查询参数
        MonthSalePlanQueryREQ monthSalePlanQueryReq = new MonthSalePlanQueryREQ();
        monthSalePlanQueryReq.setMa02PlanMonth(query.getMa02PlanMonth());
        monthSalePlanQueryReq.setBaComOrgCode(query.getBaComOrgCode());
        monthSalePlanQueryReq.setIcomCode(query.getIcomCode());
        // 为每个订单明细设置月计划相关数据
        for (TransferOrderDetail item : transferOrderDetails) {
            monthSalePlanQueryReq.setAcCgtCartonCode(item.getAcCgtCartonCode());
            // 获取月计划数据
            List<MonthSalePlanDTO> monthSalePlan = SaleResponseUtil.getCenterDTO(
                    "从中台获取月计划量：{}",
                    () -> monthSalePlanServiceAPI.list(monthSalePlanQueryReq)
            );
            // 匹配月计划并设置相关数量
            for (MonthSalePlanDTO planItem : monthSalePlan) {
                if (planItem.getAcCgtCartonCode().equals(item.getAcCgtCartonCode())) {
                    item.setMa02CgtPlQty(planItem.getMa02CgtPlAdjustedQty().toString());
                    // 计算并设置剩余移库量
                    BigDecimal surplTranQty = new BigDecimal(item.getMa02CgtPlQty())
                            .subtract(new BigDecimal(item.getMovedTranQty()));
                    item.setSurplTranQty(surplTranQty.toString());
                }
            }
        }
    }

    private void setCommonProperties(List<TransferOrderDetail> transferDetail) {
        try{
            if (transferDetail==null || transferDetail.isEmpty()) {
                return;
            }
            //获取二级编码，获得二级编码名字
            List<String> acTwoLevelCigCode = transferDetail.stream()
                    .map(TransferOrderDetail::getAcTwoLevelCigCode)
                    .collect(Collectors.toList());
            Map<String, IccItemDetail> iccItemDetailMap = ICommodityUtil.getIccItemDetailMap(acTwoLevelCigCode);
            transferDetail.forEach(transferOrderDetail -> {
                IccItemDetail iccItemDetail = iccItemDetailMap.get(transferOrderDetail.getAcTwoLevelCigCode());
                Assert.notNull(iccItemDetail, () -> new CustomException("商品中心未获取到" +
                        transferOrderDetail.getAcTwoLevelCigCode() + "的明细信息"));

                transferOrderDetail.setAcTwoLevelCigName(iccItemDetail.getAcTwoLevelCigName());
                transferOrderDetail.setAcCgtTaxAllotPrice(iccItemDetail.getAcMateTaxTranPr());
                BigDecimal packageQty = new BigDecimal(iccItemDetail.getPackageQty2());
                BigDecimal packageBoxQty = new BigDecimal(iccItemDetail.getPackageQty4());
                transferOrderDetail
                        .setAcCgtTaxAllotBoxPrice(iccItemDetail.getAcMateTaxTranPr()
                                .divide(packageQty, 6, RoundingMode.HALF_UP)
                                .multiply(packageBoxQty).setScale(6, RoundingMode.HALF_UP));
            });
        }catch (Exception e){
            log.error("获取商品信息异常", e);
        }
    }

    private List<TransferOrderDetail> handleHalfListLogic(TransferOrderDetailQuery query, Mc04IslmcXyQueryREQ xyReq, TransferOrderDetail req) {
        // 设置默认的协议周期（取半年列表第一个值）
        xyReq.setMc04CgtXyPeriodCode(query.getHalfList().get(0));

        // 判断是否为多半年且需要处理上半年剩余量的场景
        if (isMultipleHalfYearCase(query, req)) {
            return handleMultipleHalfYearCase(query, xyReq, req);
        }

        // 单半年场景：直接处理月计划和剩余量
        return setMonthPlanAndSurplTranQty(xyReq, query);
    }

    private List<TransferOrderDetail> handleMultipleHalfYearCase(TransferOrderDetailQuery query, Mc04IslmcXyQueryREQ xyReq, TransferOrderDetail req) {
        // 获取转移订单详情（附加逻辑）
        List<TransferOrderDetail> transferOrderDetail = transferOrderMapper.getTransferOrderDetailInAdd(query);

        // 处理协议数据并计算剩余量
        int surplusCount = processXyDtos(transferOrderDetail, xyReq);

        // 有剩余量：直接返回处理结果
        if (surplusCount > 0) {
            req.setIsHalfYearSurplXyQty("1");
            return transferOrderDetail;
        }
        // 无剩余量：处理当月计划
        return handleNoSurplusCase(query, xyReq, req);
    }

    private List<TransferOrderDetail> handleNoSurplusCase(TransferOrderDetailQuery query, Mc04IslmcXyQueryREQ xyReq, TransferOrderDetail req) {
        xyReq.setMc04CgtXyPeriodCode(query.getHalfList().get(1));
        query.setBeginMonth(req.getMa02PlanMonth());
        query.setEndMonth(req.getMa02PlanMonth());
        return setMonthPlanAndSurplTranQty(xyReq, query);
    }

    private int processXyDtos(List<TransferOrderDetail> transferOrderDetail, Mc04IslmcXyQueryREQ xyReq) {
        List<Mc04IslmcXyDTO> xyDtos = SaleResponseUtil.getCenterDTO(
                "从中台获取协议量：{}",
                () -> xyAPI.list(xyReq)
        );
        int count = 0;
        for (Mc04IslmcXyDTO xyDto : xyDtos) {
            for (Mc04IslmcXyItemDTO xyItem : xyDto.getXyItems()) {
                count += updateOrderDetailSurplus(transferOrderDetail, xyItem);
            }
        }
        return count;
    }

    /**
     * 更新单个订单详情的剩余量
     */
    private int updateOrderDetailSurplus(List<TransferOrderDetail> orderDetails, Mc04IslmcXyItemDTO xyItem) {
        int count = 0;
        for (TransferOrderDetail orderDetail : orderDetails) {
            if (xyItem.getAcCgtCartonCode().equals(orderDetail.getAcCgtCartonCode())) {
                // 设置协议量
                orderDetail.setXyQty(xyItem.getMd02CgtXyAdjustedQty() + "");
                // 计算剩余量
                BigDecimal halfYearTranQty = new BigDecimal(orderDetail.getHalfYearTranQty());
                BigDecimal surplXyQty = xyItem.getMd02CgtXyAdjustedQty().subtract(halfYearTranQty);
                if (surplXyQty.compareTo(BigDecimal.ZERO) > 0) {
                    orderDetail.setSurplTranQty(surplXyQty.toString());
                    count++;
                }
            }
        }
        return count;
    }

    private boolean isMultipleHalfYearCase(TransferOrderDetailQuery query, TransferOrderDetail req) {
        return query.getHalfList().size() > 1
                && (req.getIsHalfYearSurplXyQty() == null
                || Objects.equals(req.getIsHalfYearSurplXyQty(), "1"));
    }

    private Mc04IslmcXyQueryREQ buildMc04IslmcXyQueryREQ(TransferOrderDetail req) {
        Mc04IslmcXyQueryREQ mc04IslmcXyREQ = new Mc04IslmcXyQueryREQ();
        mc04IslmcXyREQ.setBaComOrgCode(req.getBaComOrgCode());
        mc04IslmcXyREQ.setIcomCode(req.getIcomCode());
        return mc04IslmcXyREQ;
    }

    private TransferOrderDetailQuery buildTransferOrderQuery(TransferOrderDetail req) {
        TransferOrderDetailQuery query = new TransferOrderDetailQuery();
        query.setBaComOrgCode(req.getBaComOrgCode());
        query.setMa02PlanMonth(req.getMa02PlanMonth());
        query.setIcomCode(req.getIcomCode());
        query.setMd02CgtInStorehouseCode(req.getMd02CgtInStorehouseCode());
        query.setMd02CgtOutStorehouseCode(req.getMd02CgtOutStorehouseCode());
        query.setMc04DatePeriodCode(req.getMc04DatePeriodCode());
        getHalfList(query);
        return query;
    }

}

