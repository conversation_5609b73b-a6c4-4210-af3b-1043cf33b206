package com.tobacco.app.isale.infrastructure.repository.supply.psc.allocdemand.manage;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.base.CustomPage;
import com.tobacco.app.isale.domain.model.supply.psc.allocdemand.TranDemandDomain;
import com.tobacco.app.isale.domain.model.supply.psc.allocdemand.manage.AllocdemandMangeDomain;
import com.tobacco.app.isale.domain.model.supply.psc.allocdemand.manage.AllocdemandMangeShowDomain;
import com.tobacco.app.isale.domain.repository.supply.psc.allocdemand.manage.TranDemandRepository;
import com.tobacco.app.isale.infrastructure.converter.supply.psc.allocdemand.TranDemandDomainToDoConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtTranDemandDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmCgtTranDemandService;
import com.tobacco.app.isale.infrastructure.tunnel.database.supply.psc.allocdemand.manage.Mc04IslmCgtTranDemandMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import static com.tobacco.app.isale.domain.enums.supply.psc.allocdemand.IsaleSupplyDemandStatusEnum.*;

@Component("ISaleMc04IslmCgtTranDemandRepository")
public class Mc04IslmCgtTranDemandRepositoryImpl implements TranDemandRepository {

    private Mc04IslmCgtTranDemandService mc04IslmCgtTranDemandService;

    @Autowired
    private Mc04IslmCgtTranDemandMapper mc04IslmCgtTranDemandMapper;

    @Autowired
    public void setMc04IslmCgtTranDemandService(Mc04IslmCgtTranDemandService mc04IslmCgtTranDemandService) {
        this.mc04IslmCgtTranDemandService = mc04IslmCgtTranDemandService;
    }

    /**
     * 查询调拨单
     * @param allocdemandMangeDomain 调拨单参数
     * @return Page<AllocdemandMangeDomain>
     */
    @Override
    public Page<AllocdemandMangeDomain> queryAllocdemandMangePage(AllocdemandMangeDomain allocdemandMangeDomain) {
        CustomPage<AllocdemandMangeDomain> page = new CustomPage<>(allocdemandMangeDomain.getOffset(), allocdemandMangeDomain.getLimit(), allocdemandMangeDomain.getSort(), "");
        return mc04IslmCgtTranDemandMapper.queryAllocdemandMangePage(page, allocdemandMangeDomain.getZaOccurrenceMonth(), allocdemandMangeDomain.getMc03CgtProdPlType());
    }

    /**
     * 批量新增调拨单
     * @param tranDemandDomainList 调拨单列表
     * @return Boolean
     */
    @Override
    public Boolean batchAdd(List<TranDemandDomain> tranDemandDomainList) {
        List<Mc04IslmCgtTranDemandDO> tranDemandDOList = TranDemandDomainToDoConverter.INSTANCE.converterModelsToDos(tranDemandDomainList);
        return mc04IslmCgtTranDemandService.saveBatch(tranDemandDOList);
    }

    /**
     * 发布计划
     * @param zaOccurrenceMonth 当前月
     * @param mc03CgtProdPlanVersion 版本
     * @param mc03CgtProdPlType 类型
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean publishPlan(@NotBlank(message = "发生月份不能为空") String zaOccurrenceMonth,
                               @NotBlank(message = "计划版本号不能为空") String mc03CgtProdPlanVersion,
                               @NotBlank(message = "计划类型不能为空") String mc03CgtProdPlType) {
        // 查询指定条件的调拨计划列表
        List<Mc04IslmCgtTranDemandDO> tranDemandDOList = mc04IslmCgtTranDemandService
                .lambdaQuery()
                .eq(Mc04IslmCgtTranDemandDO::getZaOccurrenceMonth, zaOccurrenceMonth)
                .eq(Mc04IslmCgtTranDemandDO::getMc03CgtProdPlanVersion, mc03CgtProdPlanVersion)
                .eq(Mc04IslmCgtTranDemandDO::getMc03CgtProdPlType, mc03CgtProdPlType)
                .list();

        // 校验是否存在非待发布状态(10)的计划
        boolean hasInvalidStatus = tranDemandDOList.stream()
                .anyMatch(item -> !DEMAND_STATUS_10.getStatus().equals(item.getMc04CgtTranDemandStatus()));

        Assert.isTrue(!hasInvalidStatus,
                () -> new CustomException("发布失败，存在非待发布状态的调拨计划，请检查后重新发布！"));

        // 执行发布逻辑（此处补充实际发布业务，如调用外部接口等）
        // todo: 执行发布前的业务处理，如通知相关系统等

        // 更新所有计划状态为已发布(20)
        tranDemandDOList.forEach(item -> item.setMc04CgtTranDemandStatus(DEMAND_STATUS_20.getStatus()));

        // 批量保存更新结果
        return mc04IslmCgtTranDemandService.saveOrUpdateBatch(tranDemandDOList);
    }

    /**
     * 作废计划
     * @param zaOccurrenceMonth 当前月
     * @param mc03CgtProdPlanVersion 版本
     * @param mc03CgtProdPlType 类型
     * @return Boolean
     */
    @Override
    public Boolean deletePlan(@NotBlank(message = "发生月份不能为空") String zaOccurrenceMonth,
                              @NotBlank(message = "计划版本号不能为空") String mc03CgtProdPlanVersion,
                              @NotBlank(message = "计划类型不能为空") String mc03CgtProdPlType) {
        // 查询指定条件的调拨计划列表
        List<Mc04IslmCgtTranDemandDO> tranDemandDOList = mc04IslmCgtTranDemandService
                .lambdaQuery()
                .eq(Mc04IslmCgtTranDemandDO::getZaOccurrenceMonth, zaOccurrenceMonth)
                .eq(Mc04IslmCgtTranDemandDO::getMc03CgtProdPlanVersion, mc03CgtProdPlanVersion)
                .eq(Mc04IslmCgtTranDemandDO::getMc03CgtProdPlType, mc03CgtProdPlType)
                .list();

        // 校验是否存在非待发布状态(10)的计划
        boolean hasInvalidStatus = tranDemandDOList.stream()
                .anyMatch(item -> !DEMAND_STATUS_10.getStatus().equals(item.getMc04CgtTranDemandStatus()));

        Assert.isTrue(!hasInvalidStatus,
                () -> new CustomException("发布失败，存在非待发布状态的调拨计划，请检查后重新发布！"));

        // 执行发布逻辑（此处补充实际发布业务，如调用外部接口等）
        // todo: 执行发布前的业务处理，如通知相关系统等

        // 更新所有计划状态为作废(90)
        tranDemandDOList.forEach(item -> item.setMc04CgtTranDemandStatus(DEMAND_STATUS_90.getStatus()));

        // 批量保存更新结果
        return mc04IslmCgtTranDemandService.saveOrUpdateBatch(tranDemandDOList);
    }

    /**
     * 取消发布
     * @param zaOccurrenceMonth 当前月
     * @param mc03CgtProdPlanVersion 版本
     * @param mc03CgtProdPlType 类型
     * @return Boolean
     */
    @Override
    public Boolean cancelPlan(@NotBlank(message = "发生月份不能为空") String zaOccurrenceMonth,
                              @NotBlank(message = "计划版本号不能为空") String mc03CgtProdPlanVersion,
                              @NotBlank(message = "计划类型不能为空") String mc03CgtProdPlType) {
        // 查询指定条件的调拨计划列表
        List<Mc04IslmCgtTranDemandDO> tranDemandDOList = mc04IslmCgtTranDemandService
                .lambdaQuery()
                .eq(Mc04IslmCgtTranDemandDO::getZaOccurrenceMonth, zaOccurrenceMonth)
                .eq(Mc04IslmCgtTranDemandDO::getMc03CgtProdPlanVersion, mc03CgtProdPlanVersion)
                .eq(Mc04IslmCgtTranDemandDO::getMc03CgtProdPlType, mc03CgtProdPlType)
                .list();

        // 校验是否存在非待发布状态(10)的计划
        boolean hasInvalidStatus = tranDemandDOList.stream()
                .anyMatch(item -> !DEMAND_STATUS_20.getStatus().equals(item.getMc04CgtTranDemandStatus()));

        Assert.isTrue(!hasInvalidStatus,
                () -> new CustomException("发布失败，存在非待发布状态的调拨计划，请检查后重新发布！"));

        // 执行发布逻辑（此处补充实际发布业务，如调用外部接口等）
        // todo: 执行发布前的业务处理，如通知相关系统等

        // 更新所有计划状态为已发布(20)
        tranDemandDOList.forEach(item -> item.setMc04CgtTranDemandStatus(DEMAND_STATUS_10.getStatus()));

        // 批量保存更新结果
        return mc04IslmCgtTranDemandService.saveOrUpdateBatch(tranDemandDOList);
    }

    /**
     * 获取版本号
     * @param date 当前月
     * @param isPublish 是否是发布的
     * @param mc03CgtProdPlType 类型
     * @return String
     */
    @Override
    public String getDateVersion(String date, String mc03CgtProdPlType,Boolean isPublish) {
        List<Mc04IslmCgtTranDemandDO> tranDemandDOS = mc04IslmCgtTranDemandService
                .lambdaQuery()
                .eq(Mc04IslmCgtTranDemandDO::getMc03CgtProdPlType, mc03CgtProdPlType)
                .eq(Mc04IslmCgtTranDemandDO::getZaOccurrenceMonth, date)
                .eq(isPublish,Mc04IslmCgtTranDemandDO::getMc04CgtTranDemandStatus, DEMAND_STATUS_20.getStatus())
                .list();
        // 提取不重复的版本号并找到最大值
        return tranDemandDOS.stream()
                // 提取版本号字段
                .map(Mc04IslmCgtTranDemandDO::getMc03CgtProdPlanVersion)
                // 去重
                .distinct()
                // 过滤空值（避免版本号为null的情况）
                .filter(version -> version != null && !version.isEmpty())
                // 找到最大版本号（根据实际版本号格式调整比较器）
                .max(Comparator.naturalOrder())
                // 如果没有符合条件的版本号，返回null
                .orElse("0");
    }

    /**
     * 获取调拨单管理列表
     * @param zaOccurrenceMonth 当前月
     * @param mc03CgtProdPlanVersion 版本
     * @param mc03CgtProdPlType 类型
     * @return List<AllocdemandMangeShowDomain>
     */
    @Override
    public List<AllocdemandMangeShowDomain> getAllocdemandMangeShowData(String zaOccurrenceMonth, String mc03CgtProdPlanVersion, String mc03CgtProdPlType) {
        List<Mc04IslmCgtTranDemandDO> tranDemandDOS = mc04IslmCgtTranDemandService.lambdaQuery()
                .eq(Mc04IslmCgtTranDemandDO::getZaOccurrenceMonth, zaOccurrenceMonth)
                .eq(Mc04IslmCgtTranDemandDO::getMc03CgtProdPlanVersion, mc03CgtProdPlanVersion)
                .eq(Mc04IslmCgtTranDemandDO::getMc03CgtProdPlType, mc03CgtProdPlType)
                .list();
        if (CollectionUtil.isEmpty(tranDemandDOS)) {
            return new ArrayList<>();
        }
        List<AllocdemandMangeShowDomain> resultList = new ArrayList<>();
        for (Mc04IslmCgtTranDemandDO tranDemandDO : tranDemandDOS) {
            AllocdemandMangeShowDomain allocdemandMangeShowDomain = new AllocdemandMangeShowDomain();
            BeanUtils.copyProperties(tranDemandDO, allocdemandMangeShowDomain);
            resultList.add(allocdemandMangeShowDomain);
        }
        return resultList;
    }

    /**
     * 获取调拨单管理列表
     * @param mc03CgtProdPlType 类型
     * @return  List<TranDemandDomain>
     */
    @Override
    public List<TranDemandDomain> queryTranDemandList(String ma02PlanMonth, String mc03CgtProdPlType) {
        // 获取指定月份和计划类型的最新版本号
        String dateMaxVersion = getDateVersion(ma02PlanMonth, mc03CgtProdPlType,true);
        if(StringUtils.equals("0",dateMaxVersion)){
            return new ArrayList<>();
        }
        List<Mc04IslmCgtTranDemandDO> tranDemandDOS = mc04IslmCgtTranDemandService
                .lambdaQuery()
                .eq(Mc04IslmCgtTranDemandDO::getZaOccurrenceMonth, ma02PlanMonth)
                .eq(Mc04IslmCgtTranDemandDO::getMc03CgtProdPlanVersion, dateMaxVersion)
                .eq(Mc04IslmCgtTranDemandDO::getMc03CgtProdPlType, mc03CgtProdPlType)
                .list();
        return TranDemandDomainToDoConverter.INSTANCE.converterDosToModels(tranDemandDOS);
    }
}
