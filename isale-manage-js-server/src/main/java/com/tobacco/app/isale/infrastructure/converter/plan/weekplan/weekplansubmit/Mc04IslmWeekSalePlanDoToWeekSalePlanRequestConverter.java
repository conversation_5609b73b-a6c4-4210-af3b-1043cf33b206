/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.weekplan.weekplansubmit;

import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmWeekSalePlanDO;
import com.tobacco.app.isalecenter.client.req.plan.weekplan.WeekSalePlanRequest;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> jxy
 * @Email : @inspur.com
 * @Create : 2025/08/14
 * @Description : 类型转化
 */
@Mapper
public interface Mc04IslmWeekSalePlanDoToWeekSalePlanRequestConverter extends StructureBaseConverter<Mc04IslmWeekSalePlanDO, WeekSalePlanRequest> {
    Mc04IslmWeekSalePlanDoToWeekSalePlanRequestConverter INSTANCE = Mappers.getMapper(Mc04IslmWeekSalePlanDoToWeekSalePlanRequestConverter.class);
}
