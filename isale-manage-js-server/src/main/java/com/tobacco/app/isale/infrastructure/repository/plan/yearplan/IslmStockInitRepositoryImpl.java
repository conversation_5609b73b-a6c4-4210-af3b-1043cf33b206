/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.yearplan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.inspur.ind.util.IDUtils;
import com.tobacco.app.isale.domain.enums.plan.ycplan.LatestVersionEnum;
import com.tobacco.app.isale.domain.model.plan.yearplan.*;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmStockInitRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmStockInitDoConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmStockInitDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmStockInitService;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmStockInitMapper;
import com.tobacco.app.isale.tools.utils.OrderedIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 库存初始化 领域仓库实现
 *
 * @Author: longxi
 * @Since: 2025-08-07
 */
@Component("IslmStockInitRepository")
public class IslmStockInitRepositoryImpl implements IslmStockInitRepository {

    @Autowired
    private Mc04IslmStockInitService islmStockInitService;

    @Autowired
    private IslmStockInitMapper islmStockInitMapper;

    @Override
    public IslmStockInitModel getById(String mc04StockInitId) {
        Mc04IslmStockInitDO stockInitDo = islmStockInitService.getById(mc04StockInitId);
        return Mc04IslmStockInitDoConverter.INSTANCE.converterDoToModel(stockInitDo);
    }

    @Override
    public boolean save(IslmStockInitModel stockInitModel) {
        if (stockInitModel != null) {
            stockInitModel.setMc04StockInitId(OrderedIdGenerator.generateOrderedId32());
        }
        Mc04IslmStockInitDO mc04IslmStockInitDO = Mc04IslmStockInitDoConverter.INSTANCE.converterModelToDo(stockInitModel);
        return islmStockInitService.save(mc04IslmStockInitDO);
    }

    @Override
    public boolean updateById(IslmStockInitModel stockInitModel) {
        Mc04IslmStockInitDO mc04IslmStockInitDO = Mc04IslmStockInitDoConverter.INSTANCE.converterModelToDo(stockInitModel);
        boolean status = islmStockInitService.updateById(mc04IslmStockInitDO);
        stockInitModel.setMc04StockInitId(mc04IslmStockInitDO.getMc04StockInitId());
        return status;
    }

    @Override
    public IslmStockInitModel latest(String zaOccurrenceYear) {
        LambdaQueryWrapper<Mc04IslmStockInitDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmStockInitDO::getZaOccurrenceYear, zaOccurrenceYear)
                .eq(Mc04IslmStockInitDO::getMc04IsLastestVersion, LatestVersionEnum.CURRENT.getCode());
        List<Mc04IslmStockInitDO> list = islmStockInitService.list(queryWrapper);
        if (list.size() > 0) {
            return Mc04IslmStockInitDoConverter.INSTANCE.converterDoToModel(list.get(0));
        }
        return null;
    }

    @Override
    public List<IslmStockItemHistoryModel> historyStockItem(String yearStr) {
        return islmStockInitMapper.selectStockHistoryByYear(yearStr);
    }

    @Override
    public List<IslmSaleItemHistoryModel> historySaleItem(String yearStr) {
        return islmStockInitMapper.selectSaleHistoryByYear(yearStr);
    }

    @Override
    public List<IslmPlItemHistoryModel> historyPlItem(String yearStr) {
        return islmStockInitMapper.selectPlHistoryByYear(yearStr);
    }

    @Override
    public List<IslmSaleItemHistoryMonthModel> historyMonthSaleItem(String comCode, String yearStr, String monthStr) {
        return islmStockInitMapper.selectHistoryMonthSaleItem(yearStr, monthStr);
    }

    @Override
    public List<IslmPlItemHistoryMonthModel> historyMonthPlItem(String comCode, String yearStr, String monthStr) {
        return islmStockInitMapper.selectHistoryMonthPlItem(yearStr, monthStr);
    }
}
