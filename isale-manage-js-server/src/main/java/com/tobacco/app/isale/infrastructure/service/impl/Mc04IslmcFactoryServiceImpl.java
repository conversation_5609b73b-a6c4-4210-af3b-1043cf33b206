/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcFactoryDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcFactoryMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcFactoryService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: wmd
 * @Since: 2025-07-31
 * @Email: <EMAIL>
 * @Create: 2025-07-31
        */
@Service
public class Mc04IslmcFactoryServiceImpl extends ServiceImpl<Mc04IslmcFactoryMapper, Mc04IslmcFactoryDO> implements Mc04IslmcFactoryService {

}