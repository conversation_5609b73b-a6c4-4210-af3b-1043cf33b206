/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcComItemContDelivWhseDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcComItemContDelivWhseMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcComItemContDelivWhseService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: zhangshch01
 * @Since: 2025-08-01
 * @Email: <EMAIL>
 * @Create: 2025-08-01
 */
@Service
public class Mc04IslmcComItemContDelivWhseServiceImpl extends ServiceImpl<Mc04IslmcComItemContDelivWhseMapper, Mc04IslmcComItemContDelivWhseDO> implements Mc04IslmcComItemContDelivWhseService {

}