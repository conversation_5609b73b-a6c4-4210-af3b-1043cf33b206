/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.monthplan.monthplanadj;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.inspur.ind.util.IDUtils;
import com.tobacco.app.isale.domain.constants.MonthPlanConstants;
import com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlanAdj;
import com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlanAdjSnapshot;
import com.tobacco.app.isale.domain.model.plan.monthplan.Mc04IslmMonthSalePlanSnapshot;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadj.MonthPlanAdj;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadj.MonthPlanAdjItemAdd;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmitBasicItem;
import com.tobacco.app.isale.domain.repository.plan.monthplan.monthplanadj.IslmMonthPlanAdjRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.MonthPlanAdjConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.MonthSalePlanSnapshotAdjConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanAdjDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanAdjSnapshotDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanSnapshotDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanAdjService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanAdjSnapshotService;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan.monthplanadj.MonthPlanAdjMapper;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.monthplan.monthplansubmit.MonthPlanSubmitMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Author: jinfuli
 * @Date: 2025/6/13
 * @Description:
 */
@Repository
public class IslmMonthPlanAdjRepositoryImpl implements IslmMonthPlanAdjRepository {


    private final Mc04IslmMonthSalePlanAdjService mc04IslmMonthSalePlanAdjService;


    public IslmMonthPlanAdjRepositoryImpl(Mc04IslmMonthSalePlanAdjService mc04IslmMonthSalePlanAdjService) {
        this.mc04IslmMonthSalePlanAdjService = mc04IslmMonthSalePlanAdjService;
    }

    @Autowired
    private MonthPlanAdjMapper monthPlanAdjMapper;

    @Autowired
    private MonthPlanSubmitMapper monthPlanSubmitMapper;

    @Autowired
    private Mc04IslmMonthSalePlanAdjSnapshotService mc04IslmMonthSalePlanAdjSnapshotService;

    @Override
    public List<Mc04IslmMonthSalePlanAdj> getIslmMonthSalePlan(String planMonth, List<String> baComOrgCodeList) {
        QueryWrapper<Mc04IslmMonthSalePlanAdjDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmMonthSalePlanAdjDO::getMa02PlanMonth, planMonth)
                .eq(Mc04IslmMonthSalePlanAdjDO::getMc04IsLatestAdjust, MonthPlanConstants.MC04_IS_LATEST_ADJUST_1)
                .in(Mc04IslmMonthSalePlanAdjDO::getBaComOrgCode, baComOrgCodeList);
        List<Mc04IslmMonthSalePlanAdjDO> mc04IslmMonthSalePlanAdjDOS = mc04IslmMonthSalePlanAdjService.list(queryWrapper);
        return MonthPlanAdjConverter.INSTANCE.converterDosToModels(mc04IslmMonthSalePlanAdjDOS);
    }

    @Override
    public List<MonthPlanAdj> getComItemList(String planMonth, List<String> comIds) {
        return monthPlanAdjMapper.getComItemList(planMonth, comIds);
    }

    @Override
    public List<MonthPlanSubmitBasicItem> getBacicItem(String year, String currentMonth, List<String> comIds, String subjectType, String quarter, String halfYear, String isCigar,  String lastHalfYear,  String isAdj) {
        return monthPlanSubmitMapper.getBacicItem(year, currentMonth, comIds, subjectType, quarter, halfYear, isCigar, null, lastHalfYear, isAdj);

    }

    @Override
    public List<MonthPlanAdj> getIslmMonthSalePlanAdjSnapshotList(String planMonth, List<String> baComOrgCodeList) {
        QueryWrapper<Mc04IslmMonthSalePlanAdjSnapshotDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmMonthSalePlanAdjSnapshotDO::getMa02PlanMonth, planMonth)
                .in(Mc04IslmMonthSalePlanAdjSnapshotDO::getBaComOrgCode, baComOrgCodeList);
        List<Mc04IslmMonthSalePlanAdjSnapshotDO> monthSalePlanList = mc04IslmMonthSalePlanAdjSnapshotService.list(queryWrapper);
        return MonthSalePlanSnapshotAdjConverter.INSTANCE.converterDosToModels(monthSalePlanList);
    }

    @Override
    public List<MonthPlanAdj> getCgtPlResolTotalTplanQtyList(String icomCode, String planMonth, List<String> baComOrgCodeList) {
        return monthPlanAdjMapper.getCgtPlResolTotalTplanQtyList(icomCode, planMonth, baComOrgCodeList);
    }

    @Override
    public void addMonthPlanAdj(List<Mc04IslmMonthSalePlanAdjDO> req) {
        //查询是否有90状态的月计划

//        QueryWrapper<Mc04IslmMonthSalePlanAdjDO> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lambda().eq(Mc04IslmMonthSalePlanAdjDO::getZaOccurrenceMonth, req.get(0).getZaOccurrenceMonth())
//                .eq(Mc04IslmMonthSalePlanAdjDO::getBaComOrgCode, Collections.singletonList(req.get(0).getBaComOrgCode()))
//                .eq(Mc04IslmMonthSalePlanAdjDO::getMc04MonthSalePlanStatus, MonthPlanConstants.MONTH_SALE_PLAN_STATUS_90)
//                .eq(Mc04IslmMonthSalePlanAdjDO::getMc04IsLatestAdjust, MonthPlanConstants.MC04_IS_LATEST_ADJUST_1);
//        List<Mc04IslmMonthSalePlanAdjDO> islmMonthSalePlans = mc04IslmMonthSalePlanAdjService.list(queryWrapper);
//
//        // 判断是否存在状态为90的记录
//        boolean hasStatus90 = false;
//        if (CollectionUtil.isNotEmpty(islmMonthSalePlans)) {
//            hasStatus90 = islmMonthSalePlans.stream()
//                    .anyMatch(plan -> MonthPlanConstants.MONTH_SALE_PLAN_STATUS_90.equals(plan.getMc04MonthSalePlanStatus()));
//        }
//        //将90的月计划设为非最新
//        if (hasStatus90) {
        UpdateWrapper<Mc04IslmMonthSalePlanAdjDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(Mc04IslmMonthSalePlanAdjDO::getZaOccurrenceMonth, req.get(0).getZaOccurrenceMonth())
                .eq(Mc04IslmMonthSalePlanAdjDO::getBaComOrgCode, req.get(0).getBaComOrgCode())
                .eq(Mc04IslmMonthSalePlanAdjDO::getMc04MonthSalePlanStatus, MonthPlanConstants.MONTH_SALE_PLAN_STATUS_90)
                .eq(Mc04IslmMonthSalePlanAdjDO::getMc04IsLatestAdjust, MonthPlanConstants.MC04_IS_LATEST_ADJUST_1)
                .set(Mc04IslmMonthSalePlanAdjDO::getMc04IsLatestAdjust, MonthPlanConstants.MC04_IS_LATEST_ADJUST_0);
        mc04IslmMonthSalePlanAdjService.update(updateWrapper);
//        }



        //批量插入或修改
        boolean b = mc04IslmMonthSalePlanAdjService.saveOrUpdateBatch(req);
        if (!b) {
            throw new RuntimeException("月计划调整保存失败");
        }

    }

    @Override
    public void addMonthPlanAdjSnapshot(List<Mc04IslmMonthSalePlanAdjSnapshotDO> req) {

        //更新历史数据
        UpdateWrapper<Mc04IslmMonthSalePlanAdjSnapshotDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(Mc04IslmMonthSalePlanAdjSnapshotDO::getZaOccurrenceMonth, req.get(0).getZaOccurrenceMonth())
                .eq(Mc04IslmMonthSalePlanAdjSnapshotDO::getBaComOrgCode, req.get(0).getBaComOrgCode())
                .eq(Mc04IslmMonthSalePlanAdjSnapshotDO::getMc04IsLatestAdjust, MonthPlanConstants.MC04_IS_LATEST_ADJUST_1)
                .set(Mc04IslmMonthSalePlanAdjSnapshotDO::getMc04IsLatestAdjust, MonthPlanConstants.MC04_IS_LATEST_ADJUST_0);
        mc04IslmMonthSalePlanAdjSnapshotService.update(updateWrapper);

        //批量插入或修改
        boolean b = mc04IslmMonthSalePlanAdjSnapshotService.saveOrUpdateBatch(req);
        if (!b) {
            throw new RuntimeException("月计划调整保存失败");
        }
    }

    @Override
    public void deleteIslmMonthSalePlan(String planMonth, String baComOrgCode, String salePlanAdjId) {
        QueryWrapper<Mc04IslmMonthSalePlanAdjDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmMonthSalePlanAdjDO::getMa02PlanMonth, planMonth)
                .eq(Mc04IslmMonthSalePlanAdjDO::getBaComOrgCode, baComOrgCode)
                .eq(Mc04IslmMonthSalePlanAdjDO::getMc04MonthSalePlanAdjId, salePlanAdjId);
        mc04IslmMonthSalePlanAdjService.remove(queryWrapper);

    }

    @Override
    public void deleteIslmMonthSalePlanSnapshot(String planMonth, String baComOrgCode, String salePlanAdjId) {
        QueryWrapper<Mc04IslmMonthSalePlanAdjSnapshotDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmMonthSalePlanAdjSnapshotDO::getMa02PlanMonth, planMonth)
                .eq(Mc04IslmMonthSalePlanAdjSnapshotDO::getBaComOrgCode, baComOrgCode)
                .eq(Mc04IslmMonthSalePlanAdjSnapshotDO::getMc04MonthSalePlanAdjId, salePlanAdjId);
        mc04IslmMonthSalePlanAdjSnapshotService.remove(queryWrapper);



    }

    /**
     * 查询地市树状态
     *
     * @param ma02PlanMonth
     * @param icomCode
     * @return
     */
    @Override
    public List<Map<String, Object>> queryOrgStatus(String ma02PlanMonth, String icomCode, String mc04MonthSalePlanStatus) {
        return monthPlanAdjMapper.queryOrgStatus(ma02PlanMonth, icomCode, mc04MonthSalePlanStatus);
    }

    @Override
    public List<Map<String, Object>> getAdjustedQtyByMonth(String ma02PlanMonth, List<String> baComOrgCodeList) {

        return monthPlanAdjMapper.getAdjustedQtyByMonth(ma02PlanMonth, baComOrgCodeList);
    }
}
