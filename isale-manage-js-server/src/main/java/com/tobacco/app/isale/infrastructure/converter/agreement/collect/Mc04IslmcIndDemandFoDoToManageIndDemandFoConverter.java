/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.agreement.collect;

import com.tobacco.app.isale.domain.model.agreement.collect.ManageIndDemandFo;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcIndDemandFoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/04/22 15:31
 * @description : 数据转化
 */
@Mapper
public interface Mc04IslmcIndDemandFoDoToManageIndDemandFoConverter
        extends StructureBaseConverter<Mc04IslmcIndDemandFoDO, ManageIndDemandFo> {
    Mc04IslmcIndDemandFoDoToManageIndDemandFoConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcIndDemandFoDoToManageIndDemandFoConverter.class);


}
