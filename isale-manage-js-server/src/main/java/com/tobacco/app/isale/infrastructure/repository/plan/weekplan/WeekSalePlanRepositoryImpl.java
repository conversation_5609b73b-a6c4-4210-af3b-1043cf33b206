/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.weekplan;


import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.lamboegg.common.base.BaseResultConstant;
import com.tobacco.app.isale.domain.repository.plan.weekplan.WeekSalePlanRepository;
import com.tobacco.app.isale.tools.utils.InterHttpUtils;
import com.tobacco.app.isalecenter.client.dto.plan.weekplan.WeekSalePlanDTO;
import com.tobacco.app.isalecenter.common.exception.CustomException;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: hujiarong
 * @Date: 2025/6/13
 * @Description:
 */
@Component("WeekSalePlanRepository")
@RequiredArgsConstructor
public class WeekSalePlanRepositoryImpl implements WeekSalePlanRepository {

    private static Logger logger = LoggerFactory.getLogger(WeekSalePlanRepositoryImpl.class);

    private final InterHttpUtils interHttpUtils;

    @Override
    public void pushDataList(List<WeekSalePlanDTO> plans) throws CustomException {
        String msgid = "WEEK_TRAN_PLAN_QUERY";
        Map<String, Object> dataList = interHttpUtils.createDataList(msgid, "周调拨计划查询接口", Collections.singleton(plans));
        interHttpUtils.postPush("HUB_" + msgid, dataList);
    }

}
