/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.order.dist.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.constant.CommonConstants;
import com.inspur.ind.icom.IcomUtils;
import com.inspur.ind.util.DateUtils;
import com.inspur.x1.ac.rule.utils.AcRuleUtil;
import com.tobacco.app.isale.domain.constants.DistConstants;
import com.tobacco.app.isale.domain.model.order.dist.order.DistOrder;
import com.tobacco.app.isale.domain.model.order.dist.order.DistOrderItem;
import com.tobacco.app.isale.infrastructure.entity.*;
import com.tobacco.app.isale.infrastructure.service.api.*;
import com.tobacco.app.isale.tools.utils.BigDecimalUtil;
import com.tobacco.app.isale.tools.utils.CommodityUtil;
import com.tobacco.app.isale.tools.utils.CustUtil;
import com.tobacco.app.isale.tools.utils.DistUtils;
import com.tobacco.app.isale.domain.model.common.ComItem;
import com.tobacco.app.isale.domain.model.order.dist.parm.*;
import com.tobacco.app.isale.domain.repository.nation.subsys.ISaleNationSubsysRepository;
import com.tobacco.app.isale.domain.repository.order.dist.order.DistOrderWeekRepository;
import com.tobacco.app.isale.infrastructure.converter.dist.parm.Mc04IslmcDistParmApplyDOToDistParmApplyConverter;
import com.tobacco.app.isale.infrastructure.converter.dist.parm.Mc04IslmcDistParmApplyItemDOToDistParmApplyItemConverter;
import com.tobacco.app.isale.infrastructure.converter.dist.parm.Mc04IslmcDistParmApplySeasonDOToDistParmApplySeasonConverter;
import com.tobacco.app.isale.infrastructure.repository.common.ComItemRepositoryImpl;
import com.tobacco.app.isale.infrastructure.tunnel.database.order.dist.parm.IslmDistParmApplyMapper;
import com.tobacco.app.isalecenter.client.api.order.distParm.DistParmAPI;
import com.tobacco.app.isalecenter.client.dto.common.SaleSingleResponse;
import com.tobacco.app.isalecenter.client.dto.order.distParm.Mc04IslmcDistParmDTO;
import com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmREQ;
import com.tobacco.sc.icommodity.dto.common.constant.dto.item.IccItemDetailDTO;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liuwancheng
 * @create_time : 2025/07/28 13:18
 * @description : 周配货持久层服务实现
 */
@Slf4j
@Component("ISaleDistOrderWeekRepository")
public class DistOrderWeekRepositoryImpl implements DistOrderWeekRepository {
    @Resource
    private Mc04IslmcDistParmApplyService mc04IslmcDistParmApplyService;
    @Resource
    private Mc04IslmcDistParmApplyItemService mc04IslmcDistParmApplyItemService;
    @Resource
    private Mc04IslmcDistParmApplySeasonService mc04IslmcDistParmApplySeasonService;

    @Resource
    private ComItemRepositoryImpl comItemRepositoryImpl;

    @Resource
    private IslmDistParmApplyMapper islmDistParmApplyMapper;

    @Resource
    private ISaleNationSubsysRepository iSaleNationSubsysRepository;

    @Resource
    private DistOrderRealtimeRepositoryImpl distOrderRealtimeRepository;

    @Resource
    private DistParmAPI distParmApi;


    @Resource
    private NationDistRegionService nationDistRegionService;

    @Resource
    private NationDistReceiveRegionService nationDistReceiveRegionService;
    @Resource
    private  Mc04IslmCgtDistOrderService mc04IslmCgtDistOrderService;

    @Resource
    private Mc04IslmcComItemContDelivWhseService mc04IslmcComItemContDelivWhseService;
    private <T, F> Page<F> getPage(Page<T> pageModel) {
        Page<F> page = new Page<>();
        page.setCurrent(pageModel.getCurrent());
        page.setSize(pageModel.getSize());
        if (CollectionUtil.isNotEmpty(pageModel.getOrders())) {
            List<OrderItem> orderItemList = pageModel.getOrders().stream().map(item -> {
                OrderItem orderItem = new OrderItem();
                orderItem.setColumn(item.getColumn());
                orderItem.setAsc(item.isAsc());
                return orderItem;
            }).collect(Collectors.toList());
            page.setOrders(orderItemList);
        }
        return page;
    }

    /**
     * 分页查询配货参数申请列表
     *
     * @param req 查询请求参数
     * @return 分页数据响应对象
     */
    @Override
    public Page<DistParmApply> queryDistParmApplyPage(DistParmApplyPage req) {
        Page<DistParmApply> page = getPage(req);
        return islmDistParmApplyMapper.queryDistParmApplyPage(page, req);
    }

    /**
     * 查询配货参数申请的详细信息
     *
     * @param req 配货参数申请查询
     * @return 配货参数申请明细数据
     */
    @Override
    public DistOrder queryDistParmApplyDetail(DistParmApply req) {
        String baComOrgCode = req.getMa02CgtTradeReqMembCode();
        String md02CgtDistRegionCode = req.getMd02CgtDistRegionCode();
        String mc04DatePeriodCode = req.getMc04DatePeriodCode();
        String md02DistReceiveregionCode = req.getMd02DistReceiveregionCode();
        QueryWrapper<Mc04IslmCgtDistOrderDO> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.lambda().eq(Mc04IslmCgtDistOrderDO::getBaComOrgCode,baComOrgCode).eq(Mc04IslmCgtDistOrderDO::getMc04DatePeriodCode,mc04DatePeriodCode)
                .eq(Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderType,"20").groupBy(Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderCode);
        List<Mc04IslmCgtDistOrderDO> list1 = mc04IslmCgtDistOrderService.list(queryWrapper1);
        // 中心获取配货参数
        DistOrder distOrder = new DistOrder();
        String icomCode = IcomUtils.getIcomCode();
        // 获取配货参数
        Mc04IslmcDistParmDTO distParmDto = null;
        if (!list1.isEmpty() && StrUtil.isNotBlank(list1.get(0).getMc04CgtDistOrderCode())) {
            String mc04CgtDistOrderCode = list1.get(0).getMc04CgtDistOrderCode();
            // 按配货参数申请编号查询明细
            QueryWrapper<Mc04IslmCgtDistOrderDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(
                    Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderCode,mc04CgtDistOrderCode
            );
            //按申请编号造一条订单
            QueryWrapper<Mc04IslmCgtDistOrderDO> queryWrapper2 = new QueryWrapper<>();
            queryWrapper2.lambda().eq(
                    Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderCode,mc04CgtDistOrderCode
            ).groupBy(Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderCode);
            List<Mc04IslmCgtDistOrderDO> list = mc04IslmCgtDistOrderService.list(queryWrapper);
            List<Mc04IslmCgtDistOrderDO> list2 = mc04IslmCgtDistOrderService.list(queryWrapper2);

            distParmDto = getDistParm(icomCode, baComOrgCode,md02CgtDistRegionCode, md02DistReceiveregionCode);
            distOrder = getDistOrder(icomCode,list2.get(0));
            distOrder.setDistOrderItems(BeanUtil.copyToList(list,DistOrderItem.class));
        } else {
            distParmDto = getDistParm(icomCode, baComOrgCode,md02CgtDistRegionCode, md02DistReceiveregionCode);
            distOrder = initDistOrder(icomCode, baComOrgCode,md02CgtDistRegionCode, md02DistReceiveregionCode, distParmDto);
        }
        // 卷烟名称、卷烟顺序、调拨价条、配货单位、是否新品、是否网配规格
        distOrderRealtimeRepository.setCgtInfo(distOrder, distParmDto);
        // 设置地市名称，发运地区名称，收货地区名称
        setName(distOrder);
        // 托盘容量
        distOrderRealtimeRepository.setTrayCap(distOrder);
        // 添加业务规则
        distOrderRealtimeRepository.addRules(distOrder);

        return distOrder;
    }

    /**
     * 设置地市名称，发运地区名称，收货地区名称
     *
     * @param distOrder 配货订单
     */
    private void setName(DistOrder distOrder) {
        String icomCode = distOrder.getIcomCode();
        String baComOrgCode = distOrder.getBaComOrgCode();
        BusiComDto busiComDto = CustUtil.getBusiComDto(baComOrgCode);
        Assert.notNull(busiComDto, () -> new CustomException("获取地市失败"));
        assert busiComDto != null;
        distOrder.setBaComOrgName(busiComDto.getMc04ComOrgShortName());
        // 发运地区编码不为空则设置名称
        String distRegionCode = distOrder.getMd02CgtDistRegionCode();
        if (StrUtil.isNotBlank(distRegionCode)) {
            // 配货发运地区查询
            QueryWrapper<NationDistRegionDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(NationDistRegionDO::getMemberCode, icomCode).eq(NationDistRegionDO::getRegionCode, distRegionCode);
            List<NationDistRegionDO> distRegionList = nationDistRegionService.list(queryWrapper);
            if (CollUtil.isNotEmpty(distRegionList)) {
                distOrder.setMd02CgtDistRegionName(distRegionList.get(0).getRegionName());
            }
        }
        // 收货地区编码不为空则设置名称
        String md02DistReceiveregionCode = distOrder.getMd02DistReceiveregionCode();
        if (StrUtil.isNotBlank(md02DistReceiveregionCode)) {
            // 配货收货地区查询
            QueryWrapper<NationDistReceiveRegionDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(NationDistReceiveRegionDO::getMa02CgtTradeReqMembCode, baComOrgCode).eq(NationDistReceiveRegionDO::getMd02DistReceiveregionCode, md02DistReceiveregionCode);
            List<NationDistReceiveRegionDO> distReceiveRegionDOList = nationDistReceiveRegionService.list(queryWrapper);
            if (CollUtil.isNotEmpty(distReceiveRegionDOList)) {
                distOrder.setMd02DistReceiveregionName(distReceiveRegionDOList.get(0).getMd02DistReceiveregionName());
            }
        }
    }
    /**
     * 订单表查询配货单
     *
     * @param icomCode                  工业编码
     * @return 实时配货单
     */
    private DistOrder getDistOrder(String icomCode, Mc04IslmCgtDistOrderDO mc04IslmCgtDistOrderDO) {
        // 中心获取配货参数
        DistOrder distOrder = new DistOrder();
        BeanUtil.copyProperties(mc04IslmCgtDistOrderDO,distOrder);
        distOrder.setIcomCode(icomCode);
        distOrder.setMd02CgtInStorehouseCodeConct(mc04IslmCgtDistOrderDO.getMc04CgtPraInStorehouseCode());
        return distOrder;
    }

    /**
     * 初始化配货单
     *
     * @param icomCode                  工业编码
     * @param baComOrgCode              商业公司编码
     * @param distParmDto               配货参数
     * @return 实时配货单
     */
    private DistOrder initDistOrder(String icomCode, String baComOrgCode, String md02CgtDistRegionCode, String md02DistReceiveregionCode,Mc04IslmcDistParmDTO distParmDto) {
        // 中心获取配货参数
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String today = LocalDate.now().format(dateTimeFormatter);
        // 初始化
        DistOrder distOrder = new DistOrder();
        distOrder.setIcomCode(icomCode);
        distOrder.setBaComOrgCode(baComOrgCode);
        distOrder.setMd02CgtDistRegionCode(md02CgtDistRegionCode);
        distOrder.setMd02DistReceiveregionCode(md02DistReceiveregionCode);
        // 协议和月份默认取当前
        String month = today.substring(0, 6);
        distOrder.setMa02PlanMonth(month);
        distOrder.setMc04CgtXyPeriodCode(DateUtils.getAgreementPeriodCode(month));
        // 配货日期
        distOrder.setMc04CgtDistOrderDistDate(today);
        // 不存在配货单编号，初始化一条配货单编号
        List<ComItem> comItemList = comItemRepositoryImpl.getComItemList(CommonConstants.NO, baComOrgCode);
        List<DistOrderItem> distOrderItems = comItemList.stream().map(comItem -> {
            DistOrderItem orderItem = new DistOrderItem();
            orderItem.setAcCgtCartonCode(comItem.getAcCgtCartonCode());
            orderItem.setAcTwoLevelCigCode(comItem.getAcTwoLevelCigCode());
            orderItem.setMd03LogtTrayCombTspTrayType(comItem.getMd03LogtTrayCombTspTrayType());
            // 初始化值，防止前端不能双向绑定
            orderItem.setMc04CgtDistOrderSaleAreaReqQty(null);
            return orderItem;
        }).collect(Collectors.toList());
        // 获取配货参数
        if (ObjectUtil.isNotNull(distParmDto)) {
            // 发货日期
            distOrder.setMc04ContExpecOutDate(LocalDate.now().plusDays(BigDecimalUtil.getZeroBigDeciamal(distParmDto.getMd02CgtDistPrepDays()).intValue()).format(dateTimeFormatter));
            // 收货日期
            distOrder.setMc04ContExpecArrivalDate(LocalDate.parse(distOrder.getMc04ContExpecOutDate(), dateTimeFormatter).plusDays(BigDecimalUtil.getZeroBigDeciamal(distParmDto.getMd02CgtDistOnWayDays()).intValue()).format(dateTimeFormatter));
        } else {
            // 发货收货日期
            distOrder.setMc04ContExpecOutDate(LocalDate.now().plusDays(1).format(dateTimeFormatter));
            distOrder.setMc04ContExpecArrivalDate(LocalDate.now().plusDays(2).format(dateTimeFormatter));
        }
        // 设置卷烟发货仓库
        setDelivWhse(distOrderItems, baComOrgCode, icomCode);
        distOrder.setDistOrderItems(distOrderItems);
        return distOrder;
    }
    /**
     * 获取配货参数
     *
     * @param icomCode                  工业公司编码
     * @param baComOrgCode              公司code编码
     * @return Mc04IslmcDistParmDTO
     */
    public Mc04IslmcDistParmDTO getDistParm(String icomCode, String baComOrgCode, String md02CgtDistRegionCode, String md02DistReceiveregionCode) {
        Mc04IslmcDistParmREQ req = new Mc04IslmcDistParmREQ();
        req.setMd02CgtDistRegionCode(StrUtil.isNotBlank(md02CgtDistRegionCode) ? md02CgtDistRegionCode : "");
        req.setMd02DistReceiveregionCode(StrUtil.isNotBlank(md02DistReceiveregionCode) ? md02DistReceiveregionCode : "");
        req.setMa02CgtTradeReqMembCode(baComOrgCode);
        req.setIcomCode(icomCode);
        SaleSingleResponse<List<Mc04IslmcDistParmDTO>> response = distParmApi.list(req);
        Assert.isTrue(response.isSuccess(), () -> new CustomException("获取配货参数失败"));
        if (CollectionUtil.isNotEmpty(response.getData())) {
            return response.getData().get(0);
        }
        return null;
    }

    /**
     * 设置卷烟发货仓库
     *
     * @param distOrderItems 配货订单卷烟列表
     * @param baComOrgCode   商业公司编码
     * @param icomCode       工业公司编码
     */
    private void setDelivWhse(List<DistOrderItem> distOrderItems, String baComOrgCode, String icomCode) {
        // 二级牌号列表
        List<String> twoLevelCigCodeList = distOrderItems.stream().map(DistOrderItem::getAcTwoLevelCigCode).collect(Collectors.toList());
        // 各卷烟的默认发货仓库
        QueryWrapper<Mc04IslmcComItemContDelivWhseDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(Mc04IslmcComItemContDelivWhseDO::getAcTwoLevelCigCode, twoLevelCigCodeList).eq(Mc04IslmcComItemContDelivWhseDO::getBaComOrgCode, baComOrgCode);
        List<Mc04IslmcComItemContDelivWhseDO> delivList = mc04IslmcComItemContDelivWhseService.list(queryWrapper);
        Map<String, String> delivMap = delivList.stream().collect(Collectors.toMap(Mc04IslmcComItemContDelivWhseDO::getAcTwoLevelCigCode, Mc04IslmcComItemContDelivWhseDO::getMd02CgtOutStorehouseCode));
        // 机构参数设置的统一默认发货仓库
        String defaultDelivWhse = AcRuleUtil.getRuleValue(DistConstants.ISALE_ORDER_DIST_DEF_DELIV_WHSE, icomCode);
        distOrderItems.forEach(item -> {
            String delivWhse = delivMap.get(item.getAcTwoLevelCigCode());
            item.setMd02CgtOutStorehouseCode(StrUtil.isNotBlank(delivWhse) ? delivWhse : defaultDelivWhse);
        });
    }
    /**
     * 查询本工业所有协议单位的配货参数申请的状态
     *
     * @param icomCode
     * @param mc04DatePeriodCode
     * @return 配货参数申请状态数据
     */
    @Override
    public List<DistParmApplyStatus> queryDistOrderWeekStatus(String icomCode, String mc04DatePeriodCode) {
        return islmDistParmApplyMapper.queryDistOrderWeekStatus(icomCode,mc04DatePeriodCode);
    }

    /**
     * 保存配货参数申请
     *
     * @param distParmApply 配货参数申请
     * @return 操作结果布尔值响应
     */
    @Override
    public DistParmApply saveDistParmApply(DistParmApply distParmApply) {
        Assert.notNull(distParmApply.getMc04CgtDistParmApplyCode(),
                () -> new CustomException("配货参数申请编码不能为空"));
        List<DistParmApplyItem> items = distParmApply.getItems();
        List<DistParmApplySeason> seasons = distParmApply.getSeasons();
        Assert.notEmpty(items,
                () -> new CustomException("配货参数卷烟列表不能为空"));

        // 配货参数申请保存
        boolean success = mc04IslmcDistParmApplyService.saveOrUpdate(Mc04IslmcDistParmApplyDOToDistParmApplyConverter.INSTANCE.converterModelToDo(distParmApply));
        // 配货参数申请卷烟批量保存   saveOrUpdateBatch太慢采用先删后批量插入
        if (success) {
            for (DistParmApplyItem item : items) {
                item.setMc04CgtDistParmApplyId(distParmApply.getMc04CgtDistParmApplyId());
            }
            mc04IslmcDistParmApplyItemService.remove(new LambdaQueryWrapper<Mc04IslmcDistParmApplyItemDO>().eq(Mc04IslmcDistParmApplyItemDO::getMc04CgtDistParmApplyId, distParmApply.getMc04CgtDistParmApplyId()));
            // 因mybatis  plus 批量插入单条处理 实际很快、可以使用
            success = mc04IslmcDistParmApplyItemService.saveBatch(Mc04IslmcDistParmApplyItemDOToDistParmApplyItemConverter.INSTANCE.converterModelsToDos(items));
        }

        // 配货参数申请季节因子 若有的话批量保存
        if (success) {
            mc04IslmcDistParmApplySeasonService.remove(new LambdaQueryWrapper<Mc04IslmcDistParmApplySeasonDO>().eq(Mc04IslmcDistParmApplySeasonDO::getMc04CgtDistParmApplyId, distParmApply.getMc04CgtDistParmApplyId()));
            if (ObjectUtil.isNotEmpty(seasons)) {
                for (DistParmApplySeason item : seasons) {
                    item.setMc04CgtDistParmApplyId(distParmApply.getMc04CgtDistParmApplyId());
                }
                // 因mybatis  plus 批量插入单条处理 实际很快、可以使用
                mc04IslmcDistParmApplySeasonService.saveBatch(Mc04IslmcDistParmApplySeasonDOToDistParmApplySeasonConverter.INSTANCE.converterModelsToDos(seasons));
            }
        }
        // 判断是否禁用发给行业子系统，默认否 ，否的话发送给行业子系统
        if (isSendToIndustry(distParmApply.getMc04CgtDistParmApplyStatus())) {
            boolean ok = iSaleNationSubsysRepository.pushDistparmToNation(distParmApply.getMc04CgtDistParmApplyCode());
            // 推送失败状态改回当前状态
            if (!ok) {
                distParmApply.setMc04CgtDistParmApplyStatus(distParmApply.getCurStatus());
            }
        }
        return distParmApply;
    }

    /**
     * 查询本工业所有协议单位的配货参数申请列表
     *
     * @param distParmApply 查询请求参数
     * @return 配货参数申请数据
     */
    @Override
    public List<DistParmApply> queryDistParmApplyList(DistParmApply distParmApply) {
        List<String> mc04CgtDistParmApplyIds = distParmApply.getMc04CgtDistParmApplyIds();
        List<String> ma02CgtTradeReqMembCodes = distParmApply.getMa02CgtTradeReqMembCodes();
        List<Mc04IslmcDistParmApplyDO> applys = mc04IslmcDistParmApplyService.lambdaQuery()
                .eq(Mc04IslmcDistParmApplyDO::getMa02CgtTradeSuppMembCode, distParmApply.getMa02CgtTradeSuppMembCode())
                .eq(StrUtil.isNotBlank(distParmApply.getMc04CgtDistParmApplyStatus()), Mc04IslmcDistParmApplyDO::getMc04CgtDistParmApplyStatus, distParmApply.getMc04CgtDistParmApplyStatus())
                .in(CollectionUtil.isNotEmpty(mc04CgtDistParmApplyIds), Mc04IslmcDistParmApplyDO::getMc04CgtDistParmApplyId, mc04CgtDistParmApplyIds)
                .in(CollectionUtil.isNotEmpty(ma02CgtTradeReqMembCodes), Mc04IslmcDistParmApplyDO::getMa02CgtTradeReqMembCode, ma02CgtTradeReqMembCodes)
                .list();
        return Mc04IslmcDistParmApplyDOToDistParmApplyConverter.INSTANCE.converterDosToModels(applys);
    }

    /**
     * 批量更新配货参数申请状态
     *
     * @param distParmApplyStatus 配货参数申请状态信息
     * @return 处理后的配货参数申请状态列表
     */
    @Override
    public DistParmApplyStatus saveBatchDistParmApplyStatus(DistParmApplyStatus distParmApplyStatus) {
        Assert.notNull(distParmApplyStatus,
                () -> new CustomException("配货参数申请状态信息不能为空"));
        List<String> mc04CgtDistParmApplyIds = distParmApplyStatus.getMc04CgtDistParmApplyIds();
        Assert.notEmpty(mc04CgtDistParmApplyIds,
                () -> new CustomException("配货参数申请编码列表不能为空"));
        String mc04CgtDistParmApplyStatus = distParmApplyStatus.getMc04CgtDistParmApplyStatus();
        Assert.notBlank(mc04CgtDistParmApplyStatus,
                () -> new CustomException("配货参数申请状态不能为空"));
        // 查询配货参数申请单
        DistParmApply distParmApply = new DistParmApply();
        distParmApply.setMc04CgtDistParmApplyIds(mc04CgtDistParmApplyIds);
        distParmApply.setMa02CgtTradeSuppMembCode(distParmApplyStatus.getMa02CgtTradeSuppMembCode());
        List<DistParmApply> applys = queryDistParmApplyList(distParmApply);
        // 过滤当前状态的配货参数申请单
        applys = applys.stream().filter(apply -> apply.getMc04CgtDistParmApplyStatus().equals(distParmApplyStatus.getCurStatus())).collect(Collectors.toList());
        Assert.notEmpty(applys,
                () -> new CustomException("配货参数申请单不能为空"));
        // 成功和失败的配货参数申请编码列表
        List<String> successMc04CgtDistParmApplyIds = new ArrayList<>();
        List<String> successMc04CgtDistParmApplyCodes = new ArrayList<>();
        List<String> failMc04CgtDistParmApplyCodes = new ArrayList<>();
        // 判断是否禁用发给行业子系统，默认否 ，否的话发送给行业子系统
        if (isSendToIndustry(mc04CgtDistParmApplyStatus)) {
            for (DistParmApply apply : applys) {
                boolean ok = iSaleNationSubsysRepository.pushDistparmToNation(apply.getMc04CgtDistParmApplyCode());
                if (ok) {
                    successMc04CgtDistParmApplyIds.add(apply.getMc04CgtDistParmApplyId());
                    successMc04CgtDistParmApplyCodes.add(apply.getMc04CgtDistParmApplyCode());
                } else {
                    failMc04CgtDistParmApplyCodes.add(apply.getMc04CgtDistParmApplyCode());
                }
            }
        } else {
            successMc04CgtDistParmApplyIds = applys.stream().map(DistParmApply::getMc04CgtDistParmApplyId).collect(Collectors.toList());
        }
        Assert.notEmpty(successMc04CgtDistParmApplyIds,
                () -> new CustomException("配货参数申请批量审核失败"));
        // 批量更新调用行业成功的配货参数申请的状态
        mc04IslmcDistParmApplyService.update(new LambdaUpdateWrapper<Mc04IslmcDistParmApplyDO>()
                .eq(Mc04IslmcDistParmApplyDO::getMa02CgtTradeSuppMembCode, distParmApplyStatus.getMa02CgtTradeSuppMembCode())
                .in(CollectionUtil.isNotEmpty(successMc04CgtDistParmApplyIds), Mc04IslmcDistParmApplyDO::getMc04CgtDistParmApplyId, successMc04CgtDistParmApplyIds)
                .set(Mc04IslmcDistParmApplyDO::getMc04CgtDistParmApplyStatus, distParmApplyStatus.getMc04CgtDistParmApplyStatus())
        );
        // 返回成功和失败的配货参数申请状态列表
        distParmApplyStatus.setSuccessMc04CgtDistParmApplyCodes(successMc04CgtDistParmApplyCodes);
        distParmApplyStatus.setFailMc04CgtDistParmApplyCodes(failMc04CgtDistParmApplyCodes);
        return distParmApplyStatus;
    }

    @Override
    public Map<String, String> getRules(List<String> rules) {
        String icomCode = IcomUtils.getIcomCode();
        Map<String, String> rules1 = distOrderRealtimeRepository.getRules(rules, icomCode);
        String isaleOrderDistAgreeRange = rules1.get("ISALE_ORDER_DIST_AGREE_RANGE");
        // 日期是否在此区间内
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyyMMdd");
        String today = LocalDate.now().format(format);
        if (StrUtil.isNotBlank(isaleOrderDistAgreeRange)) {
            List<String> split = StrUtil.split(isaleOrderDistAgreeRange, ",");
            for (String s : split) {
                List<String> split1 = StrUtil.split(s, "-");
                if (Integer.parseInt(today.substring(4, 8)) >= Integer.parseInt(split1.get(0), 10)
                        && Integer.parseInt(today.substring(4, 8)) <= Integer.parseInt(split1.get(1), 10)) {
                    rules1.put("agreeRange", "1");
                } else {
                    rules1.put("agreeRange", "0");
                }
            }
        }
        return rules1;
    }

    /**
     * 配货参数申请状态-商业待确认
     */
    private static final String CGT_DIST_PARM_APPLY_STATUS_APPROVE = "50";

    /**
     * 是否发送给行业子系统
     *
     * @param mc04CgtDistParmApplyStatus 配货参数申请状态
     * @return 是否发送给行业子系统
     */
    private boolean isSendToIndustry(String mc04CgtDistParmApplyStatus) {
        return StrUtil.equals(mc04CgtDistParmApplyStatus, CGT_DIST_PARM_APPLY_STATUS_APPROVE);
    }

    /**
     * 根据供方会员代码、需方会员代码以及网配订单发货地区代码、配货收货地区编码查询配货参数申请的详细信息
     *
     * @param ma02CgtTradeReqMembCode   卷烟需方交易会员代码
     * @param ma02CgtTradeSuppMembCode  卷烟供方交易会员代码
     * @param md02CgtDistRegionCode     网配订单发货地区代码
     * @param md02DistReceiveregionCode 配货收货地区编码
     * @return 配货参数申请明细数据
     */
    private DistParmApply getDistParmApply(String ma02CgtTradeReqMembCode, String ma02CgtTradeSuppMembCode, String md02CgtDistRegionCode, String md02DistReceiveregionCode) {
        // 按供方会员代码、需方会员代码以及网配订单发货地区代码查询明细
        Assert.notNull(ma02CgtTradeReqMembCode,
                () -> new CustomException("卷烟需方交易会员代码不能为空"));
        Assert.notNull(ma02CgtTradeSuppMembCode,
                () -> new CustomException("卷烟供方交易会员代码不能为空"));
        // 按理同一个供方会员代码、需方会员代码的参数申请状态在60之前的只能有一个
        Mc04IslmcDistParmApplyDO mc04IslmcDistParmApplyDO = mc04IslmcDistParmApplyService.lambdaQuery()
                .eq(Mc04IslmcDistParmApplyDO::getMa02CgtTradeReqMembCode, ma02CgtTradeReqMembCode)
                .eq(Mc04IslmcDistParmApplyDO::getMa02CgtTradeSuppMembCode, ma02CgtTradeSuppMembCode)
                .eq(StrUtil.isNotBlank(md02CgtDistRegionCode), Mc04IslmcDistParmApplyDO::getMd02CgtDistRegionCode, md02CgtDistRegionCode)
                .eq(StrUtil.isNotBlank(md02DistReceiveregionCode), Mc04IslmcDistParmApplyDO::getMd02DistReceiveregionCode, md02DistReceiveregionCode)
                .lt(Mc04IslmcDistParmApplyDO::getMc04CgtDistParmApplyStatus, "60")
                .one();
        DistParmApply distParmApply = null;
        if (ObjectUtil.isNotNull(mc04IslmcDistParmApplyDO)) {
            // 如果有配货参数申请取配货参数申请
            distParmApply = Mc04IslmcDistParmApplyDOToDistParmApplyConverter.INSTANCE.converterDoToModel(mc04IslmcDistParmApplyDO);
            // 查询配货参数申请卷烟列表
            queryDistParmApplyItems(distParmApply);
        }
        Assert.notNull(distParmApply, () -> new CustomException("配货参数申请不能为空"));
        assert distParmApply != null;
        List<DistParmApplyItem> items = distParmApply.getItems();
        Assert.notEmpty(items,
                () -> new CustomException("配货参数卷烟列表不能为空"));
        // 查询商业公司的卷烟列表
        List<ComItem> comItems = comItemRepositoryImpl.getComItemList("0", ma02CgtTradeReqMembCode);
        Assert.notEmpty(comItems,
                () -> new CustomException("商业公司卷烟列表不能为空"));
        // 找到公司卷烟里有的 但是配货参数里没有的卷烟
        comItems = comItems.stream().filter(comItem -> items.stream().noneMatch(item -> item.getAcCgtCartonCode().equals(comItem.getAcCgtCartonCode()))).collect(Collectors.toList());
        // 添加商业公司卷烟里有但是配货参数里没有的烟
        if (CollectionUtil.isNotEmpty(comItems)) {
            // 卷烟名称为空的话，查询商品中心取名称
            Map<String, String> productMap = DistUtils.getProductMap(DistUtils.getProducts(ma02CgtTradeSuppMembCode));
            for (ComItem comItem : comItems) {
                DistParmApplyItem item = new DistParmApplyItem();
                item.setAcCgtCartonCode(comItem.getAcCgtCartonCode());
                String acCgtName = productMap.get(item.getAcCgtCartonCode());
                // 可能取不到
                if (StrUtil.isNotBlank(acCgtName)) {
                    item.setAcCgtName(acCgtName);
                }
                items.add(item);
            }
        }
        return distParmApply;
    }

    /**
     * 根据配货参数申请编号查询配货参数申请的详细信息
     *
     * @param mc04CgtDistParmApplyId 配货参数申请编号
     * @return 配货参数申请明细数据
     */
    private DistParmApply getDistParmApplyById(String mc04CgtDistParmApplyId) {
        // 按配货参数申请编号查询明细
        Assert.notNull(mc04CgtDistParmApplyId,
                () -> new CustomException("配货参数申请编号不能为空"));
        DistParmApply distParmApply = Mc04IslmcDistParmApplyDOToDistParmApplyConverter.INSTANCE.converterDoToModel(mc04IslmcDistParmApplyService.getById(mc04CgtDistParmApplyId));
        Assert.isTrue(ObjectUtil.isNotNull(distParmApply),
                () -> new CustomException(mc04CgtDistParmApplyId + "配货参数申请不存在"));
        // 查询配货参数申请卷烟列表
        queryDistParmApplyItems(distParmApply);
        return distParmApply;
    }

    /**
     * 查询配货参申请卷烟和季节因子
     *
     * @param distParmApply 配货参数申请
     */
    private void queryDistParmApplyItems(DistParmApply distParmApply) {
        String mc04CgtDistParmApplyId = distParmApply.getMc04CgtDistParmApplyId();
        // 查询配货参数申请卷烟列表
        List<Mc04IslmcDistParmApplyItemDO> items = mc04IslmcDistParmApplyItemService.lambdaQuery()
                .eq(Mc04IslmcDistParmApplyItemDO::getMc04CgtDistParmApplyId, mc04CgtDistParmApplyId)
                .list();
        Assert.notEmpty(items,
                () -> new CustomException("配货参数申请卷烟列表不能为空"));
        distParmApply.setItems(Mc04IslmcDistParmApplyItemDOToDistParmApplyItemConverter.INSTANCE.converterDosToModels(items));
        // 查询配货参数申请季节因子列表 可能为空
        List<Mc04IslmcDistParmApplySeasonDO> seasons = mc04IslmcDistParmApplySeasonService.lambdaQuery()
                .eq(Mc04IslmcDistParmApplySeasonDO::getMc04CgtDistParmApplyId, mc04CgtDistParmApplyId)
                .list();
        distParmApply.setSeasons(Mc04IslmcDistParmApplySeasonDOToDistParmApplySeasonConverter.INSTANCE.converterDosToModels(seasons));

    }

}
