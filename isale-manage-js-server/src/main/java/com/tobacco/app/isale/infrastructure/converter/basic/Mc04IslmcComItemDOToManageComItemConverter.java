/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.basic;

import com.tobacco.app.isale.domain.model.basic.ManageComItem;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcComItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/04/22 15:31
 * @description : 数据转化
 */
@Mapper
public interface Mc04IslmcComItemDOToManageComItemConverter
        extends StructureBaseConverter<Mc04IslmcComItemDO, ManageComItem> {
    Mc04IslmcComItemDOToManageComItemConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcComItemDOToManageComItemConverter.class);

}

