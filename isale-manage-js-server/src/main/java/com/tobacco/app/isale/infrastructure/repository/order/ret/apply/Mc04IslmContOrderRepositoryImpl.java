/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.order.ret.apply;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tobacco.app.isale.domain.model.order.ret.apply.Mc04IslmContOrder;
import com.tobacco.app.isale.domain.model.order.ret.apply.Mc04IslmContOrderPage;
import com.tobacco.app.isale.domain.repository.order.ret.apply.IslmContOrderRepository;
import com.tobacco.app.isale.infrastructure.converter.order.ret.apply.Mc04IslmContOrderDOToMc04IslmContOrderConverter;
import com.tobacco.app.isale.infrastructure.converter.order.ret.apply.Mc04IslmContOrderDOToMc04IslmContOrderPageConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContReachWhseDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmContOrderService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContDelivWhseService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContReachWhseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: liuwancheng
 * @Since: 2025-07-25
 */
@Component("ISaleMc04IslmContOrderDORepository")
public class Mc04IslmContOrderRepositoryImpl implements IslmContOrderRepository{

    @Autowired
    private final Mc04IslmContOrderService mc04IslmContOrderService;
    @Autowired
    private final Mc04IslmcContReachWhseService mc04IslmcContReachWhseService;
    @Autowired
    private final Mc04IslmcContDelivWhseService mc04IslmcContDelivWhseService;
    public Mc04IslmContOrderRepositoryImpl(Mc04IslmContOrderService mc04IslmContOrderService, Mc04IslmcContReachWhseService mc04IslmcContReachWhseService, Mc04IslmcContDelivWhseService mc04IslmcContDelivWhseService) {
        this.mc04IslmContOrderService = mc04IslmContOrderService;
        this.mc04IslmcContReachWhseService = mc04IslmcContReachWhseService;
        this.mc04IslmcContDelivWhseService = mc04IslmcContDelivWhseService;
    }


    @Override
    public Page<Mc04IslmContOrderPage> PageContOrder(Mc04IslmContOrderPage contOrderPage) {
        // 构建查询条件
        LambdaQueryWrapper<Mc04IslmContOrderDO> queryWrapper = new LambdaQueryWrapper<>();

        Page<Mc04IslmContOrderDO> mc04IslmContOrderDOPage = new Page<>(contOrderPage.getOffset(), contOrderPage.getLimit());
        // 订单查询条件，已下单合同  状态20-60
        queryWrapper.between(Mc04IslmContOrderDO::getMc04CgtTradeContStatus, "20", "60");

        Page<Mc04IslmContOrderDO> resultPage = mc04IslmContOrderService.page(mc04IslmContOrderDOPage,queryWrapper);

        List<Mc04IslmContOrderDO> records = resultPage.getRecords();
        List<Mc04IslmContOrderPage> mc04IslmContOrderList = Mc04IslmContOrderDOToMc04IslmContOrderPageConverter.INSTANCE.converterDosToModels(records);

        for (Mc04IslmContOrderPage mc04IslmContOrderPage : mc04IslmContOrderList) {
            String md02CgtInStorehouseCode = mc04IslmContOrderPage.getMd02CgtInStorehouseCode();
            Mc04IslmcContReachWhseDO islmcContReachWhseDO = mc04IslmcContReachWhseService.getById(md02CgtInStorehouseCode);

            String md02CgtOutStorehouseCode = mc04IslmContOrderPage.getMd02CgtOutStorehouseCode();
            Mc04IslmcContDelivWhseDO islmcContDelivWhseDO = mc04IslmcContDelivWhseService.getById(md02CgtOutStorehouseCode);

            mc04IslmContOrderPage.setMd02CgtInStorehouseName(islmcContReachWhseDO.getMd02CgtInStorehouseName());
            mc04IslmContOrderPage.setMd02CgtOutStorehouseName(islmcContDelivWhseDO.getMd02CgtOutStorehouseName());
        }


        Page<Mc04IslmContOrderPage> mc04IslmContOrderPagePage = new Page<>();
        mc04IslmContOrderPagePage.setRecords(mc04IslmContOrderList);
        mc04IslmContOrderPagePage.setTotal(resultPage.getTotal());
        mc04IslmContOrderPagePage.setSize(resultPage.getSize());
        mc04IslmContOrderPagePage.setCurrent(resultPage.getCurrent());

        return mc04IslmContOrderPagePage;
    }

    @Override
    public Mc04IslmContOrder getOneByTradeContNo(String tradeContNo) {

        LambdaQueryWrapper<Mc04IslmContOrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmContOrderDO::getMd02CgtTradeContNo, tradeContNo);
        Mc04IslmContOrderDO mc04IslmContOrderDO = mc04IslmContOrderService.getOne(queryWrapper);
        Mc04IslmContOrder mc04IslmContOrder = Mc04IslmContOrderDOToMc04IslmContOrderConverter.INSTANCE.converterDoToModel(mc04IslmContOrderDO);

        String md02CgtInStorehouseCode = mc04IslmContOrder.getMd02CgtInStorehouseCode();
        Mc04IslmcContReachWhseDO islmcContReachWhseDO = mc04IslmcContReachWhseService.getById(md02CgtInStorehouseCode);

        String md02CgtOutStorehouseCode = mc04IslmContOrder.getMd02CgtOutStorehouseCode();
        Mc04IslmcContDelivWhseDO islmcContDelivWhseDO = mc04IslmcContDelivWhseService.getById(md02CgtOutStorehouseCode);

        mc04IslmContOrder.setMd02CgtInStorehouseName(islmcContReachWhseDO.getMd02CgtInStorehouseName());
        mc04IslmContOrder.setMd02CgtOutStorehouseName(islmcContDelivWhseDO.getMd02CgtOutStorehouseName());

        return mc04IslmContOrder;
    }
}