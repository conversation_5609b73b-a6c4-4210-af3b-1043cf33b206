/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.monthplan;


import com.tobacco.app.isale.domain.model.monthplan.Mc04IslmMonthSalePlanLock;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanLockDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> jinfuli
 * @Email : @inspur.com
 * @Create : 2025/05/19 11:28
 * @Description : 类型转化
 */
@Mapper
public interface MonthPlanLockConverter extends StructureBaseConverter<Mc04IslmMonthSalePlanLockDO, Mc04IslmMonthSalePlanLock> {
    MonthPlanLockConverter INSTANCE = Mappers.getMapper(MonthPlanLockConverter.class);
//
//    @Mappings({})
//    @Override
//    Mc04IslmMonthSalePlanLockDO converterModelToDo(Mc04IslmMonthSalePlanLock mc04IslmMonthSalePlanLock);
//    @Mappings({})
//    @Override
//    Mc04IslmMonthSalePlanLock converterDoToModel(Mc04IslmMonthSalePlanLockDO DO);
//    @Mappings({})
//    @Override
//    List<Mc04IslmMonthSalePlanLockDO> converterModelsToDos(List<Mc04IslmMonthSalePlanLock> mc04IslmMonthSalePlanLocks);
//    @Mappings({})
//    @Override
//    List<Mc04IslmMonthSalePlanLock> converterDosToModels(List<Mc04IslmMonthSalePlanLockDO> DOList);
}
