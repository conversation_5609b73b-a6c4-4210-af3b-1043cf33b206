/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.NationAgreementItemDO;
import com.tobacco.app.isale.infrastructure.mapper.NationAgreementItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.NationAgreementItemService;
import org.springframework.stereotype.Service;

/**
 * @Description: 服务实现类
 *
 * @Author: renyonghui
 * @Since: 2025-05-07
 * @Email: <EMAIL>
 * @Create: 2025-05-07
 */
@Service
public class NationAgreementItemServiceImpl extends ServiceImpl<NationAgreementItemMapper, NationAgreementItemDO> implements NationAgreementItemService {

}