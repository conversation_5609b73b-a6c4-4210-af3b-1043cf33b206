/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcContDelivWhseItemTrayDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcContDelivWhseItemTrayMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcContDelivWhseItemTrayService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: wmd
 * @Since: 2025-08-04
 * @Email: <EMAIL>
 * @Create: 2025-08-04
        */
@Service
public class Mc04IslmcContDelivWhseItemTrayServiceImpl extends ServiceImpl<Mc04IslmcContDelivWhseItemTrayMapper, Mc04IslmcContDelivWhseItemTrayDO> implements Mc04IslmcContDelivWhseItemTrayService {

}