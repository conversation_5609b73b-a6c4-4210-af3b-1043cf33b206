/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.domain.model.summary;

import com.alibaba.bizworks.core.specification.Field;
import com.alibaba.bizworks.core.specification.ddd.DomainObject;
import com.inspur.ind.base.BaseFactor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * @Description: 综合日报表
 *
 * @Author: hujiarong
 * @Since: 2025-08-22
 * @Email: <EMAIL>
 * @Create: 2025-08-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@DomainObject(name = "综合日报表", desc = "综合日报表")
    public class Mc04IslmcCgtStockSummary extends BaseFactor {

private static final long serialVersionUID = 1L;
                        /**
                 * 单据编号
                 */

                        @Field(value = "单据编号", name = "单据编号")
        private String zaBillNo;
                 /**
                 * 单据日期
                 */

        @Field(value = "单据日期", name = "单据日期")
        private String zaBillDate;
                                            /**
                 * 所属中烟代码
                 */

        @Field(value = "所属中烟代码", name = "所属中烟代码")
        private String baFactPopCode;
                                            /**
                 * 所属中烟公司代码描述
                 */

        @Field(value = "所属中烟公司代码描述", name = "所属中烟公司代码描述")
        private String baFactPopName;
                                            /**
                 * 生产点
                 */

        @Field(value = "生产点", name = "生产点")
        private String baFactOrgCode;
                                            /**
                 * 生产点描述
                 */

        @Field(value = "生产点描述", name = "生产点描述")
        private String baFactOrgName;
                                            /**
                 * 物料编码
                 */

        @Field(value = "物料编码", name = "物料编码")
        private String acCgtCartonCode;
                                            /**
                 * 物料描述
                 */

        @Field(value = "物料描述", name = "物料描述")
        private String acCgtName;
                                            /**
                 * 一级牌号
                 */

        @Field(value = "一级牌号", name = "一级牌号")
        private String acOneLevelCigCode;
                                            /**
                 * 二级牌号
                 */

        @Field(value = "二级牌号", name = "二级牌号")
        private String acTwoLevelCigCode;
                                            /**
                 * 物料组
                 */

        @Field(value = "物料组", name = "物料组")
        private String mc05MateGroupCode;
                                            /**
                 * 物料组描述
                 */

        @Field(value = "物料组描述", name = "物料组描述")
        private String mc05CigStkMateGroupDesc;
                                            /**
                 * 基本单位，主数据计量单位编码
                 */

        @Field(value = "基本单位，主数据计量单位编码", name = "基本单位，主数据计量单位编码")
        private String zaMeasureCode;
                                            /**
                 * 基本单位描述，主数据计量单位名称
                 */

        @Field(value = "基本单位描述，主数据计量单位名称", name = "基本单位描述，主数据计量单位名称")
        private String zaMeasureName;
                                            /**
                 * 含税单价
                 */

        @Field(value = "含税单价", name = "含税单价")
        private BigDecimal acMateTaxTranPr;
                                            /**
                 * 价类
                 */

        @Field(value = "价类", name = "价类")
        private String acCigPriceClassCode;
                                            /**
                 * 量本利编码
                 */

        @Field(value = "量本利编码", name = "量本利编码")
        private String mc04VcpCode;
                                            /**
                 * 期初在库库存，月初在库库存
                 */

        @Field(value = "期初在库库存，月初在库库存", name = "期初在库库存，月初在库库存")
        private BigDecimal mc04CgtInitStkQty;
                                            /**
                 * 期初冻结库存，月初锁定+冻结库存
                 */

        @Field(value = "期初冻结库存，月初锁定+冻结库存", name = "期初冻结库存，月初锁定+冻结库存")
        private BigDecimal mc04CgtInitStkFreezeQty;
                                            /**
                 * 期初退货库存，月初退货库存
                 */

        @Field(value = "期初退货库存，月初退货库存", name = "期初退货库存，月初退货库存")
        private BigDecimal mc04CgtInitStkReturnQty;
                                            /**
                 * 期初库存（含厂内在途），月初在库库存+厂内在途库存
                 */

        @Field(value = "期初库存（含厂内在途），月初在库库存+厂内在途库存", name = "期初库存（含厂内在途），月初在库库存+厂内在途库存")
        private BigDecimal mc04CgtInitStkInclInplantTransitQty;
                                            /**
                 * 期初库存（不含联营在途），月初在库库存+在途库存-联营在途库存
                 */

        @Field(value = "期初库存（不含联营在途），月初在库库存+在途库存-联营在途库存", name = "期初库存（不含联营在途），月初在库库存+在途库存-联营在途库存")
        private BigDecimal mc04CgtInitStkExclCoopTransitQty;
                                            /**
                 * 期初厂内在途库存，月初厂内在途库存
                 */

        @Field(value = "期初厂内在途库存，月初厂内在途库存", name = "期初厂内在途库存，月初厂内在途库存")
        private BigDecimal mc04CgtInitStkInplantTransitQty;
                                            /**
                 * 期初厂间在途库存，月初厂间在途库存
                 */

        @Field(value = "期初厂间在途库存，月初厂间在途库存", name = "期初厂间在途库存，月初厂间在途库存")
        private BigDecimal mc04CgtInitStkFac2facTransitQty;
                                            /**
                 * 期初联营在途库存，月初联营在途库存
                 */

        @Field(value = "期初联营在途库存，月初联营在途库存", name = "期初联营在途库存，月初联营在途库存")
        private BigDecimal mc04CgtInitStkCoopTransitQty;
                                            /**
                 * 期初提前发货量，月初提前发货数量
                 */

        @Field(value = "期初提前发货量，月初提前发货数量", name = "期初提前发货量，月初提前发货数量")
        private BigDecimal mc04CgtInitStkAdvanceShipQty;
                                            /**
                 * 期初销售未达，月初销售未完成数量
                 */

        @Field(value = "期初销售未达，月初销售未完成数量", name = "期初销售未达，月初销售未完成数量")
        private BigDecimal mc04CgtInitStkSaleUnarrivalQty;
                                            /**
                 * 期初零点行动销售未达，月初零点行动销售未完成数量
                 */

        @Field(value = "期初零点行动销售未达，月初零点行动销售未完成数量", name = "期初零点行动销售未达，月初零点行动销售未完成数量")
        private BigDecimal mc04CgtInitStkZeroUnarrivalQty;
                                            /**
                 * 本日生产入库，查询日期生产入库数量
                 */

        @Field(value = "本日生产入库，查询日期生产入库数量", name = "本日生产入库，查询日期生产入库数量")
        private BigDecimal mc03DayProdWhng;
                                            /**
                 * 累计生产入库，本月累计生产入库数量
                 */

        @Field(value = "累计生产入库，本月累计生产入库数量", name = "累计生产入库，本月累计生产入库数量")
        private BigDecimal mc03AccumProdWhng;
                                            /**
                 * 本日厂内移库入库，查询日期厂内移库入库数量
                 */

        @Field(value = "本日厂内移库入库，查询日期厂内移库入库数量", name = "本日厂内移库入库，查询日期厂内移库入库数量")
        private BigDecimal mc04CigInplantMoveinQty;
                                            /**
                 * 累计厂内移库入库，本月累计厂内移库入库数量
                 */

        @Field(value = "累计厂内移库入库，本月累计厂内移库入库数量", name = "累计厂内移库入库，本月累计厂内移库入库数量")
        private BigDecimal mc04CigInplantMoveinQtyMA;
                                            /**
                 * 本日厂间移库入库，查询日期厂间移库入库数量
                 */

        @Field(value = "本日厂间移库入库，查询日期厂间移库入库数量", name = "本日厂间移库入库，查询日期厂间移库入库数量")
        private BigDecimal mc04CigFac2facMoveinQty;
                                            /**
                 * 累计厂间移库入库，本月累计厂间移库入库数量
                 */

        @Field(value = "累计厂间移库入库，本月累计厂间移库入库数量", name = "累计厂间移库入库，本月累计厂间移库入库数量")
        private BigDecimal mc04CigFac2facMoveinQtyMA;
                                            /**
                 * 本日联营移库入库，查询日期联营移库入库数量
                 */

        @Field(value = "本日联营移库入库，查询日期联营移库入库数量", name = "本日联营移库入库，查询日期联营移库入库数量")
        private BigDecimal mc04CigCoopMoveinQty;
                                            /**
                 * 累计联营移库入库，本月累计联营移库入库数量
                 */

        @Field(value = "累计联营移库入库，本月累计联营移库入库数量", name = "累计联营移库入库，本月累计联营移库入库数量")
        private BigDecimal mc04CigCoopMoveinQtyMA;
                                            /**
                 * 本日省内非武汉开票，查询日期省内非武汉销售订单数
                 */

        @Field(value = "本日省内非武汉开票，查询日期省内非武汉销售订单数", name = "本日省内非武汉开票，查询日期省内非武汉销售订单数")
        private BigDecimal mc04CigInProvNoWhInvQty;
                                            /**
                 * 累计省内非武汉开票，本月累计省内非武汉销售订单数
                 */

        @Field(value = "累计省内非武汉开票，本月累计省内非武汉销售订单数", name = "累计省内非武汉开票，本月累计省内非武汉销售订单数")
        private BigDecimal mc04CigInProvNoWhInvQtyMA;
                                            /**
                 * 本日省外开票，查询日期省外销售订单数
                 */

        @Field(value = "本日省外开票，查询日期省外销售订单数", name = "本日省外开票，查询日期省外销售订单数")
        private BigDecimal mc04CigOutProvInvQty;
                                            /**
                 * 累计省外开票，本月累计省外销售订单数
                 */

        @Field(value = "累计省外开票，本月累计省外销售订单数", name = "累计省外开票，本月累计省外销售订单数")
        private BigDecimal mc04CigOutProvInvQtyMA;
                                            /**
                 * 本日其他开票，查询日期其他订单数（如样品烟领用）
                 */

        @Field(value = "本日其他开票，查询日期其他订单数（如样品烟领用）", name = "本日其他开票，查询日期其他订单数（如样品烟领用）")
        private BigDecimal mc04CigOtherInvQty;
                                            /**
                 * 累计其他开票，本月累计其他订单数（如样品烟领用）
                 */

        @Field(value = "累计其他开票，本月累计其他订单数（如样品烟领用）", name = "累计其他开票，本月累计其他订单数（如样品烟领用）")
        private BigDecimal mc04CigOtherInvQtyMA;
                                            /**
                 * 本日武汉开票，查询日期武汉销售订单数
                 */

        @Field(value = "本日武汉开票，查询日期武汉销售订单数", name = "本日武汉开票，查询日期武汉销售订单数")
        private BigDecimal mc04CigWhInvQty;
                                            /**
                 * 累计武汉开票，本月累计武汉销售订单数
                 */

        @Field(value = "累计武汉开票，本月累计武汉销售订单数", name = "累计武汉开票，本月累计武汉销售订单数")
        private BigDecimal mc04CigWhInvQtyMA;
                                            /**
                 * 本日下单量，本日总订单数
                 */

        @Field(value = "本日下单量，本日总订单数", name = "本日下单量，本日总订单数")
        private BigDecimal mc04CigOrdQty;
                                            /**
                 * 累计下单量，本月累计总订单数
                 */

        @Field(value = "累计下单量，本月累计总订单数", name = "累计下单量，本月累计总订单数")
        private BigDecimal mc04CigOrdQtyMA;
                                            /**
                 * 本日其他出库，查询日期其他出库物料数量
                 */

        @Field(value = "本日其他出库，查询日期其他出库物料数量", name = "本日其他出库，查询日期其他出库物料数量")
        private BigDecimal mz04CgtOthersOutQty;
                                            /**
                 * 累计其他出库，本月累计其他出库物料数量
                 */

        @Field(value = "累计其他出库，本月累计其他出库物料数量", name = "累计其他出库，本月累计其他出库物料数量")
        private BigDecimal mc04CgtOthersOutQtyMA;
                                            /**
                 * 本日销售出库，查询日期销售出库物料数量
                 */

        @Field(value = "本日销售出库，查询日期销售出库物料数量", name = "本日销售出库，查询日期销售出库物料数量")
        private BigDecimal mc05Cgt10thIndSelloutQty;
                                            /**
                 * 累计销售出库，本月累计销售出库物料数量
                 */

        @Field(value = "累计销售出库，本月累计销售出库物料数量", name = "累计销售出库，本月累计销售出库物料数量")
        private BigDecimal mc04Cgt10thIndSelloutQtyMA;
                                            /**
                 * 本日零点行动提货未过账，查询日期提货但未过账物料数量
                 */

        @Field(value = "本日零点行动提货未过账，查询日期提货但未过账物料数量", name = "本日零点行动提货未过账，查询日期提货但未过账物料数量")
        private BigDecimal mc04CgtZeroPnpQty;
                                            /**
                 * 累计零点行动提货未过账，本月累计提货但未过账物料数量
                 */

        @Field(value = "累计零点行动提货未过账，本月累计提货但未过账物料数量", name = "累计零点行动提货未过账，本月累计提货但未过账物料数量")
        private BigDecimal mc04CgtZeroPnpQtyMA;
                                            /**
                 * 期末在库库存，查询日期月末在库库存数量
                 */

        @Field(value = "期末在库库存，查询日期月末在库库存数量", name = "期末在库库存，查询日期月末在库库存数量")
        private BigDecimal mc04CgtEndStkQty;
                                            /**
                 * 期末冻结库存，查询日期月末锁定+冻结库存数量
                 */

        @Field(value = "期末冻结库存，查询日期月末锁定+冻结库存数量", name = "期末冻结库存，查询日期月末锁定+冻结库存数量")
        private BigDecimal mc04CgtEndStkFreezeQty;
                                            /**
                 * 期末退货库存，查询日期月末退货库存数量
                 */

        @Field(value = "期末退货库存，查询日期月末退货库存数量", name = "期末退货库存，查询日期月末退货库存数量")
        private BigDecimal mc04CgtEndStkReturnQty;
                                            /**
                 * 期末库存（含厂内在途），查询日期月末在库库存+厂内在途库存
                 */

        @Field(value = "期末库存（含厂内在途），查询日期月末在库库存+厂内在途库存", name = "期末库存（含厂内在途），查询日期月末在库库存+厂内在途库存")
        private BigDecimal mc04CgtEndStkInclInplantTransitQty;
                                            /**
                 * 期末库存（不含联营在途），查询日期月末在库库存+在途库存-联营在途库存
                 */

        @Field(value = "期末库存（不含联营在途），查询日期月末在库库存+在途库存-联营在途库存", name = "期末库存（不含联营在途），查询日期月末在库库存+在途库存-联营在途库存")
        private BigDecimal mc04CgtEndStkExclCoopTransitQty;
                                            /**
                 * 期末厂内在途库存，查询日期月末厂内在途库存
                 */

        @Field(value = "期末厂内在途库存，查询日期月末厂内在途库存", name = "期末厂内在途库存，查询日期月末厂内在途库存")
        private BigDecimal mc04CgtEndStkInplantTransitQty;
                                            /**
                 * 期末厂间在途库存，查询日期月末厂间在途库存
                 */

        @Field(value = "期末厂间在途库存，查询日期月末厂间在途库存", name = "期末厂间在途库存，查询日期月末厂间在途库存")
        private BigDecimal mc04CgtEndStkFac2facTransitQty;
                                            /**
                 * 期末联营在途库存，查询日期月末联营在途库存
                 */

        @Field(value = "期末联营在途库存，查询日期月末联营在途库存", name = "期末联营在途库存，查询日期月末联营在途库存")
        private BigDecimal mc04CgtEndStkCoopTransitQty;
                                            /**
                 * 期末提前发货量，查询日期月末提前发货数量
                 */

        @Field(value = "期末提前发货量，查询日期月末提前发货数量", name = "期末提前发货量，查询日期月末提前发货数量")
        private BigDecimal mc04CgtEndStkAdvanceShipQty;
                                            /**
                 * 期末销售未达，查询日期月末销售未完成数量
                 */

        @Field(value = "期末销售未达，查询日期月末销售未完成数量", name = "期末销售未达，查询日期月末销售未完成数量")
        private BigDecimal mc04CgtEndStkSaleUnarrivalQty;
                                            /**
                 * 期末零点行动销售未达，查询日期月末零点行动销售未完成数量
                 */

        @Field(value = "期末零点行动销售未达，查询日期月末零点行动销售未完成数量", name = "期末零点行动销售未达，查询日期月末零点行动销售未完成数量")
        private BigDecimal mc04CgtEndStkZeroUnarrivalQty;
                                            /**
                 * 期末在库库存DJ，查询日期（醇化烟）月末在库库存数量
                 */

        @Field(value = "期末在库库存DJ，查询日期（醇化烟）月末在库库存数量", name = "期末在库库存DJ，查询日期（醇化烟）月末在库库存数量")
        private BigDecimal mc04CgtEndStkQtyDj;
                                            /**
                 * 期末厂内在途库存DJ，查询日期（醇化烟）月末厂内在途库存
                 */

        @Field(value = "期末厂内在途库存DJ，查询日期（醇化烟）月末厂内在途库存", name = "期末厂内在途库存DJ，查询日期（醇化烟）月末厂内在途库存")
        private BigDecimal mc04CgtEndStkInplantTransitQtyDj;
                                            /**
                 * 期末销售未达DJ，查询日期（醇化烟）月末销售未完成数量
                 */

        @Field(value = "期末销售未达DJ，查询日期（醇化烟）月末销售未完成数量", name = "期末销售未达DJ，查询日期（醇化烟）月末销售未完成数量")
        private BigDecimal mc04CgtEndStkSaleUnarrivalQtyDj;
                                            /**
                 * 期末零点行动销售未达DJ，查询日期（醇化烟）月末零点行动销售未完成数量
                 */

        @Field(value = "期末零点行动销售未达DJ，查询日期（醇化烟）月末零点行动销售未完成数量", name = "期末零点行动销售未达DJ，查询日期（醇化烟）月末零点行动销售未完成数量")
        private BigDecimal mc04CgtEndStkZeroUnarrivalQtyDj;
                                            /**
                 * 期末提前发货量DJ，查询日期（醇化烟）月末提前发货数量
                 */

        @Field(value = "期末提前发货量DJ，查询日期（醇化烟）月末提前发货数量", name = "期末提前发货量DJ，查询日期（醇化烟）月末提前发货数量")
        private BigDecimal mc04CgtEndStkAdvanceShipQtyDj;
                                            /**
                 * 期末在库库存BHYKYM，查询日期（烟码相贴）月末在库库存数量
                 */

        @Field(value = "期末在库库存BHYKYM，查询日期（烟码相贴）月末在库库存数量", name = "期末在库库存BHYKYM，查询日期（烟码相贴）月末在库库存数量")
        private BigDecimal mc04CgtEndStkQtyYm;
                                            /**
                 * 期末厂内在途库存BHYKYM，查询日期（烟码相贴）月末厂内在途库存
                 */

        @Field(value = "期末厂内在途库存BHYKYM，查询日期（烟码相贴）月末厂内在途库存", name = "期末厂内在途库存BHYKYM，查询日期（烟码相贴）月末厂内在途库存")
        private BigDecimal mc04CgtEndStkInplantTransitQtyYm;
                                            /**
                 * 期末销售未达BHYKYM，查询日期（烟码相贴）月末销售未完成数量
                 */

        @Field(value = "期末销售未达BHYKYM，查询日期（烟码相贴）月末销售未完成数量", name = "期末销售未达BHYKYM，查询日期（烟码相贴）月末销售未完成数量")
        private BigDecimal mc04CgtEndStkSaleUnarrivalQtyYm;
                                            /**
                 * 期末零点行动销售未达BHYKYM，查询日期（烟码相贴）月末零点行动销售未完成数量
                 */

        @Field(value = "期末零点行动销售未达BHYKYM，查询日期（烟码相贴）月末零点行动销售未完成数量", name = "期末零点行动销售未达BHYKYM，查询日期（烟码相贴）月末零点行动销售未完成数量")
        private BigDecimal mc04CgtEndStkZeroUnarrivalQtyYm;
                                            /**
                 * 期末提前发货量BHYKYM，查询日期（烟码相贴）月末提前发货数量
                 */

        @Field(value = "期末提前发货量BHYKYM，查询日期（烟码相贴）月末提前发货数量", name = "期末提前发货量BHYKYM，查询日期（烟码相贴）月末提前发货数量")
        private BigDecimal mc04CgtEndStkAdvanceShipQtyYm;
                                            /**
                 * 期末在库库存BHYKDJ，查询日期（烟码分离）月末在库库存数量
                 */

        @Field(value = "期末在库库存BHYKDJ，查询日期（烟码分离）月末在库库存数量", name = "期末在库库存BHYKDJ，查询日期（烟码分离）月末在库库存数量")
        private BigDecimal mc04CgtEndStkQtyYmfl;
                                            /**
                 * 期末厂内在途库存BHYKDJ，查询日期（烟码分离）月末厂内在途库存
                 */

        @Field(value = "期末厂内在途库存BHYKDJ，查询日期（烟码分离）月末厂内在途库存", name = "期末厂内在途库存BHYKDJ，查询日期（烟码分离）月末厂内在途库存")
        private BigDecimal mc04CgtEndStkInplantTransitQtyYmfl;
                                            /**
                 * 期末销售未达BHYKDJ，查询日期（烟码分离）月末销售未完成数量
                 */

        @Field(value = "期末销售未达BHYKDJ，查询日期（烟码分离）月末销售未完成数量", name = "期末销售未达BHYKDJ，查询日期（烟码分离）月末销售未完成数量")
        private BigDecimal mc04CgtEndStkSaleUnarrivalQtyYmfl;
                                            /**
                 * 期末零点行动销售未达BHYKDJ，查询日期（烟码分离）月末零点行动销售未完成数量
                 */

        @Field(value = "期末零点行动销售未达BHYKDJ，查询日期（烟码分离）月末零点行动销售未完成数量", name = "期末零点行动销售未达BHYKDJ，查询日期（烟码分离）月末零点行动销售未完成数量")
        private BigDecimal mc04CgtEndStkZeroUnarrivalQtyYmfl;
                                            /**
                 * 期末提前发货量BHYKDJ，查询日期（烟码分离）月末提前发货数量
                 */

        @Field(value = "期末提前发货量BHYKDJ，查询日期（烟码分离）月末提前发货数量", name = "期末提前发货量BHYKDJ，查询日期（烟码分离）月末提前发货数量")
        private BigDecimal mc04CgtEndStkAdvanceShipQtyYmfl;
                                                                                                                                    

        }
