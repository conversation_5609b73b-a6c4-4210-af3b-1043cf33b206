/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.order.ret.apply;

import com.tobacco.app.isale.domain.model.order.ret.apply.Mc04IslmcIndReturnApplyItem;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcIndReturnApplyItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: liuwancheng
 * @Email: <EMAIL>
 * @Create: 2025-07-25
 */

@Mapper

public interface Mc04IslmcIndReturnApplyItemDOToMc04IslmcIndReturnApplyItemConverter extends StructureBaseConverter<Mc04IslmcIndReturnApplyItemDO, Mc04IslmcIndReturnApplyItem> {

        Mc04IslmcIndReturnApplyItemDOToMc04IslmcIndReturnApplyItemConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcIndReturnApplyItemDOToMc04IslmcIndReturnApplyItemConverter.class);

}