/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.parm;

import com.tobacco.app.isale.domain.model.order.dist.parm.DistParmApply;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcDistParmApplyDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> liu<PERSON><PERSON>
 * @create_time : 2025/06/05 19:18
 * @description : 配货参数申请数据转化
 */
@Mapper
public interface Mc04IslmcDistParmApplyDOToDistParmApplyConverter extends StructureBaseConverter<Mc04IslmcDistParmApplyDO, DistParmApply> {

    Mc04IslmcDistParmApplyDOToDistParmApplyConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcDistParmApplyDOToDistParmApplyConverter.class);

}
