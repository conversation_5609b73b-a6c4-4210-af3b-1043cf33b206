/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.order.ret.apply;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomException;
import com.tobacco.app.isale.domain.model.order.ret.apply.Mc04IslmcIndReturnApply;
import com.tobacco.app.isale.domain.model.order.ret.apply.Mc04IslmcIndReturnApplyItem;
import com.tobacco.app.isale.domain.repository.order.ret.apply.IslmcIndReturnApplyRepository;
import com.tobacco.app.isale.dto.order.ret.apply.Mc04IslmcIndReturnApplyDTO;
import com.tobacco.app.isale.infrastructure.converter.order.ret.apply.Mc04IslmcIndReturnApplyDOToMc04IslmcIndReturnApplyConverter;
import com.tobacco.app.isale.infrastructure.converter.order.ret.apply.Mc04IslmcIndReturnApplyItemDOToMc04IslmcIndReturnApplyItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcIndReturnApplyDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcIndReturnApplyItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmContOrderMapper;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcIndReturnApplyItemMapper;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcIndReturnApplyMapper;
import com.tobacco.app.isale.infrastructure.repository.order.ret.apply.utils.ApplyUtils;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcIndReturnApplyItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcIndReturnApplyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: liuwancheng
 * @Since: 2025-07-25
 */
@Component("ISaleMc04IslmcIndReturnApplyDORepository")
public class Mc04IslmcIndReturnApplyRepositoryImpl implements IslmcIndReturnApplyRepository{

    @Autowired
    private final Mc04IslmcIndReturnApplyMapper mc04IslmcIndReturnApplyMapper;
    @Autowired
    private final Mc04IslmcIndReturnApplyService mc04IslmcIndReturnApplyService;
    @Autowired
    private final Mc04IslmcIndReturnApplyItemService mc04IslmcIndReturnApplyItemService;
    @Autowired
    private final Mc04IslmcIndReturnApplyItemMapper mc04IslmcIndReturnApplyItemMapper;
    @Autowired
    private final Mc04IslmContOrderMapper mc04IslmContOrderMapper;


    public Mc04IslmcIndReturnApplyRepositoryImpl(Mc04IslmcIndReturnApplyMapper mc04IslmcIndReturnApplyMapper,
                                                 Mc04IslmcIndReturnApplyItemMapper mc04IslmcIndReturnApplyItemMapper,
                                                 Mc04IslmContOrderMapper mc04IslmContOrderMapper,
                                                 Mc04IslmcIndReturnApplyService mc04IslmcIndReturnApplyService,
                                                 Mc04IslmcIndReturnApplyItemService mc04IslmcIndReturnApplyItemService
                                                 ) {
        this.mc04IslmcIndReturnApplyMapper = mc04IslmcIndReturnApplyMapper;
        this.mc04IslmcIndReturnApplyItemMapper = mc04IslmcIndReturnApplyItemMapper;
        this.mc04IslmContOrderMapper = mc04IslmContOrderMapper;
        this.mc04IslmcIndReturnApplyService = mc04IslmcIndReturnApplyService;
        this.mc04IslmcIndReturnApplyItemService = mc04IslmcIndReturnApplyItemService;
    }


    @Override
    public Page<Mc04IslmcIndReturnApplyDTO> page(int offset, int limit, String sort, String order, Mc04IslmcIndReturnApply domain) {
        // 构建查询条件
        LambdaQueryWrapper<Mc04IslmcIndReturnApplyDO> queryWrapper = new LambdaQueryWrapper<>();

        if (domain.getMd02CgtTradeContNo() != null) {
            queryWrapper.like(Mc04IslmcIndReturnApplyDO::getMd02CgtTradeContNo, domain.getMd02CgtTradeContNo());
        }

        if (domain.getBaComOrgCode() != null) {
            queryWrapper.in(Mc04IslmcIndReturnApplyDO::getBaComOrgCode, domain.getBaComOrgCode().split(","));
        }

        if (domain.getCreateTime() != null) {
            String[] timeRange = domain.getCreateTime().split(",");
            if (timeRange.length == 2) {
                queryWrapper.between(Mc04IslmcIndReturnApplyDO::getCreateTime, timeRange[0], timeRange[1]);
            }
        }

        // 执行分页查询
        Page<Mc04IslmcIndReturnApplyDO> doPage = new Page<>(offset / limit + 1, limit);
        Page<Mc04IslmcIndReturnApplyDO> applyPage = mc04IslmcIndReturnApplyMapper.selectPage(doPage, queryWrapper);

        List<Mc04IslmcIndReturnApplyDO> applyList = applyPage.getRecords();
        List<Mc04IslmcIndReturnApplyDTO> mc04IslmcIndReturnApplyDTOS = new ArrayList<>();
        for (Mc04IslmcIndReturnApplyDO mc04IslmcIndReturnApplyDO : applyList) {
            String md02CgtTradeContNo = mc04IslmcIndReturnApplyDO.getMd02CgtTradeContNo();

            LambdaQueryWrapper<Mc04IslmContOrderDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(Mc04IslmContOrderDO::getMd02CgtTradeContNo, md02CgtTradeContNo);
            List<Mc04IslmContOrderDO> mc04IslmContOrderDOS = mc04IslmContOrderMapper.selectList(lambdaQueryWrapper);

            Mc04IslmcIndReturnApplyDTO mc04IslmcIndReturnApplyDTO = new Mc04IslmcIndReturnApplyDTO();
            // 将DO的值赋值到DTO
            BeanUtil.copyProperties(mc04IslmcIndReturnApplyDO, mc04IslmcIndReturnApplyDTO);
            mc04IslmcIndReturnApplyDTO.setMd02CgtInStorehouseCode(mc04IslmContOrderDOS.get(0).getMd02CgtInStorehouseCode());
            mc04IslmcIndReturnApplyDTOS.add(mc04IslmcIndReturnApplyDTO);
        }
        // 转换为领域模型分页
        Page<Mc04IslmcIndReturnApplyDTO> resultPage = new Page<>();

        resultPage.setSize(applyPage.getSize());
        resultPage.setTotal(applyPage.getTotal());
        resultPage.setPages(applyPage.getPages());
        resultPage.setCurrent(applyPage.getCurrent());
        resultPage.setRecords(mc04IslmcIndReturnApplyDTOS);

        return resultPage;
    }



    @Override
    public Boolean removeReturnApplyById(Mc04IslmcIndReturnApply returnApply) {
        Assert.isTrue(returnApply.getMc04IndReturnApplyId() != null, () -> new CustomException("退库申请单ID不能为空"));
        mc04IslmcIndReturnApplyService.removeById(returnApply);
        // 删除申请单Items
        LambdaQueryWrapper<Mc04IslmcIndReturnApplyItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmcIndReturnApplyItemDO::getMc04IndReturnApplyId, returnApply.getMc04IndReturnApplyId());
        mc04IslmcIndReturnApplyItemService.remove(queryWrapper);
        return true;

    }

    @Override
    public Boolean addReturnApply(Mc04IslmcIndReturnApply mc04IslmcIndReturnApply, List<Mc04IslmcIndReturnApplyItem> mc04IslmcIndReturnApplyItems) {
        // 保存主表
        Mc04IslmcIndReturnApplyDO applyDO = Mc04IslmcIndReturnApplyDOToMc04IslmcIndReturnApplyConverter.INSTANCE.converterModelToDo(mc04IslmcIndReturnApply);
        // todo 需要从合同表的mc04_cgt_trade_cont_type做对应 10对应0 20对应1
        applyDO.setMd02TobaProdTradeReturnTypeCpde("0");
        ApplyUtils.processBaseFactorProperties(applyDO);
        boolean result = mc04IslmcIndReturnApplyService.save(applyDO);

        Assert.isTrue(result ,()-> new CustomException("保存退库申请单明细失败"));


        // 保存明细表
        for (Mc04IslmcIndReturnApplyItem item : mc04IslmcIndReturnApplyItems) {
            item.setMc04IndReturnApplyId(applyDO.getMc04IndReturnApplyId());
            Mc04IslmcIndReturnApplyItemDO itemDO = Mc04IslmcIndReturnApplyItemDOToMc04IslmcIndReturnApplyItemConverter.INSTANCE.converterModelToDo(item);
            ApplyUtils.processBaseFactorProperties(itemDO);
            result = mc04IslmcIndReturnApplyItemService.save(itemDO);
            if (!result) {
                throw new RuntimeException("保存退库申请单明细失败");
            }
        }

        return true;
    }

    @Override
    public List<Mc04IslmcIndReturnApply> list(String comIds, String startDate, String endDate, String tradeContNo) {
        LambdaQueryWrapper<Mc04IslmcIndReturnApplyDO> queryWrapper = new LambdaQueryWrapper<>();

        if (!StringUtils.isEmpty(tradeContNo)) {
            queryWrapper.like(Mc04IslmcIndReturnApplyDO::getMd02CgtTradeContNo, tradeContNo);
        }

        if (!StringUtils.isEmpty(comIds)) {
            queryWrapper.in(Mc04IslmcIndReturnApplyDO::getBaComOrgCode, Arrays.asList(comIds.split(",")));
        }

        if (!StringUtils.isEmpty(startDate) && !StringUtils.isEmpty(endDate)) {
            queryWrapper.between(Mc04IslmcIndReturnApplyDO::getCreateTime, startDate, endDate);
        }

        List<Mc04IslmcIndReturnApplyDO> applyList = mc04IslmcIndReturnApplyMapper.selectList(queryWrapper);

        return Mc04IslmcIndReturnApplyDOToMc04IslmcIndReturnApplyConverter.INSTANCE.converterDosToModels(applyList);
    }

    @Override
    public Boolean updateApplyStatus(Mc04IslmcIndReturnApply mc04IslmcIndReturnApply) {
        return mc04IslmcIndReturnApplyMapper.updateById(Mc04IslmcIndReturnApplyDOToMc04IslmcIndReturnApplyConverter.INSTANCE.converterModelToDo(mc04IslmcIndReturnApply)) == 1;
    }

    @Override
    public Boolean updateApply(Mc04IslmcIndReturnApply mc04IslmcIndReturnApply, List<Mc04IslmcIndReturnApplyItem> mc04IslmcIndReturnApplyItems) {
        // 更新主表
        Mc04IslmcIndReturnApplyDO applyDO = Mc04IslmcIndReturnApplyDOToMc04IslmcIndReturnApplyConverter.INSTANCE.converterModelToDo(mc04IslmcIndReturnApply);
        ApplyUtils.processBaseFactorProperties(applyDO);
        int result = mc04IslmcIndReturnApplyMapper.updateById(applyDO);

        if (result <= 0) {
            return false;
        }

        // 处理明细表
        for (Mc04IslmcIndReturnApplyItem item : mc04IslmcIndReturnApplyItems) {
            item.setMc04IndReturnApplyId(applyDO.getMc04IndReturnApplyId());
            Mc04IslmcIndReturnApplyItemDO itemDO = Mc04IslmcIndReturnApplyItemDOToMc04IslmcIndReturnApplyItemConverter.INSTANCE.converterModelToDo(item);
            ApplyUtils.processBaseFactorProperties(itemDO);

            if (itemDO.getMc04IndReturnApplyItemId() != null) {
                // 已有ID则更新
                result = mc04IslmcIndReturnApplyItemMapper.updateById(itemDO);
            } else {
                // 无ID则新增
                result = mc04IslmcIndReturnApplyItemMapper.insert(itemDO);
            }

            if (result <= 0) {
                throw new RuntimeException("更新退库申请单明细失败");
            }
        }

        return true;
    }
}