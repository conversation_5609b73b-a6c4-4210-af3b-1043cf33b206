package com.tobacco.app.isale.infrastructure.repository.supply.psc.allocdemand.manage;

import cn.hutool.core.lang.Assert;
import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.base.CustomPage;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.constants.DistConstants;
import com.tobacco.app.isale.domain.enums.supply.psc.allocdemand.IsaleSupplyDemandAjStatusEnum;
import com.tobacco.app.isale.domain.model.supply.psc.allocdemand.TranDemandAdjDomain;
import com.tobacco.app.isale.domain.model.supply.psc.allocdemand.adjust.AddAdjustDomain;
import com.tobacco.app.isale.domain.model.supply.psc.allocdemand.adjust.AddAdjustItemDomain;
import com.tobacco.app.isale.domain.model.supply.psc.allocdemand.adjust.AdjustItemDomain;
import com.tobacco.app.isale.domain.model.supply.psc.allocdemand.adjust.AllocdemandAdjustDomain;
import com.tobacco.app.isale.domain.model.supply.psc.allocdemand.adjustaudit.AllocdemandAdjustauditDomain;
import com.tobacco.app.isale.domain.repository.supply.psc.allocdemand.manage.TranDemandAdjRepository;
import com.tobacco.app.isale.infrastructure.converter.supply.psc.allocdemand.TranDemandAdjDomainToDoConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtTranDemandAdjDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtTranDemandAdjItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmCgtTranDemandAdjItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmCgtTranDemandAdjService;
import com.tobacco.app.isale.infrastructure.tunnel.database.supply.psc.allocdemand.adjust.Mc04IslmCgtTranDemandAdjMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.tobacco.app.isale.domain.enums.supply.psc.allocdemand.IsaleSupplyDemandAjStatusEnum.DEMAND_AJ_STATUS_20;

/**
 * <AUTHOR>
 */
@Component("ISaleMc04IslmCgtTranDemandAdjRepository")
public class Mc04IslmCgtTranDemandAdjRepositoryImpl implements TranDemandAdjRepository {

    private Mc04IslmCgtTranDemandAdjService mc04IslmCgtTranDemandAdjService;

    private Mc04IslmCgtTranDemandAdjItemService mc04IslmCgtTranDemandAdjItemService;

    @Autowired
    private Mc04IslmCgtTranDemandAdjMapper mc04IslmCgtTranDemandAdjMapper;

    @Autowired
    public void setMc04IslmCgtTranDemandAdjService(Mc04IslmCgtTranDemandAdjService mc04IslmCgtTranDemandAdjService) {
        this.mc04IslmCgtTranDemandAdjService = mc04IslmCgtTranDemandAdjService;
    }

    @Autowired
    public void setMc04IslmCgtTranDemandAdjItemService(Mc04IslmCgtTranDemandAdjItemService mc04IslmCgtTranDemandAdjItemService) {
        this.mc04IslmCgtTranDemandAdjItemService = mc04IslmCgtTranDemandAdjItemService;
    }

    /**
     * 根据查询条件查询调拨单
     * @param allocdemandAdjustDomain 调拨单参数
     * @return Page<AllocdemandAdjustDomain>
     */
    @Override
    public Page<AllocdemandAdjustDomain> queryAllocdemandAdjustPage(AllocdemandAdjustDomain allocdemandAdjustDomain) {
        CustomPage<AllocdemandAdjustDomain> page = new CustomPage<>(allocdemandAdjustDomain.getOffset(), allocdemandAdjustDomain.getLimit(), allocdemandAdjustDomain.getSort(), "");
        return mc04IslmCgtTranDemandAdjMapper.queryAllocdemandAdjustPage(page, allocdemandAdjustDomain.getMa02PlanMonth(), allocdemandAdjustDomain.getMc03CgtProdPlType(),allocdemandAdjustDomain.getMc04CgtTranDemandAdjDate(),allocdemandAdjustDomain.getMc04CgtTranDemandAdjStatus());
    }

    /**
     * 根据查询条件查询调拨单
     * @param allocdemandAdjustauditDomain 调拨单参数
     * @return Page<AllocdemandAdjustDomain>
     */
    @Override
    public Page<AllocdemandAdjustDomain> queryAllocdemandAdjustauditPage(AllocdemandAdjustauditDomain allocdemandAdjustauditDomain) {
        CustomPage<AllocdemandAdjustDomain> page = new CustomPage<>(allocdemandAdjustauditDomain.getOffset(), allocdemandAdjustauditDomain.getLimit(), allocdemandAdjustauditDomain.getSort(), "");
        return mc04IslmCgtTranDemandAdjMapper.queryAllocdemandAdjustPage(page, allocdemandAdjustauditDomain.getMa02PlanMonth(), allocdemandAdjustauditDomain.getMc03CgtProdPlType(),allocdemandAdjustauditDomain.getMc04CgtTranDemandAdjDate(),allocdemandAdjustauditDomain.getMc04CgtTranDemandAdjStatus());
    }

    /**
     * 提交调拨单
     * @param addAdjustDomain 调拨单参数
     * @param opeType 操作类型
     * @return Boolean
     */
    @Override
    public Boolean submitAdjust(AddAdjustDomain addAdjustDomain,String opeType) {
        List<AddAdjustItemDomain> adjustItemList = addAdjustDomain.getAdjustItemList();

        adjustItemList = adjustItemList.stream().filter(item -> item.getMa02CgtPlAdjusQty() != null && item.getAcTwoLevelCigCode()!=null).collect(Collectors.toList());

        //过滤掉当前为null的值 检验调整后量不能为负值
        boolean hasNegativeValue = adjustItemList.stream()
                .filter(item -> item.getMa02CgtPlAdjustedQty() != null && item.getMa02CgtPlAdjusQty() != null)
                .anyMatch(item -> item.getMa02CgtPlAdjustedQty().add(item.getMa02CgtPlAdjusQty()).compareTo(BigDecimal.ZERO) < 0);

        Assert.isTrue(!hasNegativeValue, () -> new CustomException("提交失败，调整后数据不能为负数！请检查后重新提交！！"));

        List<Mc04IslmCgtTranDemandAdjItemDO> list = new ArrayList<>();
        for (AddAdjustItemDomain item : adjustItemList) {
            Mc04IslmCgtTranDemandAdjItemDO mc04IslmCgtTranDemandAdjItemDO = new Mc04IslmCgtTranDemandAdjItemDO();
            BeanUtils.copyProperties(item, mc04IslmCgtTranDemandAdjItemDO);
            mc04IslmCgtTranDemandAdjItemDO.setMc04MonthSalePlanAdjId(addAdjustDomain.getMc04MonthSalePlanAdjId());
            mc04IslmCgtTranDemandAdjItemDO.setMc04CgtAllotPlanPdConfirmAdjQty(item.getMa02CgtPlResolOrigplanQty().add(item.getMa02CgtPlAdjusQty()));
            list.add(mc04IslmCgtTranDemandAdjItemDO);
        }
        mc04IslmCgtTranDemandAdjItemService.saveOrUpdateBatch(list);
        if(!StringUtils.equals(opeType,"save")){
            mc04IslmCgtTranDemandAdjService.lambdaUpdate()
                    .eq(Mc04IslmCgtTranDemandAdjDO::getMc04MonthSalePlanAdjId, addAdjustDomain.getMc04MonthSalePlanAdjId())
                    .set(Mc04IslmCgtTranDemandAdjDO::getMc04CgtTranDemandAdjStatus, DEMAND_AJ_STATUS_20.getStatus())
                    .update();
        }
        return true;
    }


    /**
     * 保存调拨单
     * @param tranDemandAdjDomain 调拨单参数
     * @return Boolean
     */
    @Override
    public Boolean saveAdjustData(TranDemandAdjDomain tranDemandAdjDomain) {
        Mc04IslmCgtTranDemandAdjDO tranDemandDO = TranDemandAdjDomainToDoConverter.INSTANCE.converterModelToDo(tranDemandAdjDomain);
        return mc04IslmCgtTranDemandAdjService.save(tranDemandDO);
    }

    /**
     * 批量更新状态
     * @param adjIdList 调拨列表
     * @param preState 前置条件
     * @param operationStatus 变更状态
     * @return Boolean
     */
    @Override
    public Boolean updateStatusByAdjId(List<String> adjIdList, IsaleSupplyDemandAjStatusEnum preState, IsaleSupplyDemandAjStatusEnum operationStatus) {
        Assert.notEmpty(adjIdList, () -> new CustomException("提交不能为空!"));

        List<Mc04IslmCgtTranDemandAdjDO> demandAdjDOList = mc04IslmCgtTranDemandAdjService.lambdaQuery()
                .in(Mc04IslmCgtTranDemandAdjDO::getMc04MonthSalePlanAdjId, adjIdList)
                .list();

        Assert.notEmpty(demandAdjDOList, () -> new CustomException("获取到的调拨单为空!"));

        boolean hasInvalidStatus = demandAdjDOList.stream()
                .anyMatch(demandAdjDO -> !StringUtils.equals(preState.getStatus(), demandAdjDO.getMc04CgtTranDemandAdjStatus()));
        Assert.isTrue(!hasInvalidStatus, () -> new CustomException("提交失败，存在非"+preState.getDesc()+"状态调拨，请检查后重新提交！"));
        demandAdjDOList.forEach(item -> item.setMc04CgtTranDemandAdjStatus(operationStatus.getStatus()));
        return mc04IslmCgtTranDemandAdjService.saveOrUpdateBatch(demandAdjDOList);
    }

    /**
     * 根据ID获取调拨单
     * @param mc04MonthSalePlanAdjId 调拨单ID
     * @return TranDemandAdjDomain
     */
    @Override
    public TranDemandAdjDomain getTranDemandAdjById(String mc04MonthSalePlanAdjId) {
        if(mc04MonthSalePlanAdjId==null){
            return null;
        }
        Mc04IslmCgtTranDemandAdjDO mc04IslmCgtTranDemandAdjDO = mc04IslmCgtTranDemandAdjService.lambdaQuery().eq(Mc04IslmCgtTranDemandAdjDO::getMc04MonthSalePlanAdjId, mc04MonthSalePlanAdjId).one();
        return TranDemandAdjDomainToDoConverter.INSTANCE.converterDoToModel(mc04IslmCgtTranDemandAdjDO);
    }
}
