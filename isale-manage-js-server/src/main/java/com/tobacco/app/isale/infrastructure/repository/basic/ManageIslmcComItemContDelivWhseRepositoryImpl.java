/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.basic;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.model.basic.ManageIslmcComItemContDelivWhse;
import com.tobacco.app.isale.domain.repository.basic.ManageIslmcComItemContDelivWhseRepository;
import com.tobacco.app.isale.infrastructure.converter.basic.Mc04IslmcComItemContDelivWhseDOToManageIslmcComItemContDelivWhseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcComItemContDelivWhseDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcComItemContDelivWhseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR> zhangshch01
 * @email : <EMAIL>
 * @create_time : 2025/07/31 09:47
 * @description : 销售管理-基础数据-公司卷烟默认发货仓库 infrastructureRepository
 */
@Slf4j
@Component("ISaleManageIslmcComItemRepositoryImplDORepository")
public class ManageIslmcComItemContDelivWhseRepositoryImpl implements ManageIslmcComItemContDelivWhseRepository {

    @Autowired
    private Mc04IslmcComItemContDelivWhseService mc04IslmcComItemContDelivWhseService;


    @Override
    public List<ManageIslmcComItemContDelivWhse> list(String acTwoLevelCigCode, String md02CgtOutStorehouseCode) {
        QueryWrapper<Mc04IslmcComItemContDelivWhseDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(StrUtil.isNotBlank(acTwoLevelCigCode), Mc04IslmcComItemContDelivWhseDO::getAcTwoLevelCigCode, acTwoLevelCigCode)
                .eq(StrUtil.isNotBlank(md02CgtOutStorehouseCode), Mc04IslmcComItemContDelivWhseDO::getMd02CgtOutStorehouseCode, md02CgtOutStorehouseCode)
                .eq(Mc04IslmcComItemContDelivWhseDO::getIcomCode, IcomUtils.getIcomCode());
        List<Mc04IslmcComItemContDelivWhseDO> list = mc04IslmcComItemContDelivWhseService.list(wrapper);
        return Mc04IslmcComItemContDelivWhseDOToManageIslmcComItemContDelivWhseConverter.INSTANCE.converterDosToModels(list);
    }

    @Override
    public Boolean create(List<ManageIslmcComItemContDelivWhse> islmcComItemContDelivWhses) {
        List<Mc04IslmcComItemContDelivWhseDO> mc04IslmcComItemContDelivWhseDOList = Mc04IslmcComItemContDelivWhseDOToManageIslmcComItemContDelivWhseConverter.INSTANCE
                .converterModelsToDos(islmcComItemContDelivWhses);
        return mc04IslmcComItemContDelivWhseService.saveBatch(mc04IslmcComItemContDelivWhseDOList);
    }

    @Override
    public Boolean delete(List<String> baComOrgCodes, String acTwoLevelCigCode, String md02CgtOutStorehouseCode, String acCgtCartonCode) {
        QueryWrapper<Mc04IslmcComItemContDelivWhseDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(StrUtil.isNotBlank(acTwoLevelCigCode), Mc04IslmcComItemContDelivWhseDO::getAcTwoLevelCigCode, acTwoLevelCigCode)
                .eq(StrUtil.isNotBlank(md02CgtOutStorehouseCode), Mc04IslmcComItemContDelivWhseDO::getMd02CgtOutStorehouseCode, md02CgtOutStorehouseCode)
                .eq(StrUtil.isNotBlank(acCgtCartonCode), Mc04IslmcComItemContDelivWhseDO::getAcCgtCartonCode, acCgtCartonCode)
                .eq(Mc04IslmcComItemContDelivWhseDO::getIcomCode, IcomUtils.getIcomCode())
                .in(baComOrgCodes != null && !baComOrgCodes.isEmpty(), Mc04IslmcComItemContDelivWhseDO::getBaComOrgCode, baComOrgCodes);
        return mc04IslmcComItemContDelivWhseService.remove(wrapper);
    }


    @Override
    public List<ManageIslmcComItemContDelivWhse> listByTwoLevelCodesAndBaComOrgCode(List<String> acTwoLevelCigCodes, String baComOrgCode,String icomCode) {
        QueryWrapper<Mc04IslmcComItemContDelivWhseDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .in(CollectionUtil.isNotEmpty(acTwoLevelCigCodes), Mc04IslmcComItemContDelivWhseDO::getAcTwoLevelCigCode, acTwoLevelCigCodes)
                .eq(StrUtil.isNotBlank(baComOrgCode), Mc04IslmcComItemContDelivWhseDO::getBaComOrgCode, baComOrgCode)
                .eq(Mc04IslmcComItemContDelivWhseDO::getIcomCode, icomCode);
        List<Mc04IslmcComItemContDelivWhseDO> list = mc04IslmcComItemContDelivWhseService.list(wrapper);
        return Mc04IslmcComItemContDelivWhseDOToManageIslmcComItemContDelivWhseConverter.INSTANCE.converterDosToModels(list);
    }


}