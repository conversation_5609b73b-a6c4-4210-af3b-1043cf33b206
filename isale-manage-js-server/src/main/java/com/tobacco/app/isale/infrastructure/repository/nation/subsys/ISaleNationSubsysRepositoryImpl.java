/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.nation.subsys;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.icom.IcomUtils;
import com.inspur.ind.util.DateUtils;
import com.inspur.x1.ac.rule.utils.AcRuleUtil;
import com.tobacco.app.isale.domain.constants.DistConstants;
import com.tobacco.app.isale.domain.enums.basic.InterfaceLogBusiTypeEnum;
import com.tobacco.app.isale.domain.enums.basic.InterfaceLogChannelEnum;
import com.tobacco.app.isale.domain.enums.order.dist.order.DistOrderStatusEnum;
import com.tobacco.app.isale.domain.model.cont.order.UploadCont;
import com.tobacco.app.isale.domain.model.nation.subsys.NationSubsysModel;
import com.tobacco.app.isale.domain.repository.nation.subsys.ISaleNationSubsysRepository;
import com.tobacco.app.isale.domain.repository.nation.subsys.ISaleThreePartyRepository;
import com.tobacco.app.isale.infrastructure.converter.cont.order.Mc04IslmcContOrderItemReqToMc04IslmContOrderItemDoConverter;
import com.tobacco.app.isale.infrastructure.converter.cont.order.Mc04IslmcContOrderReqToMc04IslmContOrderDoConverter;
import com.tobacco.app.isale.infrastructure.converter.dist.order.Mc04IslmCgtDistOrderDOToMc04IslmcCgtDistOrderREQConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtDistOrderDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmCgtDistOrderService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmContOrderItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmContOrderService;
import com.tobacco.app.isale.infrastructure.tunnel.database.nation.*;
import com.tobacco.app.isale.third.service.api.inter.NationSubsysApi;
import com.tobacco.app.isale.third.service.api.inter.NationSubsysConstants;
import com.tobacco.app.isale.third.service.api.inter.NationSubsysPullREQ;
import com.tobacco.app.isale.third.service.api.inter.NationSubsysServiceEnum;
import com.tobacco.app.isale.tools.utils.ISaleDateUtil;
import com.tobacco.app.isale.tools.utils.InterfaceLogUtil;
import com.tobacco.app.isale.tools.utils.SaleResponseUtil;
import com.tobacco.app.isalecenter.client.api.cont.order.ContOrderApi;
import com.tobacco.app.isalecenter.client.api.order.distOrder.CgtDistOrderAPI;
import com.tobacco.app.isalecenter.client.api.order.distParm.DistParmAPI;
import com.tobacco.app.isalecenter.client.dto.cont.order.Mc04IslmcContOrderDTO;
import com.tobacco.app.isalecenter.client.dto.order.distOrder.Mc04IslmcCgtDistOrderDTO;
import com.tobacco.app.isalecenter.client.dto.order.distParm.Mc04IslmcDistParmDTO;
import com.tobacco.app.isalecenter.client.req.cont.order.Mc04IslmcContOrderItemREQ;
import com.tobacco.app.isalecenter.client.req.cont.order.Mc04IslmcContOrderREQ;
import com.tobacco.app.isalecenter.client.req.distOrder.Mc04IslmcCgtDistOrderREQ;
import com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmComREQ;
import com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmItemREQ;
import com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmREQ;
import com.tobacco.app.isalecenter.client.req.distParm.Mc04IslmcDistParmSeasonREQ;
import com.tobacco.app.isalecenter.common.constants.SaleCenterConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/05/12 10:30
 * @description : 行业子系统数据仓库实现类
 */
@Component("ISaleNationSubsysRepositoryImpl")
@Slf4j
public class ISaleNationSubsysRepositoryImpl implements ISaleNationSubsysRepository {
    @Resource
    private NationSubsysApi nationSubsysApi;
    @Resource
    private Mc04IslmContOrderService mc04IslmContOrderService;
    @Resource
    private Mc04IslmContOrderItemService mc04IslmContOrderItemService;
    @Resource
    private ContOrderApi contOrderApi;

    @Resource
    private DistParmAPI distParmApi;
    @Resource
    private CgtDistOrderAPI cgtDistOrderApi;

    @Resource
    private Mc04IslmCgtDistOrderService mc04IslmCgtDistOrderService;
    @Resource
    private BasicDownloadMapper basicDownloadMapper;
    @Resource
    private DistDownloadMapper distDownloadMapper;
    @Resource
    private ContractDownloadMapper contractDownloadMapper;
    @Resource
    private AgreeDownloadMapper agreeDownloadMapper;
    @Resource
    private ISaleThreePartyRepository iSaleThreePartyRepository;

    @Value("${simulate.nationSystem.enabled:0}")
    private String simulateNationSystemEnabled;

    /**
     * 下载交易目录
     *
     * @param days      天数
     * @param beginDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public List<Map<String, Object>> downloadNationProductControl(int days, String beginDate, String endDate) {
        NationSubsysPullREQ pullReq = processAgreeReq(days, beginDate, endDate, null, null);
        return batchPullDataByDate(pullReq, NationSubsysServiceEnum.APITRADECGTQRY);
    }

    /**
     * 下载交易目录（含价格）
     *
     * @param year 年份
     */
    @Override
    public List<Map<String, Object>> downloadNationProductControlPrice(String year) {
        NationSubsysPullREQ pullReq = new NationSubsysPullREQ();
        //获取参数  默认取今年
        if (StrUtil.isBlank(year)) {
            year = DateUtils.getCurMonth().substring(0, 4);
        }
        pullReq.setYear(year);
        return pullData(pullReq, NationSubsysServiceEnum.APITRADECGTWITHPRICEQRY);
    }

    /**
     * 下载产品目录
     *
     * @param days      天数
     * @param beginDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public List<Map<String, Object>> downloadNationProduct(int days, String beginDate, String endDate) {
        NationSubsysPullREQ pullReq = processAgreeReq(days, beginDate, endDate, null, null);
        return batchPullDataByDate(pullReq, NationSubsysServiceEnum.TRADEPRODUCT);
    }

    /**
     * 下载会员
     *
     * @param days      天数
     * @param beginDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public List<Map<String, Object>> downloadNationMember(int days, String beginDate, String endDate) {
        NationSubsysPullREQ pullReq = processAgreeReq(days, beginDate, endDate, null, null);
        return batchPullDataByDate(pullReq, NationSubsysServiceEnum.MEMBERQRY);
    }

    /**
     * 下载会员代表
     *
     * @param days      天数
     * @param beginDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public List<Map<String, Object>> downloadNationMemberDeployee(int days, String beginDate, String endDate) {
        NationSubsysPullREQ pullReq = processAgreeReq(days, beginDate, endDate, null, null);
        return batchPullDataByDate(pullReq, NationSubsysServiceEnum.MEMBERDEPUTYQRY);
    }

    /**
     * 下载仓库
     *
     * @param days      天数
     * @param beginDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public List<Map<String, Object>> downloadNationWhse(int days, String beginDate, String endDate) {
        NationSubsysPullREQ pullReq = processAgreeReq(days, beginDate, endDate, null, null);
        return batchPullDataByDate(pullReq, NationSubsysServiceEnum.MEMBERWAREHOUSEQRY);
    }

    /**
     * 下载税号
     *
     * @param days      天数
     * @param beginDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public List<Map<String, Object>> downloadNationTax(int days, String beginDate, String endDate) {
        NationSubsysPullREQ pullReq = processAgreeReq(days, beginDate, endDate, null, null);
        return batchPullDataByDate(pullReq, NationSubsysServiceEnum.MEMBERTAXNUMQRY);
    }

    /**
     * 下载银行账号
     *
     * @param days      天数
     * @param beginDate 开始日期
     * @param endDate   结束日期
     */
    @Override
    public List<Map<String, Object>> downloadNationBank(int days, String beginDate, String endDate) {
        NationSubsysPullREQ pullReq = processAgreeReq(days, beginDate, endDate, null, null);
        return batchPullDataByDate(pullReq, NationSubsysServiceEnum.MEMBERACCOUNTQRY);
    }

    /**
     * 下载核定量
     */
    @Override
    public List<Map<String, Object>> downloadNationRatifyQty() {
        return pullData(new NationSubsysPullREQ(), NationSubsysServiceEnum.QTYQRY);
    }

    /**
     * @param days      天数
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @param year      年份
     * @param icomCode  工业编码
     * @return List<String>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-12 15:43:02
     * @description : 协议导回
     */
    @Override
    public List<Map<String, Object>> downAgreement(int days, String beginDate, String endDate, String year, String icomCode) {
        NationSubsysPullREQ pullReq = processAgreeReq(days, beginDate, endDate, icomCode, year);
        return batchPullDataByDate(pullReq, NationSubsysServiceEnum.PROTOCOLQRY);
    }

    /**
     * @param datas 数据列表
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-12 15:43:02
     * @description : 处理数据
     */
    @Override
    public Boolean processAgreementData(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            // TODO 调用销售中心接口
            agreeDownloadMapper.deleteIsmAgreement(datas);
            agreeDownloadMapper.insertIsmAgreementFromNation(datas);
            agreeDownloadMapper.deleteIsmAgreementItem(datas);
            agreeDownloadMapper.insertIsmAgreementItemFromNation(datas);
            agreeDownloadMapper.updateIsmAgreementItemPriceFromNation(datas);
        }
        return true;
    }


    /**
     * @param days      天数
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @param year      年份
     * @param icomCode  工业编码
     * @return List<String>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-12 15:43:02
     * @description : 协议调整导回
     */
    @Override
    public List<Map<String, Object>> downAgreementAdjust(int days, String beginDate, String endDate, String year, String icomCode) {
        NationSubsysPullREQ pullReq = processAgreeReq(days, beginDate, endDate, icomCode, year);
        return batchPullDataByDate(pullReq, NationSubsysServiceEnum.PROTOCOLADJQRY);
    }

    /**
     * @param datas 数据列表
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-12 15:43:02
     * @description : 处理协议调整数据
     */
    @Override
    public Boolean processAgreementAdjustData(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            // TODO 调用销售中心接口
            agreeDownloadMapper.deleteIsmAgreementAdjust(datas);
            agreeDownloadMapper.insertIsmAgreementAdjustFromNation(datas);
            agreeDownloadMapper.deleteIsmAgreementAdjustItem(datas);
            agreeDownloadMapper.insertIsmAgreementAdjustItemFromNation(datas);

            agreeDownloadMapper.deleteIsmAgreementItem(datas);
            agreeDownloadMapper.insertIsmAgreementItemFromNation(datas);
            agreeDownloadMapper.updateIsmAgreementItemPriceFromNation(datas);
        }
        return true;
    }

    /**
     * 协议解除导回
     *
     * @param days      天数
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @param year      年份
     * @param icomCode  工业编码
     * @return List<String>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-12 15:43:02
     */
    @Override
    public List<Map<String, Object>> downAgreementCancel(int days, String beginDate, String endDate, String year, String icomCode) {
        NationSubsysPullREQ pullReq = processAgreeReq(days, beginDate, endDate, icomCode, year);
        return batchPullDataByDate(pullReq, NationSubsysServiceEnum.PROTOCOLCANCELQRY);
    }

    /**
     * @param datas 数据列表
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-12 15:43:02
     * @description : 处理协议解除数据
     */
    @Override
    public Boolean processAgreementCancelData(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            // TODO 调用销售中心接口
            //agreeDownloadMapper.updateIsmAgreementCancel(datas);
        }
        return true;
    }


    /**
     * 最终协议导回
     *
     * @param year          年份 必填
     * @param cycleType     协议半年
     * @param protocolno    协议编码
     * @param supmemberCode 供方编码
     * @param reqmemberCode 需方编码
     * @return List<Map < String, Object>> 数据
     */
    @Override
    public List<Map<String, Object>> downAgreementFinal(String year, String cycleType, String protocolno, String supmemberCode, String reqmemberCode) {
        NationSubsysPullREQ pullReq = new NationSubsysPullREQ();
        //获取参数  默认取今年
        if (StrUtil.isBlank(year)) {
            year = DateUtils.getCurMonth().substring(0, 4);
        }
        pullReq.setYear(year);
        if (StrUtil.isNotBlank(cycleType)) {
            pullReq.setCycleType(cycleType);
        }
        if (StrUtil.isNotBlank(protocolno)) {
            pullReq.setProtocolno(protocolno);
        }
        if (StrUtil.isNotBlank(supmemberCode)) {
            pullReq.setSupmemberCode(supmemberCode);
        }
        if (StrUtil.isNotBlank(reqmemberCode)) {
            pullReq.setReqmemberCode(reqmemberCode);
        }
        return pullData(pullReq, NationSubsysServiceEnum.PROTOCOLCTRLQRY);
    }

    /**
     * 处理最终协议数据
     *
     * @param datas 数据列表
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-12 15:43:02
     */
    @Override
    public Boolean processAgreementFinalData(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            // TODO 如果最终协议准确，调用销售中心传递协议数据

        }
        return true;
    }

    /**
     * @return List<Map < String, Object>> 数据列表
     * <AUTHOR> liuwancheng
     * @create_time : 2025-05-14 09:01:24
     * @description : 营销子系统配货地区查询
     */
    @Override
    public List<Map<String, Object>> downloadDistRegion() {
        NationSubsysPullREQ pullReq = new NationSubsysPullREQ();
        return pullData(pullReq, NationSubsysServiceEnum.MT_API_DISTREGION);
    }

    /**
     * @return Boolean
     * <AUTHOR> liuwancheng
     * @create_time : 2025-05-22 11:26:16
     * @description : 营销子系统配货收货地区查询
     */
    @Override
    public List<Map<String, Object>> downloadDistReceiveRegion() {
        NationSubsysPullREQ pullReq = new NationSubsysPullREQ();
        return pullData(pullReq, NationSubsysServiceEnum.MT_API_DISTRECEIVEREGION);
    }

    /**
     * @param billDate                  单据日期
     * @param supmemberCode             供方编码
     * @param reqmemberCode             需方编码
     * @param distregionCode            配货地区编码
     * @param md02DistReceiveregionCode 收货地区编码
     * @return Boolean
     * <AUTHOR> liuwancheng
     * @create_time : 2025-05-22 11:26:16
     * @description : 营销子系统配货订单预览查询
     */
    @Override
    public List<Map<String, Object>> downloadDistPreview(String billDate, String supmemberCode, String reqmemberCode, String distregionCode, String md02DistReceiveregionCode) {
        NationSubsysPullREQ pullReq = new NationSubsysPullREQ();
        pullReq.setBillDate(billDate);
        pullReq.setSupmemberCode(supmemberCode);
        pullReq.setReqmemberCode(reqmemberCode);
        pullReq.setDistregionCode(distregionCode);
        pullReq.setMd02DistReceiveregionCode(md02DistReceiveregionCode);
        return pullData(pullReq, NationSubsysServiceEnum.DISTORDERPREVIEW);
    }

    /**
     * @param supmemberCode 供方编码
     * @param reqmemberCode 需方编码
     * @param pks           行业子系统主键
     * @return List<Map < String, Object>> 数据列表
     * <AUTHOR> liuwancheng
     * @create_time : 2025-05-14 09:01:24
     * @description : 营销子系统配货模式设置查询
     */
    @Override
    public List<Map<String, Object>> downloadDistParm(String supmemberCode, String reqmemberCode, String pks) {
        NationSubsysPullREQ pullReq = new NationSubsysPullREQ();
        pullReq.setSupmemberCode(supmemberCode);
        if (StrUtil.isNotBlank(pks)) {
            pullReq.setPks(pks);
        }
        if (StrUtil.isNotBlank(reqmemberCode)) {
            pullReq.setReqmemberCode(reqmemberCode);
        }
        return pullData(pullReq, NationSubsysServiceEnum.MT_API_DISTRULE);
    }

    /**
     * @param datas         数据列表
     * @param confirmStatus 确认后的状态
     * @param rejectStatus  驳回后的状态
     * @return Boolean
     * <AUTHOR> liuwancheng
     * @create_time : 2025-05-14 09:01:24
     * @description : 处理配货模式数据
     */
    @Override
    public NationSubsysModel processDistParmData(List<Map<String, Object>> datas, String confirmStatus, String rejectStatus) {
        NationSubsysModel model = new NationSubsysModel();
        if (CollectionUtil.isNotEmpty(datas)) {
            // 需要确认的单据编码列表
            List<String> confirmIds = distDownloadMapper.getConfirmIsmDistParmApplys(datas);
            // 需要驳回的单据编码列表
            List<String> rejectIds = distDownloadMapper.getRejectIsmDistParmApplys(datas);
            model.setConfirmIds(confirmIds);
            model.setRejectIds(rejectIds);

            // 取已生效的配货参数,可能没有配货参数申请需要直接从行业下载
            List<Map<String, Object>> effectDatas = new ArrayList<>();
            for (Map<String, Object> data : datas) {
                if ("1".equals(data.get("EFFECT_STATUS"))) {
                    effectDatas.add(data);
                }
            }
            if (CollectionUtil.isNotEmpty(effectDatas)) {
                /*
                    List<String> deleteIds = distDownloadMapper.getIsmDistParms(effectDatas);
                    if (CollectionUtil.isNotEmpty(deleteIds)) {
                        distDownloadMapper.deleteIsmDistParmItem(deleteIds);
                        distDownloadMapper.deleteIsmDistParmSeason(deleteIds);
                        distDownloadMapper.deleteIsmDistParm(deleteIds);
                    }
                    distDownloadMapper.insertIsmDistParmItemFromNation(effectDatas);
                    distDownloadMapper.insertIsmDistParmSeasonFromNation(effectDatas);
                    distDownloadMapper.insertIsmDistParmFromNation(effectDatas);


                    // 第二次调用单个接口 先删后插
                    List<DistParmDTO> distParms = distDownloadMapper.getIsmDistParmFromNation(effectDatas);
                    List<DistParmItemDTO> distParmItems = distDownloadMapper.getIsmDistParmItemFromNation(effectDatas);
                    List<DistParmSeasonDTO> distParmSeasons = distDownloadMapper.getIsmDistParmSeasonFromNation(effectDatas);
                    for (DistParmDTO distParm : distParms) {
                        // 匹配从表
                        distParm.setItems(distParmItems.stream().filter(item -> item.getMc04CgtDistParmId().equals(distParm.getMc04CgtDistParmId())).collect(Collectors.toList()));
                        distParm.setSeasons(distParmSeasons.stream().filter(item -> item.getMc04CgtDistParmId().equals(distParm.getMc04CgtDistParmId())).collect(Collectors.toList()));
                    }
                */
                // 确认状态才会处理配货参数表
                if (DistOrderStatusEnum.FINISH.getCode().equals(confirmStatus)) {
                    // 主表
                    List<Mc04IslmcDistParmREQ> distParms = distDownloadMapper.getIsmDistParmFromNation(effectDatas);
                    List<Mc04IslmcDistParmItemREQ> distParmItems = distDownloadMapper.getIsmDistParmItemFromNation(effectDatas);
                    List<Mc04IslmcDistParmSeasonREQ> distParmSeasons = distDownloadMapper.getIsmDistParmSeasonFromNation(effectDatas);
                    for (Mc04IslmcDistParmREQ distParm : distParms) {
                        // 匹配从表
                        distParm.setMc04IslmcDistParmItemList(distParmItems.stream().filter(item -> item.getMc04CgtDistParmId().equals(distParm.getMc04CgtDistParmId())).collect(Collectors.toList()));
                        distParm.setMc04IslmcDistParmSeasonList(distParmSeasons.stream().filter(item -> item.getMc04CgtDistParmId().equals(distParm.getMc04CgtDistParmId())).collect(Collectors.toList()));
                    }

                    // 因配货参数表在销售中心不能直接处理，需调研销售中心接口
                    Mc04IslmcDistParmREQ req = new Mc04IslmcDistParmREQ();
                    //  取地市编码，发运地区编码，收货地区编码
                    req.setMc04IslmcDistParmComList(distParms.stream().map(item -> {
                        Mc04IslmcDistParmComREQ com = new Mc04IslmcDistParmComREQ();
                        com.setMa02CgtTradeReqMembCode(item.getMa02CgtTradeReqMembCode());
                        com.setMd02CgtDistRegionCode(item.getMd02CgtDistRegionCode());
                        com.setMd02DistReceiveregionCode(item.getMd02DistReceiveregionCode());
                        return com;
                    }).collect(Collectors.toList()));
                    // 先查询
                    List<Mc04IslmcDistParmDTO> distParmDtos = distParmApi.list(req).getData();
                    if (CollectionUtil.isNotEmpty(distParmDtos)) {
                        // 批量删除
                        distParmApi.deleteBatch(distParmDtos.stream().map(Mc04IslmcDistParmDTO::getMc04CgtDistParmId).collect(Collectors.toList()));
                    }
                    // 批量保存
                    distParmApi.createBatch(distParms);
                }
            }

            if (CollectionUtil.isNotEmpty(confirmIds)) {
                // 需要确认的配货参数申请
                distDownloadMapper.updateIsmDistParmApply(confirmIds, confirmStatus);
            }
            if (CollectionUtil.isNotEmpty(rejectIds)) {
                // 需要驳回的配货参数申请
                distDownloadMapper.updateIsmDistParmApply(rejectIds, rejectStatus);
            }
        }
        return model;
    }

    /**
     * @param itfpk 配货单号 多个以,分隔
     * @return List<Map < String, Object>> 数据列表
     * <AUTHOR> liuwancheng
     * @create_time : 2025-05-14 09:01:24
     * @description : 营销子系统配货订单查询
     */
    @Override
    public List<Map<String, Object>> downloadDist(String itfpk) {
        NationSubsysPullREQ pullReq = new NationSubsysPullREQ();
        pullReq.setItfpks(itfpk);
        return pullData(pullReq, NationSubsysServiceEnum.MT_API_DISTORDER);
    }

    /**
     * @param datas         数据列表
     * @param confirmStatus 确认后的状态
     * @param rejectStatus  驳回后的状态
     * @return Boolean
     * <AUTHOR> liuwancheng
     * @Create : 2025-05-14 09:01:24
     * @description : 配货订单数据处理
     */
    @Override
    public NationSubsysModel processDistData(List<Map<String, Object>> datas, String confirmStatus, String rejectStatus) {
        NationSubsysModel model = new NationSubsysModel();
        if (CollectionUtil.isNotEmpty(datas)) {
            // 需要确认的单据编码列表
            List<String> confirmIds = distDownloadMapper.getConfirmDistOrders(datas);
            // 需要驳回的单据编码列表
            List<String> rejectIds = distDownloadMapper.getRejectDistOrders(datas);
            // 需要作废的单据编码列表
            List<String> cancelIds = distDownloadMapper.getCancelDistOrders(datas);
            model.setConfirmIds(confirmIds);
            model.setRejectIds(rejectIds);
            model.setCancelIds(cancelIds);
            List<String> allIds = new ArrayList<>();
            allIds.addAll(confirmIds);
            allIds.addAll(rejectIds);

            if (CollectionUtil.isNotEmpty(allIds)) {
                // 更新商业确认量，和最终量
                distDownloadMapper.updateDistOrderQty(allIds);
            }

            if (CollectionUtil.isNotEmpty(confirmIds)) {
                // 增加确认操作日志
                distDownloadMapper.insertIsmDistLog(confirmIds, confirmStatus);
                // 更新配货商业确认状态
                distDownloadMapper.updateDistOrderStatus(confirmIds, confirmStatus);
                if (DistOrderStatusEnum.FINISH.getCode().equals(confirmStatus)) {
                    // 已完成的配货单更新日计划表生效状态
                    distDownloadMapper.updateIsmItemDayPlanEffect(confirmIds);

                    List<Mc04IslmCgtDistOrderDO> list = mc04IslmCgtDistOrderService.lambdaQuery().in(Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderCode, confirmIds).list();
                    // 已完成配货单传给销售中心
                    List<Mc04IslmcCgtDistOrderREQ> dists = Mc04IslmCgtDistOrderDOToMc04IslmcCgtDistOrderREQConverter.SELF.converterDosToModels(list);
                    // 先查询
                    Mc04IslmcCgtDistOrderREQ req = new Mc04IslmcCgtDistOrderREQ();
                    req.setMc04CgtDistOrderCodes(confirmIds);
                    List<Mc04IslmcCgtDistOrderDTO> distDtos = cgtDistOrderApi.list(req).getData();
                    if (CollectionUtil.isNotEmpty(distDtos)) {
                        // 批量删除
                        cgtDistOrderApi.deleteBatch(distDtos.stream().map(Mc04IslmcCgtDistOrderDTO::getMc04CgtDistOrderId).collect(Collectors.toList()));
                    }
                    // 批量保存
                    cgtDistOrderApi.createBatch(dists);
                }

                // 判断是否发送给物流
                String sendLogStatus = AcRuleUtil.getRuleValue(DistConstants.ISALE_ORDER_DIST_PUSH_LOG, IcomUtils.getIcomCode());
                if (confirmStatus.equals(sendLogStatus)) {
                    iSaleThreePartyRepository.pushDistToLogisticses(confirmIds);
                }
            }
            if (CollectionUtil.isNotEmpty(rejectIds)) {
                // 增加驳回操作日志
                distDownloadMapper.insertIsmDistLog(rejectIds, rejectStatus);
                // 更新配货驳回状态
                distDownloadMapper.updateDistOrderStatus(rejectIds, rejectStatus);
                if (DistOrderStatusEnum.SALE_SUBMIT.getCode().equals(rejectStatus)) {
                    // 已驳回到保存的配货单删除日计划表
                    distDownloadMapper.removeIsmItemDayPlans(rejectIds);
                }
            }

            // 作废状态
            String cancelStatus = DistOrderStatusEnum.CANCEL.getCode();
            if (CollectionUtil.isNotEmpty(cancelIds)) {
                // 增加作废操作日志
                distDownloadMapper.insertIsmDistLog(cancelIds, cancelStatus);
                // 更新配货商业作废
                distDownloadMapper.updateDistOrderStatus(cancelIds, cancelStatus);
                // 作废的配货单删除日计划表
                distDownloadMapper.removeIsmItemDayPlans(cancelIds);
            }
        }
        return model;
    }

    /**
     * @param mc04CgtDistParmApplyCode 配货参数申请编码
     * @return Boolean 是否推送成功
     * @description : 配货参数到行业子系统
     */
    @Override
    public boolean pushDistparmToNation(String mc04CgtDistParmApplyCode) {
        boolean ok = false;
        try {
            //获取配货参数数据
            Map<String, Object> param = distDownloadMapper.getParm(mc04CgtDistParmApplyCode);
            Assert.notNull(param, () -> new CustomException("配货参数不存在"));
            String itfpk = (String) param.get("ITFPK");
            String pk = (String) param.get("PK");
            // pk 为空时，新增，不为空时，修改
            boolean add = true;
            if (StrUtil.isNotBlank(pk)) {
                log.info(itfpk + "--已经调用过新增接口，需要调用更新接口-");
                add = false;
            }
            // 卷烟需方交易会员代码
            String reqmemberCode = (String) param.get("REQMEMBER_CODE");
            // 卷烟供方交易会员代码
            String supmemberCode = (String) param.get("SUPMEMBER_CODE");
            // 网配订单发货地区代码
            String distregionCode = (String) param.get("SUPMEMBER_CODE");
            // 配货收货地区编码
            String md02DistReceiveregionCode = (String) param.get("SUPMEMBER_CODE");
            // 获取配货参数卷烟列表
            List<Map<String, Object>> parmItems = distDownloadMapper.getParmItems(mc04CgtDistParmApplyCode, supmemberCode, reqmemberCode, distregionCode, md02DistReceiveregionCode);
            // 配货参数季节系数列表
            List<Map<String, Object>> parmSeasons = distDownloadMapper.getParmSeasons(mc04CgtDistParmApplyCode, supmemberCode, reqmemberCode, distregionCode, md02DistReceiveregionCode);

            // 当前日期日期格式化
            String billDate = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
            param.put("BILL_DATE", billDate);
            //更新处理主表标识
            if (add) {
                param.remove("VERSION");
            } else {
                //sql默认新增,更新的action从表可单独设置
                param.put("ACTION", "UPDATE");
            }
            // 处理从表数据,组织字表
            Map detail = new HashMap(4);
            detail.put("DISTULE", parmItems);
            detail.put("SEASON", parmSeasons);
            if (add) {
                // 新增和更新格式不一样
                List<Map> details = new ArrayList<>();
                details.add(detail);
                param.put("DETAIL", details);
            } else {
                param.put("DETAIL", detail);
            }
            //调用inter接口
            NationSubsysServiceEnum service = add ? NationSubsysServiceEnum.ADD_DISTRULE : NationSubsysServiceEnum.MT_API_DISTRULE_UPDATE;
            Map<String, Object> paraMap = new HashMap<>(4);
            paraMap.put("serviceId", service.getServiceId());
            paraMap.put("pk", itfpk);
            paraMap.put("data", param);
            Map<String, Object> result = push(paraMap, service);
            if (NationSubsysConstants.SUCCESS_CODE.equals(result.get(NationSubsysConstants.RESULT_CODE))) {
                log.debug(itfpk + "推送成功");
                if (add) {
                    // 请求配货参数
                    downloadDistParm(supmemberCode, reqmemberCode, null);
                }
                ok = true;
            } else {
                log.debug(itfpk + "推送失败");
            }
        } catch (Exception e) {
            log.error("接口调用异常:", e);
        }
        return ok;
    }

    /**
     * @param mc04CgtDistOrderCode 配货订单编码
     * @return Boolean 是否推送成功
     * @description : 配货订单到行业子系统
     */
    @Override
    public String pushDistToNation(String mc04CgtDistOrderCode) {
        String msg = addDist(mc04CgtDistOrderCode);
        // 1 新增配货单
        if (StrUtil.isBlank(msg)) {
            // 2 调用查询配货单接口
            boolean ok = pullDist(mc04CgtDistOrderCode);
            if (!ok) {
                return "调用行业营销管控子系统配货订单查询接口失败，请联系管理员！";
            }
        } else {
            return msg;
        }
        // 3 更新配货单
        return updateDist(mc04CgtDistOrderCode);
    }

    /**
     * 删除配货订单到行业子系统
     *
     * @param mc04CgtDistOrderCode 配货订单编码
     * @return Boolean 是否推送成功
     */
    @Override
    public String pushDeleteDistToNation(String mc04CgtDistOrderCode) {
        try {
            NationSubsysServiceEnum service = NationSubsysServiceEnum.DELETEDISORDER;
            Map<String, Object> dist = distDownloadMapper.getDeleteDist(mc04CgtDistOrderCode);
            if (dist == null) {
                log.info(mc04CgtDistOrderCode + "---已经删除配货单，无需重复调用--");
                return null;
            }
            String pk = (String) dist.get("PK");
            //日期格式化
            Map<String, Object> paraMap = new HashMap<>(4);
            paraMap.put("serviceId", service.getServiceId());
            //调用inter接口
            paraMap.put("pk", pk);
            paraMap.put("data", dist);
            Map result = push(paraMap, service);
            if (sendResultSuccess(result)) {
                log.debug(pk + "删除配货单成功");
            } else {
                String msg = (String) result.get("MSG");
                log.debug(pk + "删除配货单失败:" + msg);
                return msg;
            }
        } catch (Exception e) {
            log.error("删除配货单异常", e);
        }
        return null;
    }


    /**
     * @param cont 合同信息
     * @return Boolean 是否推送成功
     * @description : 合同到行业子系统
     */
    @Override
    public boolean pushContToNation(UploadCont cont) {
        Map<String, Object> paraMap = new HashMap<>(4);
        paraMap.put("serviceId", NationSubsysServiceEnum.PUSH_CONTRACT.getServiceId());
        paraMap.put("pk", cont.getItfpk());
        paraMap.put("data", cont);
        Map<String, Object> result = push(paraMap, NationSubsysServiceEnum.PUSH_CONTRACT);
        if (NationSubsysConstants.SUCCESS_CODE.equals(result.get(NationSubsysConstants.RESULT_CODE))) {
            log.debug("{}推送成功", cont.getItfpk());
            return true;
        } else {
            throw new CustomException(String.valueOf(result.get("MSG")));
        }
    }

    /**
     * 拉取正式合同号
     *
     * @param itfpkList 订单号列表
     * @return Boolean
     * @description : 拉取正式合同号
     */
    @Override
    public Boolean pullContractNo(List<String> itfpkList) {
        if (CollUtil.isEmpty(itfpkList)) {
            return true;
        }
        NationSubsysServiceEnum contractno = NationSubsysServiceEnum.CONTRACTNO;
        List<List<String>> split = CollUtil.split(itfpkList, contractno.getPageSize());
        //分页
        for (List<String> list : split) {
            NationSubsysPullREQ req = new NationSubsysPullREQ();
            req.setItfpks(StrUtil.join(",", list));
            pullData(req, contractno);
        }
        return true;
    }

    /**
     * 处理正式合同号
     *
     * @return Boolean
     * @description : 处理正式合同号
     */
    @Override
    public Boolean processContractNo(List<String> codeList) {
        //更新合同
        contractDownloadMapper.processContractNo(codeList);
        //记录日志
        return true;
    }

    /**
     * 更新日计划状态
     *
     * @param codeList 订单号列表
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-08-05 11:59:52
     * @description : 更新日计划状态
     */
    @Override
    public Boolean updateItemDayPlanStatus(List<String> codeList) {
        contractDownloadMapper.updateItemDayPlanStatus(codeList);
        return true;
    }

    /**
     * 拉取合同
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return List<String> 订单号列表
     * @description : 拉取合同
     */
    @Override
    public List<Map<String, Object>> pullContract(String beginDate, String endDate) {
        NationSubsysPullREQ req = new NationSubsysPullREQ();
        req.setBeginDate(beginDate);
        req.setEndDate(endDate);
        return pullData(req, NationSubsysServiceEnum.CONTRACTQRY);
    }


    /**
     * 处理合同数据
     *
     * @param dataList 数据列表
     * @description : 处理合同数据
     */
    @Override
    public List<String> processContractData(List<Map<String, Object>> dataList) {
        if (CollUtil.isNotEmpty(dataList)) {
            // 改之前先查询当前为02的合同
            List<String> contIdList = contractDownloadMapper.getNeedSignContract(dataList);

            // 处理业务表 更新合同状态到已鉴章，增加操作日志
            contractDownloadMapper.updateSignContract(dataList);
            //改之后查询哪些变成了10
            List<String> changeContIdList;
            if (CollUtil.isNotEmpty(contIdList)) {
                changeContIdList = contractDownloadMapper.getChangeSignContract(contIdList);
            } else {
                changeContIdList = Collections.emptyList();
            }
            // 更新合同规格已变更的合同量
            contractDownloadMapper.updateContractItemFromNation(dataList);
            // 新增行业子系统的新合同或者新合同规格
            contractDownloadMapper.insertContractFromNation(dataList, IcomUtils.getIcomCode());
            contractDownloadMapper.insertContractItemFromNation(dataList, IcomUtils.getIcomCode());
            // 重新汇总主表总量与总金额
            contractDownloadMapper.summaryContract(dataList);

            return changeContIdList;
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 营销子系统合同解除拉取
     *
     * @param beginDate 开始时间
     * @param endDate   结束时间
     * @return Boolean
     * @description : 营销子系统合同解除拉取
     */
    @Override
    public List<Map<String, Object>> pullContractCancel(String beginDate, String endDate) {
        NationSubsysPullREQ req = new NationSubsysPullREQ();
        req.setBeginDate(beginDate);
        req.setEndDate(endDate);
        return pullData(req, NationSubsysServiceEnum.CONTRACTCANCELQRY);
    }

    /**
     * 营销子系统合同解除数据处理
     *
     * @param dataList 数据列表
     * @return Boolean
     * <AUTHOR> liuwancheng
     * @create_time : 2025-05-14 09:01:24
     * @description : 处理合同数据
     */
    @Override
    public List<String> processContractCancelData(List<Map<String, Object>> dataList) {
        if (CollUtil.isNotEmpty(dataList)) {
            // 改之前先查询当前非为90的合同
            List<String> contIdList = contractDownloadMapper.getNeedCancelContract(dataList);
            if (CollUtil.isNotEmpty(contIdList)) {
                // 处理业务表 更新合同状态到已解除，增加操作日志
                contractDownloadMapper.updateCancelContract(dataList);
            }
            return contIdList;
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 推送合同数据
     *
     * @param contIdList 数据列表
     * @description : 推送合同数据
     */
    @Override
    public void pushContToCenter(List<String> contIdList) {
        if (CollUtil.isNotEmpty(contIdList)) {
            List<Mc04IslmContOrderDO> orderList = mc04IslmContOrderService.lambdaQuery().in(Mc04IslmContOrderDO::getMc04ContOrderId, contIdList).list();
            List<Mc04IslmContOrderItemDO> itemList = mc04IslmContOrderItemService.lambdaQuery().in(Mc04IslmContOrderItemDO::getMc04ContOrderId, contIdList).list();
            Map<String, List<Mc04IslmContOrderItemDO>> collect = itemList.stream().collect(Collectors.groupingBy(Mc04IslmContOrderItemDO::getMc04ContOrderId));
            orderList.forEach(order -> {
                try {
                    Mc04IslmcContOrderDTO centerOrder = SaleResponseUtil.getCenterDTO("从中台侧获取合同列表", () -> contOrderApi.detail(order.getMc04ContOrderId()));
                    if (ObjectUtil.isNull(centerOrder)) {
                        Mc04IslmcContOrderREQ req = Mc04IslmcContOrderReqToMc04IslmContOrderDoConverter.INSTANCE.converterModelToDo(order);
                        List<Mc04IslmcContOrderItemREQ> itemReqList = Mc04IslmcContOrderItemReqToMc04IslmContOrderItemDoConverter.INSTANCE.converterModelsToDos(collect.get(order.getMc04ContOrderId()));
                        req.setContOrderItems(itemReqList);
                        Boolean success = SaleResponseUtil.getCenterDTO("中台侧保存合同", () -> contOrderApi.create(req));
                        if (success) {
                            InterfaceLogUtil.addSuccessLog(InterfaceLogChannelEnum.ISALE_CENTER, InterfaceLogBusiTypeEnum.CONTRACT_NEW, order.getMc04ContOrderId(), "");
                        } else {
                            InterfaceLogUtil.addErrorLog(InterfaceLogChannelEnum.ISALE_CENTER, InterfaceLogBusiTypeEnum.CONTRACT_NEW, order.getMc04ContOrderId(), "", false);
                        }
                    }
                } catch (Exception e) {
                    log.error("创建合同失败：{}", e.getMessage());
                    InterfaceLogUtil.addErrorLog(InterfaceLogChannelEnum.ISALE_CENTER, InterfaceLogBusiTypeEnum.CONTRACT_NEW, order.getMc04ContOrderId(), "", false);
                }
            });
        }
    }

    /**
     * 重新推送合同数据
     *
     * @param contId 数据列表
     * @description : 重新推送合同数据
     */
    @Override
    public Boolean retryPushContToCenter(String contId) {
        Mc04IslmContOrderDO order = mc04IslmContOrderService.lambdaQuery().eq(Mc04IslmContOrderDO::getMc04ContOrderId, contId).one();
        List<Mc04IslmContOrderItemDO> itemList = mc04IslmContOrderItemService.lambdaQuery().eq(Mc04IslmContOrderItemDO::getMc04ContOrderId, contId).list();
        Mc04IslmcContOrderDTO centerOrder = SaleResponseUtil.getCenterDTO("从中台侧获取合同列表", () -> contOrderApi.detail(contId));
        if (ObjectUtil.isNull(centerOrder)) {
            Mc04IslmcContOrderREQ req = Mc04IslmcContOrderReqToMc04IslmContOrderDoConverter.INSTANCE.converterModelToDo(order);
            List<Mc04IslmcContOrderItemREQ> itemReqList = Mc04IslmcContOrderItemReqToMc04IslmContOrderItemDoConverter.INSTANCE.converterModelsToDos(itemList);
            req.setContOrderItems(itemReqList);
            return SaleResponseUtil.getCenterDTO("中台侧保存合同", () -> contOrderApi.create(req));
        } else {
            //中台已经有了认为很成功
            return true;
        }
    }

    /**
     * 判断返回成功
     *
     * @param result 返回结果
     * @return 成功
     */
    private static boolean sendResultSuccess(Map result) {
        return result != null && NationSubsysConstants.SUCCESS_CODE.equals(result.get(NationSubsysConstants.RESULT_CODE));
    }

    /**
     * 新增配货单
     *
     * @param mc04CgtDistOrderCode 配货订单编码
     * @return 结果
     */
    private String addDist(String mc04CgtDistOrderCode) {
        try {
            NationSubsysServiceEnum service = NationSubsysServiceEnum.ADD_DISTORDER;
            Map<String, Object> dist = distDownloadMapper.getAddDist(mc04CgtDistOrderCode);
            Assert.notNull(dist, () -> new CustomException("配货订单不存在"));
            String itfpk = (String) dist.get("ITFPK");
            String pk = (String) dist.get("PK");
            if (StrUtil.isNotBlank(pk)) {
                log.info(itfpk + "---已经新增过配货单，无需重复调用--");
                return null;
            }
            //日期格式化
            Map<String, Object> paraMap = new HashMap<>(4);
            paraMap.put("serviceId", service.getServiceId());
            //调用inter接口
            paraMap.put("pk", itfpk);
            paraMap.put("data", dist);
            Map result = push(paraMap, service);
            if (sendResultSuccess(result)) {
                log.debug(itfpk + "新增推送成功");
            } else {
                String msg = (String) result.get("MSG");
                log.debug(itfpk + "新增推送失败:" + msg);
                return msg;
            }
        } catch (Exception e) {
            log.error("新增配货单异常", e);
        }
        return null;
    }

    /**
     * 获取配货单
     *
     * @param mc04CgtDistOrderCode 配货单号
     * @return 结果
     */
    private boolean pullDist(String mc04CgtDistOrderCode) {
        int num = 0;
        boolean ok = false;
        int max = 3;
        // 循环三次 每次间隔2秒钟
        for (int i = 0; i < max; i++) {
            if (!ok) {
                List<Map<String, Object>> resusts = downloadDist(mc04CgtDistOrderCode);
                num++;
                if (CollectionUtil.isNotEmpty(resusts)) {
                    ok = true;
                } else {
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        log.error("sleep等待2秒异常" + e.getMessage());
                    }
                }
            }
        }
        log.info("调用行业营销管控子系统配货订单查询接口次数:" + num);
        return ok;
    }

    /**
     * 更新配货单
     *
     * @param mc04CgtDistOrderCode 配货单号
     * @return 结果
     */
    private String updateDist(String mc04CgtDistOrderCode) {
        try {
            //获取主表数据
            Map<String, Object> dist = distDownloadMapper.getUpdateDist(mc04CgtDistOrderCode);
            if (dist == null) {
                return "配货订单不存在";
            }
            String pk = (String) dist.get("ITFPK");
            //获取字表数据
            List<Map<String, Object>> distItems = distDownloadMapper.getDistItems(mc04CgtDistOrderCode);
            NationSubsysServiceEnum service = NationSubsysServiceEnum.MT_API_DISTORDER_UPDATE;
            Map<String, Object> paraMap = new HashMap<>(4);
            paraMap.put("serviceId", service.getServiceId());
            //处理从表数据,组织字表
            dist.put("DETAIL", distItems);
            //调用inter接口
            paraMap.put("pk", pk);
            paraMap.put("data", dist);
            Map result = push(paraMap, service);
            if (sendResultSuccess(result)) {
                log.debug(pk + "更新配货单推送成功");
            } else {
                String msg = (String) result.get("MSG");
                log.debug(pk + "更新配货单推送失败:" + msg);
                return msg;
            }
        } catch (Exception e) {
            log.error("更新配货单异常", e);
            return "推送行业子系统更新配货单接口异常";
        }
        return null;
    }

    /**
     * 处理协议查询条件
     *
     * @param days      天数
     * @param beginDate 起始日期
     * @param endDate   截止日期
     * @param icomCode  工业编码
     * @param year      年份
     */
    private NationSubsysPullREQ processAgreeReq(int days, String beginDate, String endDate, String icomCode, String year) {
        NationSubsysPullREQ pullReq = new NationSubsysPullREQ();
        String today = ISaleDateUtil.getToday();
        // 优先使用调用频率
        if (days > 0) {
            beginDate = ISaleDateUtil.offsetDay(today, -days);
            endDate = today;
        } else {
            // 起始和截止日期默认取7天前和今日
            if (StrUtil.isBlank(beginDate)) {
                beginDate = ISaleDateUtil.offsetDay(today, -7);
            }
            if (StrUtil.isBlank(endDate)) {
                endDate = today;
            }
        }
        pullReq.setBeginDate(beginDate);
        pullReq.setEndDate(endDate);
        if (StrUtil.isNotBlank(year)) {
            pullReq.setYear(year);
        }
        if (StrUtil.isNotBlank(icomCode)) {
            pullReq.setIcomCode(icomCode);
        }
        return pullReq;
    }

    /**
     * 最大日期
     */
    private static final int MAX_DAY = 29;

    /**
     * 超过最大天数 分批调用接口
     *
     * @param req     参数
     * @param service 接口
     */
    private List<Map<String, Object>> batchPullDataByDate(NationSubsysPullREQ req, NationSubsysServiceEnum service) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        String beginDate = req.getBeginDate();
        String endDate = req.getEndDate();
        if (StrUtil.isNotBlank(beginDate) && StrUtil.isNotBlank(endDate)) {
            String maxDate = ISaleDateUtil.offsetDay(beginDate, MAX_DAY);
            if (maxDate.compareTo(endDate) >= 0) {
                // 不超过的话执行调用
                log.debug("{}循环次数1: {}-{}", service.getServiceName(), beginDate, endDate);
                NationSubsysPullREQ cloneReq = BeanUtil.copyProperties(req, NationSubsysPullREQ.class);
                resultList.addAll(pullData(cloneReq, service));
            } else {
                int num = 1;
                // 日期超过最大天数 分批调用接口
                while (maxDate.compareTo(endDate) < 0) {
                    log.debug("{}循环次数{}: {}-{}", service.getServiceName(), num, beginDate, maxDate);
                    NationSubsysPullREQ cloneReq = BeanUtil.copyProperties(req, NationSubsysPullREQ.class);
                    cloneReq.setBeginDate(beginDate);
                    cloneReq.setEndDate(maxDate);
                    resultList.addAll(pullData(cloneReq, service));
                    beginDate = ISaleDateUtil.offsetDay(maxDate, 1);
                    maxDate = ISaleDateUtil.offsetDay(beginDate, MAX_DAY);
                    num++;
                }
                if (maxDate.compareTo(endDate) >= 0) {
                    // 最后一次超过取截止日期
                    maxDate = endDate;
                    log.debug("{}最后一次循环 当前次数{}: {}-{}", service.getServiceName(), num, beginDate, maxDate);
                    NationSubsysPullREQ cloneReq = BeanUtil.copyProperties(req, NationSubsysPullREQ.class);
                    resultList.addAll(pullData(cloneReq, service));
                }
            }
        }
        return resultList;
    }

    /**
     * @param req     参数
     * @param service 接口定义
     * @return List<Map < Object>>
     * <AUTHOR> liuwancheng
     * @Create : 2025-05-22 15:26:44
     * @Description : 查询营销子系统数据
     */
    private List<Map<String, Object>> pullData(NationSubsysPullREQ req, NationSubsysServiceEnum service) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        if (ObjectUtil.isNotNull(service.getPageSize())) {
            // 要分页查询
            int pageSize = service.getPageSize();
            int pageIndex = 1;
            Map<String, Object> result = pull(req, service, pageIndex, resultList);
            // 总数据行数
            Object recordCount = result.get(NationSubsysConstants.RECORDCOUNT);
            // 分页数据取其他页数据
            if (ObjectUtil.isNotEmpty(recordCount)) {
                int count = Integer.parseInt(recordCount.toString());
                if (count > pageSize) {
                    //计算总共有多少页
                    int max = count / pageSize + 1;
                    //当前页数小于最大页数，说明还有剩余数据，再次调用
                    while (pageIndex < max) {
                        pageIndex++;
                        pull(req, service, pageIndex, resultList);
                    }
                }
            }
        } else {
            // 不分页查询
            pull(req, service, 1, resultList);
        }
        return resultList;
    }

    /**
     * @param req        请求参数
     * @param service    接口枚举
     * @param pageIndex  当前页码
     * @param resultList 返回数据
     * @return Map<Object> 返回结果
     * <AUTHOR> liuwancheng
     * @Create : 2025-05-14 14:01:22
     * @Description : 请求行业数据
     */
    private Map<String, Object> pull(NationSubsysPullREQ req, NationSubsysServiceEnum service, int pageIndex, List<Map<String, Object>> resultList) {
        Map<String, Object> result;
        log.debug("pull请求行业接口: {}，参数：{}", service, req);
        if (SaleCenterConstants.YES.equals(simulateNationSystemEnabled)) {
            result = getVirtualResult(service, req);
        } else {
            result = nationSubsysApi.pull(service.getServiceId(), req.getBeginDate(),
                    req.getEndDate(), req.getSupmemberCode(), req.getReqmemberCode(), pageIndex, service.getPageSize(),
                    req.getYear(), req.getCycleType(), req.getProtocolno(), req.getPks(), req.getItfpks(),
                    req.getBillDate(), req.getDistregionCode(), req.getMd02DistReceiveregionCode());
        }
        log.debug("pull请求行业接口: {}，返回：{}", service, result);
        Assert.notNull(result, () -> new CustomException("接口返回结果为空"));
        Assert.isTrue(NationSubsysConstants.SUCCESS_CODE.equals(result.get(NationSubsysConstants.CODE)), () -> new CustomException("接口返回结果为空"));
        Object data = result.get(NationSubsysConstants.DATA);
        if (ObjectUtil.isNull(data)) {
            log.info("NationSubsysApi.pull--接口返回结果的data为空");
        }
        List<Map<String, Object>> datas = (List<Map<String, Object>>) data;
        if (CollectionUtil.isEmpty(datas)) {
            log.info("NationSubsysApi.pull--接口返回结果的datas为空");
        }
        log.info("NationSubsysServiceImpl.pull--返回数据数目{}", datas.size());
        // 处理下`DATA`:[{`DETAIL`:[]}]的情况，不再执行插入，即从表为空的情况
        if (datas.size() == 1 && NationSubsysConstants.EMPTY_DATA.equals(JSONUtil.toJsonStr(datas.get(0)))) {
            log.info("NationSubsysApi.pull--接口返回结果为空,`DATA`:[{`DETAIL`:[]}]。");
        }
        resultList.addAll(save(req, service, result));
        return result;
    }

    /**
     * @param data    传输数据
     * @param service 接口枚举
     * @return Map<Object> 返回结果
     * @Description : 请求行业数据
     */
    private Map<String, Object> push(Map<String, Object> data, NationSubsysServiceEnum service) {
        Map<String, Object> result;
        log.debug("push请求行业接口: {}，参数：{}", service, data);
        if (SaleCenterConstants.YES.equals(simulateNationSystemEnabled)) {
            result = getVirtualResult(service, null);
        } else {
            result = nationSubsysApi.push(data);
        }
        log.debug("push 请求行业接口: {}，返回：{}", service, result);
        Assert.notNull(result, () -> new CustomException("接口返回结果为空"));
        Assert.isTrue(NationSubsysConstants.SUCCESS_CODE.equals(result.get(NationSubsysConstants.CODE)), () -> new CustomException("接口返回结果失败"));
        return result;
    }

    /**
     * 获取虚拟返回结果
     *
     * @param nationSubsysServiceEnum 接口枚举
     * @param param                   查询参数
     * @return 返回
     */
    private static Map<String, Object> getVirtualResult(NationSubsysServiceEnum nationSubsysServiceEnum, NationSubsysPullREQ param) {
        String resultJson;
        switch (nationSubsysServiceEnum) {
            case PROTOCOLQRY:
                // 协议
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"PAGEINDEX\":\"1\",\"PAGESIZE\":\"20\",\"RECORDCOUNT\":\"20\",\"DATA\":[{\"REQREGION\":\"青海省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"吕强\",\"ORDER_DATE\":\"2023-12-29\",\"REQMEMBER_CODE\":\"11140401\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115663750\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"250.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115663747\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115663751\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"QTY\":\"1000.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115663747\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"山东省烟草公司长治市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111018749\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115663747\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-29 16:02:31\"},{\"REQREGION\":\"辽宁省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"徐薇\",\"ORDER_DATE\":\"2023-12-29\",\"REQMEMBER_CODE\":\"11150101\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115663755\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"100.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115663753\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"内蒙古自治区烟草公司呼和浩特市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111016572\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115663753\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-29 16:02:31\"},{\"REQREGION\":\"河北省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"程连国\",\"ORDER_DATE\":\"2023-12-24\",\"REQMEMBER_CODE\":\"11131001\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115683986\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"250.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115683984\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"河北省烟草公司廊坊市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111012077\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115683984\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:05:35\"},{\"REQREGION\":\"河北省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"杨彦辉\",\"ORDER_DATE\":\"2023-12-24\",\"REQMEMBER_CODE\":\"11130601\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115683991\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"1000.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115683988\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115683992\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"QTY\":\"75.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115683988\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"河北省烟草公司保定市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111012139\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115683988\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:05:35\"},{\"REQREGION\":\"大连市\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"姜楠\",\"ORDER_DATE\":\"2023-12-29\",\"REQMEMBER_CODE\":\"11210201\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115684142\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"80.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684140\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"}],\"REQMEMBER_NAME\":\"中国烟草总公司大连市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111011223\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115684140\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:08:36\"},{\"REQREGION\":\"云南省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"张静\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11532601\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115684439\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"100.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684437\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"云南省烟草公司文山州公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111012629\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115684437\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:11:38\"},{\"REQREGION\":\"山东省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"李勇\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11370601\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115684444\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"40.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684441\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115684445\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"QTY\":\"810.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684441\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"山东烟台烟草有限公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013297\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115684441\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:11:38\"},{\"REQREGION\":\"江苏省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"欧宇婷\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11320701\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115684637\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"QTY\":\"500.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684635\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"江苏省烟草公司连云港市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111012878\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115684635\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:14:40\"},{\"REQREGION\":\"安徽省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"张帆\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11340101\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115684909\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"325.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684906\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115684910\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"525.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684906\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"安徽省烟草公司合肥市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111012636\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115684906\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:17:43\"},{\"REQREGION\":\"云南省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"杨树\",\"ORDER_DATE\":\"2023-12-29\",\"REQMEMBER_CODE\":\"11533501\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115687620\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"30.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687617\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115687621\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"QTY\":\"150.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687617\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"云南省烟草公司临沧市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111010415\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115687617\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:55:02\"},{\"REQREGION\":\"安徽省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"尹飞\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11341301\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115687661\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"60.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687657\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"12212.50000000\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(汾清香)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203593\",\"ROW_PK\":\"115687662\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"35.********\",\"PK_TRADECIGARETTE\":\"TC690102820360920420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687657\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"3\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115687663\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"QTY\":\"330.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687657\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"安徽省烟草公司宿州市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013360\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115687657\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:55:03\"},{\"REQREGION\":\"安徽省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"李云龙\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11341701\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115687667\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"50.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687665\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"}],\"REQMEMBER_NAME\":\"安徽省烟草公司池州市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013469\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115687665\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:55:03\"},{\"REQREGION\":\"安徽省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"吴正栋\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11341001\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115687671\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"QTY\":\"125.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687669\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"安徽省烟草公司黄山市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013495\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115687669\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:55:03\"},{\"REQREGION\":\"云南省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"沈昕\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11530101\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115691610\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"275.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115691608\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"}],\"REQMEMBER_NAME\":\"云南省烟草公司昆明市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013756\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115691608\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 16:49:59\"},{\"REQREGION\":\"云南省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"赵俊娴\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11530501\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115691624\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"75.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115691622\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"云南省烟草公司保山市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013986\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115691622\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 16:49:59\"},{\"REQREGION\":\"安徽省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"王军\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11340501\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115694609\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"100.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115694606\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115694610\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"QTY\":\"325.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115694606\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"安徽省烟草公司马鞍山市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013639\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115694606\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 17:56:23\"},{\"REQREGION\":\"山东省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"韩建鹏\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"********\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115694614\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"QTY\":\"175.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115694612\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"}],\"REQMEMBER_NAME\":\"山东济南烟草有限公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111014092\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115694612\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 17:56:23\"}]} \n";
                break;
            case PROTOCOLADJQRY:
                // 协议调整
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"PAGEINDEX\":\"1\",\"PAGESIZE\":\"20\",\"RECORDCOUNT\":\"20\",\"DATA\":[{\"REQREGION\":\"青海省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"吕强\",\"ORDER_DATE\":\"2023-12-29\",\"REQMEMBER_CODE\":\"11140401\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115663750\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"50.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115663747\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115663751\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"ADJ_QTY\":\"-10.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115663747\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"山东省烟草公司长治市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111018749\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115663747\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-29 16:02:31\"},{\"REQREGION\":\"辽宁省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"徐薇\",\"ORDER_DATE\":\"2023-12-29\",\"REQMEMBER_CODE\":\"11150101\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115663755\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"20.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115663753\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"内蒙古自治区烟草公司呼和浩特市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111016572\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115663753\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-29 16:02:31\"},{\"REQREGION\":\"河北省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"程连国\",\"ORDER_DATE\":\"2023-12-24\",\"REQMEMBER_CODE\":\"11131001\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115683986\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"-10.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115683984\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"河北省烟草公司廊坊市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111012077\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115683984\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:05:35\"},{\"REQREGION\":\"河北省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"杨彦辉\",\"ORDER_DATE\":\"2023-12-24\",\"REQMEMBER_CODE\":\"11130601\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115683991\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"-20.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115683988\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115683992\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"ADJ_QTY\":\"5.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115683988\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"河北省烟草公司保定市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111012139\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115683988\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:05:35\"},{\"REQREGION\":\"大连市\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"姜楠\",\"ORDER_DATE\":\"2023-12-29\",\"REQMEMBER_CODE\":\"11210201\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115684142\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"10.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684140\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"}],\"REQMEMBER_NAME\":\"中国烟草总公司大连市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111011223\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115684140\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:08:36\"},{\"REQREGION\":\"云南省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"张静\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11532601\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115684439\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"-6.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684437\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"云南省烟草公司文山州公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111012629\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115684437\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:11:38\"},{\"REQREGION\":\"山东省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"李勇\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11370601\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115684444\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"20.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684441\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115684445\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"ADJ_QTY\":\"-10.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684441\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"山东烟台烟草有限公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013297\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115684441\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:11:38\"},{\"REQREGION\":\"江苏省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"欧宇婷\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11320701\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115684637\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"ADJ_QTY\":\"200.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684635\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"江苏省烟草公司连云港市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111012878\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115684635\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:14:40\"},{\"REQREGION\":\"安徽省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"张帆\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11340101\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115684909\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"15.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684906\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115684910\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"-5.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115684906\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"安徽省烟草公司合肥市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111012636\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115684906\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:17:43\"},{\"REQREGION\":\"云南省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"杨树\",\"ORDER_DATE\":\"2023-12-29\",\"REQMEMBER_CODE\":\"11533501\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115687620\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"-10.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687617\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115687621\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"ADJ_QTY\":\"10.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687617\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"云南省烟草公司临沧市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111010415\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115687617\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:55:02\"},{\"REQREGION\":\"安徽省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"尹飞\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11341301\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115687661\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"10.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687657\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"12212.50000000\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(汾清香)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203593\",\"ROW_PK\":\"115687662\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"15.********\",\"PK_TRADECIGARETTE\":\"TC690102820360920420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687657\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"3\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115687663\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"ADJ_QTY\":\"30.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687657\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"安徽省烟草公司宿州市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013360\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115687657\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:55:03\"},{\"REQREGION\":\"安徽省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"李云龙\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11341701\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115687667\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"0.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687665\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"}],\"REQMEMBER_NAME\":\"安徽省烟草公司池州市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013469\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115687665\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:55:03\"},{\"REQREGION\":\"安徽省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"吴正栋\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11341001\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115687671\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"ADJ_QTY\":\"-5.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115687669\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"安徽省烟草公司黄山市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013495\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115687669\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 15:55:03\"},{\"REQREGION\":\"云南省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"沈昕\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11530101\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115691610\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"-5.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115691608\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"}],\"REQMEMBER_NAME\":\"云南省烟草公司昆明市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013756\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115691608\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 16:49:59\"},{\"REQREGION\":\"云南省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"赵俊娴\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11530501\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"6137.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(吉祥天下)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203449\",\"ROW_PK\":\"115691624\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"5.********\",\"PK_TRADECIGARETTE\":\"TC690102820345620420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115691622\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"云南省烟草公司保山市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013986\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115691622\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 16:49:59\"},{\"REQREGION\":\"安徽省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"王军\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"11340501\",\"DETAIL\":[{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"7938.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"一类\",\"PRODUCT_NAME\":\"紫气东来(祥瑞)\",\"CGTPRTYPENAME\":\"一类\",\"PRODUCT_CODE\":\"6901028203951\",\"ROW_PK\":\"115694609\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"紫气东来\",\"ADJ_QTY\":\"0.********\",\"PK_TRADECIGARETTE\":\"TC690102820394420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115694606\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"70.********\"},{\"PROV_TYPE\":\"1\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3441.********\",\"CROWNO\":\"2\",\"CGTPACKTYPENAME\":\"三类\",\"PRODUCT_NAME\":\"云烟(福)\",\"CGTPRTYPENAME\":\"三类\",\"PRODUCT_CODE\":\"6901028310987\",\"ROW_PK\":\"115694610\",\"REGIONNAME\":\"山东省\",\"BRAND_NAME\":\"云烟\",\"ADJ_QTY\":\"5.********\",\"PK_TRADECIGARETTE\":\"TC690102831099420420001002023H101\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"115694606\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"59.********\"}],\"REQMEMBER_NAME\":\"安徽省烟草公司马鞍山市公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111013639\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115694606\",\"CANCEL_TIME\":null,\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 17:56:23\"}]} \n";
                break;
            case PROTOCOLCANCELQRY:
                // 协议解除
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"PAGEINDEX\":\"1\",\"DATA\":[{\"REQREGION\":\"山东省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"山东省\",\"DEPUTY_NAME\":\"韩建鹏\",\"ORDER_DATE\":\"2023-12-25\",\"REQMEMBER_CODE\":\"********\",\"REQMEMBER_NAME\":\"山东济南烟草有限公司\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"111014092\",\"AUDIT_STATUS\":\"2\",\"SUPDEPUTY_NAME\":\"王睿\",\"PK\":\"115694612\",\"CANCEL_TIME\":\"2023-12-25 17:56:24\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_NAME\":\"湖北中烟有限责任公司\",\"FINALQTOTAL\":1000,\"SUPMEMBER_CODE\":\"20420001\",\"AUDIT_TIME\":\"2023-12-25 17:56:23\"}],\"PAGESIZE\":\"20\",\"RECORDCOUNT\":\"1\"}";
                break;
            case PROTOCOLCTRLQRY:
                // 最终协议量  没有调过空的
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"PAGEINDEX\":\"1\",\"RECORDCOUNT\":\"15\",\"DATA\":[{\"CYCLE_CODE\":\"2023H1\",\"PROTOCOLNO\":\"1001230627000020651\",\"REQMEMBER_CODE\":\"********\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"BUSI_TYPE\":\"2\",\"QTY\":127,\"PROTQTY\":\"231\",\"CGTPRTYPENAME\":\"一类\",\"BRAND_NAME\":\"品牌\",\"IS_LOW_PRICE\":\"0\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"PRODUCT_NAME\":\"甘肃烟草工业有限责任公司\",\"PRODUCT_CODE\":\"********\",\"DEADLINE_TIME\":\"2023-06-2715:55:00\"},{\"CYCLE_CODE\":\"2023H1\",\"PROTOCOLNO\":\"1001230627000010643\",\"REQMEMBER_CODE\":\"********\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"BUSI_TYPE\":\"1\",\"QTY\":121,\"PROTQTY\":\"221\",\"CGTPRTYPENAME\":\"二类\",\"BRAND_NAME\":\"品牌\",\"IS_LOW_PRICE\":\"1\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"PRODUCT_NAME\":\"甘肃烟草工业有限责任公司\",\"PRODUCT_CODE\":\"********\",\"DEADLINE_TIME\":\"2023-06-27 15:55:07\"}]}";
                break;
            case APITRADECGTQRY:
                // 交易目录
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"DATA\":[{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010282126251031000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"0001YE1********0XREI\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"0001YE1********0T32I\",\"BRANCH_QTY\":\"0\",\"BOX_CODE\":\"6901028212625\",\"PACKAGE_CODE\":\"6901028212601\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212618\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"50.********\",\"BAR_CODE\":\"6901028212618\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"中华(金短支)\",\"CGTTOTAL_LENGTH\":\"75.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010282125331031000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"XM\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1049\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"0001YE1********0XTPQ\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"0001YE1********0T2AR\",\"BRANCH_QTY\":\"0\",\"BOX_CODE\":\"6901028212533\",\"PACKAGE_CODE\":\"6901028212519\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212526\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"54.********\",\"BAR_CODE\":\"6901028212526\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"熊猫\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"熊猫(典藏中支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010282123281131000100\",\"CGT_TARCONTENT\":\"8.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0462\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"690102821232811310001002023H101\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"P6901028212328113100010001\",\"BRANCH_QTY\":null,\"BOX_CODE\":\"6901028212328\",\"PACKAGE_CODE\":null,\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212311\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028212311\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"恒大\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"恒大(蓝金中支)\",\"CGTTOTAL_LENGTH\":\"89.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280181421131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0462\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801814211310001002023H101\",\"CGT_GIRTH\":\"24.35000000\",\"PK_TRADEPRODUCT\":\"P6901028018142113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018142\",\"PACKAGE_CODE\":\"6901028018128\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018135\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018135\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"恒大\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"恒大(记忆1949)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.80000000\",\"GJJ_CODE\":\"69010280182341131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"DQM\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0278\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801823411310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018234113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018234\",\"PACKAGE_CODE\":\"6901028018210\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018227\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"50.********\",\"BAR_CODE\":\"6901028018227\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"大前门\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"大前门(短支)\",\"CGTTOTAL_LENGTH\":\"75.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280182961131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"XM\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1049\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801829611310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018296113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018296\",\"PACKAGE_CODE\":\"6901028018272\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018289\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"54.********\",\"BAR_CODE\":\"6901028018289\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"熊猫\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"熊猫(硬经典)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.60000000\",\"GJJ_CODE\":\"69010280183571131000100\",\"CGT_TARCONTENT\":\"6.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801835711310001002023H101\",\"CGT_GIRTH\":\"17.********\",\"PK_TRADEPRODUCT\":\"P6901028018357113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018357\",\"PACKAGE_CODE\":\"6901028018333\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018340\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"67.********\",\"BAR_CODE\":\"6901028018340\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"牡丹(金细支)\",\"CGTTOTAL_LENGTH\":\"97.********\"},{\"CGT_NICOTINIC\":\"0.80000000\",\"GJJ_CODE\":\"69010280183881131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801838811310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018388113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018388\",\"PACKAGE_CODE\":\"6901028018364\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018371\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018371\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬江山精品)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280184181131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801841811310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018418113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018418\",\"PACKAGE_CODE\":\"6901028018395\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018401\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018401\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬江山珍品)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280184491131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801844911310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018449113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018449\",\"PACKAGE_CODE\":\"6901028018425\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018432\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018432\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬上海)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280186471131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801864711310001002023H101\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"P6901028018647113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018647\",\"PACKAGE_CODE\":\"6901028018623\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018630\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018630\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"中华(金中支)\",\"CGTTOTAL_LENGTH\":\"89.********\"},{\"CGT_NICOTINIC\":\"1.10000000\",\"GJJ_CODE\":\"69010280186781131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801867811310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018678113100010001\",\"BRANCH_QTY\":\"120\",\"BOX_CODE\":\"6901028018678\",\"PACKAGE_CODE\":\"6901028018654\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018661\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"62.********\",\"BAR_CODE\":\"6901028018661\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"22.********\",\"CIGARETTE_NAME\":\"中华(硬12支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.10000000\",\"GJJ_CODE\":\"69010280187081131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801870811310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018708113100010001\",\"BRANCH_QTY\":\"50\",\"BOX_CODE\":\"6901028018708\",\"PACKAGE_CODE\":\"6901028018685\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018692\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"62.********\",\"BAR_CODE\":\"6901028018692\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"22.********\",\"CIGARETTE_NAME\":\"中华(硬5支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280187391131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801873911310001002023H101\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"P6901028018739113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018739\",\"PACKAGE_CODE\":\"6901028018715\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018722\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018722\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"中华(双中支)\",\"CGTTOTAL_LENGTH\":\"89.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010280187601131000100\",\"CGT_TARCONTENT\":\"8.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0462\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801876011310001002023H101\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"P6901028018760113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018760\",\"PACKAGE_CODE\":\"6901028018746\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018753\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018753\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"恒大\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"恒大(烟魁1949中支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.60000000\",\"GJJ_CODE\":\"69010280188451131000100\",\"CGT_TARCONTENT\":\"6.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801884511310001002023H101\",\"CGT_GIRTH\":\"17.********\",\"PK_TRADEPRODUCT\":\"P6901028018845113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018845\",\"PACKAGE_CODE\":\"6901028018777\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018791\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"67.********\",\"BAR_CODE\":\"6901028018791\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"牡丹(青柠细支)\",\"CGTTOTAL_LENGTH\":\"97.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280188831131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801888311310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018883113100010001\",\"BRANCH_QTY\":\"100\",\"BOX_CODE\":\"6901028018883\",\"PACKAGE_CODE\":\"6901028018869\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"05\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018876\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"62.********\",\"BAR_CODE\":\"6901028018876\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"22.********\",\"CIGARETTE_NAME\":\"中华(听50支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280189131131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"XM\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1049\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801891311310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018913113100010001\",\"BRANCH_QTY\":\"100\",\"BOX_CODE\":\"6901028018913\",\"PACKAGE_CODE\":\"6901028018890\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"05\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018906\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"54.********\",\"BAR_CODE\":\"6901028018906\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"熊猫\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"熊猫(听50支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280189751131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801897511310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018975113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018975\",\"PACKAGE_CODE\":\"6901028018951\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018968\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"50.********\",\"BAR_CODE\":\"6901028018968\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"中华(金短支)\",\"CGTTOTAL_LENGTH\":\"75.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280735781131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807357811310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028073578113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028073578\",\"PACKAGE_CODE\":null,\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028073561\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028073561\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬晶派)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280738131131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0462\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807381311310001002023H101\",\"CGT_GIRTH\":\"24.35000000\",\"PK_TRADEPRODUCT\":\"P6901028073813113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028073813\",\"PACKAGE_CODE\":\"6901028073790\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028073806\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028073806\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"恒大\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"恒大(全开式烟魁)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.80000000\",\"GJJ_CODE\":\"69010280740631131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807406311310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028074063113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028074063\",\"PACKAGE_CODE\":\"6901028074018\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028074025\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028074025\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"牡丹(软蓝)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010280748581131000100\",\"CGT_TARCONTENT\":\"8.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0462\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807485811310001002023H101\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"P6901028074858113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028074858\",\"PACKAGE_CODE\":\"6901028074834\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028074841\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028074841\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"恒大\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"恒大(记忆1949中支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280751211131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807512111310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028075121113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028075121\",\"PACKAGE_CODE\":\"6901028075763\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075015\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028075015\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"中华(硬)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280752061131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807520611310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028075206113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028075206\",\"PACKAGE_CODE\":\"6901028075862\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075053\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028075053\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"牡丹(软)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280752511131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807525111310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028075251113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028075251\",\"PACKAGE_CODE\":\"6901028075800\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075084\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028075084\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010280752751131000100\",\"CGT_TARCONTENT\":\"8.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807527511310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028075275113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028075275\",\"PACKAGE_CODE\":\"6901028075831\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075145\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028075145\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬8mg)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.20000000\",\"GJJ_CODE\":\"69010280754591131000100\",\"CGT_TARCONTENT\":\"13.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807545911310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028075459113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028075459\",\"PACKAGE_CODE\":\"6901028075718\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"08\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075725\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"62.********\",\"BAR_CODE\":\"6901028075725\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"22.********\",\"CIGARETTE_NAME\":\"中华(全开式)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":null,\"GJJ_CODE\":null,\"CGT_TARCONTENT\":null,\"CGT_TYPE_NAME\":null,\"MEMORY_CODE\":null,\"YEAR\":\"2023\",\"BRAND_CODE\":null,\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807573211310001002023H101\",\"CGT_GIRTH\":null,\"PK_TRADEPRODUCT\":\"P6901028075732113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":null,\"PACKAGE_CODE\":null,\"CGT_TYPE_CODE\":null,\"CGT_PACKTYPE_CODE\":null,\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075473\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":null,\"BAR_CODE\":null,\"IS_IMPORT\":\"N\",\"REGION_NAME\":null,\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":null,\"CGTFILTER_LENGTH\":null,\"CIGARETTE_NAME\":\"大前门(硬)\",\"CGTTOTAL_LENGTH\":null},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280757871131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807578711310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028075787113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028075787\",\"PACKAGE_CODE\":\"6901028075770\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075022\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"62.********\",\"BAR_CODE\":\"6901028075022\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"22.********\",\"CIGARETTE_NAME\":\"中华(软)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280761041131000100\",\"CGT_TARCONTENT\":\"12.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"DQM\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0278\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807610411310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028076104113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028076104\",\"PACKAGE_CODE\":\"6901028075916\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075138\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028075138\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"大前门\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"大前门(软)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.10000000\",\"GJJ_CODE\":\"69010280761281131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807612811310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028076128113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028076128\",\"PACKAGE_CODE\":\"6901028075992\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028076005\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028076005\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬百顺)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280763261131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MFS\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0100\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807632611310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028076326113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028076326\",\"PACKAGE_CODE\":\"6901028076302\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028076319\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028076319\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"孟菲斯\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"孟菲斯(硬红)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280763571131000100\",\"CGT_TARCONTENT\":\"9.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MFS\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0100\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807635711310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028076357113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028076357\",\"PACKAGE_CODE\":\"6901028076333\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028076340\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028076340\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"孟菲斯\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"孟菲斯(硬蓝)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280765791131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807657911310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028076579113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028076579\",\"PACKAGE_CODE\":\"6901028076555\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028076562\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"50.********\",\"BAR_CODE\":\"6901028076562\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"牡丹(金短支)\",\"CGTTOTAL_LENGTH\":\"75.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010282121441131000100\",\"CGT_TARCONTENT\":\"8.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102821214411310001002023H101\",\"CGT_GIRTH\":\"17.********\",\"PK_TRADEPRODUCT\":\"P6901028212144113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028212144\",\"PACKAGE_CODE\":\"6901028212120\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212137\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"67.********\",\"BAR_CODE\":\"6901028212137\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"中华(金细支)\",\"CGTTOTAL_LENGTH\":\"97.********\"},{\"CGT_NICOTINIC\":\"0.80000000\",\"GJJ_CODE\":\"69010282121751131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102821217511310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028212175113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028212175\",\"PACKAGE_CODE\":\"6901028212151\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212168\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"54.********\",\"BAR_CODE\":\"6901028212168\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"牡丹(飞马)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.80000000\",\"GJJ_CODE\":\"69010282122051131000100\",\"CGT_TARCONTENT\":\"9.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102821220511310001002023H101\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"P6901028212205113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028212205\",\"PACKAGE_CODE\":\"6901028212182\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212199\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028212199\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"牡丹(蓝中支)\",\"CGTTOTAL_LENGTH\":\"89.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010282122361131000100\",\"CGT_TARCONTENT\":\"8.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102821223611310001002023H101\",\"CGT_GIRTH\":\"17.********\",\"PK_TRADEPRODUCT\":\"P6901028212236113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028212236\",\"PACKAGE_CODE\":\"6901028212212\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212229\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"67.********\",\"BAR_CODE\":\"6901028212229\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"中华(细支)\",\"CGTTOTAL_LENGTH\":\"97.********\"}]}";
                break;
            case APITRADECGTWITHPRICEQRY:
                // 交易目录含价格
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"DATA\":[{\"SALE_PRICE\":\"100.90000000\",\"TRANS_PRICE\":\"102.90000000\",\"TRANS_W_PRICE\":\"104.90000000\",\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010282126251031000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"0001YE1********0XREI\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"0001YE1********0T32I\",\"BRANCH_QTY\":\"0\",\"BOX_CODE\":\"6901028212625\",\"PACKAGE_CODE\":\"6901028212601\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212618\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"50.********\",\"BAR_CODE\":\"6901028212618\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"中华(金短支)\",\"CGTTOTAL_LENGTH\":\"75.********\"},{\"SALE_PRICE\":\"100.90000000\",\"TRANS_PRICE\":\"102.90000000\",\"TRANS_W_PRICE\":\"104.90000000\",\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010282125331031000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"XM\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1049\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"0001YE1********0XTPQ\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"0001YE1********0T2AR\",\"BRANCH_QTY\":\"0\",\"BOX_CODE\":\"6901028212533\",\"PACKAGE_CODE\":\"6901028212519\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212526\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"54.********\",\"BAR_CODE\":\"6901028212526\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"熊猫\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"熊猫(典藏中支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"SALE_PRICE\":\"108.90000000\",\"TRANS_PRICE\":\"106.90000000\",\"TRANS_W_PRICE\":\"104.90000000\",\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010282123281131000100\",\"CGT_TARCONTENT\":\"8.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0462\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"690102821232811310001002023H101\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"P6901028212328113100010001\",\"BRANCH_QTY\":null,\"BOX_CODE\":\"6901028212328\",\"PACKAGE_CODE\":null,\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212311\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028212311\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"恒大\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"恒大(蓝金中支)\",\"CGTTOTAL_LENGTH\":\"89.********\"},{\"SALE_PRICE\":\"100.90000000\",\"TRANS_PRICE\":\"102.90000000\",\"TRANS_W_PRICE\":\"104.90000000\",\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280181421131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0462\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801814211310001002023H101\",\"CGT_GIRTH\":\"24.35000000\",\"PK_TRADEPRODUCT\":\"P6901028018142113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018142\",\"PACKAGE_CODE\":\"6901028018128\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018135\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018135\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"恒大\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"恒大(记忆1949)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"SALE_PRICE\":\"101.90000000\",\"TRANS_PRICE\":\"103.90000000\",\"TRANS_W_PRICE\":\"105.90000000\",\"CGT_NICOTINIC\":\"0.80000000\",\"GJJ_CODE\":\"69010280182341131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"DQM\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0278\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801823411310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018234113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018234\",\"PACKAGE_CODE\":\"6901028018210\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018227\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"50.********\",\"BAR_CODE\":\"6901028018227\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"大前门\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"大前门(短支)\",\"CGTTOTAL_LENGTH\":\"75.********\"}]}";
                break;
            case TRADEPRODUCT:
                // 产品目录
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"DATA\":[{\"CGT_NICOTINIC\":\"0.80000000\",\"GJJ_CODE\":\"69010282126251031000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"0001YE1********0XREI\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"0001YE1********0T32I\",\"BRANCH_QTY\":\"0\",\"BOX_CODE\":\"6901028212625\",\"PACKAGE_CODE\":\"6901028212601\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212618\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"50.********\",\"BAR_CODE\":\"6901028212618\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"中华(金短支)\",\"CGTTOTAL_LENGTH\":\"75.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010282125331031000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"XM\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1049\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"0001YE1********0XTPQ\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"0001YE1********0T2AR\",\"BRANCH_QTY\":\"0\",\"BOX_CODE\":\"6901028212533\",\"PACKAGE_CODE\":\"6901028212519\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212526\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"54.********\",\"BAR_CODE\":\"6901028212526\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"熊猫\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"熊猫(典藏中支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010282123281131000100\",\"CGT_TARCONTENT\":\"8.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0462\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"690102821232811310001002023H101\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"P6901028212328113100010001\",\"BRANCH_QTY\":null,\"BOX_CODE\":\"6901028212328\",\"PACKAGE_CODE\":null,\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212311\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028212311\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"恒大\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"恒大(蓝金中支)\",\"CGTTOTAL_LENGTH\":\"89.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280181421131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0462\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801814211310001002023H101\",\"CGT_GIRTH\":\"24.35000000\",\"PK_TRADEPRODUCT\":\"P6901028018142113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018142\",\"PACKAGE_CODE\":\"6901028018128\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018135\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018135\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"恒大\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"恒大(记忆1949)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.80000000\",\"GJJ_CODE\":\"69010280182341131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"DQM\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0278\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801823411310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018234113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018234\",\"PACKAGE_CODE\":\"6901028018210\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018227\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"50.********\",\"BAR_CODE\":\"6901028018227\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"大前门\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"大前门(短支)\",\"CGTTOTAL_LENGTH\":\"75.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280182961131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"XM\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1049\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801829611310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018296113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018296\",\"PACKAGE_CODE\":\"6901028018272\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018289\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"54.********\",\"BAR_CODE\":\"6901028018289\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"熊猫\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"熊猫(硬经典)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.60000000\",\"GJJ_CODE\":\"69010280183571131000100\",\"CGT_TARCONTENT\":\"6.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801835711310001002023H101\",\"CGT_GIRTH\":\"17.********\",\"PK_TRADEPRODUCT\":\"P6901028018357113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018357\",\"PACKAGE_CODE\":\"6901028018333\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018340\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"67.********\",\"BAR_CODE\":\"6901028018340\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"牡丹(金细支)\",\"CGTTOTAL_LENGTH\":\"97.********\"},{\"CGT_NICOTINIC\":\"0.80000000\",\"GJJ_CODE\":\"69010280183881131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801838811310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018388113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018388\",\"PACKAGE_CODE\":\"6901028018364\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018371\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018371\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬江山精品)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280184181131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801841811310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018418113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018418\",\"PACKAGE_CODE\":\"6901028018395\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018401\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018401\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬江山珍品)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280184491131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801844911310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018449113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018449\",\"PACKAGE_CODE\":\"6901028018425\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018432\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018432\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬上海)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280186471131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801864711310001002023H101\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"P6901028018647113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018647\",\"PACKAGE_CODE\":\"6901028018623\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018630\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018630\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"中华(金中支)\",\"CGTTOTAL_LENGTH\":\"89.********\"},{\"CGT_NICOTINIC\":\"1.10000000\",\"GJJ_CODE\":\"69010280186781131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801867811310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018678113100010001\",\"BRANCH_QTY\":\"120\",\"BOX_CODE\":\"6901028018678\",\"PACKAGE_CODE\":\"6901028018654\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018661\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"62.********\",\"BAR_CODE\":\"6901028018661\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"22.********\",\"CIGARETTE_NAME\":\"中华(硬12支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.10000000\",\"GJJ_CODE\":\"69010280187081131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801870811310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018708113100010001\",\"BRANCH_QTY\":\"50\",\"BOX_CODE\":\"6901028018708\",\"PACKAGE_CODE\":\"6901028018685\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018692\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"62.********\",\"BAR_CODE\":\"6901028018692\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"22.********\",\"CIGARETTE_NAME\":\"中华(硬5支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280187391131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801873911310001002023H101\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"P6901028018739113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018739\",\"PACKAGE_CODE\":\"6901028018715\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018722\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018722\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"中华(双中支)\",\"CGTTOTAL_LENGTH\":\"89.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010280187601131000100\",\"CGT_TARCONTENT\":\"8.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0462\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801876011310001002023H101\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"P6901028018760113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018760\",\"PACKAGE_CODE\":\"6901028018746\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018753\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028018753\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"恒大\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"恒大(烟魁1949中支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.60000000\",\"GJJ_CODE\":\"69010280188451131000100\",\"CGT_TARCONTENT\":\"6.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801884511310001002023H101\",\"CGT_GIRTH\":\"17.********\",\"PK_TRADEPRODUCT\":\"P6901028018845113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018845\",\"PACKAGE_CODE\":\"6901028018777\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018791\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"67.********\",\"BAR_CODE\":\"6901028018791\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"牡丹(青柠细支)\",\"CGTTOTAL_LENGTH\":\"97.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280188831131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801888311310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018883113100010001\",\"BRANCH_QTY\":\"100\",\"BOX_CODE\":\"6901028018883\",\"PACKAGE_CODE\":\"6901028018869\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"05\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018876\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"62.********\",\"BAR_CODE\":\"6901028018876\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"22.********\",\"CIGARETTE_NAME\":\"中华(听50支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280189131131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"XM\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1049\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801891311310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018913113100010001\",\"BRANCH_QTY\":\"100\",\"BOX_CODE\":\"6901028018913\",\"PACKAGE_CODE\":\"6901028018890\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"05\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018906\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"54.********\",\"BAR_CODE\":\"6901028018906\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"熊猫\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"熊猫(听50支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280189751131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102801897511310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028018975113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028018975\",\"PACKAGE_CODE\":\"6901028018951\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028018968\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"50.********\",\"BAR_CODE\":\"6901028018968\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"中华(金短支)\",\"CGTTOTAL_LENGTH\":\"75.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280735781131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807357811310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028073578113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028073578\",\"PACKAGE_CODE\":null,\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028073561\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028073561\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬晶派)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280738131131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0462\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807381311310001002023H101\",\"CGT_GIRTH\":\"24.35000000\",\"PK_TRADEPRODUCT\":\"P6901028073813113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028073813\",\"PACKAGE_CODE\":\"6901028073790\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028073806\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028073806\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"恒大\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"恒大(全开式烟魁)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.80000000\",\"GJJ_CODE\":\"69010280740631131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807406311310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028074063113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028074063\",\"PACKAGE_CODE\":\"6901028074018\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028074025\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028074025\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"牡丹(软蓝)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010280748581131000100\",\"CGT_TARCONTENT\":\"8.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0462\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807485811310001002023H101\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"P6901028074858113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028074858\",\"PACKAGE_CODE\":\"6901028074834\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028074841\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028074841\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"恒大\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"恒大(记忆1949中支)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280751211131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807512111310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028075121113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028075121\",\"PACKAGE_CODE\":\"6901028075763\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075015\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028075015\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"中华(硬)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280752061131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807520611310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028075206113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028075206\",\"PACKAGE_CODE\":\"6901028075862\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075053\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028075053\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"牡丹(软)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280752511131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807525111310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028075251113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028075251\",\"PACKAGE_CODE\":\"6901028075800\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075084\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028075084\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010280752751131000100\",\"CGT_TARCONTENT\":\"8.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807527511310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028075275113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028075275\",\"PACKAGE_CODE\":\"6901028075831\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075145\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028075145\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬8mg)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.20000000\",\"GJJ_CODE\":\"69010280754591131000100\",\"CGT_TARCONTENT\":\"13.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807545911310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028075459113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028075459\",\"PACKAGE_CODE\":\"6901028075718\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"08\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075725\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"62.********\",\"BAR_CODE\":\"6901028075725\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"22.********\",\"CIGARETTE_NAME\":\"中华(全开式)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":null,\"GJJ_CODE\":null,\"CGT_TARCONTENT\":null,\"CGT_TYPE_NAME\":null,\"MEMORY_CODE\":null,\"YEAR\":\"2023\",\"BRAND_CODE\":null,\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807573211310001002023H101\",\"CGT_GIRTH\":null,\"PK_TRADEPRODUCT\":\"P6901028075732113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":null,\"PACKAGE_CODE\":null,\"CGT_TYPE_CODE\":null,\"CGT_PACKTYPE_CODE\":null,\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075473\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":null,\"BAR_CODE\":null,\"IS_IMPORT\":\"N\",\"REGION_NAME\":null,\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":null,\"CGTFILTER_LENGTH\":null,\"CIGARETTE_NAME\":\"大前门(硬)\",\"CGTTOTAL_LENGTH\":null},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280757871131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807578711310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028075787113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028075787\",\"PACKAGE_CODE\":\"6901028075770\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075022\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"62.********\",\"BAR_CODE\":\"6901028075022\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"22.********\",\"CIGARETTE_NAME\":\"中华(软)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280761041131000100\",\"CGT_TARCONTENT\":\"12.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"DQM\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0278\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807610411310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028076104113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028076104\",\"PACKAGE_CODE\":\"6901028075916\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028075138\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028075138\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"大前门\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"大前门(软)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.10000000\",\"GJJ_CODE\":\"69010280761281131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"HSX\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0494\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807612811310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028076128113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028076128\",\"PACKAGE_CODE\":\"6901028075992\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028076005\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028076005\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"红双喜(上海)\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"红双喜(硬百顺)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"1.********\",\"GJJ_CODE\":\"69010280763261131000100\",\"CGT_TARCONTENT\":\"11.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MFS\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0100\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807632611310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028076326113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028076326\",\"PACKAGE_CODE\":\"6901028076302\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028076319\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028076319\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"孟菲斯\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"孟菲斯(硬红)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280763571131000100\",\"CGT_TARCONTENT\":\"9.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MFS\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"0100\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807635711310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028076357113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028076357\",\"PACKAGE_CODE\":\"6901028076333\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028076340\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028076340\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"孟菲斯\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"孟菲斯(硬蓝)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.90000000\",\"GJJ_CODE\":\"69010280765791131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102807657911310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028076579113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028076579\",\"PACKAGE_CODE\":\"6901028076555\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"02\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028076562\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"50.********\",\"BAR_CODE\":\"6901028076562\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"25.********\",\"CIGARETTE_NAME\":\"牡丹(金短支)\",\"CGTTOTAL_LENGTH\":\"75.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010282121441131000100\",\"CGT_TARCONTENT\":\"8.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102821214411310001002023H101\",\"CGT_GIRTH\":\"17.********\",\"PK_TRADEPRODUCT\":\"P6901028212144113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028212144\",\"PACKAGE_CODE\":\"6901028212120\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212137\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"67.********\",\"BAR_CODE\":\"6901028212137\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"中华(金细支)\",\"CGTTOTAL_LENGTH\":\"97.********\"},{\"CGT_NICOTINIC\":\"0.80000000\",\"GJJ_CODE\":\"69010282121751131000100\",\"CGT_TARCONTENT\":\"10.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102821217511310001002023H101\",\"CGT_GIRTH\":\"24.50000000\",\"PK_TRADEPRODUCT\":\"P6901028212175113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028212175\",\"PACKAGE_CODE\":\"6901028212151\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212168\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"54.********\",\"BAR_CODE\":\"6901028212168\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"牡丹(飞马)\",\"CGTTOTAL_LENGTH\":\"84.********\"},{\"CGT_NICOTINIC\":\"0.80000000\",\"GJJ_CODE\":\"69010282122051131000100\",\"CGT_TARCONTENT\":\"9.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"MD\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1213\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102821220511310001002023H101\",\"CGT_GIRTH\":\"20.********\",\"PK_TRADEPRODUCT\":\"P6901028212205113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028212205\",\"PACKAGE_CODE\":\"6901028212182\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212199\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"59.********\",\"BAR_CODE\":\"6901028212199\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"牡丹\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"牡丹(蓝中支)\",\"CGTTOTAL_LENGTH\":\"89.********\"},{\"CGT_NICOTINIC\":\"0.70000000\",\"GJJ_CODE\":\"69010282122361131000100\",\"CGT_TARCONTENT\":\"8.********\",\"CGT_TYPE_NAME\":\"烤烟型\",\"MEMORY_CODE\":\"ZH\",\"YEAR\":\"2023\",\"BRAND_CODE\":\"1157\",\"MARK_ORG_CODE\":\"10310001\",\"PK_TRADECIGARETTE\":\"TC690102821223611310001002023H101\",\"CGT_GIRTH\":\"17.********\",\"PK_TRADEPRODUCT\":\"P6901028212236113100010001\",\"BRANCH_QTY\":\"200\",\"BOX_CODE\":\"6901028212236\",\"PACKAGE_CODE\":\"6901028212212\",\"CGT_TYPE_CODE\":\"02\",\"CGT_PACKTYPE_CODE\":\"01\",\"ORG_NAME\":\"上海烟草集团有限责任公司\",\"REGION_CODE\":null,\"CIGARETTE_CODE\":\"6901028212229\",\"DEXPDATE\":\"2023-12-31\",\"MARK_ORG_NAME\":\"上海烟草集团有限责任公司\",\"VCYCLETYPE\":\"ALL\",\"CGT_LENGTH\":\"67.********\",\"BAR_CODE\":\"6901028212229\",\"IS_IMPORT\":\"N\",\"REGION_NAME\":\"上海市\",\"IS_CIGAR\":\"N\",\"ORG_CODE\":\"11310001\",\"BRAND_NAME\":\"中华\",\"CGTFILTER_LENGTH\":\"30.********\",\"CIGARETTE_NAME\":\"中华(细支)\",\"CGTTOTAL_LENGTH\":\"97.********\"}]}";
                break;
            case MEMBERQRY:
                // 会员信息
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"DATA\":[{\"FINANCIAL_PSNNAME\":\"张晶嫣\",\"MEMBER_NAME\":\"中国烟草上海进出口有限责任公司\",\"FINANCIAL_PHONE\":\"021-61667055\",\"TELEPHONE\":\"61667120\",\"REGION_CODE\":\"310000\",\"POSTCODE\":\"200082\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"上海市\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"上海市杨浦区长阳路717号C楼516室\",\"ORG_PROPERTY\":\"4\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********04GLF\",\"MEMBER_CODE\":\"18310001\"},{\"FINANCIAL_PSNNAME\":\"郝国军\",\"MEMBER_NAME\":\"吉林烟草进出口有限责任公司\",\"FINANCIAL_PHONE\":\"0433-2853110\",\"TELEPHONE\":\"0433-2853090\",\"REGION_CODE\":\"220000\",\"POSTCODE\":\"133001\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"吉林省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"吉林省延吉市天池路79号\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********04GLH\",\"MEMBER_CODE\":\"18220001\"},{\"FINANCIAL_PSNNAME\":\"王琼林\",\"MEMBER_NAME\":\"深圳烟草进出口有限公司\",\"FINANCIAL_PHONE\":\"0755-25188680\",\"TELEPHONE\":\"0755-25188754 25188672\",\"REGION_CODE\":\"440000\",\"POSTCODE\":\"518001\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"广东省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"深圳市罗湖区滨河路1011号深城投中心9楼\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********04GLJ\",\"MEMBER_CODE\":\"18440301\"},{\"FINANCIAL_PSNNAME\":\"斯建国\",\"MEMBER_NAME\":\"浙江烟草进出口有限公司\",\"FINANCIAL_PHONE\":\"0571-87032785\",\"TELEPHONE\":\"0571-87017574 87032835\",\"REGION_CODE\":\"330000\",\"POSTCODE\":\"310001\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"浙江省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"浙江省杭州市浣纱路17号\",\"ORG_PROPERTY\":\"4\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":\"09-2-10\",\"PK_MEMBER\":\"0001YE1********04GLL\",\"MEMBER_CODE\":\"18330001\"},{\"FINANCIAL_PSNNAME\":\"蒋静然\",\"MEMBER_NAME\":\"中国烟草河南进出口有限责任公司\",\"FINANCIAL_PHONE\":\"0371-65583293\",\"TELEPHONE\":\"0371-65583281 65583277\",\"REGION_CODE\":\"410000\",\"POSTCODE\":\"450018\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"河南省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"河南自贸试验区郑州片区（郑东）商务外环15号12层\",\"ORG_PROPERTY\":\"4\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********04GLN\",\"MEMBER_CODE\":\"18410001\"},{\"FINANCIAL_PSNNAME\":\"张向民\",\"MEMBER_NAME\":\"中国烟草云南进出口有限公司\",\"FINANCIAL_PHONE\":\"0871-65127753\",\"TELEPHONE\":\"0871-65127675\",\"REGION_CODE\":\"530000\",\"POSTCODE\":\"650031\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"云南省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"昆明市圆通街35号\",\"ORG_PROPERTY\":\"4\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":\"09-2-10\",\"PK_MEMBER\":\"0001YE1********04GLP\",\"MEMBER_CODE\":\"18530001\"},{\"FINANCIAL_PSNNAME\":\"王成辉\",\"MEMBER_NAME\":\"湖南省烟草公司张家界市公司\",\"FINANCIAL_PHONE\":\"0744-8863207 18207440886\",\"TELEPHONE\":\"0744-8863168\",\"REGION_CODE\":\"430000\",\"POSTCODE\":\"427000\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"2\",\"REGION_NAME\":\"湖南省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"湖南省张家界市永定区紫舞西路且住岗段烟草新城\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7000911\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000422\",\"MEMBER_CODE\":\"11430801\"},{\"FINANCIAL_PSNNAME\":\"陈娟\",\"MEMBER_NAME\":\"湖北省烟草公司鄂州市公司\",\"FINANCIAL_PHONE\":\"0711-3870972 15586599956\",\"TELEPHONE\":\"0711-3870945\",\"REGION_CODE\":\"420000\",\"POSTCODE\":\"436000\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"2\",\"REGION_NAME\":\"湖北省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"湖北省鄂州市文星大道102号\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7000910\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000847\",\"MEMBER_CODE\":\"11420701\"},{\"FINANCIAL_PSNNAME\":\"苏丽\",\"MEMBER_NAME\":\"中国烟草山东进出口有限责任公司\",\"FINANCIAL_PHONE\":\"0532-83096065\",\"TELEPHONE\":\"0532-83096272\",\"REGION_CODE\":\"370000\",\"POSTCODE\":\"266071\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"山东省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"青岛市香港西路69号光大国际金融中心24层\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001124\",\"MEMBER_CODE\":\"18370001\"},{\"FINANCIAL_PSNNAME\":\"田茹云\",\"MEMBER_NAME\":\"中国烟草贵州进出口公司\",\"FINANCIAL_PHONE\":\"0851-88235320 13312429691\",\"TELEPHONE\":\"0851-5917078\",\"REGION_CODE\":\"520000\",\"POSTCODE\":\"550005\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"贵州省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"贵州省贵阳市云岩区威清路334号\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":\"09-2-10\",\"PK_MEMBER\":\"0001YE1000S8R7001140\",\"MEMBER_CODE\":\"18520001\"},{\"FINANCIAL_PSNNAME\":\"叶朕邵怡\",\"MEMBER_NAME\":\"江苏省烟草公司无锡市公司\",\"FINANCIAL_PHONE\":\"0510-85726689\",\"TELEPHONE\":\"0510-82104743 82104917\",\"REGION_CODE\":\"320000\",\"POSTCODE\":\"214023\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"2\",\"REGION_NAME\":\"江苏省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"江苏省无锡市运河东路588号\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7000450\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001177\",\"MEMBER_CODE\":\"11320201\"},{\"FINANCIAL_PSNNAME\":\"汪晶\",\"MEMBER_NAME\":\"湖北省烟草公司宜昌市公司\",\"FINANCIAL_PHONE\":\"0717-6265115\",\"TELEPHONE\":\"0717-6772888 6265178\",\"REGION_CODE\":\"420000\",\"POSTCODE\":\"443000\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"2\",\"REGION_NAME\":\"湖北省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"湖北省宜昌市沿江大道42号\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7000910\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001376\",\"MEMBER_CODE\":\"11420501\"},{\"FINANCIAL_PSNNAME\":\"杨洪滨\",\"MEMBER_NAME\":\"中国烟草福建进出口有限责任公司\",\"FINANCIAL_PHONE\":\"0592-5886519\",\"TELEPHONE\":\"0592-5161563\",\"REGION_CODE\":\"350000\",\"POSTCODE\":\"361009\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"福建省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"厦门市思明区湖滨南路357-359号海晟国际大厦19层\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":\"09-2-10\",\"PK_MEMBER\":\"0001YE1000S8R7001705\",\"MEMBER_CODE\":\"18350001\"},{\"FINANCIAL_PSNNAME\":\"秦晓艳\",\"MEMBER_NAME\":\"河南省烟草公司郑州市公司\",\"FINANCIAL_PHONE\":\"0371-86168089 861680921\",\"TELEPHONE\":\"0371-86168970 86168092\",\"REGION_CODE\":\"410000\",\"POSTCODE\":\"450016\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"2\",\"REGION_NAME\":\"河南省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"河南省郑州市经济技术开发区第三大街999号\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7000984\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001874\",\"MEMBER_CODE\":\"********\"},{\"FINANCIAL_PSNNAME\":\"张丽萍\",\"MEMBER_NAME\":\"陕西烟草进出口有限责任公司\",\"FINANCIAL_PHONE\":\"029-85466231 85466251\",\"TELEPHONE\":\"029-85466236\",\"REGION_CODE\":\"610000\",\"POSTCODE\":\"710061\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"陕西省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"西安市曲江新区雁南四路19号10楼\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001933\",\"MEMBER_CODE\":\"18610001\"},{\"FINANCIAL_PSNNAME\":\"张瑛\",\"MEMBER_NAME\":\"新疆烟草进出口有限责任公司\",\"FINANCIAL_PHONE\":\"0991-4815061\",\"TELEPHONE\":\"0991-4815253\",\"REGION_CODE\":\"650000\",\"POSTCODE\":\"830000\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"新疆区\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"乌鲁木齐市沙依巴克区巴州路258号\",\"ORG_PROPERTY\":\"4\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":\"09-2-10\",\"PK_MEMBER\":\"0001YE1000S8R7001935\",\"MEMBER_CODE\":\"18650001\"},{\"FINANCIAL_PSNNAME\":\"何燕青\",\"MEMBER_NAME\":\"中国烟草广东进出口有限公司\",\"FINANCIAL_PHONE\":\"020-35668491\",\"TELEPHONE\":\"020-35668476 35668478\",\"REGION_CODE\":\"440000\",\"POSTCODE\":\"510623\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"广东省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"广东省广州市天河区林和东路128号11楼\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001938\",\"MEMBER_CODE\":\"18440001\"},{\"FINANCIAL_PSNNAME\":\"张家铭\",\"MEMBER_NAME\":\"中国烟草黑龙江进出口有限责任公司\",\"FINANCIAL_PHONE\":\"0451-82708774\",\"TELEPHONE\":\"0451-82708744\",\"REGION_CODE\":\"230000\",\"POSTCODE\":\"150001\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"黑龙江省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"黑龙江省哈尔滨市南岗区中山路175号14-17层\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":\"09-2-10\",\"PK_MEMBER\":\"0001YE1000S8R7001940\",\"MEMBER_CODE\":\"18230001\"},{\"FINANCIAL_PSNNAME\":\"王韶兵\",\"MEMBER_NAME\":\"中国烟草湖南进出口有限责任公司\",\"FINANCIAL_PHONE\":\"0731-85799370 85566486\",\"TELEPHONE\":\"0731-85799383\",\"REGION_CODE\":\"430000\",\"POSTCODE\":\"410004\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"湖南省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"湖南省长沙市天心区芙蓉南路一段628号\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":\"01/8/7更名\",\"PK_MEMBER\":\"0001YE1000S8R7001941\",\"MEMBER_CODE\":\"18430001\"},{\"FINANCIAL_PSNNAME\":\"管颖\",\"MEMBER_NAME\":\"中国烟草辽宁进出口公司\",\"FINANCIAL_PHONE\":\"0411-82690175\",\"TELEPHONE\":\"0411-82690173\",\"REGION_CODE\":\"210000\",\"POSTCODE\":\"116001\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"辽宁省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"大连市中山区自立街35号\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001942\",\"MEMBER_CODE\":\"18210001\"},{\"FINANCIAL_PSNNAME\":\"肖苹\",\"MEMBER_NAME\":\"中国烟草四川进出口有限责任公司\",\"FINANCIAL_PHONE\":\"028-6743360\",\"TELEPHONE\":\"028-86162422 86162429\",\"REGION_CODE\":\"510000\",\"POSTCODE\":\"610041\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"四川省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"四川省成都市高新区世纪城路936号烟草兴业大厦29层\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":\"09-2-10\",\"PK_MEMBER\":\"0001YE1000S8R7001943\",\"MEMBER_CODE\":\"18510001\"},{\"FINANCIAL_PSNNAME\":\"荚葭\",\"MEMBER_NAME\":\"中国烟草湖北进出口有限责任公司\",\"FINANCIAL_PHONE\":\"027-83883109\",\"TELEPHONE\":\"027-85497020\",\"REGION_CODE\":\"420000\",\"POSTCODE\":\"430015\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"3\",\"REGION_NAME\":\"湖北省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"湖北省武汉市硚口区宝丰路6号香溢大酒店6楼\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001139\",\"REMARK\":\"09-2-10\",\"PK_MEMBER\":\"0001YE1000S8R7001955\",\"MEMBER_CODE\":\"18420001\"},{\"FINANCIAL_PSNNAME\":\"赵娇\",\"MEMBER_NAME\":\"云南省烟草公司曲靖市公司\",\"FINANCIAL_PHONE\":\"0874-3361961 15287823068\",\"TELEPHONE\":\"0874-3361961\",\"REGION_CODE\":\"530000\",\"POSTCODE\":\"655000\",\"LICENSE_CODE\":\"**********\",\"ORG_TYPE\":\"2\",\"REGION_NAME\":\"云南省\",\"ISTRADE\":\"Y\",\"ADDRESS\":\"云南省曲靖市麒麟区官坡巷51号\",\"ORG_PROPERTY\":\"5\",\"PARENTMEMBER_CODE\":\"0001YE1000S8R7001987\",\"REMARK\":\"2001.5.15改 工业\",\"PK_MEMBER\":\"0001YE1000S8R7001991\",\"MEMBER_CODE\":\"11530301\"}],\"RECORDCOUNT\":\"23\"}";
                break;
            case MEMBERDEPUTYQRY:
                // 会员代表
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"DATA\":[{\"MEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"EMPLOYEECODE\":\"1053230207000041975\",\"PK\":\"0001YE1********007TG\",\"PK_MEMBER\":\"0001YE1000S8R7001040\",\"MEMBER_CODE\":\"********\",\"EMPLOYEENAME\":\"李鹏\"},{\"MEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"EMPLOYEECODE\":\"1053230207000041979\",\"PK\":\"0001YE1********007TK\",\"PK_MEMBER\":\"0001YE1000S8R7001040\",\"MEMBER_CODE\":\"********\",\"EMPLOYEENAME\":\"唐蕾\"},{\"MEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"EMPLOYEECODE\":\"1053230207000041970\",\"PK\":\"0001YE1********007TN\",\"PK_MEMBER\":\"0001YE1000S8R7001040\",\"MEMBER_CODE\":\"********\",\"EMPLOYEENAME\":\"王强\"},{\"MEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"EMPLOYEECODE\":\"1053230207000041976\",\"PK\":\"0001YE1********007TQ\",\"PK_MEMBER\":\"0001YE1000S8R7001040\",\"MEMBER_CODE\":\"********\",\"EMPLOYEENAME\":\"马丽\"},{\"MEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"EMPLOYEECODE\":\"1053221024000010005\",\"PK\":\"0001YE1********00ILR\",\"PK_MEMBER\":\"0001YE1000S8R7001040\",\"MEMBER_CODE\":\"********\",\"EMPLOYEENAME\":\"薛剑冰\"}],\"RECORDCOUNT\":5}";
                break;
            case MEMBERWAREHOUSEQRY:
                // 会员仓库
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"DATA\":[{\"WAREHOUSENAME\":\"重庆中烟工业有限责任公司东港卷烟成品库\",\"MEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"REGION_CODE\":\"500100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"市辖区\",\"ADDRESS\":\"重庆市市辖区南岸区富源大道316号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08650010800033\",\"PK\":\"********074552ad888711eea11b55b4f740e0c7\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"浙江中烟与重庆中烟合作生产就地调拨业务\",\"PK_MEMBER\":\"0001YE1000S8R7001337\",\"MEMBER_CODE\":\"********\"},{\"WAREHOUSENAME\":\"浙江省烟草公司金华市公司卷烟配送中心库\",\"MEMBER_NAME\":\"内蒙古昆明卷烟有限责任公司\",\"REGION_CODE\":\"330700\",\"SEAL_DATE\":null,\"REGION_NAME\":\"金华市\",\"ADDRESS\":\"浙江省金华市金东区江东镇金武街238号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08633070300016\",\"PK\":\"********15f1503e596111eea0cb591754f6bde1\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001052\",\"MEMBER_CODE\":\"12150101\"},{\"WAREHOUSENAME\":\"浙江省烟草公司宁波市公司卷烟配送中心库\",\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REGION_CODE\":\"330200\",\"SEAL_DATE\":null,\"REGION_NAME\":\"宁波市\",\"ADDRESS\":\"浙江省宁波市海曙区科创南路199号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08633020300015\",\"PK\":\"********1c127371468511eeb0aef53353712458\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"WAREHOUSENAME\":\"浙江省烟草公司杭州市公司卷烟配送中心库\",\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REGION_CODE\":\"330100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"杭州市\",\"ADDRESS\":\"浙江省杭州市拱墅区莫干山路1418-52号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08633010500016\",\"PK\":\"********1c127372468511eeb0ae232c02dec694\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"WAREHOUSENAME\":\"浙江省烟草公司温州市公司卷烟配送中心库\",\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REGION_CODE\":\"330300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"温州市\",\"ADDRESS\":\"浙江省温州市龙湾区滨海六道357号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08633030300017\",\"PK\":\"********1c127373468511eeb0aebd56742a33a2\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"WAREHOUSENAME\":\"浙江省烟草公司嘉兴市公司卷烟配送中心库\",\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REGION_CODE\":\"330400\",\"SEAL_DATE\":null,\"REGION_NAME\":\"嘉兴市\",\"ADDRESS\":\"浙江省嘉兴市庆丰路777号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08633040100016\",\"PK\":\"********1c127374468511eeb0ae23c77fde7101\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"WAREHOUSENAME\":\"浙江省烟草公司湖州市公司卷烟配送中心库\",\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REGION_CODE\":\"330500\",\"SEAL_DATE\":null,\"REGION_NAME\":\"湖州市\",\"ADDRESS\":\"浙江省湖州市吴兴区康山街道东坡路699号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08633050200015\",\"PK\":\"********1c127375468511eeb0ae7d303b7d19c9\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"WAREHOUSENAME\":\"浙江省烟草公司绍兴市公司卷烟配送中心库\",\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REGION_CODE\":\"330600\",\"SEAL_DATE\":null,\"REGION_NAME\":\"绍兴市\",\"ADDRESS\":\"浙江省绍兴市越城区329国道越王路口于越中路1800号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08633060200017\",\"PK\":\"********1c127376468511eeb0ae9df25e024958\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"WAREHOUSENAME\":\"浙江省烟草公司金华市公司卷烟配送中心库\",\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REGION_CODE\":\"330700\",\"SEAL_DATE\":null,\"REGION_NAME\":\"金华市\",\"ADDRESS\":\"浙江省金华市金东区江东镇金武街238号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08633070300016\",\"PK\":\"********1c127377468511eeb0ae4dd9136ad9ed\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"WAREHOUSENAME\":\"浙江省烟草公司衢州市公司卷烟配送中心库\",\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REGION_CODE\":\"330800\",\"SEAL_DATE\":null,\"REGION_NAME\":\"衢州市\",\"ADDRESS\":\"浙江省衢州市衢江区信安东路108号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08633080300018\",\"PK\":\"********1c127378468511eeb0ae09f5e7daee81\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"WAREHOUSENAME\":\"浙江省烟草公司丽水市公司卷烟配送中心库\",\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REGION_CODE\":\"331100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"丽水市\",\"ADDRESS\":\"浙江省丽水市莲都区飞雨路157号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08633110200011\",\"PK\":\"********1c127379468511eeb0ae3339e4f910da\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"WAREHOUSENAME\":\"浙江省烟草公司台州市公司卷烟配送中心库\",\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REGION_CODE\":\"331000\",\"SEAL_DATE\":null,\"REGION_NAME\":\"台州市\",\"ADDRESS\":\"浙江省台州市椒江区长浦路666号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08633100200018\",\"PK\":\"********1c12737a468511eeb0ae4159dbbc466d\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"WAREHOUSENAME\":\"浙江省烟草公司舟山市公司卷烟配送中心库\",\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REGION_CODE\":\"330900\",\"SEAL_DATE\":null,\"REGION_NAME\":\"舟山市\",\"ADDRESS\":\"浙江省舟山市定沈路616号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08633090100017\",\"PK\":\"********1c12737b468511eeb0ae61c55048f677\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"WAREHOUSENAME\":\"内蒙古自治区烟草公司通辽市公司物流中心仓库\",\"MEMBER_NAME\":\"内蒙古自治区烟草公司兴安盟公司\",\"REGION_CODE\":\"150500\",\"SEAL_DATE\":null,\"REGION_NAME\":\"通辽市\",\"ADDRESS\":\"内蒙古通辽市科尔沁区河西经济技术开发区兴工大街与甘旗卡路交汇处0号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08615050200012\",\"PK\":\"********25308db82a3b11eeae6d95ed1d7e0200\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"将内蒙古兴安盟公司卷烟仓储、分拣物流业务按照就近原则分别整合至内蒙古通辽市公司物流中心、内蒙古呼伦贝尔岭西海拉尔物流分中心、内蒙古呼伦贝尔岭东扎兰屯物流分中心。\",\"PK_MEMBER\":\"0001YE1000S8R7001134\",\"MEMBER_CODE\":\"11152201\"},{\"WAREHOUSENAME\":\"内蒙古自治区烟草公司呼伦贝尔市公司岭西物流配送分中心仓库\",\"MEMBER_NAME\":\"内蒙古自治区烟草公司兴安盟公司\",\"REGION_CODE\":\"150700\",\"SEAL_DATE\":null,\"REGION_NAME\":\"呼伦贝尔市\",\"ADDRESS\":\"内蒙古呼伦贝尔市海拉尔区西大街666号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08615070200016\",\"PK\":\"********25308db92a3b11eeae6d23436b38708c\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"将兴安盟公司物流中心卷烟仓储、分拣物流业务按照就近原则分别整合至内蒙古通辽市公司物流中心、内蒙古呼伦贝尔岭西海拉尔物流分中心、内蒙古呼伦贝尔岭东扎兰屯物流分中心。\",\"PK_MEMBER\":\"0001YE1000S8R7001134\",\"MEMBER_CODE\":\"11152201\"},{\"WAREHOUSENAME\":\"内蒙古自治区烟草公司呼伦贝尔市公司岭东物流分中心仓库\",\"MEMBER_NAME\":\"内蒙古自治区烟草公司兴安盟公司\",\"REGION_CODE\":\"150700\",\"SEAL_DATE\":null,\"REGION_NAME\":\"呼伦贝尔市\",\"ADDRESS\":\"内蒙古呼伦贝尔市扎兰屯市铁西路137号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08615078300012\",\"PK\":\"********25308dba2a3b11eeae6ded63eba5b050\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"将兴安盟公司物流中心卷烟仓储、分拣物流业务按照就近原则分别整合至内蒙古通辽市公司物流中心、内蒙古呼伦贝尔岭西海拉尔物流分中心、内蒙古呼伦贝尔岭东扎兰屯物流分中心。\",\"PK_MEMBER\":\"0001YE1000S8R7001134\",\"MEMBER_CODE\":\"11152201\"},{\"WAREHOUSENAME\":\"贵州省烟草公司遵义市公司物流中心播州备货库\",\"MEMBER_NAME\":\"贵州省烟草公司遵义市公司\",\"REGION_CODE\":\"520301\",\"SEAL_DATE\":null,\"REGION_NAME\":\"市辖区\",\"ADDRESS\":\"贵州省遵义市万寿北街104号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08652030100027\",\"PK\":\"********2e43f11292be11eeba98b35727159aa2\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001821\",\"MEMBER_CODE\":\"11520301\"},{\"WAREHOUSENAME\":\"山东烟叶复烤有限公司山东区域物流集散中心仓库\",\"MEMBER_NAME\":\"黑龙江烟草工业有限责任公司\",\"REGION_CODE\":\"371300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"临沂市\",\"ADDRESS\":\"山东省临沂市罗庄区盛庄街道兴隆路与清河北路交汇处向南200米（0号）\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08637131100048\",\"PK\":\"********31682619048311eeaeb979ec4b57542d\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001171\",\"MEMBER_CODE\":\"12230101\"},{\"WAREHOUSENAME\":\"山东烟叶复烤有限公司山东区域物流集散中心仓库\",\"MEMBER_NAME\":\"红塔辽宁烟草有限责任公司\",\"REGION_CODE\":\"371300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"临沂市\",\"ADDRESS\":\"山东省临沂市罗庄区盛庄街道兴隆路与清河北路交汇处向南200米（0号）\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08637131100048\",\"PK\":\"********3168261a048311eeaeb98b6ed6262121\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001363\",\"MEMBER_CODE\":\"12210101\"},{\"WAREHOUSENAME\":\"内蒙古自治区烟草公司巴彦淖尔市公司物流中心仓库\",\"MEMBER_NAME\":\"内蒙古自治区烟草公司阿拉善盟公司\",\"REGION_CODE\":\"150800\",\"SEAL_DATE\":null,\"REGION_NAME\":\"巴彦淖尔盟\",\"ADDRESS\":\"内蒙古巴彦淖尔市临河区朔方路0号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08615080200018\",\"PK\":\"********3168261b048311eeaeb90780e353e736\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001159\",\"MEMBER_CODE\":\"11152901\"},{\"WAREHOUSENAME\":\"四川省烟草公司成都市公司物流中心仓库\",\"MEMBER_NAME\":\"红云红河烟草（集团）有限责任公司\",\"REGION_CODE\":\"510100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"成都市\",\"ADDRESS\":\"四川省成都市成华区成济路西段2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08651010800010\",\"PK\":\"********3168261c048311eeaeb987548d91d88f\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000935\",\"MEMBER_CODE\":\"12530104\"},{\"WAREHOUSENAME\":\"四川省烟草公司成都市公司物流中心仓库\",\"MEMBER_NAME\":\"红塔烟草(集团)有限责任公司\",\"REGION_CODE\":\"510100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"成都市\",\"ADDRESS\":\"四川省成都市成华区成济路西段2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08651010800010\",\"PK\":\"********3168261d048311eeaeb9ff7b0b105f00\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000758\",\"MEMBER_CODE\":\"12530401\"},{\"WAREHOUSENAME\":\"四川省烟草公司成都市公司物流中心仓库\",\"MEMBER_NAME\":\"云南中烟工业有限责任公司\",\"REGION_CODE\":\"510100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"成都市\",\"ADDRESS\":\"四川省成都市成华区成济路西段2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08651010800010\",\"PK\":\"********31684d2e048311eeaeb96d8ffd873895\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001334\",\"MEMBER_CODE\":\"20530001\"},{\"WAREHOUSENAME\":\"四川省烟草公司成都市公司物流中心仓库\",\"MEMBER_NAME\":\"贵州中烟工业有限责任公司\",\"REGION_CODE\":\"510100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"成都市\",\"ADDRESS\":\"四川省成都市成华区成济路西段2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08651010800010\",\"PK\":\"********3357aa56849911ee957977c651432da9\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001333\",\"MEMBER_CODE\":\"20520001\"},{\"WAREHOUSENAME\":\"四川省烟草公司成都市公司物流中心仓库\",\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REGION_CODE\":\"510100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"成都市\",\"ADDRESS\":\"四川省成都市成华区成济路西段2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08651010800010\",\"PK\":\"********3357aa57849911ee95793329f72244bc\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"WAREHOUSENAME\":\"四川省烟草公司成都市公司物流中心仓库\",\"MEMBER_NAME\":\"湖南中烟工业有限责任公司\",\"REGION_CODE\":\"510100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"成都市\",\"ADDRESS\":\"四川省成都市成华区成济路西段2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08651010800010\",\"PK\":\"********3357aa58849911ee9579514851dce3ff\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001346\",\"MEMBER_CODE\":\"20430001\"},{\"WAREHOUSENAME\":\"广西壮族自治区烟草公司桂林市公司卷烟物流配送中心仓库\",\"MEMBER_NAME\":\"广西壮族自治区烟草公司贺州市公司\",\"REGION_CODE\":\"450300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"桂林市\",\"ADDRESS\":\"广西区桂林市临桂县鲁山路37号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08645032200016\",\"PK\":\"********46949dc5474e11eeac227f735c7b3e39\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001293\",\"MEMBER_CODE\":\"11451101\"},{\"WAREHOUSENAME\":\"新疆维吾尔自治区烟草公司吐鲁番市卷烟物流配送中心\",\"MEMBER_NAME\":\"新疆维吾尔自治区烟草公司\",\"REGION_CODE\":\"652101\",\"SEAL_DATE\":null,\"REGION_NAME\":\"吐鲁番市\",\"ADDRESS\":\"新疆区吐鲁番地区吐鲁番市高昌区东环路1280号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08665210100013\",\"PK\":\"********512f5e98264d11eeb76a69bf1a6b2da3\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001739\",\"MEMBER_CODE\":\"11650001\"},{\"WAREHOUSENAME\":\"山东烟叶复烤有限公司山东区域物流集散中心仓库\",\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REGION_CODE\":\"371300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"临沂市\",\"ADDRESS\":\"山东省临沂市罗庄区盛庄街道兴隆路与清河北路交汇处向南200米（0号）\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08637131100048\",\"PK\":\"********52bd9048219611eea4ab69bba0789ff6\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"湖北中烟在山东设立山东区域集散中心\",\"PK_MEMBER\":\"0001YE1000S8R7001361\",\"MEMBER_CODE\":\"20420001\"},{\"WAREHOUSENAME\":\"云南省烟草公司普洱市公司普洱版纳区域物流配送中心\",\"MEMBER_NAME\":\"云南省烟草公司西双版纳州公司\",\"REGION_CODE\":\"532701\",\"SEAL_DATE\":null,\"REGION_NAME\":\"思茅市\",\"ADDRESS\":\"云南省普洱市思茅市倚象镇帝泊洱大道27号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08653270100013\",\"PK\":\"********64dfcbb86dcf11eeb15b915008f496cf\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001993\",\"MEMBER_CODE\":\"11532801\"},{\"WAREHOUSENAME\":\"内蒙古自治区烟草公司包头市公司物流中心仓库\",\"MEMBER_NAME\":\"内蒙古自治区烟草公司鄂尔多斯市公司\",\"REGION_CODE\":\"150200\",\"SEAL_DATE\":null,\"REGION_NAME\":\"包头市\",\"ADDRESS\":\"内蒙古包头市九原区稀土高新区稀土大街与呼得木林大街交叉口西北角0号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08615020700018\",\"PK\":\"********7a0ee2d22bcd11ee8e3079eeef669bca\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"将内蒙古鄂尔多斯市公司物流仓储、分拣业务整合至内蒙古包头市公司物流中心。\",\"PK_MEMBER\":\"0001YE1000S8R7001132\",\"MEMBER_CODE\":\"11150601\"},{\"WAREHOUSENAME\":\"山东烟叶复烤有限公司山东区域物流集散中心仓库\",\"MEMBER_NAME\":\"红云红河烟草（集团）有限责任公司\",\"REGION_CODE\":\"371300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"临沂市\",\"ADDRESS\":\"山东省临沂市罗庄区盛庄街道兴隆路与清河北路交汇处向南200米（0号）\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08637131100048\",\"PK\":\"********8301ecbe945011ee8c230db4fc1e7060\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000935\",\"MEMBER_CODE\":\"12530104\"},{\"WAREHOUSENAME\":\"山东烟叶复烤有限公司山东区域物流集散中心仓库\",\"MEMBER_NAME\":\"红塔烟草(集团)有限责任公司\",\"REGION_CODE\":\"371300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"临沂市\",\"ADDRESS\":\"山东省临沂市罗庄区盛庄街道兴隆路与清河北路交汇处向南200米（0号）\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08637131100048\",\"PK\":\"********8301ecbf945011ee8c23a9bd9511bff5\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000758\",\"MEMBER_CODE\":\"12530401\"},{\"WAREHOUSENAME\":\"山东烟叶复烤有限公司山东区域物流集散中心仓库\",\"MEMBER_NAME\":\"云南中烟工业有限责任公司\",\"REGION_CODE\":\"371300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"临沂市\",\"ADDRESS\":\"山东省临沂市罗庄区盛庄街道兴隆路与清河北路交汇处向南200米（0号）\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08637131100048\",\"PK\":\"********8301ecc0945011ee8c233dc213bf82aa\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001334\",\"MEMBER_CODE\":\"20530001\"},{\"WAREHOUSENAME\":\"浙江省烟草公司杭州市公司卷烟配送中心库\",\"MEMBER_NAME\":\"广西中烟工业有限责任公司\",\"REGION_CODE\":\"330100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"杭州市\",\"ADDRESS\":\"浙江省杭州市拱墅区莫干山路1418-52号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08633010500016\",\"PK\":\"********8c2955c9780611eeb408a7ee23f5df51\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001357\",\"MEMBER_CODE\":\"20450001\"},{\"WAREHOUSENAME\":\"四川省烟草公司成都市公司物流中心仓库\",\"MEMBER_NAME\":\"吉林烟草工业有限责任公司\",\"REGION_CODE\":\"510100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"成都市\",\"ADDRESS\":\"四川省成都市成华区成济路西段2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08651010800010\",\"PK\":\"********8e09af7a851911ee9579e58ec6ad407f\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000837\",\"MEMBER_CODE\":\"12222402\"},{\"WAREHOUSENAME\":\"四川省烟草公司成都市公司物流中心仓库\",\"MEMBER_NAME\":\"福建中烟工业有限责任公司\",\"REGION_CODE\":\"510100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"成都市\",\"ADDRESS\":\"四川省成都市成华区成济路西段2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08651010800010\",\"PK\":\"********8e09af7b851911ee95790dbf8134c682\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001338\",\"MEMBER_CODE\":\"20350001\"},{\"WAREHOUSENAME\":\"四川中烟工业有限责任公司成品中心库\",\"MEMBER_NAME\":\"河南中烟工业有限责任公司\",\"REGION_CODE\":\"510100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"成都市\",\"ADDRESS\":\"四川省成都市锦江区成龙大道一段56号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08651010400024\",\"PK\":\"********a5006a14ebbc11edb0ae13f40d327c45\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001340\",\"MEMBER_CODE\":\"20410001\"},{\"WAREHOUSENAME\":\"湖北省烟草公司武汉市公司区域物流中心库\",\"MEMBER_NAME\":\"湖北省烟草公司仙桃市公司\",\"REGION_CODE\":\"420100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"武汉市\",\"ADDRESS\":\"湖北省武汉市东西湖区吴家山台商投资区鑫桥产业园金桥三路2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08642011200014\",\"PK\":\"********a5006a15ebbc11edb0aef76bfa3e74dd\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001900\",\"MEMBER_CODE\":\"11429001\"},{\"WAREHOUSENAME\":\"湖北省烟草公司武汉市公司区域物流中心库\",\"MEMBER_NAME\":\"湖北省烟草公司潜江市公司\",\"REGION_CODE\":\"420100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"武汉市\",\"ADDRESS\":\"湖北省武汉市东西湖区吴家山台商投资区鑫桥产业园金桥三路2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08642011200014\",\"PK\":\"********a5006a16ebbc11edb0ae771107ffb5c4\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001279\",\"MEMBER_CODE\":\"11429002\"},{\"WAREHOUSENAME\":\"甘肃省烟草公司兰州市公司物流中心仓库\",\"MEMBER_NAME\":\"甘肃省烟草公司白银市公司\",\"REGION_CODE\":\"620100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"兰州市\",\"ADDRESS\":\"甘肃省兰州市七里河区南滨河中路1120号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08662010300011\",\"PK\":\"********a5006a17ebbc11edb0ae51497acf92b7\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001974\",\"MEMBER_CODE\":\"11620401\"},{\"WAREHOUSENAME\":\"甘肃省烟草公司兰州市公司物流中心仓库\",\"MEMBER_NAME\":\"甘肃省烟草公司临夏回族自治州公司\",\"REGION_CODE\":\"620100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"兰州市\",\"ADDRESS\":\"甘肃省兰州市七里河区南滨河中路1120号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08662010300011\",\"PK\":\"********a5006a18ebbc11edb0aed7aabc265cc4\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001461\",\"MEMBER_CODE\":\"11622901\"},{\"WAREHOUSENAME\":\"甘肃省烟草公司武威市公司物流中心仓库\",\"MEMBER_NAME\":\"甘肃省烟草公司金昌市公司\",\"REGION_CODE\":\"620600\",\"SEAL_DATE\":null,\"REGION_NAME\":\"武威市\",\"ADDRESS\":\"甘肃省武威市凉州区火车站街道迎宾路365号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08662060200015\",\"PK\":\"********a5006a19ebbc11edb0ae7fc852addd1f\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001456\",\"MEMBER_CODE\":\"11620301\"},{\"WAREHOUSENAME\":\"甘肃省烟草公司酒泉市公司酒泉物流中心仓库\",\"MEMBER_NAME\":\"甘肃省烟草公司嘉峪关市公司\",\"REGION_CODE\":\"620900\",\"SEAL_DATE\":null,\"REGION_NAME\":\"酒泉市\",\"ADDRESS\":\"甘肃省酒泉市肃州区飞天路39号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08662090200012\",\"PK\":\"********a5006a1aebbc11edb0aedbdeb7069da1\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000561\",\"MEMBER_CODE\":\"11620201\"},{\"WAREHOUSENAME\":\"湖北省烟草公司武汉市公司区域物流中心库\",\"MEMBER_NAME\":\"湖北省烟草公司鄂州市公司\",\"REGION_CODE\":\"420100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"武汉市\",\"ADDRESS\":\"湖北省武汉市东西湖区吴家山台商投资区鑫桥产业园金桥三路2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08642011200014\",\"PK\":\"********a5006a1bebbc11edb0aeb71e610fc65e\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000847\",\"MEMBER_CODE\":\"11420701\"},{\"WAREHOUSENAME\":\"宁夏回族自治区烟草公司固原市公司物流配送中心\",\"MEMBER_NAME\":\"中国烟草总公司宁夏回族自治区公司\",\"REGION_CODE\":\"640400\",\"SEAL_DATE\":null,\"REGION_NAME\":\"固原市\",\"ADDRESS\":\"宁夏区固原市原州区长丰路106号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08664040200031\",\"PK\":\"********a5006a1cebbc11edb0aed554cb58e9f9\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001220\",\"MEMBER_CODE\":\"11640001\"},{\"WAREHOUSENAME\":\"宁夏回族自治区烟草公司银川市公司物流配送中心\",\"MEMBER_NAME\":\"中国烟草总公司宁夏回族自治区公司\",\"REGION_CODE\":\"640100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"银川市\",\"ADDRESS\":\"宁夏区银川市永宁县望通路1号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08664012100041\",\"PK\":\"********a5006a1debbc11edb0ae1fcce886e459\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001220\",\"MEMBER_CODE\":\"11640001\"},{\"WAREHOUSENAME\":\"宁夏回族自治区烟草公司中卫市公司物流配送中心\",\"MEMBER_NAME\":\"中国烟草总公司宁夏回族自治区公司\",\"REGION_CODE\":\"640500\",\"SEAL_DATE\":null,\"REGION_NAME\":\"中卫县\",\"ADDRESS\":\"宁夏区中卫市沙坡头区中央大道81号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08664050200022\",\"PK\":\"********a5006a1eebbc11edb0ae5b57c51d42d6\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001220\",\"MEMBER_CODE\":\"11640001\"},{\"WAREHOUSENAME\":\"宁夏回族自治区烟草公司吴忠市公司物流配送中心\",\"MEMBER_NAME\":\"中国烟草总公司宁夏回族自治区公司\",\"REGION_CODE\":\"640300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"吴忠市\",\"ADDRESS\":\"宁夏区吴忠市利通区开元大道565号（西端北侧）\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08664030200062\",\"PK\":\"********a500912febbc11edb0ae2fea2be45c02\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001220\",\"MEMBER_CODE\":\"11640001\"},{\"WAREHOUSENAME\":\"黑龙江省烟草公司佳木斯市公司卷烟物流配送中心仓库\",\"MEMBER_NAME\":\"黑龙江省烟草公司鹤岗市公司\",\"REGION_CODE\":\"230800\",\"SEAL_DATE\":null,\"REGION_NAME\":\"佳木斯市\",\"ADDRESS\":\"黑龙江佳木斯市郊区圃东街300号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08623081100011\",\"PK\":\"********a5009130ebbc11edb0ae1df65ba6893e\",\"IS_DEFAULT\":\"Y\",\"REMARK\":\"佳木斯-鹤岗跨区物流业务整合，鹤岗为被整合单位。\",\"PK_MEMBER\":\"0001YE1000S8R7001882\",\"MEMBER_CODE\":\"11230401\"},{\"WAREHOUSENAME\":\"黑龙江省烟草公司牡丹江市公司卷烟物流配送中心仓库\",\"MEMBER_NAME\":\"黑龙江省烟草公司绥芬河市公司\",\"REGION_CODE\":\"231000\",\"SEAL_DATE\":null,\"REGION_NAME\":\"牡丹江市\",\"ADDRESS\":\"黑龙江牡丹江市东安区牡丹江大街999号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08623100200024\",\"PK\":\"********a5009131ebbc11edb0aeafb0c9b29f17\",\"IS_DEFAULT\":\"Y\",\"REMARK\":\"牡丹江-绥芬河跨区域物流业务整合，绥芬河为被整合单位\",\"PK_MEMBER\":\"0001YE1000S8R7001819\",\"MEMBER_CODE\":\"11231003\"},{\"WAREHOUSENAME\":\"黑龙江省烟草公司绥化市公司卷烟物流配送中心仓库\",\"MEMBER_NAME\":\"黑龙江省烟草公司伊春市公司\",\"REGION_CODE\":\"231200\",\"SEAL_DATE\":null,\"REGION_NAME\":\"绥化市\",\"ADDRESS\":\"黑龙江绥化市北林区北辰路与康庄路交叉口0号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08623120200028\",\"PK\":\"********a5009132ebbc11edb0ae91979c893a10\",\"IS_DEFAULT\":\"Y\",\"REMARK\":\"绥化-伊春跨区域物流业务整合，伊春为被整合单位。\",\"PK_MEMBER\":\"0001YE1000S8R7001160\",\"MEMBER_CODE\":\"11230701\"},{\"WAREHOUSENAME\":\"湖北省烟草公司武汉市公司区域物流中心库\",\"MEMBER_NAME\":\"湖北省烟草公司黄石市公司\",\"REGION_CODE\":\"420100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"武汉市\",\"ADDRESS\":\"湖北省武汉市东西湖区吴家山台商投资区鑫桥产业园金桥三路2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08642011200014\",\"PK\":\"********a5009133ebbc11edb0ae1f235cec52cb\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001894\",\"MEMBER_CODE\":\"11420201\"},{\"WAREHOUSENAME\":\"湖北省烟草公司武汉市公司区域物流中心库\",\"MEMBER_NAME\":\"湖北省烟草公司天门市公司\",\"REGION_CODE\":\"420100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"武汉市\",\"ADDRESS\":\"湖北省武汉市东西湖区吴家山台商投资区鑫桥产业园金桥三路2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08642011200014\",\"PK\":\"********a5009134ebbc11edb0ae9d80d2497e5f\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001260\",\"MEMBER_CODE\":\"11429003\"},{\"WAREHOUSENAME\":\"甘肃省烟草公司兰州市公司物流中心仓库\",\"MEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REGION_CODE\":\"620100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"兰州市\",\"ADDRESS\":\"甘肃省兰州市七里河区南滨河中路1120号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08662010300011\",\"PK\":\"********a5009135ebbc11edb0ae4171e425a161\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001040\",\"MEMBER_CODE\":\"********\"},{\"WAREHOUSENAME\":\"深圳烟草工业有限责任公司成品仓库\",\"MEMBER_NAME\":\"吉林烟草工业有限责任公司\",\"REGION_CODE\":\"440300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"深圳市\",\"ADDRESS\":\"深圳市深圳市龙华区清宁路2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08644030600012\",\"PK\":\"********a5009136ebbc11edb0ae31fad718c953\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000837\",\"MEMBER_CODE\":\"12222402\"},{\"WAREHOUSENAME\":\"深圳烟草工业有限责任公司成品仓库\",\"MEMBER_NAME\":\"红塔辽宁烟草有限责任公司\",\"REGION_CODE\":\"440300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"深圳市\",\"ADDRESS\":\"深圳市深圳市龙华区清宁路2号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08644030600012\",\"PK\":\"********a5009137ebbc11edb0aea9384cad2206\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001363\",\"MEMBER_CODE\":\"12210101\"},{\"WAREHOUSENAME\":\"龙岩烟草工业有限责任公司一区卷烟成品F库\",\"MEMBER_NAME\":\"中国卷烟销售公司厦门卷烟调拨站\",\"REGION_CODE\":\"350800\",\"SEAL_DATE\":null,\"REGION_NAME\":\"龙岩市\",\"ADDRESS\":\"福建省龙岩市新罗区曹溪镇乘风路1299号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08635080200032\",\"PK\":\"********a5009138ebbc11edb0ae1183ea2f0eaa\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"共用龙烟仓库，安排万宝路、金桥各规格卷烟发货。\",\"PK_MEMBER\":\"0001YE1000S8R7001393\",\"MEMBER_CODE\":\"22350202\"},{\"WAREHOUSENAME\":\"龙岩烟草工业有限责任公司东肖7-8号卷烟成品库\",\"MEMBER_NAME\":\"中国卷烟销售公司厦门卷烟调拨站\",\"REGION_CODE\":\"350800\",\"SEAL_DATE\":null,\"REGION_NAME\":\"龙岩市\",\"ADDRESS\":\"福建省龙岩市新罗区东肖北路17号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08635080200043\",\"PK\":\"********a5009139ebbc11edb0aed7498452dd2e\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"共用龙烟仓库，安排万宝路、金桥各规格卷烟发货。\",\"PK_MEMBER\":\"0001YE1000S8R7001393\",\"MEMBER_CODE\":\"22350202\"},{\"WAREHOUSENAME\":\"龙岩烟草工业有限责任公司二区卷烟成品高架库\",\"MEMBER_NAME\":\"中国卷烟销售公司厦门卷烟调拨站\",\"REGION_CODE\":\"350800\",\"SEAL_DATE\":null,\"REGION_NAME\":\"龙岩市\",\"ADDRESS\":\"福建省龙岩市新罗区曹溪镇乘风路1299号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08635080200021\",\"PK\":\"********a500913aebbc11edb0aed16b8400c1d6\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"共用龙烟仓库，安排万宝路、金桥各规格卷烟发货。\",\"PK_MEMBER\":\"0001YE1000S8R7001393\",\"MEMBER_CODE\":\"22350202\"},{\"WAREHOUSENAME\":\"湖南中烟工业有限责任公司长沙卷烟厂成品库\",\"MEMBER_NAME\":\"中国卷烟销售公司厦门卷烟调拨站\",\"REGION_CODE\":\"430100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"长沙市\",\"ADDRESS\":\"湖南省长沙市雨花区劳动中路426号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08643011100016\",\"PK\":\"********a500913bebbc11edb0ae3b855d0698c1\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"共用湖南中烟长沙烟厂仓库，安排万宝路各规格卷烟发货。\",\"PK_MEMBER\":\"0001YE1000S8R7001393\",\"MEMBER_CODE\":\"22350202\"},{\"WAREHOUSENAME\":\"厦门烟草工业有限责任公司卷烟成品库\",\"MEMBER_NAME\":\"中国卷烟销售公司厦门卷烟调拨站\",\"REGION_CODE\":\"350200\",\"SEAL_DATE\":null,\"REGION_NAME\":\"厦门市\",\"ADDRESS\":\"福建省厦门市海沧区新阳路1号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08635020500016\",\"PK\":\"********a500913cebbc11edb0ae6b804358c33f\",\"IS_DEFAULT\":\"Y\",\"REMARK\":\"共用厦烟仓库，安排万宝路、金桥、长寿各规格卷烟发货。\",\"PK_MEMBER\":\"0001YE1000S8R7001393\",\"MEMBER_CODE\":\"22350202\"},{\"WAREHOUSENAME\":\"厦门烟草工业有限责任公司一区卷烟成品北H库\",\"MEMBER_NAME\":\"中国卷烟销售公司厦门卷烟调拨站\",\"REGION_CODE\":\"350200\",\"SEAL_DATE\":null,\"REGION_NAME\":\"厦门市\",\"ADDRESS\":\"福建省厦门市海沧区新阳路1号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08635020500027\",\"PK\":\"********a500913debbc11edb0ae818d21302d3b\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"共用厦烟仓库，安排万宝路、金桥、长寿各规格卷烟发货。\",\"PK_MEMBER\":\"0001YE1000S8R7001393\",\"MEMBER_CODE\":\"22350202\"},{\"WAREHOUSENAME\":\"厦门烟草工业有限责任公司二区卷烟成品高架库\",\"MEMBER_NAME\":\"中国卷烟销售公司厦门卷烟调拨站\",\"REGION_CODE\":\"350200\",\"SEAL_DATE\":null,\"REGION_NAME\":\"厦门市\",\"ADDRESS\":\"福建省厦门市海沧区新阳路1号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08635020500038\",\"PK\":\"********a500913eebbc11edb0aed7902a1ead88\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"共用厦烟仓库，安排万宝路、金桥、长寿各规格卷烟发货。\",\"PK_MEMBER\":\"0001YE1000S8R7001393\",\"MEMBER_CODE\":\"22350202\"},{\"WAREHOUSENAME\":\"海南红塔卷烟有限责任公司成品库\",\"MEMBER_NAME\":\"中国烟草总公司海南省公司\",\"REGION_CODE\":\"460100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"海口市\",\"ADDRESS\":\"海南省海口市琼山区云龙产业园横二路0号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08646010100012\",\"PK\":\"********a500913febbc11edb0ae6b879fd67a96\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000908\",\"MEMBER_CODE\":\"11460001\"},{\"WAREHOUSENAME\":\"吉林烟草工业有限责任公司长春卷烟厂成品库\",\"MEMBER_NAME\":\"安徽中烟工业有限责任公司\",\"REGION_CODE\":\"220100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"长春市\",\"ADDRESS\":\"吉林省长春市长春经济技术开发区自由大路8999号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08622010100026\",\"PK\":\"********a5009140ebbc11edb0aea9eb4fc8aba0\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"吉林大路1099号仓库不再存放成品，以现有地址仓库为准，即：吉林省长春市长春经济技术开发区自由大路8999号。\",\"PK_MEMBER\":\"0001YE1000S8R7001342\",\"MEMBER_CODE\":\"20340001\"},{\"WAREHOUSENAME\":\"广东烟草揭阳市有限公司物流配送中心仓库\",\"MEMBER_NAME\":\"安徽中烟工业有限责任公司\",\"REGION_CODE\":\"445200\",\"SEAL_DATE\":null,\"REGION_NAME\":\"揭阳市\",\"ADDRESS\":\"广东省揭阳市普宁市环城南路赤水路段1号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08644528100017\",\"PK\":\"********a5009141ebbc11edb0aedf0466dde601\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001342\",\"MEMBER_CODE\":\"20340001\"},{\"WAREHOUSENAME\":\"内蒙古自治区烟草公司锡林郭勒盟公司物流中心仓库\",\"MEMBER_NAME\":\"内蒙古自治区烟草公司二连浩特市公司\",\"REGION_CODE\":\"152500\",\"SEAL_DATE\":null,\"REGION_NAME\":\"锡林郭勒盟\",\"ADDRESS\":\"内蒙古锡林郭勒盟锡林浩特市经济开发区锡林西大街宝昌路东0号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08615250200011\",\"PK\":\"********a5009142ebbc11edb0ae9fdc60c62803\",\"IS_DEFAULT\":\"Y\",\"REMARK\":\"内蒙古自治区烟草公司二连浩特市公司和锡林郭勒市公司因物流业务整合，按照统一仓储、统一分拣、分级配送模式要求，两家单位共用一个仓库，地点设在锡林郭勒市公司物流中心，原二连浩特市公司物流仓库停用。\",\"PK_MEMBER\":\"0001YE1000S8R7000036\",\"MEMBER_CODE\":\"11152502\"},{\"WAREHOUSENAME\":\"龙岩烟草工业有限责任公司卷烟成品库\",\"MEMBER_NAME\":\"福建中烟工业有限责任公司\",\"REGION_CODE\":\"350800\",\"SEAL_DATE\":null,\"REGION_NAME\":\"龙岩市\",\"ADDRESS\":\"福建省龙岩市新罗区曹溪镇乘风路1299号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08635080200010\",\"PK\":\"********a5009143ebbc11edb0ae018f8bcc531e\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001338\",\"MEMBER_CODE\":\"20350001\"},{\"WAREHOUSENAME\":\"厦门烟草工业有限责任公司卷烟成品库\",\"MEMBER_NAME\":\"福建中烟工业有限责任公司\",\"REGION_CODE\":\"350200\",\"SEAL_DATE\":null,\"REGION_NAME\":\"厦门市\",\"ADDRESS\":\"福建省厦门市海沧区新阳路1号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08635020500016\",\"PK\":\"********a5009144ebbc11edb0ae17a838da9a43\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001338\",\"MEMBER_CODE\":\"20350001\"},{\"WAREHOUSENAME\":\"安徽省烟草公司宿州市公司卷烟物流中心仓库\",\"MEMBER_NAME\":\"安徽省烟草公司淮北市公司\",\"REGION_CODE\":\"341300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"宿州市\",\"ADDRESS\":\"安徽省宿州市墉桥区经济开发区迎宾大道9号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08634130200014\",\"PK\":\"********a5009145ebbc11edb0aec97a9d393202\",\"IS_DEFAULT\":\"Y\",\"REMARK\":\"宿州、淮北2007年进行了跨区域物流整合试点。宿州承担宿州、淮北两地的仓储、分拣等业务。\",\"PK_MEMBER\":\"0001YE1000S8R7000004\",\"MEMBER_CODE\":\"11340601\"},{\"WAREHOUSENAME\":\"安徽省烟草公司池州市公司卷烟物流中心仓库\",\"MEMBER_NAME\":\"安徽省烟草公司铜陵市公司\",\"REGION_CODE\":\"341700\",\"SEAL_DATE\":null,\"REGION_NAME\":\"池州市\",\"ADDRESS\":\"安徽省池州市贵池区清风东路89号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08634170200013\",\"PK\":\"********a5009146ebbc11edb0ae838fb6df8e42\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"2007年，铜陵、池州进行了跨区域物流整合试点，池州主要承担池州、铜陵两地的仓储、分拣等业务。\",\"PK_MEMBER\":\"0001YE1000S8R7001431\",\"MEMBER_CODE\":\"11340701\"},{\"WAREHOUSENAME\":\"重庆市烟草公司涪陵分公司涪陵卷烟配送中心仓库\",\"MEMBER_NAME\":\"重庆中烟工业有限责任公司\",\"REGION_CODE\":\"500100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"市辖区\",\"ADDRESS\":\"重庆市市辖区涪陵区李渡工业园区鹤凤大道12号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08650010200011\",\"PK\":\"********a5009147ebbc11edb0aea75e7c2c3dce\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7002035\",\"MEMBER_CODE\":\"20500001\"},{\"WAREHOUSENAME\":\"重庆中烟工业有限责任公司黔江卷烟厂卷烟成品库\",\"MEMBER_NAME\":\"中国烟草总公司重庆市公司\",\"REGION_CODE\":\"500100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"市辖区\",\"ADDRESS\":\"重庆市市辖区黔江区长征路南段55号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08650011400015\",\"PK\":\"********a5009148ebbc11edb0ae1f254580cdf1\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000988\",\"MEMBER_CODE\":\"11500001\"},{\"WAREHOUSENAME\":\"海南省烟草公司海口公司物流配送中心仓库\",\"MEMBER_NAME\":\"中国烟草总公司海南省公司\",\"REGION_CODE\":\"460100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"海口市\",\"ADDRESS\":\"海南省海口市秀英区南海大道西路174号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08646010400012\",\"PK\":\"********a5009149ebbc11edb0aea11fc2fa27c1\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000908\",\"MEMBER_CODE\":\"11460001\"},{\"WAREHOUSENAME\":\"海南省烟草公司三亚公司物流配送中心仓库\",\"MEMBER_NAME\":\"中国烟草总公司海南省公司\",\"REGION_CODE\":\"460200\",\"SEAL_DATE\":null,\"REGION_NAME\":\"三亚市\",\"ADDRESS\":\"海南省三亚市天涯区绕城高速公路凤凰山居0号（东侧）\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08646020100014\",\"PK\":\"********a500914aebbc11edb0ae81a8c68e15d3\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000908\",\"MEMBER_CODE\":\"11460001\"},{\"WAREHOUSENAME\":\"安徽中烟工业有限责任公司合肥卷烟厂成品仓库\",\"MEMBER_NAME\":\"安徽省烟草公司合肥市公司\",\"REGION_CODE\":\"340100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"合肥市\",\"ADDRESS\":\"安徽省合肥市经开区芙蓉路820号（繁华大道与芙蓉路交口）\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08634010400018\",\"PK\":\"********a500914bebbc11edb0ae27265771f3b4\",\"IS_DEFAULT\":\"Y\",\"REMARK\":\"依据2003年工商共库一体化建设相关协议，自2003年安徽中烟仓库建成后，一直由合肥市公司租赁使用。\",\"PK_MEMBER\":\"0001YE1000S8R7000906\",\"MEMBER_CODE\":\"11340101\"},{\"WAREHOUSENAME\":\"安徽省烟草公司安庆市公司卷烟物流中心仓库\",\"MEMBER_NAME\":\"安徽省烟草公司铜陵市公司\",\"REGION_CODE\":\"340800\",\"SEAL_DATE\":null,\"REGION_NAME\":\"安庆市\",\"ADDRESS\":\"安徽省安庆市郊区长江大桥综合经济开发区纬五路0号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08634081100015\",\"PK\":\"********a500914cebbc11edb0ae1dab963a4089\",\"IS_DEFAULT\":\"Y\",\"REMARK\":\"2007年，铜陵、池州进行了跨区域物流整合试点，池州主要承担池州、铜陵两地的仓储、分拣等业务。2016年，安徽省部分地区行政区划调整，安庆市枞阳县划入铜陵市管辖。由于铜陵市烟草公司原本实行的区域物流模式，本次行政区划调整后，铜陵市公司卷烟物流配送实行“三地两库”模式。即：卷烟仓储、分拣业务分别由池州、安庆市公司负责，铜陵本级由池州市公司负责分拣打码、封箱转运到铜陵市公司中转库，铜陵送货车隔日送达零售户，枞阳县由安庆市公司物流中心负责卷烟分拣打码、封箱，枞阳送货车辆直接到安庆仓库取货后再返回枞阳县进行配送。\",\"PK_MEMBER\":\"0001YE1000S8R7001431\",\"MEMBER_CODE\":\"11340701\"},{\"WAREHOUSENAME\":\"贵州省烟草公司贵阳市公司区域物流中心库\",\"MEMBER_NAME\":\"贵州省烟草公司黔南州公司\",\"REGION_CODE\":\"520100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"贵阳市\",\"ADDRESS\":\"贵州省贵阳市花溪区开发大道98号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08652011100024\",\"PK\":\"********a500914debbc11edb0aebf62b8f17738\",\"IS_DEFAULT\":\"N\",\"REMARK\":\"仓库存放贵阳贵安销售全部卷烟，黔南龙里、长顺、惠水部分区县分库卷烟\",\"PK_MEMBER\":\"0001YE1000S8R7000752\",\"MEMBER_CODE\":\"11522701\"},{\"WAREHOUSENAME\":\"广东烟草揭阳市有限公司物流配送中心仓库\",\"MEMBER_NAME\":\"河南中烟工业有限责任公司\",\"REGION_CODE\":\"445200\",\"SEAL_DATE\":null,\"REGION_NAME\":\"揭阳市\",\"ADDRESS\":\"广东省揭阳市普宁市环城南路赤水路段1号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08644528100017\",\"PK\":\"********a500914eebbc11edb0aeddd79499029e\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001340\",\"MEMBER_CODE\":\"20410001\"},{\"WAREHOUSENAME\":\"辽宁省烟草公司鞍山市公司卷烟仓库\",\"MEMBER_NAME\":\"安徽中烟工业有限责任公司\",\"REGION_CODE\":\"210300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"鞍山市\",\"ADDRESS\":\"辽宁省鞍山市铁西区千山西路889号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08621030300014\",\"PK\":\"********a500914febbc11edb0ae4974d736734c\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001342\",\"MEMBER_CODE\":\"20340001\"},{\"WAREHOUSENAME\":\"重庆中烟工业有限责任公司重庆卷烟厂大石坝成品高架库\",\"MEMBER_NAME\":\"安徽中烟工业有限责任公司\",\"REGION_CODE\":\"500100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"市辖区\",\"ADDRESS\":\"重庆市市辖区南岸区涂山路589号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08650010800011\",\"PK\":\"********a5009150ebbc11edb0ae2737d9e67065\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001342\",\"MEMBER_CODE\":\"20340001\"},{\"WAREHOUSENAME\":\"新疆维吾尔自治区烟草公司工商一体化物流配送中心库房\",\"MEMBER_NAME\":\"安徽中烟工业有限责任公司\",\"REGION_CODE\":\"650100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"乌鲁木齐市\",\"ADDRESS\":\"新疆区乌鲁木齐市头屯河区喀纳斯湖路新疆卷烟厂东门\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08665010600017\",\"PK\":\"********a5009151ebbc11edb0aebdee8a9772ec\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001342\",\"MEMBER_CODE\":\"20340001\"},{\"WAREHOUSENAME\":\"辽宁省烟草公司鞍山市公司卷烟仓库\",\"MEMBER_NAME\":\"四川中烟工业有限责任公司\",\"REGION_CODE\":\"210300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"鞍山市\",\"ADDRESS\":\"辽宁省鞍山市铁西区千山西路889号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08621030300014\",\"PK\":\"********a5009152ebbc11edb0ae2756bfad07f5\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7002034\",\"MEMBER_CODE\":\"20510002\"},{\"WAREHOUSENAME\":\"广东烟草揭阳市有限公司物流配送中心仓库\",\"MEMBER_NAME\":\"四川中烟工业有限责任公司\",\"REGION_CODE\":\"445200\",\"SEAL_DATE\":null,\"REGION_NAME\":\"揭阳市\",\"ADDRESS\":\"广东省揭阳市普宁市环城南路赤水路段1号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08644528100017\",\"PK\":\"********a5009153ebbc11edb0ae378fb6b31d7f\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7002034\",\"MEMBER_CODE\":\"20510002\"},{\"WAREHOUSENAME\":\"安徽中烟工业有限责任公司合肥卷烟厂成品仓库\",\"MEMBER_NAME\":\"四川中烟工业有限责任公司\",\"REGION_CODE\":\"340100\",\"SEAL_DATE\":null,\"REGION_NAME\":\"合肥市\",\"ADDRESS\":\"安徽省合肥市经开区芙蓉路820号（繁华大道与芙蓉路交口）\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08634010400018\",\"PK\":\"********a5009154ebbc11edb0ae7971e2e3cf27\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7002034\",\"MEMBER_CODE\":\"20510002\"},{\"WAREHOUSENAME\":\"内蒙古自治区烟草公司呼伦贝尔市公司岭西物流配送分中心仓库\",\"MEMBER_NAME\":\"内蒙古自治区烟草公司满洲里市公司\",\"REGION_CODE\":\"150700\",\"SEAL_DATE\":null,\"REGION_NAME\":\"呼伦贝尔市\",\"ADDRESS\":\"内蒙古呼伦贝尔市海拉尔区西大街666号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08615070200016\",\"PK\":\"********a5009155ebbc11edb0aefbf8673839b8\",\"IS_DEFAULT\":\"Y\",\"REMARK\":\"内蒙古自治区烟草公司满洲里市公司和呼伦贝尔市公司因物流业务整合，按照统一仓储、统一分拣、分级配送模式要求，两家单位共用一个仓库，地点设在呼伦贝尔市公司岭西物流配送分中心，原满洲里市公司物流仓库停用。\",\"PK_MEMBER\":\"0001YE1000S8R7001397\",\"MEMBER_CODE\":\"11150709\"},{\"WAREHOUSENAME\":\"河南省烟草公司许昌市公司卷烟物流配送中心仓库\",\"MEMBER_NAME\":\"河北中烟工业有限责任公司\",\"REGION_CODE\":\"411000\",\"SEAL_DATE\":null,\"REGION_NAME\":\"许昌市\",\"ADDRESS\":\"河南省许昌市工农路1370号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08641100100021\",\"PK\":\"********a5009156ebbc11edb0ae291ae4107750\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001092\",\"MEMBER_CODE\":\"20130001\"},{\"WAREHOUSENAME\":\"辽宁省烟草公司鞍山市公司卷烟仓库\",\"MEMBER_NAME\":\"河北中烟工业有限责任公司\",\"REGION_CODE\":\"210300\",\"SEAL_DATE\":null,\"REGION_NAME\":\"鞍山市\",\"ADDRESS\":\"辽宁省鞍山市铁西区千山西路889号\",\"IS_SEAL\":\"N\",\"WAREHOUSECODE\":\"08621030300014\",\"PK\":\"********a5009157ebbc11edb0ae3d7b4f9c2874\",\"IS_DEFAULT\":\"N\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001092\",\"MEMBER_CODE\":\"20130001\"}]}";
                break;
            case MEMBERTAXNUMQRY:
                // 会员税号
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"DATA\":[{\"MEMBER_NAME\":\"广东烟草江门市有限公司\",\"ENTERPRISE_NAME\":\"广东烟草江门市有限公司\",\"PK\":\"0001YE1********07LXN\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"914407037314711800\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001778\",\"MEMBER_CODE\":\"11440701\"},{\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"ENTERPRISE_NAME\":\"湖北中烟工业有限责任公司\",\"PK\":\"0001YE1********07ZTA\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"91370000759162101E\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"MEMBER_NAME\":\"中国烟草总公司江西省公司\",\"ENTERPRISE_NAME\":\"中国烟草总公司江西省公司\",\"PK\":\"0001YE1********09X9F\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"360101858283902\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000851\",\"MEMBER_CODE\":\"22360001\"},{\"MEMBER_NAME\":\"新疆维吾尔自治区烟草公司巴音郭楞蒙古自治州公司\",\"ENTERPRISE_NAME\":\"新疆维吾尔自治区烟草公司巴音郭楞州公司\",\"PK\":\"0001YE1********0A3FF\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"652801229471097\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001305\",\"MEMBER_CODE\":\"11652801\"},{\"MEMBER_NAME\":\"江苏省烟草公司南京市公司\",\"ENTERPRISE_NAME\":\"江苏省烟草公司南京市公司\",\"PK\":\"0001YE1********0CN8Z\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"913201001349231266\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPOH\",\"MEMBER_CODE\":\"11320101\"},{\"MEMBER_NAME\":\"江苏省烟草公司徐州市公司\",\"ENTERPRISE_NAME\":\"江苏省烟草公司徐州市公司\",\"PK\":\"0001YE1********0CN91\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"91320300136406616H\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001191\",\"MEMBER_CODE\":\"11320301\"},{\"MEMBER_NAME\":\"陕西省烟草公司榆林市公司\",\"ENTERPRISE_NAME\":\"陕西省烟草公司榆林市公司\",\"PK\":\"0001YE1********0CSNF\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"91610800223930184F\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000230\",\"MEMBER_CODE\":\"11610801\"},{\"MEMBER_NAME\":\"江苏省烟草公司无锡市公司\",\"ENTERPRISE_NAME\":\"江苏省烟草公司无锡市公司\",\"PK\":\"0001YE1********0CSNI\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"91320200135906010E\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001177\",\"MEMBER_CODE\":\"11320201\"},{\"MEMBER_NAME\":\"河北省烟草公司张家口市公司\",\"ENTERPRISE_NAME\":\"河北省烟草公司张家口市公司\",\"PK\":\"0001YE1********0CSNX\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"91130700106154331Q\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001865\",\"MEMBER_CODE\":\"11130701\"},{\"MEMBER_NAME\":\"中国烟草总公司江苏省公司\",\"ENTERPRISE_NAME\":\"中国烟草总公司江苏省公司\",\"PK\":\"0001YE1********0CTF6\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"91320000134755468W\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7000450\",\"MEMBER_CODE\":\"22320001\"},{\"MEMBER_NAME\":\"河北省烟草公司唐山市公司\",\"ENTERPRISE_NAME\":\"河北省烟草公司唐山市公司\",\"PK\":\"0001YE1********0CU77\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"91130293104744995M\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001863\",\"MEMBER_CODE\":\"11130201\"},{\"MEMBER_NAME\":\"中国烟草总公司青海省公司\",\"ENTERPRISE_NAME\":\"中国烟草总公司青海省公司\",\"PK\":\"0001YE1********0CU79\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"91630000226580888H\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001216\",\"MEMBER_CODE\":\"11630001\"},{\"MEMBER_NAME\":\"安徽省烟草公司马鞍山市公司\",\"ENTERPRISE_NAME\":\"安徽省烟草公司马鞍山市公司\",\"PK\":\"0001YE1********0J8WW\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"9134050015050276XT\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001957\",\"MEMBER_CODE\":\"11340501\"},{\"MEMBER_NAME\":\"中国烟草实业发展中心\",\"ENTERPRISE_NAME\":\"中国烟草实业发展中心\",\"PK\":\"11010410176929X\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"11010410176929X\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001524\",\"MEMBER_CODE\":\"99000002\"},{\"MEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"ENTERPRISE_NAME\":\"湖北中烟工业有限责任公司\",\"PK\":\"370112759162101\",\"IS_DEFAULT\":\"N\",\"TAXPAYER_NO\":\"370112759162101\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1********2GPON\",\"MEMBER_CODE\":\"20420001\"},{\"MEMBER_NAME\":\"中国烟草总公司深圳市公司\",\"ENTERPRISE_NAME\":\"中国烟草总公司深圳市公司\",\"PK\":\"440301192179593\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"440301192179593\",\"REMARK\":\"因龙岗仓库处于测试阶段，当前该仓库尚无法取得该仓库货运车辆准确的经纬度信息，本表经纬度为预估值。\",\"PK_MEMBER\":\"0001YE1000S8R7001035\",\"MEMBER_CODE\":\"********\"},{\"MEMBER_NAME\":\"江苏省烟草公司常州市公司\",\"ENTERPRISE_NAME\":\"江苏省烟草公司常州市公司\",\"PK\":\"9132040013720360X8\",\"IS_DEFAULT\":\"Y\",\"TAXPAYER_NO\":\"9132040013720360X8\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001180\",\"MEMBER_CODE\":\"********\"}],\"RECORDCOUNT\":\"17\"}";
                break;
            case MEMBERACCOUNTQRY:
                // 会员银行账号
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"DATA\":[{\"BANK_ACCNAME\":\"河南省烟草公司郑州市公司\",\"MEMBER_NAME\":\"河南省烟草公司郑州市公司\",\"BANK_ACCOUNT\":\"411899991010004694944\",\"BANKDOC_NAME\":\"交通银行郑州自贸区分行\",\"PK\":\"********\",\"IS_DEFAULT\":\"Y\",\"REMARK\":null,\"PK_MEMBER\":\"0001YE1000S8R7001874\",\"MEMBER_CODE\":\"********\"}],\"RECORDCOUNT\":\"1\"}";
                break;
            case QTYQRY:
                // 核定量
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"DATA\":[{\"CYCLE_CODE\":\"2023H2\",\"MEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"INOUT_TYPE\":\"1\",\"NORMALGRDQTY\":\"250000.********\",\"LOWGRDQTY\":\"0.********\",\"CONTRQTY\":\"75000.********\",\"PROTNORMGRDQTY\":null,\"PROTQTY\":null,\"BUSI_TYPE\":\"2\",\"PLANQTY\":\"250000.********\",\"PROTLOWGRDQTY\":null,\"DEADLINE_TIME\":null,\"MEMBER_CODE\":\"********\"},{\"CYCLE_CODE\":\"2023H2\",\"MEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"INOUT_TYPE\":\"1\",\"NORMALGRDQTY\":\"878000.********\",\"LOWGRDQTY\":\"952000.********\",\"CONTRQTY\":\"505789.********\",\"PROTNORMGRDQTY\":\"877719.********\",\"PROTQTY\":\"1820314.********\",\"BUSI_TYPE\":\"0\",\"PLANQTY\":\"1830000.********\",\"PROTLOWGRDQTY\":\"942595.********\",\"DEADLINE_TIME\":null,\"MEMBER_CODE\":\"********\"}],\"RECORDCOUNT\":\"2\"}";
                break;
            case CONTRACTQRY:
                // 合同
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"RECORDCOUNT\":\"20\",\"PAGEINDEX\":\"1\",\"DATA\":[{\"DELIWAREHOUSE_NAME\":\"山东济南烟草有限公司卷烟配送中心仓库\",\"REQBANKACCT\":\"1602023619200134081\",\"DEPUTY_NAME\":\"admin_ind\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"1\",\"CANCEL_TIME\":null,\"ITFPK\":\"****************\",\"SUPMEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:40:51\",\"SUPBANKDOCNAME\":\"工商银行\",\"REQBANKDOCNAME\":\"当前无名称\",\"REQREGION\":\"山东\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"湖北中烟青岛卷烟厂成品库\",\"SUPREGION\":\"山东省\",\"RECWAREHOUSE_CODE\":\"36922fd7ab25d0fa54b627ca93ab99f8\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"91370100724985706R\",\"SUPTAXNO\":\"*********\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"山东省\",\"PRICE\":\"3195.64\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"泰山(华贵)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000022217\",\"REGIONNAME\":\"山东省\",\"ITFROW_PK\":\"SD_GY2024040300000086901028156783\",\"BRAND_NAME\":\"泰山\",\"QTY\":\"20.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000022216\",\"ITFPK\":\"SD_GY202404030000008\",\"SUPMEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"SUPMEMBER_CODE\":\"20420001\",\"CGTLENGTH\":\"10.000000\"}],\"RECADDRESS\":\"山东济南烟草有限公司卷烟配送中心仓库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"山东济南烟草市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"admin_ind\",\"SUPBANKACCT\":\"62220202010101010101\",\"PK\":\"1001240102000022216\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141386\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:40:53\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000002219\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141386*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000002218\",\"ITFPK\":\"GS_GY00141386\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000002218\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141385\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:40:55\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000002217\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141385*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000002216\",\"ITFPK\":\"GS_GY00141385\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000002216\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141384\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:40:57\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000022215\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141384*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000022214\",\"ITFPK\":\"GS_GY00141384\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000022214\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141383\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:40:58\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000022213\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141383*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000022212\",\"ITFPK\":\"GS_GY00141383\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000022212\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141382\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:41:00\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000022211\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141382*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000022210\",\"ITFPK\":\"GS_GY00141382\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000022210\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141381\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:41:02\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000002215\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141381*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000002214\",\"ITFPK\":\"GS_GY00141381\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000002214\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141380\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:45:45\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000012135\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141380*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000012134\",\"ITFPK\":\"GS_GY00141380\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000012134\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141379\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:45:46\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000022209\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141379*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000022208\",\"ITFPK\":\"GS_GY00141379\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000022208\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141378\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:45:48\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000012133\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141378*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000012132\",\"ITFPK\":\"GS_GY00141378\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000012132\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141377\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:45:50\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000012131\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141377*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000012130\",\"ITFPK\":\"GS_GY00141377\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000012130\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141376\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:45:52\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000012129\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141376*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000012128\",\"ITFPK\":\"GS_GY00141376\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000012128\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141375\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:45:53\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000022207\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141375*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000022206\",\"ITFPK\":\"GS_GY00141375\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000022206\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141374\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:45:55\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000022205\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141374*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000022204\",\"ITFPK\":\"GS_GY00141374\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000022204\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141373\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:45:56\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000022203\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141373*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000022202\",\"ITFPK\":\"GS_GY00141373\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000022202\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141372\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:45:58\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000022201\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141372*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000022200\",\"ITFPK\":\"GS_GY00141372\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000022200\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"},{\"DELIWAREHOUSE_NAME\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"REQBANKACCT\":\"2706060509200009962\",\"DEPUTY_NAME\":\"杨丽霞\",\"PRINTNO\":null,\"TRANS_TYPE\":\"2\",\"CONTRACTNO\":\"*********\",\"REQADDRESS\":\"甘肃省天水市秦州区藉河北路\",\"SUPPOSTCODE\":\"730050\",\"BUSI_TYPE\":\"0\",\"PROTOCOLNO\":\"*********\",\"TRANSCERTNO\":null,\"PORTNAME\":null,\"BILL_DATE\":\"2024-01-02\",\"DELIWAREHOUSE_CODE\":\"**************\",\"CANCEL_TIME\":null,\"ITFPK\":\"GS_GY00141371\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"REMARK\":null,\"AUDIT_TIME\":\"2024-01-02 14:55:33\",\"SUPBANKDOCNAME\":\"兰州市工商银行城关支行\",\"REQBANKDOCNAME\":\"工行天水市东关支行\",\"REQREGION\":\"甘肃省\",\"CYCLE_CODE\":\"2024H1\",\"DELIADDRESS\":\"甘肃烟草工业有限责任公司天水卷烟厂卷烟成品高架库\",\"SUPREGION\":\"甘肃省\",\"RECWAREHOUSE_CODE\":\"**************\",\"SETTLT_TYPE\":\"2\",\"REQTELEPHONE\":\"0938-6827739\",\"REQMEMBER_CODE\":\"********\",\"REQTAXNO\":\"916205002248912289\",\"SUPTAXNO\":\"91620103224434895W\",\"DETAIL\":[{\"PROV_TYPE\":\"0\",\"SUPREGION\":\"甘肃省\",\"PRICE\":\"4906.********\",\"CROWNO\":\"1\",\"CGTPACKTYPENAME\":\"条盒硬盒\",\"PRODUCT_NAME\":\"兰州(硬珍品)\",\"CGTPRTYPENAME\":null,\"PRODUCT_CODE\":\"*************\",\"ROW_PK\":\"1001240102000002213\",\"REGIONNAME\":\"甘肃省\",\"ITFROW_PK\":\"GS_GY00141371*************\",\"BRAND_NAME\":\"兰州\",\"QTY\":\"420.********\",\"PK_TRADECIGARETTE\":\"0001000036b91ae463f211ee84f59b6bef142a68\",\"CGTTYPENAME\":\"烤烟型\",\"PK\":\"1001240102000002212\",\"ITFPK\":\"GS_GY00141371\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"CGTLENGTH\":\"84.********\"}],\"RECADDRESS\":\"甘肃省烟草公司天水市公司物流中心库\",\"SUPADDRESS\":\"甘肃省兰州市七里河区南滨河中路1111号\",\"REQMEMBER_NAME\":\"甘肃省烟草公司天水市公司\",\"DELI_START_DATE\":\"2024-01-02\",\"EXEC_STATUS\":\"0\",\"REQPOSTCODE\":\"741000\",\"SUPTELEPHONE\":\"0931-2555015 2555087\",\"DELI_END_DATE\":\"2024-01-31\",\"AUDIT_STATUS\":\"2\",\"RECWAREHOUSE_NAME\":\"甘肃省烟草公司天水市公司物流中心库\",\"PROTOCOLID\":\"1001231219000004584\",\"SUPDEPUTY_NAME\":\"冯锐\",\"SUPBANKACCT\":\"2703000129200034254\",\"PK\":\"1001240102000002212\",\"AUDIT_OPERAT\":\"自动鉴章\",\"SUPMEMBER_CODE\":\"********\"}]}";
                break;
            case CONTRACTCANCELQRY:
                // 合同解除
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"PAGEINDEX\":\"1\",\"DATA\":[{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点调整\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"*********\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-13\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-13\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-27\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230627000020651\",\"CANCEL_TIME\":\"2023-06-27 15:55:02\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"*********\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:55:00\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点调整\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112000905\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-13\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-13\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-27\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230627000010643\",\"CANCEL_TIME\":\"2023-06-27 15:55:08\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121000929\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:55:07\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点调整\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003101\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-27\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230627000020649\",\"CANCEL_TIME\":\"2023-06-27 15:56:11\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121001024\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:56:09\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点调整\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003096\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-27\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230627000000662\",\"CANCEL_TIME\":\"2023-06-27 15:55:13\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121001131\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:55:11\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点调整\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003097\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-27\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230627000020645\",\"CANCEL_TIME\":\"2023-06-27 15:56:29\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121001023\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:56:27\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点调整\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003093\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-27\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230627000000660\",\"CANCEL_TIME\":\"2023-06-27 15:55:19\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121001130\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:55:17\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点调整\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003092\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-27\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230627000010639\",\"CANCEL_TIME\":\"2023-06-27 15:56:33\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121000928\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:56:31\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点调整\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003102\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-27\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230627000000658\",\"CANCEL_TIME\":\"2023-06-27 15:55:23\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121001129\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:55:22\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点调整\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003100\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-27\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230627000010637\",\"CANCEL_TIME\":\"2023-06-27 15:59:59\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121000927\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:59:58\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点调整\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003099\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-27\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230627000020643\",\"CANCEL_TIME\":\"2023-06-27 15:55:28\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121001022\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:55:26\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点调整\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003098\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-27\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230627000000656\",\"CANCEL_TIME\":\"2023-06-27 16:00:05\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121001128\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 16:00:03\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点调整\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003095\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-27\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230627000000654\",\"CANCEL_TIME\":\"2023-06-27 15:55:33\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121001127\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:55:31\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点调整\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003094\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-27\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230627000000652\",\"CANCEL_TIME\":\"2023-06-27 15:55:37\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121001126\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:55:36\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点变更\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003172\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-26\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230626000001334\",\"CANCEL_TIME\":\"2023-06-27 15:56:56\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121001121\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:56:55\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点变更\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003178\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-26\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230626000001332\",\"CANCEL_TIME\":\"2023-06-27 15:55:41\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121001120\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:55:40\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点变更\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003179\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-26\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230626000021314\",\"CANCEL_TIME\":\"2023-06-27 16:04:22\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121000923\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 16:04:20\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点变更\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003182\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-26\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230626000021312\",\"CANCEL_TIME\":\"2023-06-27 15:55:46\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121000922\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:55:44\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点变更\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003186\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-26\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230626000021310\",\"CANCEL_TIME\":\"2023-06-27 15:57:02\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121000921\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:57:00\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点变更\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003190\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-26\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230626000011307\",\"CANCEL_TIME\":\"2023-06-27 15:55:50\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121001015\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 15:55:48\"},{\"REQREGION\":\"浙江省\",\"CYCLE_CODE\":\"2023H1\",\"SUPREGION\":\"甘肃省\",\"DEPUTY_NAME\":\"郑华丽\",\"CANCEL_REASON\":\"发货地点变更\",\"REQMEMBER_CODE\":\"********\",\"CONTRACTNO\":\"112003173\",\"REQMEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"DELI_START_DATE\":\"2023-06-14\",\"BUSI_TYPE\":\"2\",\"DELI_END_DATE\":\"2023-07-14\",\"AUDIT_STATUS\":\"2\",\"BILL_DATE\":\"2023-06-26\",\"SUPDEPUTY_NAME\":\"冯锐\",\"PK\":\"1001230626000021304\",\"CANCEL_TIME\":\"2023-06-27 16:00:10\",\"AUDIT_OPERAT\":\"朱传辉\",\"ITFPK\":\"121000920\",\"SUPMEMBER_NAME\":\"甘肃烟草工业有限责任公司\",\"SUPMEMBER_CODE\":\"********\",\"AUDIT_TIME\":\"2023-06-27 16:00:07\"}],\"PAGESIZE\":\"20\",\"RECORDCOUNT\":\"20\"}";
                break;
            case CONTRACTNO:
                // 正式合同号
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"PAGEINDEX\":\"1\",\"DATA\":[{\"EFFECT_STATUS\":\"1\",\"CONTRACTNO\":\"*********\",\"ITFPK\":\"****************\"},{\"EFFECT_STATUS\":\"1\",\"CONTRACTNO\":\"112001940\",\"ITFPK\":\"GS_GY00135645\"},{\"EFFECT_STATUS\":\"1\",\"CONTRACTNO\":\"112001943\",\"ITFPK\":\"GS_GY00135650\"},{\"EFFECT_STATUS\":\"1\",\"CONTRACTNO\":\"112001944\",\"ITFPK\":\"GS_GY00135651\"},{\"EFFECT_STATUS\":\"1\",\"CONTRACTNO\":\"112001945\",\"ITFPK\":\"GS_GY00135653\"},{\"EFFECT_STATUS\":\"1\",\"CONTRACTNO\":\"112002463\",\"ITFPK\":\"GS_GY00135647\"},{\"EFFECT_STATUS\":\"1\",\"CONTRACTNO\":\"112002464\",\"ITFPK\":\"GS_GY00135649\"},{\"EFFECT_STATUS\":\"1\",\"CONTRACTNO\":\"112002465\",\"ITFPK\":\"GS_GY00135652\"},{\"EFFECT_STATUS\":\"1\",\"CONTRACTNO\":\"112002466\",\"ITFPK\":\"GS_GY00135654\"}],\"PAGESIZE\":\"20\",\"RECORDCOUNT\":\"9\"}";
                break;
            case MT_API_DISTREGION:
                // 营销子系统配货地区查询
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"PAGEINDEX\":\"1\",\"RECORDCOUNT\":\"15\",\"DATA\":[{\"PK\":\"1001230627000020651\",\"MEMBER_CODE\":\"********\",\"MEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"ISSEAL\":\"0\",\"REGION_NAME\":\"甘肃烟草工业有限责任公司\",\"REGION_CODE\":\"********\",\"REMARK\":\"这是个备注\"},{\"PK\":\"1001230627000010643\",\"MEMBER_CODE\":\"********\",\"MEMBER_NAME\":\"浙江中烟工业有限责任公司\",\"REGION_NAME\":\"浙江中烟工业有限责任公司\",\"ISSEAL\":\"1\",\"REGION_CODE\":\"********\",\"REMARK\":\"发货点变更\"}]}";
                break;
            case MT_API_DISTRULE:
                // 营销子系统配货模式设置查询
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"PAGEINDEX\":\"1\",\"DATA\":[{\"REQ_CONFIRM_TIME\":\"2023-06-26 21:12:15\",\"DAILYSALE_DAYS\":\"7.********\",\"PREP_DAYS\":\"1.********\",\"REQMEMBER_CODE\":\"********\",\"DAYILYSALE_MODEL\":\"1\",\"DETAIL\":[{\"DISTULE\":[{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":\"130.********\",\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(乐途)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028160285\",\"ROW_PK\":\"********e8955174142111ee89dd9b98e058f752\",\"MAXSTOCKNUM\":\"400.********\",\"ITFROW_PK\":\"********6901028160285\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":null,\"PK_TRADEPRODUCT\":\"P6901028160292204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"6.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"红旗渠(硬银)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028160599\",\"ROW_PK\":\"********e8955175142111ee89dd4972403dcb37\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028160599\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"3.********\",\"PK_TRADEPRODUCT\":\"P6901028160612204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"6.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"红旗渠(芒果)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028161718\",\"ROW_PK\":\"********e8955176142111ee89dd2f6a74b2aeca\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028161718\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"3.********\",\"PK_TRADEPRODUCT\":\"P6901028161725204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":\"10.********\",\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(浓香中支)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028161749\",\"ROW_PK\":\"********e8955177142111ee89dd0bca46e318e8\",\"MAXSTOCKNUM\":\"30.********\",\"ITFROW_PK\":\"********6901028161749\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":null,\"PK_TRADEPRODUCT\":\"P6901028161756204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(炫尚)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028161817\",\"ROW_PK\":\"********e8955178142111ee89dda1e738625364\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028161817\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028161794204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":\"10.********\",\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(天叶中支)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028161862\",\"ROW_PK\":\"********e8955179142111ee89dd61e7a6b0f9e8\",\"MAXSTOCKNUM\":\"30.********\",\"ITFROW_PK\":\"********6901028161862\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":null,\"PK_TRADEPRODUCT\":\"P6901028161909204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"散花(软蓝)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028160025\",\"ROW_PK\":\"********e895517a142111ee89ddaf51c1240dc3\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028160025\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":null,\"PK_TRADEPRODUCT\":\"P6901028160032204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(百年浓香)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028160100\",\"ROW_PK\":\"********e895517b142111ee89dd4b707d28be0b\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028160100\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028160063204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"15.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(喜满堂)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028162876\",\"ROW_PK\":\"********e895517c142111ee89dd6b121f78baa4\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028162876\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"9.********\",\"PK_TRADEPRODUCT\":\"P6901028162883204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(黄金细支)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028162968\",\"ROW_PK\":\"********e895517d142111ee89dd4300b7786b37\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028162968\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028162975204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(天香)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028163590\",\"ROW_PK\":\"********e895517e142111ee89ddb9d6f0f6289a\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028163590\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028163606204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(黄金眼)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028163651\",\"ROW_PK\":\"********e895517f142111ee89dd9fec2fc056f4\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028163651\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028163620204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(商鼎)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028172509\",\"ROW_PK\":\"********e8955180142111ee89dd6bb1e4bc8dad\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028172509\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028163675204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":\"10.********\",\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(天香中支)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028163729\",\"ROW_PK\":\"********e8955181142111ee89dd178ea79db52a\",\"MAXSTOCKNUM\":\"50.********\",\"ITFROW_PK\":\"********6901028163729\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":null,\"PK_TRADEPRODUCT\":\"P6901028163842204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(豫香)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028161923\",\"ROW_PK\":\"********e8955182142111ee89dd3d00fb3ab2fb\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028161923\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028161930204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"6.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"红旗渠(新版银河)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028161954\",\"ROW_PK\":\"********e8955183142111ee89dd75f39e4b3120\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028161954\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"3.********\",\"PK_TRADEPRODUCT\":\"P6901028161961204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(国色细支)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028162777\",\"ROW_PK\":\"********e8955184142111ee89dd552141c941fc\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028162777\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028162784204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(红火)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028162845\",\"ROW_PK\":\"********e8955185142111ee89ddef879a1f266b\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028162845\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028162852204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(爱尚)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028165242\",\"ROW_PK\":\"********e8955186142111ee89dd3d3bbf419b16\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028165242\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028165259204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":\"50.********\",\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(天香细支)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028165365\",\"ROW_PK\":\"********e8955187142111ee89dd67f71d875337\",\"MAXSTOCKNUM\":\"130.********\",\"ITFROW_PK\":\"********6901028165365\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":null,\"PK_TRADEPRODUCT\":\"P6901028165372204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":\"10.********\",\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(天尊)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028165518\",\"ROW_PK\":\"********e8955188142111ee89dddbdd4a3a7c63\",\"MAXSTOCKNUM\":\"50.********\",\"ITFROW_PK\":\"********6901028165518\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":null,\"PK_TRADEPRODUCT\":\"P6901028165525204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(金丝路)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028165570\",\"ROW_PK\":\"********e8955189142111ee89dd358d0913bdaf\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028165570\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028165587204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(小黄金)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028165693\",\"ROW_PK\":\"********e895518a142111ee89dd6b388f55cc1f\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028165693\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028165709204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(软大金圆)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028163538\",\"ROW_PK\":\"********e895518b142111ee89ddb9ef78a51ae3\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028163538\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028163859204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"红旗渠(雪茄)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028164177\",\"ROW_PK\":\"********e895518c142111ee89dd6b9f552d834b\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028164177\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":null,\"PK_TRADEPRODUCT\":\"P6901028164184204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(浓香细支)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028164801\",\"ROW_PK\":\"********e895518d142111ee89dd7b8a3cbaa2a2\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028164801\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028164818204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.50000000\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(红南阳)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028167529\",\"ROW_PK\":\"********e895518e142111ee89dd89a3e22bbc0a\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028167529\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.10000000\",\"PK_TRADEPRODUCT\":\"P6901028167383204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":\"10.********\",\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(软天叶)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028167888\",\"ROW_PK\":\"********e895518f142111ee89dd836296552805\",\"MAXSTOCKNUM\":\"30.********\",\"ITFROW_PK\":\"********6901028167888\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":null,\"PK_TRADEPRODUCT\":\"P6901028167611204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"6.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(硬红旗渠)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028169356\",\"ROW_PK\":\"********e8955190142111ee89ddaf713c9b1977\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028169356\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"3.********\",\"PK_TRADEPRODUCT\":\"P6901028169226204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":\"30.********\",\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(天叶细支)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028165860\",\"ROW_PK\":\"********e8955191142111ee89dd61a0ee306275\",\"MAXSTOCKNUM\":\"80.********\",\"ITFROW_PK\":\"********6901028165860\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":null,\"PK_TRADEPRODUCT\":\"P6901028169233204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"6.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(硬帝豪)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028169219\",\"ROW_PK\":\"********e8955192142111ee89dd553be2901405\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028169219\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"3.********\",\"PK_TRADEPRODUCT\":\"P6901028169509204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(小目标)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028165921\",\"ROW_PK\":\"********e8955193142111ee89dddfc30098bb03\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028165921\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028165938204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(大M)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028165952\",\"ROW_PK\":\"********e8955194142111ee89dd4d3edc1b43f4\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028165952\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028165945204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"6.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"红旗渠(天行健)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028166690\",\"ROW_PK\":\"********e8955195142111ee89dd9100d378386f\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028166690\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"3.********\",\"PK_TRADEPRODUCT\":\"P6901028166997204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(乐途硬)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028172936\",\"ROW_PK\":\"********e8955196142111ee89dd8940425601e1\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028172936\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":null,\"PK_TRADEPRODUCT\":\"P6901028172943204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":\"50.********\",\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(天叶)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028169967\",\"ROW_PK\":\"********e8955197142111ee89dd711b09d998a0\",\"MAXSTOCKNUM\":\"130.********\",\"ITFROW_PK\":\"********6901028169967\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":null,\"PK_TRADEPRODUCT\":\"P6901028169608204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(国色双中支)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028169578\",\"ROW_PK\":\"********e8955198142111ee89dd83dfeccef7bb\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028169578\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028169653204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(软盛世金典)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028169783\",\"ROW_PK\":\"********e8955199142111ee89dd799e7b5ab30e\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028169783\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028169721204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null},{\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄金叶(乐途中支)\",\"CALC_MODEL\":\"0\",\"PRODUCT_CODE\":\"6901028172844\",\"ROW_PK\":\"********e895519a142111ee89dd3f61d7b3ce9d\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"********6901028172844\",\"FULLORDERRATE2\":null,\"APPLI_MONTH\":\"01,02,03,04,05,06,07,08,09,10,11,12\",\"FULLORDERRATE1\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"PK_TRADEPRODUCT\":\"P6901028172851204100010001\",\"ITFPK\":\"20420001_********_********02\",\"REMARK\":\"0\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null}],\"SEASON\":[{\"ITFPK\":\"20420001_********_********02\",\"ITFROW_PK\":\"********6901028169783\",\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"ROW_PK\":\"********e895519a142111ee89dd3f61d7b3ce9d\",\"MD02_IC_TRADE_BUSI_START_DATE\":\"2023-06-26\",\"MD02_IC_TRADE_BUSI_END_DATE\":\"2025-06-27\",\"MD02_CGT_DIST_SEASON_FACTOR\":\"1.1\",\"REMARK\":\"季节因子备注\"}]}],\"REQMEMBER_NAME\":\"河南省烟草公司郑州市公司\",\"REQ_CONFIRM_STATUS\":\"1\",\"EFFECT_STATUS\":\"1\",\"VERSION\":\"2023-06-26 21:12:15\",\"DISTREGION_CODE\":null,\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"ITFPK\":\"20420001_********_********02\",\"SUPMEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REMARK\":null,\"DISTREGION_NAME\":null,\"ONWAY_DAYS\":\"2.********\",\"SUPMEMBER_CODE\":\"20420001\"}],\"PAGESIZE\":\"20\",\"RECORDCOUNT\":\"1\"}";
                break;
            case MT_API_DISTORDER:
                // 营销子系统配货订单查询
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"PAGEINDEX\":\"1\",\"DATA\":[{\"REQ_CONFIRM_TIME\":\"2024-05-06 11:20:29\",\"SUP_CONFIRM_STATUS\":\"1\",\"REQMEMBER_CODE\":\"********\",\"SUP_CONFIRM_TIME\":\"2024-05-06 21:20:29\",\"DETAIL\":[{\"ONWAYNUM\":\"0.********\",\"PREP_DAYS\":\"1.********\",\"DAILYSALENUM\":\"7.********\",\"PRODUCT_CODE\":\"6901028178259\",\"ROW_PK\":\"SD_GY20240506000016901028178259\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"SD_GY20240506000016901028178259\",\"REQCONFIRMNUM\":\"8.********\",\"PK_TRADEPRODUCT\":\"P6901028178259204200010001\",\"ITFPK\":\"SD_GY2024050600001\",\"REMARK\":null,\"ONWAY_DAYS\":\"2.********\",\"STOCKNUM\":\"0.02000000\",\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"CONFIRMNUM\":\"1.********\",\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄鹤楼（1916中支）\",\"CALC_MODEL\":\"0\",\"TRANS_PRICE\":null,\"PREDORDERNUM\":null,\"TRANS_W_PRICE\":null,\"FULLORDERRATE2\":null,\"SUPCOMFIRMNUM\":\"1.********\",\"FULLORDERRATE1\":null,\"MD02_CGT_DIST_SEASON_FACTOR\":\"1\",\"MD01_CGT_MARKET_STATUS\":\"0\",\"MD02_CGT_DIST_QTY_RATE_MAX\":\"200\",\"MD02_CGT_DIST_QTY_RATE_MIN\":\"10\",\"MD02_CGT_DIST_INDU_ABNORMAL_REMARK\":\"工业备注\",\"MD02_CGT_DIST_BUSI_ABNORMAL_REMARK\":\"商业备注\",\"CALCDISTNUM\":\"2877.62230000\",\"PK\":\"SD_GY2024050600001\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null,\"STOCKSALE_DAYS\":null},{\"ONWAYNUM\":\"0.********\",\"PREP_DAYS\":\"1.********\",\"DAILYSALENUM\":\"7.********\",\"PRODUCT_CODE\":\"6901028187855\",\"ROW_PK\":\"SD_GY20240506000016901028187855\",\"MAXSTOCKNUM\":\"50.********\",\"ITFROW_PK\":\"SD_GY20240506000016901028187855\",\"REQCONFIRMNUM\":\"12.********\",\"PK_TRADEPRODUCT\":\"P6901028187855204200010001\",\"ITFPK\":\"SD_GY2024050600001\",\"REMARK\":null,\"ONWAY_DAYS\":\"2.********\",\"STOCKNUM\":\"50.26000000\",\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":\"10.********\",\"CONFIRMNUM\":\"0.********\",\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄鹤楼（软1916）\",\"CALC_MODEL\":\"0\",\"TRANS_PRICE\":null,\"PREDORDERNUM\":null,\"TRANS_W_PRICE\":null,\"FULLORDERRATE2\":null,\"SUPCOMFIRMNUM\":\"0.********\",\"FULLORDERRATE1\":null,\"MD02_CGT_DIST_SEASON_FACTOR\":\"1\",\"MD01_CGT_MARKET_STATUS\":\"1\",\"MD02_CGT_DIST_QTY_RATE_MAX\":\"210\",\"MD02_CGT_DIST_QTY_RATE_MIN\":\"15\",\"MD02_CGT_DIST_INDU_ABNORMAL_REMARK\":\"工业备注1\",\"MD02_CGT_DIST_BUSI_ABNORMAL_REMARK\":\"商业备注1\",\"CALCDISTNUM\":\"552.98000000\",\"PK\":\"SD_GY2024050600001\",\"MINSTOCKSALE_DAYS\":null,\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null,\"STOCKSALE_DAYS\":null}],\"REQMEMBER_NAME\":\"山东省烟草公司济南市公司\",\"REQ_CONFIRM_STATUS\":\"1\",\"ACTION\":null,\"EFFECT_STATUS\":\"1\",\"VERSION\":\"2024-04-05 21:36:26\",\"DISTREGION_CODE\":null,\"PK\":\"SD_GY2024050600001\",\"ITFPK\":\"SD_GY2024050600001\",\"SUPMEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REMARK\":null,\"DISTREGION_NAME\":null,\"SUPMEMBER_CODE\":\"20420001\"}],\"PAGESIZE\":\"20\",\"RECORDCOUNT\":\"1\"}";
                String itfpks = param.getItfpks();
                if (itfpks != null && !itfpks.contains(NationSubsysConstants.SPLIT)) {
                    // 单个配货单号时 替换配货单号
                    resultJson = resultJson.replaceAll("SD_GY2024050600001", itfpks);
                }
                break;
            case DISTORDERPREVIEW:
                // 配货订单预览
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"PAGEINDEX\":\"1\",\"DATA\":[{\"REQ_CONFIRM_TIME\":\"2024-05-06 11:20:29\",\"SUP_CONFIRM_STATUS\":\"1\",\"REQMEMBER_CODE\":\"********\",\"SUP_CONFIRM_TIME\":\"2024-05-06 21:20:29\",\"DETAIL\":[{\"ONWAYNUM\":\"0.********\",\"PREP_DAYS\":\"1.********\",\"DAILYSALENUM\":\"7.********\",\"PRODUCT_CODE\":\"6901028178259\",\"ROW_PK\":\"SD_GY20240506000016901028178259\",\"MAXSTOCKNUM\":null,\"ITFROW_PK\":\"SD_GY20240506000016901028178259\",\"REQCONFIRMNUM\":\"8.********\",\"PK_TRADEPRODUCT\":\"P6901028178259204200010001\",\"ITFPK\":\"SD_GY2024050600001\",\"REMARK\":null,\"ONWAY_DAYS\":\"2.********\",\"STOCKNUM\":\"0.02000000\",\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":null,\"CONFIRMNUM\":\"1.********\",\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":\"10.********\",\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄鹤楼（1916中支）\",\"CALC_MODEL\":\"0\",\"TRANS_PRICE\":null,\"PREDORDERNUM\":null,\"TRANS_W_PRICE\":null,\"FULLORDERRATE2\":null,\"SUPCOMFIRMNUM\":\"1.********\",\"FULLORDERRATE1\":null,\"MD02_CGT_DIST_SEASON_FACTOR\":\"1\",\"MD01_CGT_MARKET_STATUS\":\"0\",\"MD02_CGT_DIST_QTY_RATE_MAX\":\"200\",\"MD02_CGT_DIST_QTY_RATE_MIN\":\"10\",\"MD02_CGT_DIST_INDU_ABNORMAL_REMARK\":\"工业备注\",\"MD02_CGT_DIST_BUSI_ABNORMAL_REMARK\":\"商业备注\",\"CALCDISTNUM\":\"2877.62230000\",\"PK\":\"SD_GY2024050600001\",\"MINSTOCKSALE_DAYS\":\"5.********\",\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null,\"STOCKSALE_DAYS\":null},{\"ONWAYNUM\":\"0.********\",\"PREP_DAYS\":\"1.********\",\"DAILYSALENUM\":\"7.********\",\"PRODUCT_CODE\":\"6901028187855\",\"ROW_PK\":\"SD_GY20240506000016901028187855\",\"MAXSTOCKNUM\":\"50.********\",\"ITFROW_PK\":\"SD_GY20240506000016901028187855\",\"REQCONFIRMNUM\":\"12.********\",\"PK_TRADEPRODUCT\":\"P6901028187855204200010001\",\"ITFPK\":\"SD_GY2024050600001\",\"REMARK\":null,\"ONWAY_DAYS\":\"2.********\",\"STOCKNUM\":\"50.26000000\",\"DIST_MODEL\":\"0\",\"SUPLSAFESTOCKNUM\":null,\"DAILYSALE_DAYS\":\"7.********\",\"MINSTOCKNUM\":\"10.********\",\"CONFIRMNUM\":\"0.********\",\"ORDERSAFESTOCKNUM\":null,\"MAXSTOCKSALE_DAYS\":null,\"DAYILYSALE_MODEL\":\"1\",\"PRODUCT_NAME\":\"黄鹤楼（软1916）\",\"CALC_MODEL\":\"0\",\"TRANS_PRICE\":null,\"PREDORDERNUM\":null,\"TRANS_W_PRICE\":null,\"FULLORDERRATE2\":null,\"SUPCOMFIRMNUM\":\"0.********\",\"FULLORDERRATE1\":null,\"MD02_CGT_DIST_SEASON_FACTOR\":\"1\",\"MD01_CGT_MARKET_STATUS\":\"1\",\"MD02_CGT_DIST_QTY_RATE_MAX\":\"210\",\"MD02_CGT_DIST_QTY_RATE_MIN\":\"15\",\"MD02_CGT_DIST_INDU_ABNORMAL_REMARK\":\"工业备注1\",\"MD02_CGT_DIST_BUSI_ABNORMAL_REMARK\":\"商业备注1\",\"CALCDISTNUM\":\"552.98000000\",\"PK\":\"SD_GY2024050600001\",\"MINSTOCKSALE_DAYS\":null,\"FULLORDERRATE4\":null,\"FULLORDERRATE3\":null,\"STOCKSALE_DAYS\":null}],\"REQMEMBER_NAME\":\"山东省烟草公司济南市公司\",\"REQ_CONFIRM_STATUS\":\"1\",\"ACTION\":null,\"EFFECT_STATUS\":\"1\",\"VERSION\":\"2024-04-05 21:36:26\",\"DISTREGION_CODE\":null,\"PK\":\"SD_GY2024050600001\",\"ITFPK\":\"SD_GY2024050600001\",\"SUPMEMBER_NAME\":\"湖北中烟工业有限责任公司\",\"REMARK\":null,\"DISTREGION_NAME\":null,\"SUPMEMBER_CODE\":\"20420001\"}],\"PAGESIZE\":\"20\",\"RECORDCOUNT\":\"1\"}";
                break;
            case MT_API_DISTRECEIVEREGION:
                // 配货到货地区查询
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\",\"PAGEINDEX\":\"1\",\"DATA\":[{\"PK\":\"********e8904863142111ee89dd81472a87002d\",\"MA02_CGT_TRADE_REQ_MEMB_CODE\":\"********\",\"MD02_CGT_TRADE_REQ_MEMB_NAME\":\"河南省烟草公司郑州市公司\",\"MD02_DIST_RECEIVEREGION_CODE\":\"********0001\",\"MD02_DIST_RECEIVEREGION_NAME\":\"配货收货地区名称1\",\"ISSEAL\":\"N\",\"REMARK\":\"备注1\"},{\"PK\":\"********e8955176142111ee89dd2f6a74b2aeca\",\"MA02_CGT_TRADE_REQ_MEMB_CODE\":\"********\",\"MD02_CGT_TRADE_REQ_MEMB_NAME\":\"河南省烟草公司郑州市公司\",\"MD02_DIST_RECEIVEREGION_CODE\":\"********0002\",\"MD02_DIST_RECEIVEREGION_NAME\":\"配货收货地区名称2\",\"ISSEAL\":\"N\",\"REMARK\":\"备注2\"},{\"PK\":\"********e8955177142111ee89dd0bca46e318e8\",\"MA02_CGT_TRADE_REQ_MEMB_CODE\":\"********\",\"MD02_CGT_TRADE_REQ_MEMB_NAME\":\"河南省烟草公司郑州市公司\",\"MD02_DIST_RECEIVEREGION_CODE\":\"********0003\",\"MD02_DIST_RECEIVEREGION_NAME\":\"配货收货地区名称3\",\"ISSEAL\":\"N\",\"REMARK\":\"备注2\"}],\"PAGESIZE\":\"20\",\"RECORDCOUNT\":\"1\"}";
                break;
            default:
                resultJson = "{\"MSG\":\"查询成功！\",\"CODE\":\"000\"}";
        }
        return JSONUtil.toBean(resultJson, Map.class);
    }

    /**
     * @param req     获取参数
     * @param service 接口枚举
     * @param result  接口返回结果
     * @return List<Map < Object>> 数据列表
     * <AUTHOR> liuwancheng
     * @Create : 2025-05-14 14:01:22
     * @Description : 保存数据
     */
    private List<Map<String, Object>> save(NationSubsysPullREQ req, NationSubsysServiceEnum service, Map<String, Object> result) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(result)) {
            resultList = (List<Map<String, Object>>) result.get(NationSubsysConstants.DATA);
            switch (service) {
                case APITRADECGTQRY:
                    // 交易目录
                    saveNationProductControl(resultList);
                    break;
                case APITRADECGTWITHPRICEQRY:
                    // 交易目录（含价格）
                    saveNationProductControlPrice(resultList);
                    break;
                case TRADEPRODUCT:
                    // 产品目录
                    saveNationProduct(resultList);
                    break;
                case MEMBERQRY:
                    // 会员
                    saveNationMember(resultList);
                    break;
                case MEMBERDEPUTYQRY:
                    // 会员代表
                    saveNationMemberDeployee(resultList);
                    break;
                case MEMBERWAREHOUSEQRY:
                    // 仓库
                    saveNationWhse(resultList);
                    break;
                case MEMBERTAXNUMQRY:
                    // 税号
                    saveNationTax(resultList);
                    break;
                case MEMBERACCOUNTQRY:
                    // 银行账号
                    saveNationBank(resultList);
                    break;
                case QTYQRY:
                    // 核定量
                    saveNationRatifyQty(resultList);
                    break;
                case PROTOCOLQRY:
                    // 协议
                    saveNationAgreement(resultList);
                    break;
                case PROTOCOLADJQRY:
                    // 协议调整
                    saveNationAgreementAdjust(resultList);
                    break;
                case PROTOCOLCANCELQRY:
                    // 协议解除
                    saveNationAgreementCancel(resultList);
                    break;
                case PROTOCOLCTRLQRY:
                    saveNationAgreementFinal(resultList);
                    break;
                case MT_API_DISTREGION:
                    // 发运地区
                    saveNationDistRegion(resultList);
                    break;
                case MT_API_DISTRECEIVEREGION:
                    // 收货地区
                    saveNationDistReceiveRegion(resultList);
                    break;
                case DISTORDERPREVIEW:
                    // 配货预览
                    saveNationDistPreview(req, resultList);
                    break;
                case MT_API_DISTRULE:
                    // 配货参数查询
                    saveNationDistParm(req, resultList);
                    break;
                case MT_API_DISTORDER:
                    // 配货单
                    saveNationDist(resultList);
                    break;
                case CONTRACTNO:
                    // 正式合同号
                    saveNationContractNo(resultList);
                    break;
                case CONTRACTQRY:
                    // 合同查询
                    saveNationContract(resultList);
                    break;
                case CONTRACTCANCELQRY:
                    // 合同解除查询
                    saveNationContractCancel(resultList);
                    break;
                default:
                    break;
            }
        }
        return resultList;
    }

    /**
     * 交易目录保存至中间表
     *
     * @param datas 数据
     */
    void saveNationProductControl(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            basicDownloadMapper.deleteNationProductControl(datas);
            basicDownloadMapper.insertNationProductControl(datas);
        }

    }

    /**
     * 交易目录（含价格）保存至中间表
     *
     * @param datas 数据
     */
    void saveNationProductControlPrice(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            basicDownloadMapper.deleteNationProductControlPrice(datas);
            basicDownloadMapper.insertNationProductControlPrice(datas);
        }
    }

    /**
     * 产品目录保存至中间表
     *
     * @param datas 数据
     */
    void saveNationProduct(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            basicDownloadMapper.deleteNationProduct(datas);
            basicDownloadMapper.insertNationProduct(datas);
        }
    }

    /**
     * 会员保存至中间表
     *
     * @param datas 数据
     */
    void saveNationMember(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            basicDownloadMapper.deleteNationMember(datas);
            basicDownloadMapper.insertNationMember(datas);
        }
    }

    /**
     * 会员代表保存至中间表
     *
     * @param datas 数据
     */
    void saveNationMemberDeployee(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            basicDownloadMapper.deleteNationDeployee(datas);
            basicDownloadMapper.insertNationDeployee(datas);
        }
    }

    /**
     * 仓库保存至中间表
     *
     * @param datas 数据
     */
    void saveNationWhse(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            basicDownloadMapper.deleteNationWhse(datas);
            basicDownloadMapper.insertNationWhse(datas);
        }
    }

    /**
     * 税号保存至中间表
     *
     * @param datas 数据
     */
    void saveNationTax(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            basicDownloadMapper.deleteNationTax(datas);
            basicDownloadMapper.insertNationTax(datas);
        }
    }

    /**
     * 银行账号保存至中间表
     *
     * @param datas 数据
     */
    void saveNationBank(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            basicDownloadMapper.deleteNationBank(datas);
            basicDownloadMapper.insertNationBank(datas);
        }
    }

    /**
     * 核定量保存至中间表
     *
     * @param datas 数据
     */
    void saveNationRatifyQty(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            // 按协议半年删除
            List<String> cycleCodes = datas.stream().map(data -> data.get("CYCLE_CODE").toString()).distinct().collect(Collectors.toList());
            basicDownloadMapper.deleteNationRatifyQty(cycleCodes);
            basicDownloadMapper.insertNationRatifyQty(datas);
        }
    }

    /**
     * 协议保存至中间表
     *
     * @param datas 数据
     */
    private void saveNationAgreement(List<Map<String, Object>> datas) {
        List<Map<String, Object>> items = new ArrayList<>();
        for (Map<String, Object> data : datas) {
            items.addAll((List<Map<String, Object>>) data.get("DETAIL"));
        }
        agreeDownloadMapper.deleteNationAgreement(datas);
        agreeDownloadMapper.insertNationAgreement(datas);
        agreeDownloadMapper.deleteNationAgreementItem(items);
        agreeDownloadMapper.insertNationAgreementItem(items);
    }

    /**
     * 协议调整保存至中间表
     *
     * @param datas 数据
     */
    private void saveNationAgreementAdjust(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            List<Map<String, Object>> items = new ArrayList<>();
            for (Map<String, Object> data : datas) {
                items.addAll((List<Map<String, Object>>) data.get("DETAIL"));
            }
            agreeDownloadMapper.deleteNationAgreementAdjust(datas);
            agreeDownloadMapper.insertNationAgreementAdjust(datas);
            agreeDownloadMapper.deleteNationAgreementAdjustItem(items);
            agreeDownloadMapper.insertNationAgreementAdjustItem(items);
        }
    }

    /**
     * 协议调整保存至中间表
     *
     * @param datas 数据
     */
    private void saveNationAgreementCancel(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            agreeDownloadMapper.deleteNationAgreementCancel(datas);
            agreeDownloadMapper.insertNationAgreementCancel(datas);
        }
    }

    /**
     * 协议最终量保存至中间表
     *
     * @param datas 数据
     */
    private void saveNationAgreementFinal(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            // 按协议号删除
            List<String> protocolnos = datas.stream().map(data -> data.get("PROTOCOLNO").toString()).distinct().collect(Collectors.toList());
            agreeDownloadMapper.deleteNationFinalAgree(protocolnos);
            agreeDownloadMapper.insertNationFinalAgree(datas);
        }
    }


    /**
     * 配货发运地区保存至中间表
     *
     * @param datas 数据
     */
    private void saveNationDistRegion(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            distDownloadMapper.deleteNationDistRegion(datas);
            distDownloadMapper.insertNationDistRegion(datas);
        }
    }

    /**
     * 配货收货地区保存至中间表
     *
     * @param datas 数据
     */
    private void saveNationDistReceiveRegion(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            distDownloadMapper.deleteNationDistReceiveRegion(datas);
            distDownloadMapper.insertNationDistReceiveRegion(datas);
        }
    }

    /**
     * 空null
     */
    private static final String EMPTY_STRING = "null";

    /**
     * @param data 数据
     * <AUTHOR> liuwancheng
     * @Create : 2025-08-12 15:22:41
     * @Description : 处理发运地区编码和收货地区编码 空转换为空串
     */
    private void processDistRegionAndReceiveregionCode(Map<String, Object> data) {
        String distregionCode = data.get("DISTREGION_CODE") == null ? "" : data.get("DISTREGION_CODE").toString();
        String md02DistReceiveregionCode = data.get("MD02_DIST_RECEIVEREGION_CODE") == null ? "" : data.get("MD02_DIST_RECEIVEREGION_CODE").toString();
        // 可能有null字符串也要替换成空串
        if (EMPTY_STRING.equals(distregionCode)) {
            distregionCode = "";
        }
        if (EMPTY_STRING.equals(md02DistReceiveregionCode)) {
            md02DistReceiveregionCode = "";
        }
        data.put("DISTREGION_CODE", distregionCode);
        data.put("MD02_DIST_RECEIVEREGION_CODE", md02DistReceiveregionCode);
    }

    /**
     * 配货订单预览保存至中间表
     *
     * @param param 参数
     * @param datas 数据
     */
    private void saveNationDistPreview(NationSubsysPullREQ param, List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            List<Map<String, Object>> items = new ArrayList<>();
            // 日期
            String billDate = param.getBillDate();
            for (Map<String, Object> data : datas) {
                // 处理 PK和BILL_DATE
                String pk = data.get("PK") == null ? "" : data.get("PK").toString();
                String supmemberCode = data.get("SUPMEMBER_CODE") == null ? "" : data.get("SUPMEMBER_CODE").toString();
                String reqmemberCode = data.get("REQMEMBER_CODE") == null ? "" : data.get("REQMEMBER_CODE").toString();
                // 发运地区编码和收货地区编码空转换成空串
                processDistRegionAndReceiveregionCode(data);
                String distregionCode = data.get("DISTREGION_CODE").toString();
                String md02DistReceiveregionCode = data.get("MD02_DIST_RECEIVEREGION_CODE").toString();
                if (StrUtil.isBlank(pk)) {
                    pk = supmemberCode + reqmemberCode + distregionCode + md02DistReceiveregionCode;
                }
                data.put("BILL_DATE", billDate);
                data.put("PK", pk);
                List<Map<String, Object>> details = (List<Map<String, Object>>) data.get("DETAIL");
                if (CollectionUtil.isNotEmpty(details)) {
                    // 从表处理pk
                    for (Map<String, Object> detail : details) {
                        detail.put("PK", pk);
                    }
                    items.addAll(details);
                }
            }
            distDownloadMapper.deleteNationDistPreview(datas);
            distDownloadMapper.insertNationDistPreview(datas);
            distDownloadMapper.deleteNationDistPreviewItem(datas);
            // 卷烟可能空
            if (CollectionUtil.isNotEmpty(items)) {
                distDownloadMapper.insertNationDistPreviewItem(items);
            }
        }
    }

    /**
     * 配货模式设置保存至中间表
     *
     * @param param 参数
     * @param datas 数据
     */
    private void saveNationDistParm(NationSubsysPullREQ param, List<Map<String, Object>> datas) {
        // 先删除国家局的配货参数，防止国家局删除返回数据为空不删除的情况
        distDownloadMapper.deleteNationParmItem(param);
        distDownloadMapper.deleteNationParm(param);
        if (CollectionUtil.isNotEmpty(datas)) {
            if (CollectionUtil.isNotEmpty(datas)) {
                // 配货参数主表
                List<Map<String, Object>> parms = new ArrayList<>();
                // 配货参数从表
                List<Map<String, Object>> items = new ArrayList<>();
                // 季节系数
                List<Map<String, Object>> seasons = new ArrayList<>();
                for (Map<String, Object> data : datas) {
                    String pk = (String) data.get("PK");
                    // null替换成空串
                    processDistRegionAndReceiveregionCode(data);
                    // 可能数据空
                    if (StrUtil.isNotBlank(pk)) {
                        parms.add(data);
                    }
                    List<Map<String, Object>> details = (List<Map<String, Object>>) data.get("DETAIL");
                    // 配货参数 结构变了 DETAIL[{DISTULE:[],SEASON":[]}]
                    if (CollectionUtil.isNotEmpty(details)) {
                        items.addAll((List<Map<String, Object>>) details.get(0).get("DISTULE"));
                        if (CollectionUtil.isNotEmpty((List<Map<String, Object>>) details.get(0).get("SEASON"))) {
                            seasons.addAll((List<Map<String, Object>>) details.get(0).get("SEASON"));
                        }
                    }
                }
                // pk可能空的不算数据
                if (CollectionUtil.isNotEmpty(parms)) {
                    distDownloadMapper.insertNationParm(parms);
                }
                // 卷烟可能空
                if (CollectionUtil.isNotEmpty(items)) {
                    distDownloadMapper.insertNationParmItem(items);
                }
                // 季节因素可能空
                if (CollectionUtil.isNotEmpty(seasons)) {
                    distDownloadMapper.deleteNationParmSeason(seasons);
                    distDownloadMapper.insertNationParmSeason(seasons);
                }
            }
        }
    }

    /**
     * 配货订单保存至中间表
     *
     * @param datas 数据
     */
    private void saveNationDist(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            List<Map<String, Object>> items = new ArrayList<>();
            for (Map<String, Object> data : datas) {
                items.addAll((List<Map<String, Object>>) data.get("DETAIL"));
            }
            distDownloadMapper.deleteNationDist(datas);
            distDownloadMapper.insertNationDist(datas);
            distDownloadMapper.deleteNationDistItem(datas);
            distDownloadMapper.insertNationDistItem(items);
        }
    }

    /**
     * 正式合同号保存至中间表
     *
     * @param datas 数据
     */
    private void saveNationContractNo(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            contractDownloadMapper.deleteContractNo(datas);
            contractDownloadMapper.insertContractNo(datas);
        }
    }

    /**
     * 合同表保存至中间表
     *
     * @param datas 数据
     */
    private void saveNationContract(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            List<Map<String, Object>> items = new ArrayList<>();
            for (Map<String, Object> data : datas) {
                items.addAll((List<Map<String, Object>>) data.get("DETAIL"));
            }
            contractDownloadMapper.deleteContract(datas);
            contractDownloadMapper.insertContract(datas);
            contractDownloadMapper.deleteContractItem(items);
            contractDownloadMapper.insertContractItem(items);
        }
    }

    /**
     * 合同解除保存至中间表
     *
     * @param datas 数据
     */
    void saveNationContractCancel(List<Map<String, Object>> datas) {
        if (CollectionUtil.isNotEmpty(datas)) {
            contractDownloadMapper.deleteNationContractCancel(datas);
            contractDownloadMapper.insertNationContractCancel(datas);
        }
    }


}
