/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.nation.subsys;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ind.constant.CommonConstants;
import com.tobacco.app.isale.domain.enums.common.ProdTradeTypeCodeEnum;
import com.tobacco.app.isale.domain.model.order.dist.order.CgtDistOrderToLogistics;
import com.tobacco.app.isale.domain.repository.nation.subsys.ISaleThreePartyRepository;
import com.tobacco.app.isale.dto.basic.ContRealReachWhseDTO;
import com.tobacco.app.isale.infrastructure.converter.dist.order.CgtDistOrderToLogisticsToMc04IslmCgtDistOrderDOConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtDistOrderDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmCgtDistOrderService;
import com.tobacco.app.isale.third.service.api.inter.ThreePartySysApi;
import com.tobacco.app.isale.tools.utils.DelivUtil;
import com.tobacco.app.isale.tools.utils.InterHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/09/08 19:30
 * @description : 第三方接口实现
 */
@Component("ISaleThreePartyRepositoryImplImpl")
@Slf4j
public class ISaleThreePartyRepositoryImpl implements ISaleThreePartyRepository {
    @Resource
    private ThreePartySysApi threePartySysApi;

    @Resource
    private Mc04IslmCgtDistOrderService mc04IslmCgtDistOrderService;

    /**
     * 物流信息回传信息结构
     * {
     * "head": {
     * "msgcode": "DIST_ORDER_20241204155235012",
     * "msgid": "DIST_ORDER",
     * "msgname": "配货单信息",
     * "source_sys": "HB_I_M_D_CLMIS",
     * "target_sys": "HB_I_B_D_YX",
     * "createtime": "2024-12-04 15:52:35",
     * "user_id": "ESB分配的用户ID",
     * "user_key": "ESB分配的用户密钥"
     * },
     * "data": {
     * "msgcode": "DIST_ORDER_20241204155234416",
     * "pk_value": "HBZY_2024120300023",
     * "result": "1",
     * "msg": "消息处理成功"
     * }
     * }
     */
    private final static String LOGISTICS_RESULT_DATA = "data";
    private final static String LOGISTICS_RESULT_DATA_RESULT = "result";
    private final static String LOGISTICS_RESULT_DATA_MSG = "msg";

    /**
     * 获取配货订单
     *
     * @param mc04CgtDistOrderCode 配货订单代码
     * @return 配货订单
     */
    private List<Mc04IslmCgtDistOrderDO> getDistOrderDos(String mc04CgtDistOrderCode) {
        QueryWrapper<Mc04IslmCgtDistOrderDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Mc04IslmCgtDistOrderDO::getMc04CgtDistOrderCode, mc04CgtDistOrderCode).eq(Mc04IslmCgtDistOrderDO::getMa02TobaProdTradeTypeCode, CommonConstants.NO);
        return mc04IslmCgtDistOrderService.list(queryWrapper);
    }

    /**
     * 获取返回信息
     *
     * @param result 返回信息
     * @return String 错误信息
     */
    private static String getMsgFromLogistics(Map<String, Object> result) {
        String errorMsg = null;
        if (CollUtil.isEmpty(result)) {
            log.error("物流信息发送失败, 返回值为空");
            return "物流信息发送失败, 无返回";
        }
        if (!result.containsKey(LOGISTICS_RESULT_DATA)) {
            log.error("物流信息发送失败, 返回值为空");
            return "物流信息发送失败,无返回数据";
        }
        Map<String, String> data = Convert.convert(new TypeReference<Map<String, String>>() {
        }, result.get(LOGISTICS_RESULT_DATA));

        if (!CommonConstants.YES.equals(data.get(LOGISTICS_RESULT_DATA_RESULT))) {
            log.error("物流信息发送失败, 错误信息：{}", data.get(LOGISTICS_RESULT_DATA_MSG));
            errorMsg = data.get(LOGISTICS_RESULT_DATA_MSG);
        }

        return errorMsg;
    }


    /**
     * 传输物流的配货订单信息处理
     *
     * @param cgtDistOrderToLogistics 配货订单信息
     */
    private void processCgtDistOrderToLogistics(List<CgtDistOrderToLogistics> cgtDistOrderToLogistics) {
        // 实际到货仓库 交货站点
        List<ContRealReachWhseDTO> realReachWhseList = DelivUtil.getContRealReachWhseList(cgtDistOrderToLogistics.get(0).getBaComOrgCode());
        Map<String, String> realReachWhseMap = realReachWhseList.stream().collect(Collectors.toMap(ContRealReachWhseDTO::getMc04CgtPraInStorehouseCode, ContRealReachWhseDTO::getMc04CgtPraInStorehouseName, (v1, v2) -> v1));

        // 处理卷烟交易合同交货站点名称 是否烟码分离 计量单位字段
        for (CgtDistOrderToLogistics logistics : cgtDistOrderToLogistics) {
            // 合同零点类型 0:非备货,1:BHYK,3:LDFH
            String mc04ContZeroClockType = logistics.getMc04ContZeroClockType();
            // 是否烟码分离0否1是（备货移库传1，其余传0）
            logistics.setMc04CgtIsCartonSeparation(CommonConstants.YES.equals(mc04ContZeroClockType) ? CommonConstants.YES : CommonConstants.NO);
            // 雪茄支 卷烟箱
            boolean isXj = ProdTradeTypeCodeEnum.PROD_TRADE_TYPE_CODE_1.getCode().equals(logistics.getMa02TobaProdTradeTypeCode());
            // 判断是否是雪茄
            if (isXj) {
                logistics.setZaMeasureName("支");
                logistics.setMd02CgtDistConfirmQty(logistics.getMd02CgtDistConfirmQty().multiply(new BigDecimal(10000)));
            } else {
                logistics.setZaMeasureName("箱");
                logistics.setMd02CgtDistConfirmQty(logistics.getMd02CgtDistConfirmQty().divide(new BigDecimal(5), 6, RoundingMode.HALF_UP));
            }
            // 卷烟交易合同交货站点名称
            logistics.setMc04CgtPraInStorehouseName(realReachWhseMap.get(logistics.getMc04CgtPraInStorehouseCode()));
        }
    }

    /**
     * 传递配货订单给物流
     *
     * @param mc04CgtDistOrderCode 配货订单代码
     * @return 错误信息 空表示成功
     */
    @Override
    public String pushDistToLogistics(String mc04CgtDistOrderCode) {
        // 先查询配货单
        List<Mc04IslmCgtDistOrderDO> dbDistOrderDos = getDistOrderDos(mc04CgtDistOrderCode);
        if (CollectionUtil.isNotEmpty(dbDistOrderDos)) {
            // 转换成传给物流的数据 因有JsonProperty 会自动转换成下划线形式
            List<CgtDistOrderToLogistics> cgtDistOrderToLogistics = CgtDistOrderToLogisticsToMc04IslmCgtDistOrderDOConverter.SELF.converterDosToModels(dbDistOrderDos);
            // 处理卷烟交易合同交货站点名称 是否烟码分离 计量单位字段
            processCgtDistOrderToLogistics(cgtDistOrderToLogistics);

            // 请求物流传输数据
            Map<String, Object> param = new HashMap<>(4);
            param.put("serviceId", "sendDistOrderToLogistics");
            Map<String, Object> row = new HashMap<>(4);
            row.put("row", cgtDistOrderToLogistics);
            Map<String, Object> data = new HashMap<>(4);
            data.put("head", InterHttpUtils.createHead("DIST_ORDER", "配货单信息", null));
            data.put("data", row);
            param.put("data", data);
            log.info("推送物流平台参数:{}", param);
            Map<String, Object> result = threePartySysApi.push(param);
            log.info("推送物流平台返回:{}", result);
            return getMsgFromLogistics(result);
        } else {
            return "未找到该配货单";
        }
    }


    /**
     * 批量传递配货订单给物流
     *
     * @param mc04CgtDistOrderCodes 配货订单代码列表
     * @return 错误信息 空表示成功
     */
    @Override
    public String pushDistToLogisticses(List<String> mc04CgtDistOrderCodes) {
        StringBuilder errorMsg = new StringBuilder();
        for (String mc04CgtDistOrderCode : mc04CgtDistOrderCodes) {
            errorMsg.append(pushDistToLogistics(mc04CgtDistOrderCode));
        }
        return errorMsg.toString();
    }
}
