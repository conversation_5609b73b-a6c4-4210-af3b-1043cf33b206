/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.order.ret.bill;

import com.tobacco.app.isale.domain.model.order.ret.bill.ReturnBillDomain;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtReturnBillDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: qifengyu
 * @Email: <EMAIL>
 * @Create: 2025-07-25
 */

@Mapper

public interface ReturnBillDomainToDoConverter extends StructureBaseConverter<Mc04IslmcCgtReturnBillDO, ReturnBillDomain> {
        ReturnBillDomainToDoConverter INSTANCE = Mappers.getMapper(ReturnBillDomainToDoConverter.class);
}