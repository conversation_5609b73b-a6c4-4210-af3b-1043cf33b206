/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.order.ret.apply;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tobacco.app.isale.domain.model.order.ret.apply.Mc04IslmcIndReturnApply;
import com.tobacco.app.isale.domain.model.order.ret.apply.Mc04IslmcIndReturnApplyItem;
import com.tobacco.app.isale.domain.repository.order.ret.apply.IslmcIndReturnApplyItemRepository;
import com.tobacco.app.isale.infrastructure.converter.order.ret.apply.Mc04IslmcIndReturnApplyItemDOToMc04IslmcIndReturnApplyItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcIndReturnApplyItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcIndReturnApplyItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcIndReturnApplyItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: liuwancheng
 * @Since: 2025-07-25
 */
@Component("ISaleMc04IslmcIndReturnApplyItemDORepository")
public class Mc04IslmcIndReturnApplyItemRepositoryImpl implements IslmcIndReturnApplyItemRepository{

    @Autowired
    private final Mc04IslmcIndReturnApplyItemMapper mc04IslmcIndReturnApplyItemMapper;
    @Autowired
    private final Mc04IslmcIndReturnApplyItemService mc04IslmcIndReturnApplyItemService;

    public Mc04IslmcIndReturnApplyItemRepositoryImpl(Mc04IslmcIndReturnApplyItemMapper mc04IslmcIndReturnApplyItemMapper,
                                                     Mc04IslmcIndReturnApplyItemService mc04IslmcIndReturnApplyItemService) {
        this.mc04IslmcIndReturnApplyItemMapper = mc04IslmcIndReturnApplyItemMapper;
        this.mc04IslmcIndReturnApplyItemService = mc04IslmcIndReturnApplyItemService;
    }

    @Override
    public Page<Mc04IslmcIndReturnApplyItem> page(int offset, int limit, String sort, String order, Mc04IslmcIndReturnApplyItem mc04IslmcIndReturnApplyItem) {
        // 构建查询条件
        LambdaQueryWrapper<Mc04IslmcIndReturnApplyItemDO> queryWrapper = new LambdaQueryWrapper<>();

//        if (domain.getMd02CgtTradeContNo() != null) {
//            queryWrapper.like(Mc04IslmcIndReturnApplyItemDO::getMd02CgtTradeContNo, domain.getMd02CgtTradeContNo());
//        }

        // 执行分页查询
        Page<Mc04IslmcIndReturnApplyItemDO> doPage = new Page<>(offset / limit + 1, limit);
        Page<Mc04IslmcIndReturnApplyItemDO> resultPage = mc04IslmcIndReturnApplyItemMapper.selectPage(doPage, queryWrapper);

        // 转换为领域模型分页
        Page<Mc04IslmcIndReturnApplyItem> domainPage = new Page<>();
        domainPage.setCurrent(resultPage.getCurrent());
        domainPage.setSize(resultPage.getSize());
        domainPage.setTotal(resultPage.getTotal());
        domainPage.setPages(resultPage.getPages());

        // 转换记录列表
        List<Mc04IslmcIndReturnApplyItemDO> records = resultPage.getRecords();

        List<Mc04IslmcIndReturnApplyItem> Mc04IslmcIndReturnApplyItemList = Mc04IslmcIndReturnApplyItemDOToMc04IslmcIndReturnApplyItemConverter.INSTANCE.converterDosToModels(records);
        domainPage.setRecords(Mc04IslmcIndReturnApplyItemList);

        return domainPage;
    }

    @Override
    public List<Mc04IslmcIndReturnApplyItem> listByApplyId(String mc04IndReturnApplyId) {
        LambdaQueryWrapper<Mc04IslmcIndReturnApplyItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmcIndReturnApplyItemDO::getMc04IndReturnApplyId, mc04IndReturnApplyId);
        List<Mc04IslmcIndReturnApplyItemDO> mc04IslmcIndReturnApplyItemDOS = mc04IslmcIndReturnApplyItemMapper.selectList(queryWrapper);
        return Mc04IslmcIndReturnApplyItemDOToMc04IslmcIndReturnApplyItemConverter.INSTANCE.converterDosToModels(mc04IslmcIndReturnApplyItemDOS);
    }

    @Override
    public Boolean removeById(Mc04IslmcIndReturnApply mc04IslmcIndReturnApply) {
        // 删除申请单Items
        LambdaQueryWrapper<Mc04IslmcIndReturnApplyItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmcIndReturnApplyItemDO::getMc04IndReturnApplyId, mc04IslmcIndReturnApply.getMc04IndReturnApplyId());

        return mc04IslmcIndReturnApplyItemService.remove(queryWrapper);
    }
}