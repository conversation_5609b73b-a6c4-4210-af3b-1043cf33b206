/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.common;

import com.tobacco.app.isale.domain.model.item.IccItemDetail;
import com.tobacco.app.isale.domain.model.product.IccProductDetail;
import com.tobacco.app.isale.domain.repository.common.CommodityRepository;
import com.tobacco.app.isale.infrastructure.converter.common.IccItemDetailDTOToIccItemDetailConverter;
import com.tobacco.app.isale.tools.utils.CommodityUtil;
import com.tobacco.app.isalecenter.common.enums.AcOneLevelClassTypeCodeEnum;
import com.tobacco.sc.icommodity.dto.common.constant.dto.item.IccItemDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wanglu<PERSON>01
 * @email : <EMAIL>
 * @create_time : 2025/05/15 09:06
 * @description : 商品中心相关接口
 */
@Slf4j
@Component("ISaleCommodityRepositoryImplDORepository")
public class CommodityRepositoryImpl implements CommodityRepository {

    /**
     * @param cartonCodeList 箱码列表
     * @return Map<String, IccProductDetail>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-28 15:01:07
     * @description : 获取箱码对应的商品信息
     */
    @Override
    public Map<String, IccProductDetail> getIccProductDetailMap(List<String> cartonCodeList) {
        return CommodityUtil.getIccProductDetailMap(cartonCodeList);
    }

    /**
     * @param acTwoLevelCodeList 二级牌号编码列表
     * @param levelCode          牌号级别
     * @return List<IccItemDetail>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-28 14:52:52
     * @description : 获取二级品目编码对应的商品信息
     */
    @Override
    public List<IccItemDetail> getItemDetailList(List<String> acTwoLevelCodeList, String levelCode) {
        return getItemDetailList(acTwoLevelCodeList, levelCode, null);
    }


    /**
     * @param acTwoLevelCodeList      二级牌号编码列表
     * @param levelCode               牌号级别
     * @param acOneLevelClassTypeCode 牌号级别 {@link AcOneLevelClassTypeCodeEnum}
     * @return List<IccItemDetail>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-28 14:52:52
     * @description : 获取二级品目编码对应的商品信息
     */
    @Override
    public List<IccItemDetail> getItemDetailList(List<String> acTwoLevelCodeList, String levelCode,
                                                 String acOneLevelClassTypeCode) {
        Collection<IccItemDetailDTO> iccItemDetailDtos =
                CommodityUtil.getIccItemDetailDtos(acTwoLevelCodeList, levelCode, acOneLevelClassTypeCode);
        return iccItemDetailDtos.stream()
                .map(IccItemDetailDTOToIccItemDetailConverter.INSTANCE::converterDoToModel)
                .collect(Collectors.toList());
    }


    /**
     * @param twoLevelCigCodeList 二级品目编码列表
     * @return Map<String, IccItemDetail>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-04-28 15:01:07
     * @description : 获取二级品目编码对应的商品信息
     */
    @Override
    public Map<String, IccItemDetail> getIccItemDetailMap(List<String> twoLevelCigCodeList) {
        return CommodityUtil.getIccItemDetailMap(twoLevelCigCodeList);
    }

}
