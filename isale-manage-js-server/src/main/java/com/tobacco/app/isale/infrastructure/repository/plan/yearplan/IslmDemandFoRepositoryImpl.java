package com.tobacco.app.isale.infrastructure.repository.plan.yearplan;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tobacco.app.isale.domain.enums.plan.yearplan.YearPlanSubjectTypeEnum;
import com.tobacco.app.isale.domain.model.plan.PlanQty;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmDemandFo;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmDemandFoRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmDemandFoDOToMc04IslmDemandFoConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmDemandFoDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmDemandFoMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmDemandFoService;
import com.tobacco.app.isale.infrastructure.tunnel.database.plan.yearplan.IslmDemandFoMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Year;
import java.util.ArrayList;
import java.util.List;

/**
 * 预测需求填报仓储
 *
 * @author: shicheng Pang
 * @description: 预测需求填报仓储
 * @date: 2025/8/7 14:27
 */
@Component("IslmDemandFoRepositoryImpl")
public class IslmDemandFoRepositoryImpl implements IslmDemandFoRepository {
    private static final String HYPHEN_STRING = "-";
    private static final String COMMA = ",";
    @Resource
    Mc04IslmDemandFoService demandFoService;

    @Autowired
    private Mc04IslmDemandFoMapper demandFoMapper;

    @Resource
    private IslmDemandFoMapper islmDemandFoMapper;

    @Override
    public Mc04IslmDemandFo getDemandFoByWrapper(QueryWrapper<Mc04IslmDemandFoDO> queryWrapper) {
        Mc04IslmDemandFoDO mc04IslmDemandFoDO = demandFoMapper.selectOne(queryWrapper);
        Mc04IslmDemandFo mc04IslmDemandFo = Mc04IslmDemandFoDOToMc04IslmDemandFoConverter.INSTANCE.converterDoToModel(mc04IslmDemandFoDO);
        return mc04IslmDemandFo;
    }

    @Override
    public List<Mc04IslmDemandFo> getDemandFosByWrapper(QueryWrapper<Mc04IslmDemandFoDO> queryWrapper) {
        List<Mc04IslmDemandFoDO> mc04IslmDemandFoDOS = demandFoMapper.selectList(queryWrapper);
        List<Mc04IslmDemandFo> demandFoList = Mc04IslmDemandFoDOToMc04IslmDemandFoConverter.INSTANCE.converterDosToModels(mc04IslmDemandFoDOS);
        return demandFoList;
    }

    @Override
    public Boolean updateDemandFo(Mc04IslmDemandFoDO demandFoNeedUpdate) {
        return demandFoService.updateById(demandFoNeedUpdate);
    }

    @Override
    public Boolean saveDemandFo(Mc04IslmDemandFoDO save) {
        return demandFoService.save(save);
    }

    @Override
    public List<Mc04IslmDemandFo> getYcDemandFoListByBusiComCode(List<String> busiComCode, String zaOccurrenceYear) {
        LambdaQueryWrapper<Mc04IslmDemandFoDO> queryWrapper = new LambdaQueryWrapper<Mc04IslmDemandFoDO>()
                .eq(Mc04IslmDemandFoDO::getZaOccurrenceYear, zaOccurrenceYear)
                .eq(Mc04IslmDemandFoDO::getMc04PlanSubjectType, YearPlanSubjectTypeEnum.SUBJECT_TYPE_REVISE.getCode())
                .in(Mc04IslmDemandFoDO::getBaComOrgCode, busiComCode);
        List<Mc04IslmDemandFoDO> demandFoDOList = demandFoMapper.selectList(queryWrapper);
        return Mc04IslmDemandFoDOToMc04IslmDemandFoConverter.INSTANCE.converterDosToModels(demandFoDOList);
    }

    @Override
    public List<Mc04IslmDemandFo> list(String zaOccurrenceYear, String subjectType, String baComOrgCode, String mc04ComOrgLevel, String mc04ComOrgIsImported) {
        LambdaQueryWrapper<Mc04IslmDemandFoDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(zaOccurrenceYear)) {
            queryWrapper.eq(Mc04IslmDemandFoDO::getZaOccurrenceYear, zaOccurrenceYear);
        }
        if (StrUtil.isNotBlank(baComOrgCode)) {
            queryWrapper.eq(Mc04IslmDemandFoDO::getBaComOrgCode, baComOrgCode);
        }
        if (StrUtil.isNotBlank(mc04ComOrgLevel)) {
            queryWrapper.eq(Mc04IslmDemandFoDO::getMc04ComOrgLevel, mc04ComOrgLevel);
        }
        if (StrUtil.isNotBlank(mc04ComOrgIsImported)) {
            queryWrapper.eq(Mc04IslmDemandFoDO::getMc04ComOrgIsImported, mc04ComOrgIsImported);
        }
        queryWrapper.eq(Mc04IslmDemandFoDO::getZaOccurrenceYear, zaOccurrenceYear);
        List<Mc04IslmDemandFoDO> list = demandFoMapper.selectList(queryWrapper);
        return Mc04IslmDemandFoDOToMc04IslmDemandFoConverter.INSTANCE.converterDosToModels(list);
    }

    @Override
    public List<PlanQty> getDemandFoConfirmQty(String zaOccurrenceYear,String month, String orgIsImported) {
        return islmDemandFoMapper.getDemandFoConfirmQty(zaOccurrenceYear, month, orgIsImported);
    }

    @Override
    public List<Mc04IslmDemandFo> getNormalDemandFoList(String zaOccurrenceYear, List<String> normalCityBaComOrgCodes, String mc04DemandFoStatus, String makeDate, String mc04PlanSubjectName, String level) {
        QueryWrapper<Mc04IslmDemandFoDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("za_occurrence_year", zaOccurrenceYear);
        queryWrapper.eq("mc04_plan_subject_name", mc04PlanSubjectName);
        queryWrapper.eq("mc04_demand_fo_status", mc04DemandFoStatus);
        //日期范围筛选
        if (StringUtils.isNotBlank(makeDate) && !HYPHEN_STRING.equals(makeDate)) {
            String[] makeDates = makeDate.split(HYPHEN_STRING);
            queryWrapper.ge("md02_cgt_fo_make_begin_date", makeDates[0])
                    .le("md02_cgt_fo_make_end_date", makeDates[1]);
        }
        queryWrapper.eq("mc04_com_org_level", level);
        queryWrapper.in(!normalCityBaComOrgCodes.isEmpty(),"ba_com_org_code", normalCityBaComOrgCodes);
        return getDemandFosByWrapper(queryWrapper);
    }

    @Override
    public List<Mc04IslmDemandFo> getSpecialProvinceDemandFoList(String zaOccurrenceYear, List<String> specialProvinceBaComOrgCodes, String mc04DemandFoStatus, String makeDate, String mc04PlanSubjectName, String level) {
        if(specialProvinceBaComOrgCodes.isEmpty()){
            return new ArrayList<>();
        }
        QueryWrapper<Mc04IslmDemandFoDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("za_occurrence_year", zaOccurrenceYear);
        queryWrapper.eq("mc04_plan_subject_name", mc04PlanSubjectName);
        queryWrapper.eq("mc04_demand_fo_status", mc04DemandFoStatus);
        //日期范围筛选
        if (StringUtils.isNotBlank(makeDate) && !HYPHEN_STRING.equals(makeDate)) {
            String[] makeDates = makeDate.split(HYPHEN_STRING);
            queryWrapper.ge("md02_cgt_fo_make_begin_date", makeDates[0])
                    .le("md02_cgt_fo_make_end_date", makeDates[1]);
        }
        queryWrapper.eq("mc04_com_org_level", level);
        queryWrapper.in(!specialProvinceBaComOrgCodes.isEmpty(),"ba_com_org_code", specialProvinceBaComOrgCodes);
        return getDemandFosByWrapper(queryWrapper);
    }
}
