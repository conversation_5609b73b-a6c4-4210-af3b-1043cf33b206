/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.order.preshipment.demand;

import com.tobacco.app.isale.domain.model.order.preshipment.demand.Mc04IslmcNcStockMoveDemand;
import com.tobacco.app.isale.domain.model.order.preshipment.demand.Mc04IslmcNcStockMoveDemandItem;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcNcStockMoveDemandDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcNcStockMoveDemandItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: hujiarong
 * @Email: <EMAIL>
 * @Create: 2025-07-21
 */

@Mapper

public interface Mc04IslmcNcStockMoveDemandItemDOToMc04IslmcNcStockMoveDemandItemConverter extends StructureBaseConverter<Mc04IslmcNcStockMoveDemandItemDO, Mc04IslmcNcStockMoveDemandItem> {

    Mc04IslmcNcStockMoveDemandItemDOToMc04IslmcNcStockMoveDemandItemConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcNcStockMoveDemandItemDOToMc04IslmcNcStockMoveDemandItemConverter.class);
}