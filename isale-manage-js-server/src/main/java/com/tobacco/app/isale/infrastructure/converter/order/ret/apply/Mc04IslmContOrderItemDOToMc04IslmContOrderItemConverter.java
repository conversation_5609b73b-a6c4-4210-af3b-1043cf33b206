/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.order.ret.apply;

import com.tobacco.app.isale.domain.model.order.ret.apply.Mc04IslmContOrderItem;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: liuwancheng
 * @Email: <EMAIL>
 * @Create: 2025-07-25
 */

@Mapper

public interface Mc04IslmContOrderItemDOToMc04IslmContOrderItemConverter extends StructureBaseConverter<Mc04IslmContOrderItemDO, Mc04IslmContOrderItem> {

        Mc04IslmContOrderItemDOToMc04IslmContOrderItemConverter INSTANCE =
            Mappers.getMapper(Mc04IslmContOrderItemDOToMc04IslmContOrderItemConverter.class);

}