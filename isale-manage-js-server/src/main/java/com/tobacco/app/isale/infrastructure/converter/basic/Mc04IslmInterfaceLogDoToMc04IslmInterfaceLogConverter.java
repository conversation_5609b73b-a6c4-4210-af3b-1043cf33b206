/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.basic;

import com.tobacco.app.isale.domain.model.basic.Mc04IslmInterfaceLog;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmInterfaceLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * @Author: renyonghui
 * @Email: <EMAIL>
 * @Create: 2025-08-14
 */

@Mapper

public interface Mc04IslmInterfaceLogDoToMc04IslmInterfaceLogConverter
        extends StructureBaseConverter<Mc04IslmInterfaceLogDO, Mc04IslmInterfaceLog> {

    Mc04IslmInterfaceLogDoToMc04IslmInterfaceLogConverter INSTANCE =
            Mappers.getMapper(Mc04IslmInterfaceLogDoToMc04IslmInterfaceLogConverter.class);

}