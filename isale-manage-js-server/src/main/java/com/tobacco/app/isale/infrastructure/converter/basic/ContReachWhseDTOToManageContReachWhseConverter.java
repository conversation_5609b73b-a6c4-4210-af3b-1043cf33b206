/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.basic;

import com.tobacco.app.isale.domain.model.basic.ManageContReachWhse;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isalecenter.client.dto.basic.ContReachWhseDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/05/26 15:57
 * @description : 仓库信息转换接口
 */
@Mapper
public interface ContReachWhseDTOToManageContReachWhseConverter
        extends StructureBaseConverter<ContReachWhseDTO, ManageContReachWhse> {
    ContReachWhseDTOToManageContReachWhseConverter INSTANCE =
            Mappers.getMapper(ContReachWhseDTOToManageContReachWhseConverter.class);
}
