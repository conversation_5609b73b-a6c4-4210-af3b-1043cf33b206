/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmContOrderMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmContOrderService;
import org.springframework.stereotype.Service;


/**
 * @description : 服务实现类
 *
 * <AUTHOR> wanglu<PERSON>01
 * @since : 2025-05-20
 * @email : <EMAIL>
 * @create_time : 2025-05-20
 */
@Service
public class Mc04IslmContOrderServiceImpl extends ServiceImpl<Mc04IslmContOrderMapper, Mc04IslmContOrderDO>
        implements Mc04IslmContOrderService {

}