/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtTranDemandAdjItemDO;
import com.tobacco.app.isale.infrastructure.tunnel.database.supply.psc.allocdemand.adjustaudit.Mc04IslmCgtTranDemandAdjItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmCgtTranDemandAdjItemService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: liwensheng
 * @Since: 2025-08-14
 * @Email: <EMAIL>
 * @Create: 2025-08-14
 */
@Service
public class Mc04IslmCgtTranDemandAdjItemServiceImpl extends ServiceImpl<Mc04IslmCgtTranDemandAdjItemMapper, Mc04IslmCgtTranDemandAdjItemDO> implements Mc04IslmCgtTranDemandAdjItemService {

}