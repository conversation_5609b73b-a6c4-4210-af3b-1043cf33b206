/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.yearplan;

import com.tobacco.app.isale.domain.model.plan.yearplan.IslmStockInitItemModel;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmStockInitItemDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 库存初始化 数据转换器
 * <p>
 * Mc04IslmStockInitItemDoConverter
 *
 * @Author: longxi
 * @Since: 2025-08-11
 */

@Mapper
public interface Mc04IslmStockInitItemDoConverter extends StructureBaseConverter<Mc04IslmStockInitItemDO, IslmStockInitItemModel> {

    Mc04IslmStockInitItemDoConverter INSTANCE = Mappers.getMapper(Mc04IslmStockInitItemDoConverter.class);

}