package com.tobacco.app.isale.infrastructure.repository.order.inventory.dailyinventory;


import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.inspur.ind.icom.IcomUtils;
import com.lamboegg.common.base.BaseResultConstant;
import com.tobacco.app.isale.domain.repository.order.inventory.dailyinventory.CgtStockDayRepository;
import com.tobacco.app.isale.tools.utils.InterHttpUtils;
import com.tobacco.app.isalecenter.common.exception.CustomException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: hujiarong
 * @Since: 2025-07-21
 */
@Component("ISaleManageMc04IslmcCgtStockDayDORepository")
@Slf4j
@RequiredArgsConstructor
public class Mc04IslmcCgtStockDayRepositoryImpl implements CgtStockDayRepository {

    private static Logger logger = LoggerFactory.getLogger(Mc04IslmcCgtStockDayRepositoryImpl.class);

    private final InterHttpUtils interHttpUtils;

    @Override
    public List<Map<String,Object>> pull(HashMap<String,Object> params) throws CustomException {
        String msgid = "T_YX_SFCBB";
        Map<String, Object> data = interHttpUtils.createData(msgid, "收发存报表接口", params);
        Map<String, Object> result = interHttpUtils.postPull( "HUB_" + msgid, data);
        return (List<Map<String,Object>>) result.get("row");
    }

}