/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.ycplan;



import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tobacco.app.isale.domain.enums.plan.ycplan.OrgTypeEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.PeriodTypeEnum;
import com.tobacco.app.isale.domain.model.plan.ycplan.Mc04IslmYcSalePlan;
import com.tobacco.app.isale.domain.model.plan.ycplan.Mc04IslmYcSalePlanItem;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanItemModel;
import com.tobacco.app.isale.domain.repository.plan.ycplan.YcSalePlanItemRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.ycplan.Mc04IslmYcSalePlanItemDOToMc04IslmYcSalePlanItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmYcSalePlanDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmYcSalePlanItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmYcSalePlanItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmYcSalePlanService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: loongxi
 * @Since: 2025-08-06
 */
@Component("ISaleMc04IslmYcSalePlanItemDORepository")
public class YcSalePlanItemRepositoryImpl implements YcSalePlanItemRepository {

    @Resource
    private Mc04IslmYcSalePlanItemService mc04IslmYcSalePlanItemService;

    @Override
    public List<Mc04IslmYcSalePlanItem> getListByYcPlanIdList(List<String> planIdList) {
        LambdaQueryWrapper<Mc04IslmYcSalePlanItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Mc04IslmYcSalePlanItemDO::getMc04SalePlanId, planIdList);
        List<Mc04IslmYcSalePlanItemDO> mc04IslmYcSalePlanItemDOList = mc04IslmYcSalePlanItemService.list(queryWrapper);
        return Mc04IslmYcSalePlanItemDOToMc04IslmYcSalePlanItemConverter.INSTANCE.converterDosToModels(mc04IslmYcSalePlanItemDOList);
    }

    @Override
    public void saveBatch(List<Mc04IslmYcSalePlanItem> itemList) {
        List<Mc04IslmYcSalePlanItemDO> mc04IslmYcSalePlanItemDOList = itemList.stream().map(item -> Mc04IslmYcSalePlanItemDOToMc04IslmYcSalePlanItemConverter.INSTANCE.converterModelToDo(item)).collect(Collectors.toList());
        mc04IslmYcSalePlanItemService.saveBatch(mc04IslmYcSalePlanItemDOList);
    }

    @Override
    public void deleteByYcPlanIdList(List<String> planIdList) {
        mc04IslmYcSalePlanItemService.remove(new LambdaQueryWrapper<Mc04IslmYcSalePlanItemDO>().in(Mc04IslmYcSalePlanItemDO::getMc04SalePlanId, planIdList));
    }

    @Override
    public List<Mc04IslmYcSalePlanItem> getListByYcPlanIdListAndProductCode(List<String> planIds, String productCode) {
        LambdaQueryWrapper<Mc04IslmYcSalePlanItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Mc04IslmYcSalePlanItemDO::getMc04SalePlanId, planIds)
                .eq(Mc04IslmYcSalePlanItemDO::getAcCgtCartonCode, productCode);
        List<Mc04IslmYcSalePlanItemDO> itemDOs = mc04IslmYcSalePlanItemService.list(queryWrapper);
        return Mc04IslmYcSalePlanItemDOToMc04IslmYcSalePlanItemConverter.INSTANCE.converterDosToModels(itemDOs);
    }

    @Override
    public void deleteByYcPlanIdAndProductCode(String mc04SalePlanId, String acCgtCartonCode) {
        LambdaQueryWrapper<Mc04IslmYcSalePlanItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmYcSalePlanItemDO::getMc04SalePlanId, mc04SalePlanId)
                .eq(Mc04IslmYcSalePlanItemDO::getAcCgtCartonCode, acCgtCartonCode);
        mc04IslmYcSalePlanItemService.remove(queryWrapper);
    }

    @Override
    public List<Mc04IslmYcSalePlanItem> listByYcPlanId(String mc04SalePlanId) {
        LambdaQueryWrapper<Mc04IslmYcSalePlanItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmYcSalePlanItemDO::getMc04SalePlanId, mc04SalePlanId);
        return Mc04IslmYcSalePlanItemDOToMc04IslmYcSalePlanItemConverter.INSTANCE.converterDosToModels(mc04IslmYcSalePlanItemService.list(queryWrapper));
    }

    @Override
    public void deleteByYcPlanIdListAndProductCode(List<String> provinceYcPlanIdList, String productCode) {
        LambdaQueryWrapper<Mc04IslmYcSalePlanItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Mc04IslmYcSalePlanItemDO::getMc04SalePlanId, provinceYcPlanIdList)
                .eq(Mc04IslmYcSalePlanItemDO::getAcCgtCartonCode, productCode);
        mc04IslmYcSalePlanItemService.remove(queryWrapper);
    }

}