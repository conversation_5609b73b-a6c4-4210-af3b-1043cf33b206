/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcIndDemandFoItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcIndDemandFoItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcIndDemandFoItemService;
import org.springframework.stereotype.Service;


/**
 * @description : 服务实现类
 *
 * <AUTHOR> wangluhao
 * @since : 2025-04-22
 * @email : <EMAIL>
 * @create_time : 2025-04-22
 */
@Service
public class Mc04IslmcIndDemandFoItemServiceImpl extends ServiceImpl<Mc04IslmcIndDemandFoItemMapper, Mc04IslmcIndDemandFoItemDO> implements Mc04IslmcIndDemandFoItemService {

}