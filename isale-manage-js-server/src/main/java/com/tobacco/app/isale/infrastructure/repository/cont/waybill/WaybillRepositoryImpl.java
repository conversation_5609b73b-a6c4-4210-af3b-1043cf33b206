package com.tobacco.app.isale.infrastructure.repository.cont.waybill;


import com.tobacco.app.isale.domain.model.cont.waybill.Waybill;
import com.tobacco.app.isale.domain.repository.cont.waybill.WaybillRepository;
import com.tobacco.app.isale.infrastructure.converter.cont.waybill.Mc04IslmWaybillDoToWaybillConverter;
import com.tobacco.app.isale.infrastructure.converter.cont.waybill.Mc04IslmWaybillLineDoToWaybillLineConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmWaybillDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmWaybillLineDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmWaybillLineService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmWaybillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 承运单明细表
 * </p>
 *
 * @Author: wangluhao
 * @Since: 2025-08-26
 */
@Slf4j
@Component("ISaleManageWaybillRepository")
public class WaybillRepositoryImpl implements WaybillRepository {

    private Mc04IslmWaybillService waybillService;

    @Autowired
    public void setWaybillService(Mc04IslmWaybillService waybillService) {
        this.waybillService = waybillService;
    }

    private Mc04IslmWaybillLineService waybillLineService;

    @Autowired
    public void setWaybillLineService(Mc04IslmWaybillLineService waybillLineService) {
        this.waybillLineService = waybillLineService;
    }


    /**
     * 批量删除
     *
     * @param pkList 主键列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPkList(List<String> pkList) {
        waybillService.removeByIds(pkList);
        waybillLineService.lambdaUpdate()
                .in(Mc04IslmWaybillLineDO::getMc05LogtTransportTransOrderNumber, pkList)
                .remove();
    }

    /**
     * 批量保存
     *
     * @param waybillList 列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<Waybill> waybillList) {
        List<Mc04IslmWaybillDO> waybillDoList =
                Mc04IslmWaybillDoToWaybillConverter.INSTANCE.converterModelsToDos(waybillList);
        waybillService.saveBatch(waybillDoList);
        List<Mc04IslmWaybillLineDO> waybillLineDoList = new ArrayList<>();
        waybillList.forEach(waybill -> {
            List<Mc04IslmWaybillLineDO> waybillLineDos =
                    Mc04IslmWaybillLineDoToWaybillLineConverter.INSTANCE
                            .converterModelsToDos(waybill.getWaybillLineList());
            waybillLineDoList.addAll(waybillLineDos);
        });
        waybillLineService.saveBatch(waybillLineDoList);
    }
}