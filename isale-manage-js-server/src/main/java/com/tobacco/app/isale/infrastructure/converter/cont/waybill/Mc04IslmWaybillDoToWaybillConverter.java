/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.cont.waybill;

import com.tobacco.app.isale.domain.model.cont.waybill.Waybill;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmWaybillDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/06/12 17:11
 * @description : 订单DTO转换成订单
 */
@Mapper
public interface Mc04IslmWaybillDoToWaybillConverter
        extends StructureBaseConverter<Mc04IslmWaybillDO, Waybill> {

    Mc04IslmWaybillDoToWaybillConverter INSTANCE = Mappers.getMapper(Mc04IslmWaybillDoToWaybillConverter.class);

}
