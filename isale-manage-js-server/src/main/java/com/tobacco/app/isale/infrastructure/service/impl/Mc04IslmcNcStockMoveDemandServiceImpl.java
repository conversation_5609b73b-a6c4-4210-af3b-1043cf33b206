/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcNcStockMoveDemandDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcNcStockMoveDemandMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcNcStockMoveDemandService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: hujiarong
 * @Since: 2025-08-27
 * @Email: <EMAIL>
 * @Create: 2025-08-27
        */
@Service
public class Mc04IslmcNcStockMoveDemandServiceImpl extends ServiceImpl<Mc04IslmcNcStockMoveDemandMapper, Mc04IslmcNcStockMoveDemandDO> implements Mc04IslmcNcStockMoveDemandService {

}