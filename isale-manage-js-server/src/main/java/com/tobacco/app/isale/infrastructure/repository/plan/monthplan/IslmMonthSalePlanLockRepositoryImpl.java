/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.monthplan;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tobacco.app.isale.domain.enums.plan.monthplan.BusinessTypeCodeEnum;
import com.tobacco.app.isale.domain.enums.plan.monthplan.MonthSalePlanLockStatusEnum;
import com.tobacco.app.isale.domain.enums.plan.monthplan.TobaProdTradeTypeCodeEnum;
import com.tobacco.app.isale.domain.model.monthplan.Mc04IslmMonthSalePlanLock;
import com.tobacco.app.isale.domain.repository.plan.monthplan.IslmMonthSalePlanLockRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.MonthPlanLockConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanLockDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanLockService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2025/8/7 10:47
 * @version: V1.0
 * @description:
 **/
@Component("IslmMonthSalePlanLockRepositoryImplDORepository")
public class IslmMonthSalePlanLockRepositoryImpl implements IslmMonthSalePlanLockRepository {
    private static Logger logger = LoggerFactory.getLogger(MonthSalePlanRepositoryImpl.class);

    Mc04IslmMonthSalePlanLockService mc04IslmMonthSalePlanLockService;

    @Autowired
    public void setMc04IslmMonthSalePlanLockService(Mc04IslmMonthSalePlanLockService mc04IslmMonthSalePlanLockService) {
        this.mc04IslmMonthSalePlanLockService = mc04IslmMonthSalePlanLockService;
    }

    @Override
    public boolean updateBatchLockState(List<String> baComOrgCodes, MonthSalePlanLockStatusEnum monthSalePlanLockStatusEnum) {
        List<String> businessTypeCodes = Arrays.asList(
                BusinessTypeCodeEnum.SBUMITTIME.getCode(),
                BusinessTypeCodeEnum.MONTHPLANMEASURE.getCode()
        );
        QueryWrapper<Mc04IslmMonthSalePlanLockDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(Mc04IslmMonthSalePlanLockDO::getBaComOrgCode, baComOrgCodes)
                .eq(Mc04IslmMonthSalePlanLockDO::getMa02TobaProdTradeTypeCode, TobaProdTradeTypeCodeEnum.CIGARETTE.getCode())
                .in(Mc04IslmMonthSalePlanLockDO::getMz04BusinessTypeCode, businessTypeCodes);
        if (monthSalePlanLockStatusEnum.getCode().equals(MonthSalePlanLockStatusEnum.LOCK.getCode())) {
            queryWrapper.equals(MonthSalePlanLockStatusEnum.UNLOCK.getCode());
        } else {
            queryWrapper.equals(MonthSalePlanLockStatusEnum.LOCK.getCode());
        }
        List<Mc04IslmMonthSalePlanLockDO> mc04IslmMonthSalePlanLockDOS = mc04IslmMonthSalePlanLockService.list(queryWrapper);
        mc04IslmMonthSalePlanLockDOS.stream().forEach(e -> e.setMa02AsLockState(monthSalePlanLockStatusEnum.getCode()));
        return mc04IslmMonthSalePlanLockService.updateBatchById(mc04IslmMonthSalePlanLockDOS);
    }

    @Override
    public List<Mc04IslmMonthSalePlanLock> queryListByIds(List<String> mc04IslmMontSalePanIds) {
        List<Mc04IslmMonthSalePlanLockDO> mc04IslmMonthSalePlanLockDOS = mc04IslmMonthSalePlanLockService.listByIds(mc04IslmMontSalePanIds);
        return MonthPlanLockConverter.INSTANCE.converterDosToModels(mc04IslmMonthSalePlanLockDOS);
    }
}
