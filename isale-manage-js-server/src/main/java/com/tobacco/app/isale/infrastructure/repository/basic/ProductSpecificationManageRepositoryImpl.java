package com.tobacco.app.isale.infrastructure.repository.basic;


import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.app.converter.product.IccProductDetailToIccProductDetailDTOConverter;
import com.tobacco.app.isale.domain.model.product.IccProductDetail;
import com.tobacco.app.isale.domain.repository.basic.ProductSpecificationManageRepository;
import com.tobacco.sc.icommodity.dto.common.constant.client.api.product.ProductServiceApi;
import com.tobacco.sc.icommodity.dto.common.constant.dto.common.IccMultiResponse;
import com.tobacco.sc.icommodity.dto.common.constant.dto.product.IccProductDetailDTO;
import com.tobacco.sc.icommodity.dto.common.constant.req.product.IccGetProductListRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @Author: qintian
 * @Since: 2025-08-05
 */
@Slf4j
@Component("ProductSpecificationManageRepository")
public class ProductSpecificationManageRepositoryImpl implements ProductSpecificationManageRepository {

    @Autowired
    private ProductServiceApi productServiceApi;

    @Override
    public List<IccProductDetail> productSpecificationManage(
            String productName,
            String barCode,
            String productlifecycleType,
            String isNewProduct
    ) {
        try {
            String icomCode = IcomUtils.getIcomCode();
            IccGetProductListRequest request = new IccGetProductListRequest();
            request.setProviderCustomerId(Collections.singletonList(icomCode));
            request.setAcOneLevelClassTypeCode(Collections.singletonList("01"));
            request.setAcCigSalFlag(Collections.singletonList("1"));
            request.setAcCigBusiTypeCode("A");
            IccMultiResponse<IccProductDetailDTO> result = productServiceApi.getProductList(request);
            Collection<IccProductDetailDTO> items = result.getData().getItems();
            List<IccProductDetailDTO> items1 = (List<IccProductDetailDTO>) items;
            List<IccProductDetailDTO> filteredItems = items1.stream()
                    .filter(iccProductDetailDTO ->
                            (productName == null || iccProductDetailDTO.getProductName().contains(productName)) &&
                                    (barCode == null || iccProductDetailDTO.getProductCode().contains(barCode)) &&
                                    (productlifecycleType == null || iccProductDetailDTO.getProductlifecycleType().equals(productlifecycleType)) &&
                                    (isNewProduct == null || iccProductDetailDTO.getIsNewProduct().equals(isNewProduct))
                    )
                    .collect(Collectors.toList());
            List<IccProductDetail> iccProductDetails = IccProductDetailToIccProductDetailDTOConverter.INSTANCE.converterReqsToModels(filteredItems);
            return iccProductDetails;
        } catch (Exception e) {
            log.error("查询本工业在销的所有卷烟异常", e);
        }
        return new ArrayList<>();
    }

    @Override
    public Boolean saveProductSpecification(List<IccProductDetail> iccProductDetailDTOList) {
        return null;
    }
}