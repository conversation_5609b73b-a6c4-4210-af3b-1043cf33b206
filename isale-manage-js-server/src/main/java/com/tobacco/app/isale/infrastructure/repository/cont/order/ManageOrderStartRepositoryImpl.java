/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.cont.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.base.CustomPage;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.enums.basic.BusiActionLogBusiTypeEnum;
import com.tobacco.app.isale.domain.model.cont.order.ManageContOrder;
import com.tobacco.app.isale.domain.model.cont.order.ManageContOrderFromDayPlan;
import com.tobacco.app.isale.domain.model.cont.order.ManageContOrderItem;
import com.tobacco.app.isale.domain.model.cont.order.ManageOrderFromDayPlanPage;
import com.tobacco.app.isale.domain.repository.cont.order.ManageOrderStartRepository;
import com.tobacco.app.isale.infrastructure.converter.cont.order.Mc04IslmContOrderDOToManageContOrderConverter;
import com.tobacco.app.isale.infrastructure.converter.cont.order.Mc04IslmContOrderItemDOToManageContOrderItemConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderItemDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtDayPlanDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmContOrderItemService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmContOrderService;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcCgtDayPlanService;
import com.tobacco.app.isale.infrastructure.tunnel.database.cont.order.ManageOrderStartMapper;
import com.tobacco.app.isale.tools.utils.CommodityUtil;
import com.tobacco.app.isale.tools.utils.CustUtil;
import com.tobacco.app.isale.tools.utils.LogUtil;
import com.tobacco.app.isalecenter.client.dto.cont.order.ContOrderFromDayPlanDTO;
import com.tobacco.sc.icommodity.dto.common.constant.dto.item.IccItemDetailDTO;
import com.tobacco.sc.icommodity.dto.common.constant.dto.product.IccProductDetailDTO;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tobacco.app.isale.domain.enums.order.ret.bill.IsaleProdTradeType.PROD_TRADE_TYPE_10;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/05/22 19:02
 * @description : 订单开始
 */
@Slf4j
@Component("ISaleManageOrderStartRepository")
public class ManageOrderStartRepositoryImpl implements ManageOrderStartRepository {


    @Resource
    private ManageOrderStartMapper manageOrderStartMapper;

    private Mc04IslmContOrderService contOrderService;

    @Autowired
    public void setContOrderService(Mc04IslmContOrderService contOrderService) {
        this.contOrderService = contOrderService;
    }

    private Mc04IslmContOrderItemService contOrderItemService;

    @Autowired
    public void setContOrderItemService(Mc04IslmContOrderItemService contOrderItemService) {
        this.contOrderItemService = contOrderItemService;
    }

    private Mc04IslmcCgtDayPlanService mc04CgtDayPlanService;

    @Autowired
    public void setMc04CgtDayPlanService(Mc04IslmcCgtDayPlanService mc04CgtDayPlanService) {
        this.mc04CgtDayPlanService = mc04CgtDayPlanService;
    }

    /**
     * @param dayPlanPage 查询参数
     * @return Page<ManageContOrderFromDayPlan>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-22 19:09:36
     * @description : 查询订单分页数据
     */
    @Override
    public Page<ManageContOrderFromDayPlan> queryOrderPageFromDayPlan(ManageOrderFromDayPlanPage dayPlanPage) {

        //查主表
        CustomPage<ContOrderFromDayPlanDTO> pageQuery = new CustomPage<>(
                dayPlanPage.getOffset(),
                dayPlanPage.getLimit(),
                dayPlanPage.getSort(),
                dayPlanPage.getOrder()
        );

        Page<ManageContOrderFromDayPlan> pageResult =
                manageOrderStartMapper.queryOrderPageFromDayPlan(pageQuery, dayPlanPage, IcomUtils.getIcomCode());
        //查从表
        if (pageResult.getTotal() > 0) {
            List<String> pageDayPlanCodeList = pageResult.getRecords().stream()
                    .map(ManageContOrderFromDayPlan::getMc04CgtDayPlanCode)
                    .collect(Collectors.toList());
            List<ManageContOrderFromDayPlan> list =
                    manageOrderStartMapper.queryOrderListFromDayPlan(pageDayPlanCodeList);
            Map<String, ManageContOrderFromDayPlan> dayPlanCodeMap = list.stream()
                    .collect(Collectors.toMap(ManageContOrderFromDayPlan::getMc04CgtDayPlanCode, Function.identity()));

            //获取地市名称
            List<String> comIdList = pageResult.getRecords().stream()
                    .map(ManageContOrderFromDayPlan::getBaComOrgCode)
                    .collect(Collectors.toList());
            Map<String, BusiComDto> busiComDtoMap = CustUtil.getBusiComDtoMap(comIdList);
            //获取卷烟名称
            List<String> cartonCodeList = new ArrayList<>();
            List<String> acTwoLevelCigCodeList = new ArrayList<>();
            list.forEach(order ->
                    order.getContOrderItemList().forEach(item -> {
                        acTwoLevelCigCodeList.add(item.getAcTwoLevelCigCode());
                        cartonCodeList.add(item.getAcCgtCartonCode());
                    }));
            Map<String, IccItemDetailDTO> itemDetailDtoMap = CommodityUtil.getIccItemDetailDtoMap(acTwoLevelCigCodeList);
            Map<String, IccProductDetailDTO> productDetailDtoMap = CommodityUtil.getIccProductDetailDtoMap(cartonCodeList);
            //拼接数据
            pageResult.getRecords().forEach(contOrder -> {
                BusiComDto busiComDto = busiComDtoMap.get(contOrder.getBaComOrgCode());
                Assert.notNull(busiComDto, () -> new CustomException("中心未获取到" +
                        contOrder.getBaComOrgCode() + "的协议单位信息"));
                contOrder.setBaComOrgName(busiComDto.getBaComOrgName());
                contOrder.setMc04ComOrgShortName(busiComDto.getMc04ComOrgShortName());

                ManageContOrderFromDayPlan contOrderFromDayPlan = dayPlanCodeMap.get(contOrder.getMc04CgtDayPlanCode());
                Assert.notNull(contOrderFromDayPlan, () -> new CustomException("日计划详情获取失败"));
                Assert.notEmpty(contOrderFromDayPlan.getContOrderItemList(),
                        () -> new CustomException("日计划从表获取失败"));
                contOrderFromDayPlan.getContOrderItemList().forEach(item -> {
                    IccItemDetailDTO itemDetailDto = itemDetailDtoMap.get(item.getAcTwoLevelCigCode());
                    Assert.notNull(itemDetailDto, () -> new CustomException("商品中心未获取到" +
                            item.getAcTwoLevelCigCode() + "的二级牌号信息"));
                    item.setAcTwoLevelCigName(itemDetailDto.getAcTwoLevelCigName());

                    IccProductDetailDTO productDetailDto = productDetailDtoMap.get(item.getAcCgtCartonCode());
                    Assert.notNull(productDetailDto, () -> new CustomException("商品中心未获取到" +
                            item.getAcCgtCartonCode() + "的卷烟信息"));
                    item.setAcMateTaxTranPr(productDetailDto.getAcMateTaxTranPr());
                    item.setSeq(itemDetailDto.getProductSeq());
                });

                contOrder.setContOrderItemList(contOrderFromDayPlan.getContOrderItemList());
            });
        }
        return pageResult;
    }


    /**
     * @param dayPlanCode 日计划编码
     * @return ManageContOrderFromDayPlan
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 查询订单详情
     */
    @Override
    public ManageContOrderFromDayPlan queryOrderDetailFromDayPlan(String dayPlanCode) {
        List<ManageContOrderFromDayPlan> list =
                manageOrderStartMapper.queryOrderListFromDayPlan(Collections.singletonList(dayPlanCode));
        Assert.notEmpty(list, () -> new CustomException("日计划获取失败"));
        ManageContOrderFromDayPlan manageContOrderFromDayPlan = list.get(0);


        //获取地市名称
        BusiComDto busiComDto = CustUtil.getBusiComDto(manageContOrderFromDayPlan.getBaComOrgCode());
        Assert.notNull(busiComDto, () -> new CustomException("中心未获取到" +
                manageContOrderFromDayPlan.getBaComOrgCode() + "的协议单位信息"));
        if (busiComDto != null) {
            manageContOrderFromDayPlan.setBaComOrgName(busiComDto.getBaComOrgName());
            manageContOrderFromDayPlan.setMc04ComOrgShortName(busiComDto.getMc04ComOrgShortName());
        }
        Assert.notEmpty(manageContOrderFromDayPlan.getContOrderItemList(), () -> new CustomException("日计划从表获取失败"));
        List<String> cartonCodeList = new ArrayList<>();
        List<String> acTwoLevelCigCodeList = manageContOrderFromDayPlan.getContOrderItemList().stream()
                .map(item -> {
                    cartonCodeList.add(item.getAcCgtCartonCode());
                    return item.getAcTwoLevelCigCode();
                })
                .collect(Collectors.toList());
        Map<String, IccItemDetailDTO> itemDetailDtoMap = CommodityUtil.getIccItemDetailDtoMap(acTwoLevelCigCodeList);
        Map<String, IccProductDetailDTO> productDetailDtoMap = CommodityUtil.getIccProductDetailDtoMap(cartonCodeList);
        manageContOrderFromDayPlan.getContOrderItemList().forEach(orderItem -> {
            IccProductDetailDTO productDetailDTO = productDetailDtoMap.get(orderItem.getAcCgtCartonCode());
            Assert.notNull(productDetailDTO, () -> new CustomException("商品中心未获取到" +
                    orderItem.getAcCgtCartonCode() + "的商品信息"));
            orderItem.setAcCgtCartonName(productDetailDTO.getProductName());
            orderItem.setAcMateTaxTranPr(productDetailDTO.getAcMateTaxTranPr());
            orderItem.setPackageQty3(productDetailDTO.getPackageQty3());
            IccItemDetailDTO detailDTO = itemDetailDtoMap.get(orderItem.getAcTwoLevelCigCode());
            Assert.notNull(detailDTO, () -> new CustomException("商品中心未获取到" +
                    orderItem.getAcTwoLevelCigCode() + "的二级牌号信息"));
            orderItem.setAcTwoLevelCigName(detailDTO.getAcTwoLevelCigName());
        });
        return manageContOrderFromDayPlan;
    }

    /**
     * @param orderList 订单
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 批量保存订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSaveOrder(List<ManageContOrder> orderList) {
        List<Mc04IslmContOrderDO> contOrderDos =
                Mc04IslmContOrderDOToManageContOrderConverter.INSTANCE.converterModelsToDos(orderList);
        boolean flag = contOrderService.saveBatch(contOrderDos);
        if (!flag) {
            throw new CustomException("保存订单失败");
        }

        List<Mc04IslmContOrderItemDO> contOrderItemDos = new ArrayList<>();

        orderList.forEach(order -> {
            List<Mc04IslmContOrderItemDO> orderItemDos = Mc04IslmContOrderItemDOToManageContOrderItemConverter.INSTANCE
                    .converterModelsToDos(order.getContOrderItemList());
            contOrderItemDos.addAll(orderItemDos);
        });

        flag = contOrderItemService.saveBatch(contOrderItemDos);
        if (!flag) {
            throw new CustomException("保存订单从表失败");
        }

        //记录日志
        orderList.forEach(order ->
                LogUtil.saveCreateLog(BusiActionLogBusiTypeEnum.CONT, order.getMc04ContOrderId()));

        return true;
    }

    /**
     * 更新日计划状态
     *
     * @param mc04CgtDayPlanCode 日计划编码
     * @return Boolean
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 更新日计划状态
     */
    @Override
    public Boolean updateDayPlanFinishByDayPlanCode(String mc04CgtDayPlanCode) {
        Assert.notBlank(mc04CgtDayPlanCode, () -> new CustomException("日计划编码不能为空"));
        mc04CgtDayPlanService.lambdaUpdate()
                .eq(Mc04IslmcCgtDayPlanDO::getMc04CgtDayPlanCode, mc04CgtDayPlanCode)
                .set(Mc04IslmcCgtDayPlanDO::getMc04CgtDayPlanStatus, "4")
                .update();
        return true;
    }

    /**
     * 删除订单 根据配货单编码
     *
     * @param distOrderCodeList 配货单编码
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-23 09:14:49
     * @description : 删除订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeContOrderByDistOrderCode(List<String> distOrderCodeList) {
        List<Mc04IslmContOrderDO> list = contOrderService.lambdaQuery()
                .in(Mc04IslmContOrderDO::getMc04CgtDistOrderCode, distOrderCodeList)
                .list();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<String> orderIdList = list.stream()
                .map(Mc04IslmContOrderDO::getMc04ContOrderId)
                .collect(Collectors.toList());
        List<Mc04IslmContOrderItemDO> itemList = contOrderItemService.lambdaQuery()
                .in(Mc04IslmContOrderItemDO::getMc04ContOrderId, orderIdList)
                .list();
        contOrderItemService.lambdaUpdate()
                .in(Mc04IslmContOrderItemDO::getMc04ContOrderId, orderIdList)
                .remove();
        contOrderService.lambdaUpdate()
                .in(Mc04IslmContOrderDO::getMc04ContOrderId, orderIdList)
                .remove();

        List<ManageContOrder> orderList = Mc04IslmContOrderDOToManageContOrderConverter.INSTANCE
                .converterDosToModels(list);

        List<ManageContOrderItem> orderItemList = Mc04IslmContOrderItemDOToManageContOrderItemConverter.INSTANCE
                .converterDosToModels(itemList);

        Map<String, List<ManageContOrderItem>> contOrderIdMap = orderItemList.stream()
                .collect(Collectors.groupingBy(ManageContOrderItem::getMc04ContOrderId));

        //记录日志
        orderList.forEach(order -> {
            order.setContOrderItemList(contOrderIdMap.get(order.getMc04ContOrderId()));
            LogUtil.saveDeleteLog(BusiActionLogBusiTypeEnum.CONT, order.getMc04ContOrderId(),
                    JSON.toJSONString(order));
        });
    }



    @Override
    public List<String> getReturnBillIdListAndOrder(){
        List<Mc04IslmContOrderDO> list = contOrderService.lambdaQuery()
                .eq(Mc04IslmContOrderDO::getMa02TobaProdTradeTypeCode, PROD_TRADE_TYPE_10.getStatus())
                .isNotNull(Mc04IslmContOrderDO::getMc04CgtDayPlanCode)
                .list();
        return list.stream()
                .map(Mc04IslmContOrderDO::getMc04CgtDayPlanCode)
                .collect(Collectors.toList());
    }
}
