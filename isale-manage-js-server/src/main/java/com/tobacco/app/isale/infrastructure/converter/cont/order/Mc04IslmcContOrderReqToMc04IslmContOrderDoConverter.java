/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.cont.order;

import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmContOrderDO;
import com.tobacco.app.isalecenter.client.req.cont.order.Mc04IslmcContOrderREQ;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/08/05 19:25
 * @description : Mc04IslmcContOrderREQToMc04IslmContOrderDO
 */
@Mapper
public interface Mc04IslmcContOrderReqToMc04IslmContOrderDoConverter
        extends StructureBaseConverter<Mc04IslmcContOrderREQ, Mc04IslmContOrderDO> {

    Mc04IslmcContOrderReqToMc04IslmContOrderDoConverter INSTANCE =
            Mappers.getMapper(Mc04IslmcContOrderReqToMc04IslmContOrderDoConverter.class);


}
