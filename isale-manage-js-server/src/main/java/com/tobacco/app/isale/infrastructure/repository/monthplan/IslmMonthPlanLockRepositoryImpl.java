/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.monthplan;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tobacco.app.isale.domain.model.monthplan.Mc04IslmMonthSalePlanLock;
import com.tobacco.app.isale.domain.repository.monthplan.IslmMonthPlanLockRepository;
import com.tobacco.app.isale.infrastructure.converter.plan.monthplan.MonthPlanLockConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmMonthSalePlanLockDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmMonthSalePlanLockService;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: jinfuli
 * @Date: 2025/6/10
 * @Description:
 */
@Repository
public class IslmMonthPlanLockRepositoryImpl implements IslmMonthPlanLockRepository {

    private final Mc04IslmMonthSalePlanLockService mc04IslmMonthSalePlanLockService;

    public IslmMonthPlanLockRepositoryImpl(Mc04IslmMonthSalePlanLockService mc04IslmMonthSalePlanLockService) {
        this.mc04IslmMonthSalePlanLockService = mc04IslmMonthSalePlanLockService;
    }

    /**
     * 解锁锁定 月计划
     */
    @Override
   public Boolean updateMonthPlanLock(List<Mc04IslmMonthSalePlanLock> mc04IslmMonthSalePlanItemLocks) {
        List<Mc04IslmMonthSalePlanLockDO> res = MonthPlanLockConverter.INSTANCE.converterModelsToDos(mc04IslmMonthSalePlanItemLocks);
        return mc04IslmMonthSalePlanLockService.updateBatchById(res);
    }


    /**
     * 查询月计划锁定状态

     */
    @Override
    public List<Mc04IslmMonthSalePlanLock> queryMonthPlanLock(
            String cgtType,
            String status,
            String comIds,
            String businessType
    ) {
        QueryWrapper<Mc04IslmMonthSalePlanLockDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Mc04IslmMonthSalePlanLockDO::getMa02TobaProdTradeTypeCode, cgtType)
                .eq(Mc04IslmMonthSalePlanLockDO::getMz04BusinessTypeCode, businessType);
        if (comIds != null && !comIds.isEmpty()){
            queryWrapper.lambda().in(Mc04IslmMonthSalePlanLockDO::getBaComOrgCode, Arrays.asList(comIds.split(",")));
        }
        if (status != null && !status.isEmpty()){
            queryWrapper.lambda().in(Mc04IslmMonthSalePlanLockDO::getMa02AsLockState, Arrays.asList(status.split( ",")));
        }
        List<Mc04IslmMonthSalePlanLockDO> doList = mc04IslmMonthSalePlanLockService.list(queryWrapper);
        return MonthPlanLockConverter.INSTANCE.converterDosToModels(doList);
    }
}
