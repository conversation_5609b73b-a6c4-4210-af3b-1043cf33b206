/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.weekplan.weekplansubmit;

import com.tobacco.app.isale.domain.model.plan.weekplan.weekplansubmit.IslmcWeekPlanItem;
import com.tobacco.app.isale.dto.plan.weekplan.weekplansubmit.IslmcWeekPlanDTO;
import com.tobacco.app.isale.app.converter.BaseConverter;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @create 2025/8/27
 * @description 描述
 */
@Mapper(componentModel = "spring")
public interface IslmcWeekPlanConverter extends BaseConverter<IslmcWeekPlanDTO, IslmcWeekPlanDTO, IslmcWeekPlanItem> {
    IslmcWeekPlanConverter INSTANCE = Mappers.getMapper(IslmcWeekPlanConverter.class);
}

