/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcIndReturnApplyItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcIndReturnApplyItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcIndReturnApplyItemService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: liuwancheng
 * @Since: 2025-07-25
 * @Email: <EMAIL>
 * @Create: 2025-07-25
 */
@Service
public class Mc04IslmcIndReturnApplyItemServiceImpl extends ServiceImpl<Mc04IslmcIndReturnApplyItemMapper, Mc04IslmcIndReturnApplyItemDO> implements Mc04IslmcIndReturnApplyItemService {

}