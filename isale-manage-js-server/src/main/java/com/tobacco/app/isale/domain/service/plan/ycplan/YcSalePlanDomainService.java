/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.domain.service.plan.ycplan;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.alibaba.bizworks.core.specification.ddd.DomainService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huawei.shade.org.apache.http.util.Asserts;
import com.inspur.ind.base.CustomException;
import com.inspur.ind.icom.IcomUtils;
import com.tobacco.app.isale.domain.enums.plan.ycplan.LatestVersionEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.OrgTypeEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.PeriodTypeEnum;
import com.tobacco.app.isale.domain.enums.plan.ycplan.SalePlanStatusEnum;
import com.tobacco.app.isale.domain.model.plan.PlanQty;
import com.tobacco.app.isale.domain.model.plan.ycplan.*;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanItemModel;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanModel;
import com.tobacco.app.isale.domain.repository.plan.ycplan.YcPlanCityRepository;
import com.tobacco.app.isale.domain.repository.plan.ycplan.YcSalePlanItemRepository;
import com.tobacco.app.isale.domain.repository.plan.ycplan.YcSalePlanRepository;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmDemandFoRepository;
import com.tobacco.app.isale.dto.common.TreeHeader;
import com.tobacco.app.isale.req.plan.ycplan.YcPlanAllotItemProvinceSaveReq;
import com.tobacco.app.isale.req.plan.ycplan.YcPlanAllotProvinceReq;
import com.tobacco.sc.icommodity.dto.common.constant.client.api.product.ProductServiceApi;
import com.tobacco.sc.icommodity.dto.common.constant.dto.common.IccMultiDataDTO;
import com.tobacco.sc.icommodity.dto.common.constant.dto.common.IccMultiResponse;
import com.tobacco.sc.icommodity.dto.common.constant.dto.product.IccProductDetailDTO;
import com.tobacco.sc.icommodity.dto.common.constant.req.product.IccGetProductListRequest;
import com.tobacco.sc.icust.client.api.com.ComServiceAPI;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import com.tobacco.sc.icust.dto.com.ComTree;
import com.tobacco.sc.icust.dto.common.KCMultiResponse;
import com.tobacco.sc.icust.req.com.GetBusiComListREQ;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Component("YcSalePlanDS")
@DomainService(name = "YcSalePlan", desc = "元春计划领域服务")
@RequiredArgsConstructor
public class YcSalePlanDomainService {

    @Resource
    private YcPlanCityRepository ycPlanCityRepository;
    @Resource
    private YcSalePlanRepository ycSalePlanRepository;
    @Resource
    private YcSalePlanItemRepository ycSalePlanItemRepository;
    @Resource
    private ProductServiceApi productServiceApi;
    @Resource
    private ComServiceAPI comServiceAPI;
    @Resource
    private IslmDemandFoRepository demandFoRepository;

    @ReturnValue(desc = "元春计划市一级列表结果")
    @Method(name = "元春计划市一级列表结果")
    public List<YcPlanCity> getYcPlanCityList(String baComOrgCode, String zaOccurrenceYear) {
        Map<String, YcPlanCity> ycPlanCityMap = new LinkedHashMap<>();

        // 定义月份列表
        List<String> months = Arrays.asList("01", "02");
        // 处理每个月份的数据
        for (String month : months) {
            processMonthData(ycPlanCityMap, baComOrgCode, zaOccurrenceYear, month);
        }
        return new ArrayList<>(ycPlanCityMap.values());
    }

    @ReturnValue(desc = "元春计划列表分页查询结果")
    @Method(name = "元春计划列表分页查询")
    public Page<Mc04IslmYcSalePlan> queryYcPlanPage(YcSalePlanPage ycSalePlanPage) {
        return ycSalePlanRepository.queryYcPlanPage(ycSalePlanPage);
    }

    @ReturnValue(desc = "元春计划编制详情结果")
    @Method(name = "获取元春计划编制详情")
    public YcPlanAllotProvince getPlanAllotProvinceData(String mc04SalePlanId, String mc04OrgTypeCode) {

        // 获取基础数据
        Mc04IslmYcSalePlan provincePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(mc04SalePlanId);
        int currentYear = Integer.parseInt(provincePlan.getZaOccurrenceYear()) - 1;
        int prevYear = currentYear - 1;
        int nextYear = currentYear + 1;

        // 构建表头集合
        List<TreeHeader> headerList = new ArrayList<>(16);
        headerList.add(buildProvinceHeader(provincePlan, currentYear, prevYear, nextYear));

        // 获取并过滤有效城市
        List<BusiComDto> validCities = getValidCities(provincePlan);

        // 当指定了城市代码时，只保留该城市
        if (StringUtils.isNotBlank(mc04OrgTypeCode)) {
            validCities = validCities.stream()
                    .filter(city -> city.getBaComOrgCode().equals(mc04OrgTypeCode))
                    .collect(Collectors.toList());
        }
        headerList.addAll(buildCityHeaders(validCities, currentYear, prevYear, nextYear));

        // 生成数据部分
        List<Mc04IslmYcSalePlan> relatedPlans = ycSalePlanRepository.queryRelatedPlans(provincePlan, validCities);
        List<Map<String, Object>> dataList = generateData(provincePlan, validCities, relatedPlans, currentYear, prevYear, nextYear);

        return new YcPlanAllotProvince(headerList, dataList);
    }

    @ReturnValue(desc = "元春计划列表详情结果")
    @Method(name = "元春计划列表详情结果")
    public Mc04IslmYcSalePlan getYcSalePlanDetail(String mc04SalePlanId) {
        // 1.根据id获取元春计划
        Mc04IslmYcSalePlan mc04IslmYcSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById((mc04SalePlanId));
        // 根据全国元春的行获取其他元春计划
        List<Mc04IslmYcSalePlan> ycSalePlanList = getPlanListByYcPlan(mc04IslmYcSalePlan);

        // 2.根据id列表获取元春计划详情
        List<String> planIdList = ycSalePlanList.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
        List<Mc04IslmYcSalePlanItem> ycSalePlanItemList = ycSalePlanItemRepository.getListByYcPlanIdList(planIdList);
        // 如果元春计划详情是空的，就证明是还从未保存过，需要初始化详情信息，但初始化不保存到数据库，等待用户手动保存
        // 详情数量 产品*元春计划 每个元春计划下都有全部的产品
        List<ProductSalePlanItemQgSnSw> productSalePlanItemQgSnSwList;
        // 获取规格列表
        Collection<IccProductDetailDTO> productList = getProductList();
        if (ycSalePlanItemList.isEmpty()) {
            // 根据卷烟产品先初始化卷烟的数据
            // 在根据卷烟产品初始化其他的信息
            productSalePlanItemQgSnSwList = initProductSalePlanItemQgSnSwProductInfo(productList);
            initProductSalePlanItemQgSnSwOtherInfo(productSalePlanItemQgSnSwList, mc04IslmYcSalePlan.getZaOccurrenceYear());

        } else {
            // 需要构建产品的全国省内外列表
            productSalePlanItemQgSnSwList = buildProductSalePlanItemQgSnSwList(ycSalePlanList, ycSalePlanItemList, productList);
            // 构建其他表的信息
            buildOtherInfo(productSalePlanItemQgSnSwList, mc04IslmYcSalePlan.getZaOccurrenceYear());
        }
        mc04IslmYcSalePlan.setYcSalePlanDetailList(productSalePlanItemQgSnSwList);
        return mc04IslmYcSalePlan;
    }

    @ReturnValue(desc = "是否成功")
    @Method(name = "保存元春计划和计划详情")
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveWithItem(Mc04IslmYcSalePlan mc04IslmYcSalePlan) {
        Assert.notNull(mc04IslmYcSalePlan.getMc04SalePlanId(), () -> new CustomException("元春计划不能为空"));
        Assert.notNull(mc04IslmYcSalePlan.getYcSalePlanDetailList(), () -> new CustomException("元春计划详情不能为空"));
        // 1.首先根据id获取元春计划，如果只获取到一条数据那说明是第一次保存，需要创建好其他的元春计划的信息，如果是多条则不需要在保存元春计划，只处理元春计划详情
        Mc04IslmYcSalePlan ycSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(mc04IslmYcSalePlan.getMc04SalePlanId());
        Asserts.notNull(ycSalePlan, "元春计划不存在！请联系管理员");
        Assert.isTrue(ycSalePlan.getMc04SalePlanStatus().equals(SalePlanStatusEnum.DRAFT.getCode()), () -> new CustomException("元春计划状态不是待编制，无法保存！"));
        List<Mc04IslmYcSalePlan> ycSalePlanList = getPlanListByYcPlan(ycSalePlan);
        // 获取产品详情
        Collection<IccProductDetailDTO> productList = getProductList();
        if (ycSalePlanList.size() == 1) {
            // 第一次保存需要保存其他元春计划信息
            saveOtherYcPlan(ycSalePlan);
            // 在获取一次最新的计划
            ycSalePlanList = getPlanListByYcPlan(ycSalePlan);
            // 保存详情信息
            // 根据产品信息，元春计划，以及前端填写的详细信息构建元春计划详情
            saveSalePlanItemList(productList, ycSalePlanList, mc04IslmYcSalePlan.getYcSalePlanDetailList());
        } else {
            // 获取详细信息列表，更新详情信息，无法做到识别更改项，采用先删后插入
            List<String> planIdList = ycSalePlanList.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
            ycSalePlanItemRepository.deleteByYcPlanIdList(planIdList);
            saveSalePlanItemList(productList, ycSalePlanList, mc04IslmYcSalePlan.getYcSalePlanDetailList());
        }
        return true;
    }


    @ReturnValue(desc = "是否成功")
    @Method(name = "重新编制时保存元春计划和计划详情")
    @Transactional(rollbackFor = Exception.class)
    public String reSaveWithItem(Mc04IslmYcSalePlan mc04IslmYcSalePlan) {
        // 先获取之前的元春计划编制
        Assert.notNull(mc04IslmYcSalePlan.getMc04SalePlanId(), () -> new CustomException("元春计划不能为空"));
        Assert.notNull(mc04IslmYcSalePlan.getYcSalePlanDetailList(), () -> new CustomException("元春计划详情不能为空"));
        // 1.首先根据id获取元春计划，如果只获取到一条数据那说明是第一次保存，需要创建好其他的元春计划的信息，如果是多条则不需要在保存元春计划，只处理元春计划详情
        Mc04IslmYcSalePlan ycSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(mc04IslmYcSalePlan.getMc04SalePlanId());
        List<Mc04IslmYcSalePlan> ycSalePlanList = getPlanListByYcPlan(ycSalePlan);
        // 如果是已编制证明是重新编制的第一次保存需要创建新的元春计划，因为新的计划应该是带编制状态
        // 获取产品详情
        Collection<IccProductDetailDTO> productList = getProductList();
        if(ycSalePlan.getMc04SalePlanStatus().equals(SalePlanStatusEnum.PREPARED.getCode())) {
            // 修改为历史版本
            List<Mc04IslmYcSalePlan> newYcSalePlanList = new ArrayList<>();
            for (Mc04IslmYcSalePlan salePlan : ycSalePlanList) {
                salePlan.setMc04IsLastestVersion(LatestVersionEnum.HISTORY.getCode());
                Mc04IslmYcSalePlan newSalePlan = new Mc04IslmYcSalePlan();
                newSalePlan.setMa02TobaProdTradeTypeCode(salePlan.getMa02TobaProdTradeTypeCode());
                Integer newVersion = Integer.valueOf(salePlan.getMc04CgtSalePlanVersion()) + 1;
                newSalePlan.setMc04CgtSalePlanVersion(String.valueOf(newVersion));
                newSalePlan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
                newSalePlan.setZaOccurrenceYear(salePlan.getZaOccurrenceYear());
                newSalePlan.setMc04PlanSubjectType(salePlan.getMc04PlanSubjectType());
                newSalePlan.setMc04PlanSubjectName(salePlan.getMc04PlanSubjectName());
                newSalePlan.setMc04PlanSubjectBeginDate(salePlan.getMc04PlanSubjectBeginDate());
                newSalePlan.setMc04PlanSubjectEndDate(salePlan.getMc04PlanSubjectEndDate());
                newSalePlan.setMc04SalePlanStatus(SalePlanStatusEnum.DRAFT.getCode());
                newSalePlan.setMc04CgtSaleFoPeriodType(salePlan.getMc04CgtSaleFoPeriodType());
                newSalePlan.setMc04CgtSaleFoPeriodCode(salePlan.getMc04CgtSaleFoPeriodCode());
                newSalePlan.setMc04OrgTypeKind(salePlan.getMc04OrgTypeKind());
                newSalePlan.setMc04OrgTypeCode(salePlan.getMc04OrgTypeCode());
                newSalePlan.setMc04OrgTypeName(salePlan.getMc04OrgTypeName());
                newSalePlan.setIcomCode(salePlan.getIcomCode());
                newYcSalePlanList.add(newSalePlan);
            }
            ycSalePlanRepository.updateBatch(ycSalePlanList);
            ycSalePlanRepository.saveBatch(newYcSalePlanList);

            // 在获取一次最新的计划
            ycSalePlanList = getPlanListByYcPlan(newYcSalePlanList.get(0));
            // 保存详情信息
            // 根据产品信息，元春计划，以及前端填写的详细信息构建元春计划详情
            saveSalePlanItemList(productList, ycSalePlanList, mc04IslmYcSalePlan.getYcSalePlanDetailList());

        }
        // 获取详细信息列表，更新详情信息，无法做到识别更改项，采用先删后插入
        ycSalePlan = ycSalePlanList.get(0);
        Assert.isTrue(ycSalePlan.getMc04SalePlanStatus().equals(SalePlanStatusEnum.DRAFT.getCode()), () -> new CustomException("元春计划状态不是待编制，无法保存！"));
        List<String> planIdList = ycSalePlanList.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList());
        ycSalePlanItemRepository.deleteByYcPlanIdList(planIdList);
        saveSalePlanItemList(productList, ycSalePlanList, mc04IslmYcSalePlan.getYcSalePlanDetailList());
        // 返回任意一个新计划的id
        return ycSalePlan.getMc04SalePlanId();
    }

    @ReturnValue(desc = "是否成功")
    @Method(name = "提交元春计划和计划详情")
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitWithItem(Mc04IslmYcSalePlan mc04IslmYcSalePlan) {
        canYcSalePlanSubmit(mc04IslmYcSalePlan);
        Mc04IslmYcSalePlan ycSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(mc04IslmYcSalePlan.getMc04SalePlanId());
        if(ycSalePlan.getMc04SalePlanStatus().equals(SalePlanStatusEnum.PREPARED.getCode())) {
            throw new CustomException("元春计划状态异常：元春计划状态为已编制，不允许重复提交");
        }
        saveWithItem(mc04IslmYcSalePlan);
        List<Mc04IslmYcSalePlan> ycSalePlanList = getPlanListByYcPlan(ycSalePlan);
        for (Mc04IslmYcSalePlan salePlan : ycSalePlanList) {
            salePlan.setMc04SalePlanStatus(SalePlanStatusEnum.PREPARED.getCode());
        }
        ycSalePlanRepository.updateBatch(ycSalePlanList);
        return true;
    }

    @ReturnValue(desc = "是否成功")
    @Method(name = "重新编制时提交元春计划和计划详情")
    @Transactional(rollbackFor = Exception.class)
    public void reSubmitWithItem(Mc04IslmYcSalePlan mc04IslmYcSalePlan) {
        // 需要判断是不是第一次进入重新编制界面就直接提交了，因为会存在计划不一致的问题，需要先创建计划
        // 先获取之前的元春计划编制
        Assert.notNull(mc04IslmYcSalePlan.getMc04SalePlanId(), () -> new CustomException("元春计划不能为空"));
        Assert.notNull(mc04IslmYcSalePlan.getYcSalePlanDetailList(), () -> new CustomException("元春计划详情不能为空"));
        // 1.首先根据id获取元春计划，如果只获取到一条数据那说明是第一次保存，需要创建好其他的元春计划的信息，如果是多条则不需要在保存元春计划，只处理元春计划详情
        Mc04IslmYcSalePlan ycSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(mc04IslmYcSalePlan.getMc04SalePlanId());
        // 如果是已编制证明是重新编制的第一次保存需要创建新的元春计划
        // 获取产品详情
        if(ycSalePlan.getMc04SalePlanStatus().equals(SalePlanStatusEnum.PREPARED.getCode())) {
            String mc04SalePlanId = reSaveWithItem(mc04IslmYcSalePlan);
            mc04IslmYcSalePlan.setMc04SalePlanId(mc04SalePlanId);
            submitWithItem(mc04IslmYcSalePlan);
        } else {
            submitWithItem(mc04IslmYcSalePlan);
        }
    }

    @ReturnValue(desc = "是否成功")
    @Method(name = "开启元春计划分解")
    public Boolean startAllot(String mc04SalePlanId) {
        Assert.notNull(mc04SalePlanId, () -> new CustomException("元春计划不能为空"));
        Mc04IslmYcSalePlan ycSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(mc04SalePlanId);
        List<Mc04IslmYcSalePlan> mc04IslmYcSalePlanList = getPlanListByYcPlan(ycSalePlan);
        for (Mc04IslmYcSalePlan salePlan : mc04IslmYcSalePlanList) {
            if (!salePlan.getMc04SalePlanStatus().equals(SalePlanStatusEnum.PREPARED.getCode())) {
                throw new CustomException("元春计划状态异常！请联系管理员");
            }
            salePlan.setMc04SalePlanStatus(SalePlanStatusEnum.PROVINCIAL_PENDING.getCode());
        }
        ycSalePlanRepository.updateBatch(mc04IslmYcSalePlanList);

        // 开启分解后，自动生成省内的计划和详情
        GetBusiComListREQ q = new GetBusiComListREQ();
        q.setMc04ComOrgLevel("01");
        q.setMc04ComOrgIsImported("0");
        q.setIcomCode(IcomUtils.getIcomCode());
        KCMultiResponse<BusiComDto> comList = comServiceAPI.getBusiComList(q);
        Collection<BusiComDto> items = comList.getData().getItems();
        // 生成本省的计划和详情
        String zaOccurrenceYear = ycSalePlan.getZaOccurrenceYear();
        List<Mc04IslmYcSalePlan> provinceYcPlanList = new ArrayList<>();
        for (BusiComDto com : items) {
            provinceYcPlanList.add(buildProvinceYcPlan(ycSalePlan, PeriodTypeEnum.MONTH.getCode(), zaOccurrenceYear + "01", com));
            provinceYcPlanList.add(buildProvinceYcPlan(ycSalePlan, PeriodTypeEnum.MONTH.getCode(), zaOccurrenceYear + "02", com));
            provinceYcPlanList.add(buildProvinceYcPlan(ycSalePlan, PeriodTypeEnum.YC.getCode(), zaOccurrenceYear, com));
        }
        ycSalePlanRepository.saveBatch(provinceYcPlanList);
        // 生成省的计划，当前只有本省内的计划
        Mc04IslmYcSalePlan salePlan = new Mc04IslmYcSalePlan();
        salePlan.setMc04OrgTypeKind(OrgTypeEnum.PROVINCE.getKind());
        salePlan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        List<Mc04IslmYcSalePlan> provinceYcPanList = ycSalePlanRepository.getYcPlanList(salePlan);
        // 将省内的计划详情，拷贝一份生成本省的详情
        salePlan = new Mc04IslmYcSalePlan();
        salePlan.setMc04OrgTypeKind(OrgTypeEnum.SN.getKind());
        salePlan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        List<Mc04IslmYcSalePlan> snYcPanList = ycSalePlanRepository.getYcPlanList(salePlan);
        // 获取省内的计划详情
        List<Mc04IslmYcSalePlanItem> snYcPlanDetailList = ycSalePlanItemRepository.getListByYcPlanIdList(snYcPanList.stream().map(Mc04IslmYcSalePlan::getMc04SalePlanId).collect(Collectors.toList()));
        // 根据省内计划详情拷贝生成本省的计划详情
        List<Mc04IslmYcSalePlanItem> provinceYcPlanDetailList = new ArrayList<>();
        provinceYcPlanDetailList.addAll(buildSnProvincePlanItems(snYcPanList, snYcPlanDetailList, provinceYcPanList, PeriodTypeEnum.YC.getCode(), zaOccurrenceYear));
        provinceYcPlanDetailList.addAll(buildSnProvincePlanItems(snYcPanList, snYcPlanDetailList, provinceYcPanList, PeriodTypeEnum.MONTH.getCode(), zaOccurrenceYear + "01"));
        provinceYcPlanDetailList.addAll(buildSnProvincePlanItems(snYcPanList, snYcPlanDetailList, provinceYcPanList, PeriodTypeEnum.MONTH.getCode(), zaOccurrenceYear + "02"));
        ycSalePlanItemRepository.saveBatch(provinceYcPlanDetailList);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCityPlanData(YcPlanAllotProvinceReq ycPlanAllotProvinceReq) {
        // 1. 初始化基础数据
        PlanDataContext context = initPlanDataContext(ycPlanAllotProvinceReq);

        // 2. 删除现有城市计划数据
        deleteExistingCityPlans(context);

        // 3. 处理每个产品的城市数据
        processProductCityData(context);

        // 4. 保存所有详情项
        saveAllPlanItems(context);

        return true;
    }


    @ReturnValue(desc = "是否成功")
    @Method(name = "提交城市计划数据")
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitCityPlanData(YcPlanAllotProvinceReq ycPlanAllotProvinceReq) {
        // 1. 数据校验
        validateCityPlanData(ycPlanAllotProvinceReq);

        // 2. 保存城市计划数据
        saveCityPlanData(ycPlanAllotProvinceReq);

        // 3. 获取并更新相关计划状态
        updateRelatedPlansStatus(ycPlanAllotProvinceReq.getMc04SalePlanId());

        return true;
    }


    @ReturnValue(desc = "元春计划分解省结果")
    @Method(name = "元春计划市分解省结果")
    public List<YcPlanAllotItemProvince> getAllotItemProvinceData(String mc04SalePlanId, String acCgtCartonCode) {
        // 1. 获取省级计划
        Mc04IslmYcSalePlan provincePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(mc04SalePlanId);

        Mc04IslmYcSalePlan provinceQuery = new Mc04IslmYcSalePlan();
        provinceQuery.setMc04OrgTypeKind(OrgTypeEnum.PROVINCE.getKind());
        provinceQuery.setZaOccurrenceYear(provincePlan.getZaOccurrenceYear());
        provinceQuery.setMc04CgtSalePlanVersion(provincePlan.getMc04CgtSalePlanVersion());
        provinceQuery.setMc04OrgTypeCode(provincePlan.getMc04OrgTypeCode());
        // 2. 获取省级计划详情
        List<Mc04IslmYcSalePlan> provincePlans = ycSalePlanRepository.getYcPlanList(provinceQuery);

        List<String> provincePlanIds = provincePlans.stream()
                .map(Mc04IslmYcSalePlan::getMc04SalePlanId)
                .collect(Collectors.toList());

        List<Mc04IslmYcSalePlanItem> provinceItems = ycSalePlanItemRepository.getListByYcPlanIdListAndProductCode(
                provincePlanIds, acCgtCartonCode);
        if (provinceItems.isEmpty()) {
            throw new CustomException("该规格在省级计划中不存在");
        }

        // 3. 构建省级数据
        YcPlanAllotItemProvince provinceItem = new YcPlanAllotItemProvince();
        provinceItem.setMc04OrgTypeName(provincePlan.getMc04OrgTypeName());
        provinceItem.setMc04OrgTypeCode(provincePlan.getMc04OrgTypeCode());
        provinceItem.setIsCity(false);
        provinceItem.setAcCgtName(provinceItems.get(0).getAcCgtName());
        provinceItem.setAcCgtCartonCode(provinceItems.get(0).getAcCgtCartonCode());
        for (Mc04IslmYcSalePlan plan : provincePlans) {
            // 传入provinceItems中mc04SalePlanId等于plan的items
            List<Mc04IslmYcSalePlanItem> collect = provinceItems.stream()
                    .filter(item -> item.getMc04SalePlanId().equals(plan.getMc04SalePlanId())).collect(Collectors.toList());
            fillPlanItemData(plan, collect, provinceItem);
        }

        fillYearPlanData(provincePlan, provinceItem);
        // 4. 获取城市列表和比值
        List<BusiComDto> cities = ycSalePlanRepository.getValidCities(provincePlan);
        Map<String, Map<String, BigDecimal>> cityProvinceRatios = ycSalePlanRepository.getCityProvinceRatios(
                provincePlan.getMc04OrgTypeCode(),
                provincePlan.getZaOccurrenceYear(),
                cities
        );
        Mc04IslmYcSalePlan cityQuery = new Mc04IslmYcSalePlan();
        cityQuery.setMc04OrgTypeKind(OrgTypeEnum.CITY.getKind());
        cityQuery.setZaOccurrenceYear(provincePlan.getZaOccurrenceYear());
        cityQuery.setMc04CgtSalePlanVersion(provincePlan.getMc04CgtSalePlanVersion());
        // 5. 获取城市计划数据
        List<Mc04IslmYcSalePlan> cityPlans = ycSalePlanRepository.getYcPlanList(cityQuery);

        List<YcPlanAllotItemProvince> result = new ArrayList<>();
        result.add(provinceItem);

        if (!cityPlans.isEmpty()) {
            // 6. 如果城市计划存在，从数据库获取数据
            List<String> cityPlanIds = cityPlans.stream()
                    .map(Mc04IslmYcSalePlan::getMc04SalePlanId)
                    .collect(Collectors.toList());

            List<Mc04IslmYcSalePlanItem> cityItems = ycSalePlanItemRepository.getListByYcPlanIdListAndProductCode(
                    cityPlanIds, acCgtCartonCode);

            // 按城市分组填充数据
            Map<String, YcPlanAllotItemProvince> cityDataMap = new LinkedHashMap<>();
            Map<String, List<Mc04IslmYcSalePlanItem>> cityItemsByPlanId = cityItems.stream()
                    .collect(Collectors.groupingBy(Mc04IslmYcSalePlanItem::getMc04SalePlanId));

            for (Mc04IslmYcSalePlan cityPlan : cityPlans) {
                List<Mc04IslmYcSalePlanItem> itemsForCity = cityItemsByPlanId.get(cityPlan.getMc04SalePlanId());
                if (itemsForCity == null || itemsForCity.isEmpty()) {
                    continue;
                }

                YcPlanAllotItemProvince cityItem = cityDataMap.computeIfAbsent(cityPlan.getMc04OrgTypeName(), k -> {
                    YcPlanAllotItemProvince item = new YcPlanAllotItemProvince();
                    item.setMc04OrgTypeName(cityPlan.getMc04OrgTypeName());
                    item.setMc04OrgTypeCode(cityPlan.getMc04OrgTypeCode());
                    item.setIsCity(true);
                    item.setAcCgtName(itemsForCity.get(0).getAcCgtName());
                    item.setAcCgtCartonCode(itemsForCity.get(0).getAcCgtCartonCode());
                    return item;
                });

                fillPlanItemData(cityPlan, itemsForCity, cityItem);
                fillYearPlanData(cityPlan, cityItem);
            }
            result.addAll(cityDataMap.values());

            List<BusiComDto> remainingCities = cities.stream()
                    .filter(city -> !cityDataMap.containsKey(city.getMc04ComOrgShortName()))
                    .collect(Collectors.toList());

            result.addAll(calculateCityData(provincePlan, remainingCities, cityProvinceRatios, provinceItem));

        } else {
            // 7. 如果城市计划不存在，根据比值自动生成城市数据
            result.addAll(calculateCityData(provincePlan, cities, cityProvinceRatios, provinceItem));
        }

        return result;
    }

    @ReturnValue(desc = "是否成功")
    @Method(name = "保存分解省的数据")
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveAllotItemProvinceData(YcPlanAllotItemProvinceSaveReq ycPlanAllotProvinceReq) {
        // 1. 验证请求数据
        Assert.notNull(ycPlanAllotProvinceReq.getMc04SalePlanId(), () -> new CustomException("元春计划ID不能为空"));
        Assert.notEmpty(ycPlanAllotProvinceReq.getDataList(), () -> new CustomException("数据列表不能为空"));

        // 2. 获取省级计划信息
        Mc04IslmYcSalePlan provincePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(ycPlanAllotProvinceReq.getMc04SalePlanId());

        // 3. 获取产品信息
        Collection<IccProductDetailDTO> productList = getProductList();

        // 4. 准备要保存的详情列表
        List<Mc04IslmYcSalePlanItem> itemList = new ArrayList<>();

        // 5. 处理每个城市的数据
        for (YcPlanAllotItemProvinceSaveReq.YcPlanAllotItemProvinceData data : ycPlanAllotProvinceReq.getDataList()) {
            if (!data.getIsCity()) {
                continue; // 跳过省级数据
            }

            // 5.1 检查并创建1月计划
            Mc04IslmYcSalePlan janPlan = getOrCreateCityPlan(
                    provincePlan,
                    PeriodTypeEnum.MONTH.getCode(),
                    provincePlan.getZaOccurrenceYear() + "01",
                    data.getMc04OrgTypeCode()
            );

            // 5.2 删除该城市1月计划下该卷烟的所有详情项
            ycSalePlanItemRepository.deleteByYcPlanIdAndProductCode(
                    janPlan.getMc04SalePlanId(),
                    data.getAcCgtName()
            );

            // 5.3 创建1月计划详情
            Mc04IslmYcSalePlanItem janItem = createCityPlanItem(janPlan, data, productList);
            itemList.add(janItem);

            // 5.4 检查并创建2月计划
            Mc04IslmYcSalePlan febPlan = getOrCreateCityPlan(
                    provincePlan,
                    PeriodTypeEnum.MONTH.getCode(),
                    provincePlan.getZaOccurrenceYear() + "02",
                    data.getMc04OrgTypeCode()
            );

            // 5.5 删除该城市2月计划下该卷烟的所有详情项
            ycSalePlanItemRepository.deleteByYcPlanIdAndProductCode(
                    febPlan.getMc04SalePlanId(),
                    data.getAcCgtName()
            );

            // 5.6 创建2月计划详情
            Mc04IslmYcSalePlanItem febItem = createCityPlanItem(febPlan, data, productList);
            febItem.setMd03Cgt10thComInitStkQty(data.getNextYearJanEndStock()); // 2月期初=1月期末
            itemList.add(febItem);

            // 5.7 检查并创建元春计划(1-2月合计)
            Mc04IslmYcSalePlan ycPlan = getOrCreateCityPlan(
                    provincePlan,
                    PeriodTypeEnum.YC.getCode(),
                    provincePlan.getZaOccurrenceYear(),
                    data.getMc04OrgTypeCode()
            );

            // 5.8 删除该城市元春计划下该卷烟的所有详情项
            ycSalePlanItemRepository.deleteByYcPlanIdAndProductCode(
                    ycPlan.getMc04SalePlanId(),
                    data.getAcCgtName()
            );

            // 5.9 创建元春计划详情
            Mc04IslmYcSalePlanItem ycItem = createCityPlanItem(ycPlan, data, productList);
            ycItem.setMc04CgtSalePlanOriginQty(data.getNextYearFirstTwoMonthSalePlan());
            ycItem.setMa02CgtPlResolOrigplanQty(data.getNextYearFirstTwoMonthAllotPlan());
            itemList.add(ycItem);
        }

        // 6. 保存新的详情项
        if (!itemList.isEmpty()) {
            ycSalePlanItemRepository.saveBatch(itemList);
        }

        return true;
    }

    private Mc04IslmYcSalePlan getOrCreateCityPlan(Mc04IslmYcSalePlan provincePlan, String periodType, String periodCode, String cityCode) {
        // 查询是否已存在该城市的计划
        Mc04IslmYcSalePlan query = new Mc04IslmYcSalePlan();
        query.setZaOccurrenceYear(provincePlan.getZaOccurrenceYear());
        query.setMc04CgtSalePlanVersion(provincePlan.getMc04CgtSalePlanVersion());
        query.setMc04OrgTypeKind(OrgTypeEnum.CITY.getKind());
        query.setMc04OrgTypeCode(cityCode);
        query.setMc04CgtSaleFoPeriodType(periodType);
        query.setMc04CgtSaleFoPeriodCode(periodCode);

        List<Mc04IslmYcSalePlan> existingPlans = ycSalePlanRepository.getYcPlanList(query);
        if (!existingPlans.isEmpty()) {
            return existingPlans.get(0);
        }
        GetBusiComListREQ q = new GetBusiComListREQ();
        q.setMc04ComOrgLevel("02");
        q.setMc04ComOrgIsImported("0");
        q.setIcomCode(IcomUtils.getIcomCode());
        KCMultiResponse<BusiComDto> comList = comServiceAPI.getBusiComList(q);
        Collection<BusiComDto> items = comList.getData().getItems();

        // 不存在则创建新计划
        return buildCityPlan(provincePlan, periodType, periodCode, cityCode,(List<BusiComDto>) items);
    }
    private Mc04IslmYcSalePlanItem createCityPlanItem(Mc04IslmYcSalePlan plan,
                                                      YcPlanAllotItemProvinceSaveReq.YcPlanAllotItemProvinceData data,
                                                      Collection<IccProductDetailDTO> productList) {

        Mc04IslmYcSalePlanItem item = new Mc04IslmYcSalePlanItem();
        item.setMc04SalePlanId(plan.getMc04SalePlanId());
        item.setAcCgtName(data.getAcCgtName());

        // 设置产品信息
        IccProductDetailDTO product = getProductDetail(productList, data.getAcCgtCartonCode());
        Assert.notNull(product, () -> new CustomException("产品信息不存在: " + data.getAcCgtName()));

        item.setAcCgtCartonCode(product.getProductCode());
        item.setAcCgtPriceSegmentCode(product.getAcCigRangeCode());
        item.setAcCgtPriceSegmentName(product.getAcCigRangeName());
        item.setAcCgtTaxAllotPrice(new BigDecimal(product.getAcMateTaxTranPr()));
        item.setAcCgtTradePrice(new BigDecimal(product.getWholeSalePrice()));
        item.setAcCgtMediumBranceFlag(product.getIsMedium());
        item.setAcCgtTinyFlag(product.getIsTiny());
        item.setAcCgtTarVal(new BigDecimal(product.getTarQuantity()));
        item.setIcomCode(plan.getIcomCode());

        // 根据计划类型设置不同的数量字段
        String periodType = plan.getMc04CgtSaleFoPeriodType();
        String periodCode = plan.getMc04CgtSaleFoPeriodCode();
        String zaOccurrenceYear = plan.getZaOccurrenceYear();

        if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "01")) {
            // 1月计划
            item.setMd03Cgt10thComInitStkQty(data.getLastYearEndStock());
            item.setMc04CgtSalePlanOriginQty(data.getNextYearJanSalePlan());
            item.setMa02CgtPlResolOrigplanQty(data.getNextYearJanAllotPlan());
            item.setMd03Cgt10thComEndStkQty(data.getNextYearJanEndStock());
        } else if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "02")) {
            // 2月计划
            item.setMc04CgtSalePlanOriginQty(data.getNextYearFebSalePlan());
            item.setMa02CgtPlResolOrigplanQty(data.getNextYearFebAllotPlan());
            item.setMd03Cgt10thComEndStkQty(data.getNextYearFebEndStock());
        }

        return item;
    }


    private void processMonthData(Map<String, YcPlanCity> ycPlanCityMap,
                                  String baComOrgCode,
                                  String zaOccurrenceYear,
                                  String monthCode) {

        String yearMonth = zaOccurrenceYear + monthCode;

        LambdaQueryWrapper<Mc04IslmYcSalePlan> wrapper = new LambdaQueryWrapper<>();
        Mc04IslmYcSalePlan mc04IslmYcSalePlan = new Mc04IslmYcSalePlan();
        // 判空
        if (StrUtil.isNotBlank(baComOrgCode)){
            mc04IslmYcSalePlan.setMc04OrgTypeCode(baComOrgCode);
        }

        mc04IslmYcSalePlan.setZaOccurrenceYear(zaOccurrenceYear);
        mc04IslmYcSalePlan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        mc04IslmYcSalePlan.setMc04CgtSaleFoPeriodCode(yearMonth);

        List<Mc04IslmYcSalePlan> planList = ycSalePlanRepository.getYcPlanList(mc04IslmYcSalePlan);
        if (CollectionUtil.isEmpty(planList)){
            return;
        }

        for (Mc04IslmYcSalePlan plan : planList) {
            List<Mc04IslmYcSalePlanItem> itemList = ycSalePlanItemRepository.getListByYcPlanIdList(Collections.singletonList(plan.getMc04SalePlanId()));

            for (Mc04IslmYcSalePlanItem item : itemList) {
                String acCgtCartonCode = item.getAcCgtCartonCode();
                YcPlanCity ycPlanCity = ycPlanCityMap.computeIfAbsent(acCgtCartonCode, k ->
                        createNewYcPlanCity(item)
                );

                setMonthData(ycPlanCity, monthCode, item);
                querySales(ycPlanCity, baComOrgCode, acCgtCartonCode, yearMonth);

                // 添加计算逻辑
                calculateRatiosAndYoY(ycPlanCity, zaOccurrenceYear);
            }
        }

    }


    private void calculateRatiosAndYoY(YcPlanCity ycPlanCity, String zaOccurrenceYear) {
        calculateMonthRatios(ycPlanCity);
        calculateYoYGrowth(ycPlanCity);
    }

    private void calculateMonthRatios(YcPlanCity ycPlanCity) {
        if (ycPlanCity.getJanuarySalesPlan() != null && ycPlanCity.getFebruarySalesPlan() != null) {
            BigDecimal totalSales = ycPlanCity.getJanuarySalesPlan().add(ycPlanCity.getFebruarySalesPlan());
            if (totalSales.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal januaryRatio = calculateRatio(ycPlanCity.getJanuarySalesPlan(), totalSales);
                BigDecimal februaryRatio = calculateRatio(ycPlanCity.getFebruarySalesPlan(), totalSales);

                ycPlanCity.setJanuaryRatio(formatPercentage(januaryRatio));
                ycPlanCity.setFebruaryRatio(formatPercentage(februaryRatio));
            }
        }
    }

    private BigDecimal calculateRatio(BigDecimal monthSales, BigDecimal totalSales) {
        return monthSales.divide(totalSales, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100));
    }

    private String formatPercentage(BigDecimal value) {
        return value.setScale(2, RoundingMode.HALF_UP).toString() + "%";
    }

    private void calculateYoYGrowth(YcPlanCity ycPlanCity) {
        calculateJanuaryYoY(ycPlanCity);
        calculateFebruaryYoY(ycPlanCity);
    }

    private void calculateJanuaryYoY(YcPlanCity ycPlanCity) {
        if (ycPlanCity.getLastJanuarySales() != null && ycPlanCity.getJanuarySalesPlan() != null) {
            try {
                BigDecimal lastJanSales = new BigDecimal(ycPlanCity.getLastJanuarySales());
                if (lastJanSales.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal januaryYoY = calculateGrowthRate(ycPlanCity.getJanuarySalesPlan(), lastJanSales);
                    ycPlanCity.setJanuaryYoY(formatPercentage(januaryYoY));
                }
            } catch (NumberFormatException e) {
                log.error("Failed to parse lastJanuarySales", e);
            }
        }
    }

    private void calculateFebruaryYoY(YcPlanCity ycPlanCity) {
        if (ycPlanCity.getLastFebruarySales() != null && ycPlanCity.getFebruarySalesPlan() != null) {
            try {
                BigDecimal lastFebSales = new BigDecimal(ycPlanCity.getLastFebruarySales());
                if (lastFebSales.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal februaryYoY = calculateGrowthRate(ycPlanCity.getFebruarySalesPlan(), lastFebSales);
                    ycPlanCity.setFebruaryYoY(formatPercentage(februaryYoY));
                }
            } catch (NumberFormatException e) {
                log.error("Failed to parse lastFebruarySales", e);
            }
        }
    }

    private BigDecimal calculateGrowthRate(BigDecimal currentValue, BigDecimal lastValue) {
        return currentValue.subtract(lastValue)
                .divide(lastValue, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100));
    }

    private void querySales(YcPlanCity ycPlanCity, String baComOrgCode, String acCgtCartonCode, String yearMonth) {
        // 特殊省份编码列表
        Set<String> specialProvinces = new HashSet<>();
        specialProvinces.add("11110000"); // 北京
        specialProvinces.add("11210000"); // 大连
        specialProvinces.add("11500000"); // 重庆
        specialProvinces.add("11120001"); // 天津
        specialProvinces.add("11440301"); // 深圳
        specialProvinces.add("11460001"); // 海南
        specialProvinces.add("11540001"); // 西藏
        specialProvinces.add("11630001"); // 青海
        specialProvinces.add("11640001"); // 宁夏
        specialProvinces.add("11650001"); // 新疆
        specialProvinces.add("99310101");  // 上海

        // 特殊省份转地市编码映射
        Map<String, String> specialProvinceToCityMap = new HashMap<>();
        specialProvinceToCityMap.put("11110000", "11110001"); // 北京
        specialProvinceToCityMap.put("11210000", "11210201"); // 大连
        specialProvinceToCityMap.put("11500000", "11500001"); // 重庆

        GetBusiComListREQ q = new GetBusiComListREQ();
        q.setComCodeArr(Collections.singletonList(baComOrgCode));
        q.setIcomCode(IcomUtils.getIcomCode());
        KCMultiResponse<BusiComDto> comList = comServiceAPI.getBusiComList(q);
        Collection<BusiComDto> items = comList.getData().getItems();

        if (items.isEmpty()) {
            return;
        }

        BusiComDto com = items.iterator().next();
        String orgLevel = com.getMc04ComOrgLevel();
        String orgCode = com.getBaComOrgCode();

        // 1. 特殊省份当作地市直接查询
        if (specialProvinces.contains(orgCode)) {
            // 如果是北京、大连、重庆，使用对应的地市编码查询
            String queryCode = specialProvinceToCityMap.getOrDefault(orgCode, orgCode);
            Map<String, Object> specialProvinceMonthSaleData = ycSalePlanRepository.getSpecialProvinceMonthSaleData(
                    queryCode, acCgtCartonCode, yearMonth);
            setSalesData(ycPlanCity, specialProvinceMonthSaleData);
            return;
        }

        // 2. 正常城市直接查询
        if ("02".equals(orgLevel)) {
            Map<String, Object> monthSaleData = ycSalePlanRepository.getMonthSaleData(
                    orgCode, acCgtCartonCode, yearMonth);
            setSalesData(ycPlanCity, monthSaleData);
            return;
        }

        // 3. 正常省份查询所有地市的和
        if ("01".equals(orgLevel)) {
            Map<String, Object> normalProvinceMonthSaleData = ycSalePlanRepository.getNormalProvinceMonthSaleData(
                    orgCode, acCgtCartonCode, yearMonth);
            setSalesData(ycPlanCity, normalProvinceMonthSaleData);
        }
    }

    private void setSalesData(YcPlanCity ycPlanCity, Map<String, Object> saleData) {
        if (saleData != null) {
            ycPlanCity.setLastJanuarySales((String) saleData.get("lastJanuarySales"));
            ycPlanCity.setLastFebruarySales((String) saleData.get("lastFebruarySales"));
        }
    }

    private void setMonthData(YcPlanCity ycPlanCity, String monthCode, Mc04IslmYcSalePlanItem item) {
        // 获取最后两位数
        String lastTwoDigits = monthCode.substring(monthCode.length() - 2);
        if ("01".equals(lastTwoDigits)) {
            ycPlanCity.setJanuarySalesPlan(item.getMc04CgtSalePlanOriginQty());
            ycPlanCity.setJanuaryAllocationPlan(item.getMa02CgtPlResolOrigplanQty());
        } else if ("02".equals(lastTwoDigits)) {
            ycPlanCity.setFebruarySalesPlan(item.getMc04CgtSalePlanOriginQty());
            ycPlanCity.setFebruaryAllocationPlan(item.getMa02CgtPlResolOrigplanQty());
        }
    }
    private YcPlanCity createNewYcPlanCity(Mc04IslmYcSalePlanItem item) {
        YcPlanCity city = new YcPlanCity();
        city.setAcCgtName(item.getAcCgtName());
        city.setAcCgtTradePrice(item.getAcCgtTradePrice());
        city.setAcCgtPriceSegmentName(item.getAcCgtTradePrice());
        city.setYcSalesPlan(null);
        return city;
    }


    private TreeHeader buildProvinceHeader(Mc04IslmYcSalePlan provincePlan, int currentYear, int prevYear, int nextYear) {
        TreeHeader header = new TreeHeader();
        header.setTitle(provincePlan.getMc04OrgTypeName());
        header.setChildren(generateYearHeaders(currentYear, prevYear, nextYear, provincePlan.getMc04OrgTypeCode(), false));
        return header;
    }
    public List<BusiComDto> getValidCities(Mc04IslmYcSalePlan provincePlan) {
        // 获取原始城市列表
        List<BusiComDto> rawCities = ycSalePlanRepository.getRawCityList(provincePlan.getMc04OrgTypeCode());

        // 计算并过滤有效城市
        Map<String, Map<String, BigDecimal>> cityRatios = ycSalePlanRepository.getCityProvinceRatios(
                provincePlan.getMc04OrgTypeCode(),
                provincePlan.getZaOccurrenceYear(),
                rawCities
        );

        return rawCities.stream()
                .filter(city -> hasValidRatio(cityRatios.get(city.getBaComOrgCode())))
                .collect(Collectors.toList());
    }
    private List<TreeHeader> buildCityHeaders(List<BusiComDto> cities, int currentYear, int prevYear, int nextYear) {
        return cities.stream()
                .map(city -> {
                    TreeHeader header = new TreeHeader();
                    header.setTitle(city.getMc04ComOrgShortName());
                    header.setChildren(generateYearHeaders(currentYear, prevYear, nextYear, city.getBaComOrgCode(), true));
                    return header;
                })
                .collect(Collectors.toList());
    }
    private List<Map<String, Object>> generateData(Mc04IslmYcSalePlan provincePlan, List<BusiComDto> cities,
                                                   List<Mc04IslmYcSalePlan> plans, int currentYear,
                                                   int prevYear, int nextYear) {

        Collection<IccProductDetailDTO> productList = getProductList();

        // 获取已有的城市计划数据
        List<Map<String, Object>> existingData = generateDataList(plans, currentYear, prevYear, nextYear, productList);

        // 获取所有城市代码
        Set<String> existingCityCodes = plans.stream()
                .filter(p -> "02".equals(p.getMc04OrgTypeKind()))
                .map(Mc04IslmYcSalePlan::getMc04OrgTypeCode)
                .collect(Collectors.toSet());

        // 获取需要生成默认数据的城市
        List<BusiComDto> citiesNeedDefault = cities.stream()
                .filter(city -> !existingCityCodes.contains(city.getBaComOrgCode()))
                .collect(Collectors.toList());

        // 生成默认数据
        List<Map<String, Object>> defaultData = generateDefaultDataList(provincePlan, citiesNeedDefault, currentYear, prevYear, nextYear);

        // 合并数据
        List<Map<String, Object>> mergedData = new ArrayList<>();

        // 按卷烟分组合并数据
        Map<String, Map<String, Object>> mergedByCgt = new HashMap<>();

        // 先处理已有数据
        for (Map<String, Object> row : existingData) {
            String cgtCode = (String) row.get("acCgtCartonCode");
            mergedByCgt.put(cgtCode, row);
        }

        // 再处理默认数据，只添加不存在的城市数据
        for (Map<String, Object> row : defaultData) {
            String cgtCode = (String) row.get("acCgtCartonCode");
            Map<String, Object> existingRow = mergedByCgt.get(cgtCode);

            if (existingRow == null) {
                mergedByCgt.put(cgtCode, row);
            } else {
                // 合并数据，只添加默认数据中不存在的城市字段
                for (String key : row.keySet()) {
                    if (!existingRow.containsKey(key)) {
                        existingRow.put(key, row.get(key));
                    }
                }
            }
        }

        mergedData.addAll(mergedByCgt.values());
        return mergedData;
    }

    private List<TreeHeader> generateYearHeaders(int currentYear, int prevYear, int nextYear, String mc04OrgTypeCode, boolean isCity) {
        List<TreeHeader> headers = new ArrayList<>();

        // N-1年末商业库存
        headers.add(new TreeHeader("lastYearEndStock_"+ mc04OrgTypeCode, prevYear + "年末商业库存"));

        // N年相关计划
        headers.add(new TreeHeader("currentYearSalePlan_"+ mc04OrgTypeCode, currentYear + "年预计销售"));
        headers.add(new TreeHeader("currentYearAllotPlan_"+ mc04OrgTypeCode, currentYear + "年预计调拨"));
        headers.add(new TreeHeader("currentYearEndStock_"+ mc04OrgTypeCode, currentYear + "年末预计库存"));

        // N+1年1-2月计划
        headers.add(new TreeHeader("nextYearFirstTwoMonthSalePlan_"+ mc04OrgTypeCode, nextYear + "年1-2月销售计划", "", "","nextYearJanSalePlan_"+ mc04OrgTypeCode + "+" +"nextYearFebSalePlan_" + mc04OrgTypeCode));
        headers.add(new TreeHeader("nextYearFirstTwoMonthAllotPlan_"+ mc04OrgTypeCode, nextYear + "年1-2月调拨计划", "", "","nextYearJanAllotPlan_"+ mc04OrgTypeCode + "+" +"nextYearFebAllotPlan_" + mc04OrgTypeCode));

        // N+1年1月计划（可编辑）
        if (isCity) {
            headers.add(new TreeHeader("nextYearJanSalePlan_"+ mc04OrgTypeCode, nextYear + "年1月销售计划", "input", "ind-red"));
            headers.add(new TreeHeader("nextYearJanAllotPlan_"+ mc04OrgTypeCode, nextYear + "年1月调拨计划", "input", "ind-red"));
            headers.add(new TreeHeader("nextYearJanEndStock_"+ mc04OrgTypeCode, nextYear + "年1月末商业库存", "", "","currentYearEndStock_"+ mc04OrgTypeCode  + "-" +"nextYearJanAllotPlan_"+ mc04OrgTypeCode + "-" +"nextYearJanSalePlan_" + mc04OrgTypeCode));
        } else {
            headers.add(new TreeHeader("nextYearJanSalePlan_"+ mc04OrgTypeCode, nextYear + "年1月销售计划"));
            headers.add(new TreeHeader("nextYearJanAllotPlan_"+ mc04OrgTypeCode, nextYear + "年1月调拨计划"));
            headers.add(new TreeHeader("nextYearJanEndStock_"+ mc04OrgTypeCode, nextYear + "年1月末商业库存"));
        }

        // N+1年2月计划（可编辑）
        if (isCity) {
            headers.add(new TreeHeader("nextYearFebSalePlan_"+ mc04OrgTypeCode, nextYear + "年2月销售计划", "input", "ind-red"));
            headers.add(new TreeHeader("nextYearFebAllotPlan_"+ mc04OrgTypeCode, nextYear + "年2月调拨计划", "input", "ind-red"));
            headers.add(new TreeHeader("nextYearFebEndStock_"+ mc04OrgTypeCode, nextYear + "年2月末商业库存", "", "","nextYearJanEndStock_"+ mc04OrgTypeCode + "-" +"nextYearFebAllotPlan_"+ mc04OrgTypeCode + "-" +"nextYearFebSalePlan_" + mc04OrgTypeCode));
        } else {
            headers.add(new TreeHeader("nextYearFebSalePlan_"+ mc04OrgTypeCode, nextYear + "年2月销售计划"));
            headers.add(new TreeHeader("nextYearFebAllotPlan_"+ mc04OrgTypeCode, nextYear + "年2月调拨计划"));
            headers.add(new TreeHeader("nextYearFebEndStock_"+ mc04OrgTypeCode, nextYear + "年2月末商业库存"));
        }

        return headers;
    }

    private boolean hasValidRatio(Map<String, BigDecimal> ratios) {
        return ratios != null && ratios.values().stream()
                .anyMatch(ratio -> ratio.compareTo(BigDecimal.ZERO) > 0);
    }


    private List<Map<String, Object>> generateDefaultDataList(Mc04IslmYcSalePlan provincePlan,
                                                              List<BusiComDto> filteredCityList,
                                                              int currentYear,
                                                              int prevYear,
                                                              int nextYear) {
        List<Map<String, Object>> dataList = new ArrayList<>();

        // 1. 获取基础数据
        Map<String, Map<String, BigDecimal>> ratios = getCityProvinceRatios(provincePlan, filteredCityList);
        List<Mc04IslmYcSalePlan> provincePlans = getProvincePlans(provincePlan);
        List<Mc04IslmYcSalePlanItem> provinceItems = getProvinceItems(provincePlans);

        // 2. 获取年度计划数据
        List<Mc04IslmSalePlanModel> yearPlans = ycSalePlanRepository.getYearPlans(
                Collections.singletonList(provincePlan), currentYear, prevYear);
        List<Mc04IslmSalePlanItemModel> yearPlanItems = ycSalePlanRepository.getYearPlanItems(yearPlans);

        // 3. 按卷烟分组处理
        Map<String, List<Mc04IslmYcSalePlanItem>> itemsByCgt = provinceItems.stream()
                .collect(Collectors.groupingBy(Mc04IslmYcSalePlanItem::getAcCgtCartonCode));

        // 4. 为每种卷烟生成数据行
        itemsByCgt.forEach((cgtCode, itemList) -> {
            Map<String, Object> row = new HashMap<>();

            // 填充基础信息
            fillBasicInfo(row, itemList.get(0));

            // 填充省的计划数据
            fillProvincePlanData(row, provincePlans, itemList, nextYear);

            // 填充城市数据
            fillCityData(row, filteredCityList, ratios, cgtCode, provincePlan.getMc04OrgTypeCode());

            // 填充年度计划数据
            fillYearPlanData(row, yearPlans, yearPlanItems, cgtCode, currentYear, prevYear);

            dataList.add(row);
        });

        return dataList;
    }

    // 辅助方法：获取省市比值
    private Map<String, Map<String, BigDecimal>> getCityProvinceRatios(Mc04IslmYcSalePlan provincePlan,
                                                                       List<BusiComDto> filteredCityList) {
        return ycSalePlanRepository.getCityProvinceRatios(
                provincePlan.getMc04OrgTypeCode(),
                provincePlan.getZaOccurrenceYear(),
                filteredCityList
        );
    }

    // 辅助方法：获取省的计划列表
    private List<Mc04IslmYcSalePlan> getProvincePlans(Mc04IslmYcSalePlan provincePlan) {
        Mc04IslmYcSalePlan query = new Mc04IslmYcSalePlan();
        query.setMc04PlanSubjectName(provincePlan.getMc04PlanSubjectName());
        query.setMc04OrgTypeCode(provincePlan.getMc04OrgTypeCode());
        return ycSalePlanRepository.getYcPlanList(query);
    }

    // 辅助方法：获取省的计划项
    private List<Mc04IslmYcSalePlanItem> getProvinceItems(List<Mc04IslmYcSalePlan> provincePlans) {
        List<String> planIds = provincePlans.stream()
                .map(Mc04IslmYcSalePlan::getMc04SalePlanId)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(planIds)) {
            return Collections.emptyList();
        }
        return ycSalePlanItemRepository.getListByYcPlanIdList(planIds);
    }

    // 辅助方法：填充基础信息
    private void fillBasicInfo(Map<String, Object> row, Mc04IslmYcSalePlanItem item) {
        row.put("acCgtCartonCode", item.getAcCgtCartonCode());
        row.put("acCgtName", item.getAcCgtName());
    }

    // 辅助方法：填充省的计划数据
    private void fillProvincePlanData(Map<String, Object> row,
                                      List<Mc04IslmYcSalePlan> provincePlans,
                                      List<Mc04IslmYcSalePlanItem> itemList,
                                      int nextYear) {
        String provinceCode = provincePlans.get(0).getMc04OrgTypeCode();

        for (Mc04IslmYcSalePlan plan : provincePlans) {
            String zaOccurrenceYear = plan.getZaOccurrenceYear();
            String periodCode = plan.getMc04CgtSaleFoPeriodCode();
            String periodType = plan.getMc04CgtSaleFoPeriodType();

            Optional<Mc04IslmYcSalePlanItem> itemOpt = itemList.stream()
                    .filter(item -> item.getMc04SalePlanId().equals(plan.getMc04SalePlanId()))
                    .findFirst();

            if (!itemOpt.isPresent()) {
                continue;
            }
            Mc04IslmYcSalePlanItem item = itemOpt.get();

            if (zaOccurrenceYear.equals(String.valueOf(nextYear))) {
                if (periodCode.equals(nextYear + "01")) {
                    row.put("nextYearJanSalePlan_" + provinceCode, item.getMc04CgtSalePlanOriginQty());
                    row.put("nextYearJanAllotPlan_" + provinceCode, item.getMa02CgtPlResolOrigplanQty());
                    row.put("nextYearJanEndStock_" + provinceCode, item.getMd03Cgt10thComEndStkQty());
                } else if (periodCode.equals(nextYear + "02")) {
                    row.put("nextYearFebSalePlan_" + provinceCode, item.getMc04CgtSalePlanOriginQty());
                    row.put("nextYearFebAllotPlan_" + provinceCode, item.getMa02CgtPlResolOrigplanQty());
                    row.put("nextYearFebEndStock_" + provinceCode, item.getMd03Cgt10thComEndStkQty());
                } else if (periodType.equals(PeriodTypeEnum.YC.getCode())) {
                    row.put("nextYearFirstTwoMonthSalePlan_" + provinceCode, item.getMc04CgtSalePlanOriginQty());
                    row.put("nextYearFirstTwoMonthAllotPlan_" + provinceCode, item.getMa02CgtPlResolOrigplanQty());
                }
            }
        }
    }

    // 辅助方法：填充城市数据
    private void fillCityData(Map<String, Object> row,
                              List<BusiComDto> cities,
                              Map<String, Map<String, BigDecimal>> ratios,
                              String cgtCode,
                              String provinceCode) {
        for (BusiComDto city : cities) {
            String cityCode = city.getBaComOrgCode();
            Map<String, BigDecimal> cityRatios = ratios.getOrDefault(cityCode, Collections.emptyMap());

            // 复制省的数据并乘以比值
            copyProvinceDataToCity(row, provinceCode, cityCode, cityRatios, cgtCode);

            // 设置默认值
            setDefaultCityValues(row, cityCode);

            // 计算1-2月合计数据
            calculateTwoMonthData(row, cityCode, cityRatios, cgtCode, provinceCode);
        }
    }

    // 辅助方法：复制省数据到城市
    private void copyProvinceDataToCity(Map<String, Object> row,
                                        String provinceCode,
                                        String cityCode,
                                        Map<String, BigDecimal> cityRatios,
                                        String cgtCode) {
        // 处理所有需要复制的字段
        String[] fields = {"nextYearJanSalePlan", "nextYearJanAllotPlan", "nextYearJanEndStock",
                "nextYearFebSalePlan", "nextYearFebAllotPlan", "nextYearFebEndStock"};

        for (String field : fields) {
            String provinceKey = field + "_" + provinceCode;
            if (row.containsKey(provinceKey)) {
                BigDecimal provinceValue = (BigDecimal) row.get(provinceKey);
                String month = field.contains("Jan") ? "01" : "02";
                BigDecimal ratio = cityRatios.getOrDefault(month + "_" + cgtCode, BigDecimal.ZERO);
                BigDecimal result = provinceValue.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                row.put(field + "_" + cityCode, result);
            }
        }
    }

    // 辅助方法：设置城市默认值
    private void setDefaultCityValues(Map<String, Object> row, String cityCode) {
        String[] defaultFields = {"lastYearEndStock", "currentYearSalePlan",
                "currentYearAllotPlan", "currentYearEndStock"};

        for (String field : defaultFields) {
            row.putIfAbsent(field + "_" + cityCode, BigDecimal.ZERO);
        }
    }

    // 辅助方法：计算1-2月合计数据
    private void calculateTwoMonthData(Map<String, Object> row,
                                       String cityCode,
                                       Map<String, BigDecimal> cityRatios,
                                       String cgtCode,
                                       String provinceCode) {
        BigDecimal janRatio = cityRatios.getOrDefault("01_" + cgtCode, BigDecimal.ZERO);
        BigDecimal febRatio = cityRatios.getOrDefault("02_" + cgtCode, BigDecimal.ZERO);

        BigDecimal janSale = (BigDecimal) row.getOrDefault("nextYearJanSalePlan_" + cityCode, BigDecimal.ZERO);
        BigDecimal janAllot = (BigDecimal) row.getOrDefault("nextYearJanAllotPlan_" + cityCode, BigDecimal.ZERO);
        BigDecimal febSale = (BigDecimal) row.getOrDefault("nextYearFebSalePlan_" + cityCode, BigDecimal.ZERO);
        BigDecimal febAllot = (BigDecimal) row.getOrDefault("nextYearFebAllotPlan_" + cityCode, BigDecimal.ZERO);

        BigDecimal twoMonthSale = janSale.add(febSale);
        BigDecimal twoMonthAllot = janAllot.add(febAllot);

        row.put("nextYearFirstTwoMonthSalePlan_" + cityCode, twoMonthSale);
        row.put("nextYearFirstTwoMonthAllotPlan_" + cityCode, twoMonthAllot);
    }
    private void fillYearPlanData(Map<String, Object> row,
                                  List<Mc04IslmSalePlanModel> yearPlans,
                                  List<Mc04IslmSalePlanItemModel> yearPlanItems,
                                  String cgtCode,
                                  int currentYear,
                                  int prevYear) {
        // 按组织和年份分组计划
        Map<String, Map<String, Mc04IslmSalePlanModel>> plansByOrgAndYear = yearPlans.stream()
                .collect(Collectors.groupingBy(
                        Mc04IslmSalePlanModel::getMc04OrgTypeCode,
                        Collectors.toMap(
                                Mc04IslmSalePlanModel::getZaOccurrenceYear,
                                plan -> plan
                        )
                ));

        // 按卷烟和计划ID分组计划项
        Map<String, Mc04IslmSalePlanItemModel> itemsByCgtAndPlan = yearPlanItems.stream()
                .filter(item -> item.getAcCgtName().equals(cgtCode))
                .collect(Collectors.collectingAndThen(
                        Collectors.groupingBy(
                                Mc04IslmSalePlanItemModel::getMc04SalePlanId,
                                Collectors.collectingAndThen(
                                        Collectors.toList(),
                                        list -> list.isEmpty() ? null : list.get(0)
                                )
                        ),
                        map -> (Map<String, Mc04IslmSalePlanItemModel>)map
                ));


        // 填充每个组织的年度数据
        plansByOrgAndYear.forEach((orgCode, yearMap) -> {
            // 当前年度数据
            Mc04IslmSalePlanModel currentYearPlan = yearMap.get(String.valueOf(currentYear));
            if (currentYearPlan != null) {
                Mc04IslmSalePlanItemModel currentYearItem = itemsByCgtAndPlan.get(currentYearPlan.getMc04SalePlanId());
                if (currentYearItem != null) {
                    row.put("lastYearEndStock_" + orgCode, currentYearItem.getMd03Cgt10thComEndStkQty());

                    row.put("currentYearSalePlan_" + orgCode, currentYearItem.getMc04CgtSalePlanAdjustedQty());
                    row.put("currentYearAllotPlan_" + orgCode, currentYearItem.getMa02CgtPlAdjustedQty());
                    row.put("currentYearEndStock_" + orgCode, currentYearItem.getMd03Cgt10thComEndStkQty());
                }
            }

            // 上一年度数据
            Mc04IslmSalePlanModel prevYearPlan = yearMap.get(String.valueOf(prevYear));
            if (prevYearPlan != null) {
                Mc04IslmSalePlanItemModel prevYearItem = itemsByCgtAndPlan.get(prevYearPlan.getMc04SalePlanId());
                if (prevYearItem != null) {
                    row.put("lastYearEndStock_" + orgCode, prevYearItem.getMd03Cgt10thComInitStkQty());
                }
            }
        });
    }


    public List<Map<String, Object>> generateDataList(List<Mc04IslmYcSalePlan> plans, int currentYear, int prevYear, int nextYear, Collection<IccProductDetailDTO> productList) {
        List<Map<String, Object>> dataList = new ArrayList<>();

        // 获取所有计划项
        List<String> planIds = plans.stream()
                .map(Mc04IslmYcSalePlan::getMc04SalePlanId)
                .collect(Collectors.toList());

        List<Mc04IslmYcSalePlanItem> items = Collections.emptyList();
        if (CollectionUtil.isNotEmpty(planIds)) {
            items = ycSalePlanItemRepository.getListByYcPlanIdList(planIds);
        }

        // 获取年度计划数据
        List<Mc04IslmSalePlanModel> yearPlans = ycSalePlanRepository.getYearPlans(plans, currentYear, prevYear);
        List<Mc04IslmSalePlanItemModel> yearPlanItems = ycSalePlanRepository.getYearPlanItems(yearPlans);


        // 按卷烟分组 , 黄鹤楼 - list   list包含了省市两层的本年所有月的计划详情
        Map<String, List<Mc04IslmYcSalePlanItem>> itemsByCgt = items.stream()
                .collect(Collectors.groupingBy(Mc04IslmYcSalePlanItem::getAcCgtCartonCode));

        // 为每种卷烟生成数据行
        itemsByCgt.forEach((cgtCode, itemList) -> {
            Map<String, Object> row = new HashMap<>();

            // 设置基础信息
            Mc04IslmYcSalePlanItem firstItem = itemList.get(0);
            row.put("acCgtCartonCode", firstItem.getAcCgtCartonCode());
            row.put("acCgtName", firstItem.getAcCgtName());

            productList.stream()
                    .filter(product -> product.getProductCode().equals(cgtCode))
                    .findFirst()
                    .ifPresent(product -> {
                        // 做烟卷合并需要的信息
                        row.put("acCigBrandCode", product.getAcCigBrandCode());
                        row.put("acCgtPriceSegmentCode", product.getAcCigRangeCode());
                        row.put("acCgtTinyFlag", product.getIsTiny());
                        row.put("acCgtMediumBranceFlag", product.getIsMedium());
                        Integer packageQty2 = Integer.parseInt(product.getPackageQty2());
                        // 卷烟含税调拨价格
                        row.put("acCgtTaxAllotPrice", getAcCgtTaxAllotPrice(packageQty2, new BigDecimal(product.getAcMateTaxTranPr())));
                        // 卷烟统一批发价
                        row.put("acCgtTradePrice", getAcCgtTradePrice(packageQty2, new BigDecimal(product.getWholeSalePrice())));
                    });

            // 遍历所有计划，根据年度和月份填充数据
            for (Mc04IslmYcSalePlan plan : plans) {
                String orgCode = plan.getMc04OrgTypeCode();
                String zaOccurrenceYear = plan.getZaOccurrenceYear();
                String periodCode = plan.getMc04CgtSaleFoPeriodCode();
                String periodType = plan.getMc04CgtSaleFoPeriodType();
                // 查找对应的计划项
                Optional<Mc04IslmYcSalePlanItem> itemOpt = itemList.stream()
                        .filter(item -> item.getMc04SalePlanId().equals(plan.getMc04SalePlanId()))
                        .findFirst();

                if (!itemOpt.isPresent()) {
                    continue;
                }
                Mc04IslmYcSalePlanItem item = itemOpt.get();

                // 根据年度和月份填充数据
                if (zaOccurrenceYear.equals(String.valueOf(nextYear))) {
                    if (periodCode.equals(nextYear + "01")) { // 1月
                        row.put("nextYearJanSalePlan_" + orgCode, item.getMc04CgtSalePlanOriginQty());
                        row.put("nextYearJanAllotPlan_" + orgCode, item.getMa02CgtPlResolOrigplanQty());
                        row.put("nextYearJanEndStock_" + orgCode, item.getMd03Cgt10thComEndStkQty());
                    } else if (periodCode.equals(nextYear + "02")) { // 2月
                        row.put("nextYearFebSalePlan_" + orgCode, item.getMc04CgtSalePlanOriginQty());
                        row.put("nextYearFebAllotPlan_" + orgCode, item.getMa02CgtPlResolOrigplanQty());
                        row.put("nextYearFebEndStock_" + orgCode, item.getMd03Cgt10thComEndStkQty());
                    } else if (PeriodTypeEnum.YC.getCode().equals(periodType)) { // 1-2月
                        row.put("nextYearFirstTwoMonthSalePlan_" + orgCode, item.getMc04CgtSalePlanOriginQty());
                        row.put("nextYearFirstTwoMonthAllotPlan_" + orgCode, item.getMa02CgtPlResolOrigplanQty());
                    }
                }
            }

            // 填充年度计划数据
            fillYearPlanData(row, yearPlans, yearPlanItems, cgtCode, currentYear, prevYear);
            dataList.add(row);
        });

        return dataList;
    }



    private void initProductSalePlanItemQgSnSwOtherInfo(List<ProductSalePlanItemQgSnSw> productSalePlanItemQgSnSwList, String zaOccurrenceYear) {

        // 获取元春计划所有卷烟规格年相关信息
        SnSwEndStkSaleAllotInfoForYear snSwEndStkSaleAllotInfoForYear = ycSalePlanRepository.getSnSwEndStkSaleAllotInfoForYear(zaOccurrenceYear);

        // 获取1月，2月省内省外所有卷烟的需求预测值
        Map<String, BigDecimal> janProvinceInQtyMap = getDemandForecastMap(zaOccurrenceYear, "01", "0");
        Map<String, BigDecimal> janProvinceOutQtyMap = getDemandForecastMap(zaOccurrenceYear, "01", "1");
        Map<String, BigDecimal> febProvinceInQtyMap = getDemandForecastMap(zaOccurrenceYear, "02", "0");
        Map<String, BigDecimal> febProvinceOutQtyMap = getDemandForecastMap(zaOccurrenceYear, "02", "1");

        // 获取各区域各月份近三年库存最大值
        SnSwLastThreeYearMaxEndStkJanAndFeb snSwLastThreeYearMaxEndStkJanAndFeb = ycSalePlanRepository.getLastThreeYearSnSwJanAndFebMaxEndStkQtyMap(zaOccurrenceYear);

        for (ProductSalePlanItemQgSnSw itemDetail : productSalePlanItemQgSnSwList) {
            String productCode = itemDetail.getAcCgtCartonCode();

            // 初始化省内数据
            initProvinceInData(itemDetail, productCode, snSwEndStkSaleAllotInfoForYear, janProvinceInQtyMap,
                    febProvinceInQtyMap, snSwLastThreeYearMaxEndStkJanAndFeb);

            // 初始化省外数据
            initProvinceOutData(itemDetail, productCode, snSwEndStkSaleAllotInfoForYear, janProvinceOutQtyMap,
                    febProvinceOutQtyMap, snSwLastThreeYearMaxEndStkJanAndFeb);
        }
    }

    /**
     * 获取需求预测数据Map
     */
    private Map<String, BigDecimal> getDemandForecastMap(String zaOccurrenceYear, String month, String areaType) {
        List<PlanQty> planQtyList = demandFoRepository.getDemandFoConfirmQty(zaOccurrenceYear, month, areaType);
        return planQtyList.stream()
                .collect(Collectors.toMap(PlanQty::getProductCode, PlanQty::getDemandFoConfirmQty));
    }

    /**
     * 初始化省内数据
     */
    private void initProvinceInData(ProductSalePlanItemQgSnSw itemDetail, String productCode,
                                    SnSwEndStkSaleAllotInfoForYear snSwEndStkSaleAllotInfoForYear,
                                    Map<String, BigDecimal> janProvinceInQtyMap,
                                    Map<String, BigDecimal> febProvinceInQtyMap,
                                    SnSwLastThreeYearMaxEndStkJanAndFeb snSwLastThreeYearMaxEndStkJanAndFeb) {
        // N-1年末商业库存 协议单位汇总月表取年末商业库存
        itemDetail.setLastYearStkQtyIn(snSwEndStkSaleAllotInfoForYear.getLastYearEndStkQtyProvinceIn(productCode));
        // N年预计销售 当前年的年度计划中取最新版本数据
        itemDetail.setCurrentYearSalesIn(snSwEndStkSaleAllotInfoForYear.getCurrentYearPlanSaleQtyProvinceIn(productCode));
        // N年预计调拨：当前年的年度计划中取最新版本数据
        itemDetail.setCurrentYearAllocationIn(snSwEndStkSaleAllotInfoForYear.getCurrentYearPlanAllotQtyProvinceIn(productCode));
        // N年末预计库存：(N-1年末商业库存)+(N年预计调拨)-(N年预计销售)
        BigDecimal currentYearEndInventoryIn = snSwEndStkSaleAllotInfoForYear.getCurrentYearEndStkQtyProvinceIn(productCode);
        itemDetail.setCurrentYearEndInventoryIn(currentYearEndInventoryIn);

        // N+1年1月销售计划：按照营销区域提报的元春市场需求预测数据进行初始化
        itemDetail.setNextYearFirstMonthSalesPlanIn(janProvinceInQtyMap.getOrDefault(productCode, BigDecimal.ZERO));
        // N+1年1月末商业库存：按照近三年同期最大库存进行初始化
        itemDetail.setNextYearFirstMonthEndingStockIn(snSwLastThreeYearMaxEndStkJanAndFeb.getJanEndStockQtySn(productCode));
        // N+1年1月调拨计划：根据期末库存、销售计划推算调拨计划  N+1年1月预计调拨=N+1年1月末预计库存+N+1年1月预计销售-N年末商业库存
        itemDetail.setNextYearFirstMonthAllocationPlanIn(itemDetail.getNextYearFirstMonthEndingStockIn()
                .add(itemDetail.getNextYearFirstMonthSalesPlanIn().subtract(currentYearEndInventoryIn)));
        // N+1年2月销售计划：按照营销区域提报的元春市场需求预测数据进行初始化
        itemDetail.setNextYearSecondMonthSalesPlanIn(febProvinceInQtyMap.getOrDefault(productCode, BigDecimal.ZERO));
        // N+1年2月末商业库存：否则按照近三年同期库存最大库存初始化
        itemDetail.setNextYearSecondMonthEndingStockIn(snSwLastThreeYearMaxEndStkJanAndFeb.getFebEndStockQtySn(productCode));
        // N+1年2月调拨计划：N+1年2月预计调拨=N+1年2月末预计库存+N+2年1月预计销售-N+1月末年末商业库存
        itemDetail.setNextYearSecondMonthAllocationPlanIn(itemDetail.getNextYearSecondMonthEndingStockIn()
                .add(itemDetail.getNextYearSecondMonthSalesPlanIn().subtract(itemDetail.getNextYearFirstMonthEndingStockIn())));
    }

    /**
     * 初始化省外数据
     */
    private void initProvinceOutData(ProductSalePlanItemQgSnSw itemDetail, String productCode,
                                     SnSwEndStkSaleAllotInfoForYear snSwEndStkSaleAllotInfoForYear,
                                     Map<String, BigDecimal> janProvinceOutQtyMap,
                                     Map<String, BigDecimal> febProvinceOutQtyMap,
                                     SnSwLastThreeYearMaxEndStkJanAndFeb snSwLastThreeYearMaxEndStkJanAndFeb) {
        // 省外数据
        itemDetail.setLastYearStkQtyOut(snSwEndStkSaleAllotInfoForYear.getLastYearEndStkQtyProvinceOut(productCode));
        itemDetail.setCurrentYearSalesOut(snSwEndStkSaleAllotInfoForYear.getCurrentYearPlanSaleQtyProvinceOut(productCode));
        itemDetail.setCurrentYearAllocationOut(snSwEndStkSaleAllotInfoForYear.getCurrentYearPlanAllotQtyProvinceOut(productCode));
        // N年末预计库存：(N-1年末商业库存)+(N年预计调拨)-(N年预计销售)
        BigDecimal currentYearEndInventoryOut = snSwEndStkSaleAllotInfoForYear.getCurrentYearEndStkQtyProvinceOut(productCode);
        itemDetail.setCurrentYearEndInventoryOut(currentYearEndInventoryOut);

        itemDetail.setNextYearFirstMonthSalesPlanOut(janProvinceOutQtyMap.getOrDefault(productCode, BigDecimal.ZERO));
        itemDetail.setNextYearFirstMonthEndingStockOut(snSwLastThreeYearMaxEndStkJanAndFeb.getJanEndStockQtySw(productCode));
        itemDetail.setNextYearFirstMonthAllocationPlanOut(itemDetail.getNextYearFirstMonthEndingStockOut()
                .add(itemDetail.getNextYearFirstMonthSalesPlanOut().subtract(currentYearEndInventoryOut)));
        itemDetail.setNextYearSecondMonthSalesPlanOut(febProvinceOutQtyMap.getOrDefault(productCode, BigDecimal.ZERO));
        itemDetail.setNextYearSecondMonthEndingStockOut(snSwLastThreeYearMaxEndStkJanAndFeb.getFebEndStockQtySw(productCode));
        itemDetail.setNextYearSecondMonthAllocationPlanOut(itemDetail.getNextYearSecondMonthEndingStockOut()
                .add(itemDetail.getNextYearSecondMonthSalesPlanOut().subtract(itemDetail.getNextYearFirstMonthEndingStockOut())));
    }

    private List<ProductSalePlanItemQgSnSw> initProductSalePlanItemQgSnSwProductInfo(Collection<IccProductDetailDTO> productList) {
        List<ProductSalePlanItemQgSnSw> productSalePlanItemQgSnSwList = new ArrayList<>();
        for (IccProductDetailDTO product : productList) {
            Integer packageQty2 = Integer.parseInt(product.getPackageQty2());

            ProductSalePlanItemQgSnSw itemDetail = new ProductSalePlanItemQgSnSw();

            itemDetail.setAcCgtName(product.getProductName());
            itemDetail.setAcCgtCartonCode(product.getProductCode());
            // 做烟卷合并需要的信息
            itemDetail.setAcCigBrandCode(product.getAcCigBrandCode());
            itemDetail.setAcCgtPriceSegmentCode(product.getAcCigRangeCode());
            itemDetail.setAcCgtTinyFlag(product.getIsTiny());
            itemDetail.setAcCgtMediumBranceFlag(product.getIsMedium());
            // 卷烟含税调拨价格
            itemDetail.setAcCgtTaxAllotPrice(getAcCgtTaxAllotPrice(packageQty2, new BigDecimal(product.getAcMateTaxTranPr())));
            // 卷烟统一批发价
            itemDetail.setAcCgtTradePrice(getAcCgtTradePrice(packageQty2, new BigDecimal(product.getWholeSalePrice())));

            productSalePlanItemQgSnSwList.add(itemDetail);
        }
        return productSalePlanItemQgSnSwList;
    }

    private void buildOtherInfo(List<ProductSalePlanItemQgSnSw> productSalePlanItemQgSnSwList, String zaOccurrenceYear) {
        // 获取元春计划所有卷烟规格年相关信息
        SnSwEndStkSaleAllotInfoForYear snSwEndStkSaleAllotInfoForYear = ycSalePlanRepository.getSnSwEndStkSaleAllotInfoForYear(zaOccurrenceYear);
        // 构建Map productCode -> yearEndStock
        for (ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw : productSalePlanItemQgSnSwList) {
            // 构建该卷烟商品上一年商业库存、当前年的预计销售和、当前年的预计调拨。
            String productCode = productSalePlanItemQgSnSw.getAcCgtCartonCode();

            productSalePlanItemQgSnSw.setLastYearStkQtyIn(snSwEndStkSaleAllotInfoForYear.getLastYearEndStkQtyProvinceIn(productCode));
            productSalePlanItemQgSnSw.setCurrentYearSalesIn(snSwEndStkSaleAllotInfoForYear.getCurrentYearPlanSaleQtyProvinceIn(productCode));
            productSalePlanItemQgSnSw.setCurrentYearAllocationIn(snSwEndStkSaleAllotInfoForYear.getCurrentYearPlanAllotQtyProvinceIn(productCode));

            productSalePlanItemQgSnSw.setLastYearStkQtyOut(snSwEndStkSaleAllotInfoForYear.getLastYearEndStkQtyProvinceOut(productCode));
            productSalePlanItemQgSnSw.setCurrentYearSalesOut(snSwEndStkSaleAllotInfoForYear.getCurrentYearPlanSaleQtyProvinceOut(productCode));
            productSalePlanItemQgSnSw.setCurrentYearAllocationOut(snSwEndStkSaleAllotInfoForYear.getCurrentYearPlanAllotQtyProvinceOut(productCode));
        }
    }

    private List<ProductSalePlanItemQgSnSw> buildProductSalePlanItemQgSnSwList(List<Mc04IslmYcSalePlan> ycSalePlanList, List<Mc04IslmYcSalePlanItem> ycSalePlanItemList, Collection<IccProductDetailDTO> productList) {
        List<ProductSalePlanItemQgSnSw> productSalePlanItemQgSnSwList = new ArrayList<>();
        // 将每个卷烟的 调拨价 批发价 省内N+1 1月销售计划，省内N+1 1月调拨计划 省内 N+1 2月销售计划 省内 N+1 2月调拨计划， 以及省外的
        // 建立卷烟产品名的索引
        Map<String, ProductSalePlanItemQgSnSw> productSalePlanItemQgSnSwMap = new LinkedHashMap<>();
        for (Mc04IslmYcSalePlan salePlan : ycSalePlanList) {
            processSalePlan(salePlan, ycSalePlanItemList, productList, productSalePlanItemQgSnSwMap);
        }
        productSalePlanItemQgSnSwMap.forEach((key, value) -> {
            productSalePlanItemQgSnSwList.add(value);
        });
        return productSalePlanItemQgSnSwList;
    }

    private void processSalePlan(Mc04IslmYcSalePlan salePlan, List<Mc04IslmYcSalePlanItem> ycSalePlanItemList,
                                 Collection<IccProductDetailDTO> productList, Map<String, ProductSalePlanItemQgSnSw> productSalePlanItemQgSnSwMap) {
        // 先区分是全国还是省内、省外
        String orgTypeKind = salePlan.getMc04OrgTypeKind();
        String periodType = salePlan.getMc04CgtSaleFoPeriodType();
        String periodCode = salePlan.getMc04CgtSaleFoPeriodCode();
        String zaOccurrenceYear = salePlan.getZaOccurrenceYear();

        for (Mc04IslmYcSalePlanItem item : ycSalePlanItemList) {
            if (!item.getMc04SalePlanId().equals(salePlan.getMc04SalePlanId())) {
                continue;
            }

            ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw = getOrCreateProductSalePlanItem(item, productList, productSalePlanItemQgSnSwMap);

            if (orgTypeKind.equals(OrgTypeEnum.SN.getKind())) {
                processSnPlanItem(periodType, periodCode, zaOccurrenceYear, item, productSalePlanItemQgSnSw);
            } else if (orgTypeKind.equals(OrgTypeEnum.SW.getKind())) {
                processSwPlanItem(periodType, periodCode, zaOccurrenceYear, item, productSalePlanItemQgSnSw);
            }
        }
    }

    private ProductSalePlanItemQgSnSw getOrCreateProductSalePlanItem(Mc04IslmYcSalePlanItem item,
                                                                     Collection<IccProductDetailDTO> productList,
                                                                     Map<String, ProductSalePlanItemQgSnSw> productSalePlanItemQgSnSwMap) {
        ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw;
        if (null == productSalePlanItemQgSnSwMap.get(item.getAcCgtName())) {
            productSalePlanItemQgSnSw = createNewProductSalePlanItem(item, productList);
            productSalePlanItemQgSnSwMap.put(item.getAcCgtName(), productSalePlanItemQgSnSw);
        } else {
            productSalePlanItemQgSnSw = productSalePlanItemQgSnSwMap.get(item.getAcCgtName());
        }
        return productSalePlanItemQgSnSw;
    }

    private ProductSalePlanItemQgSnSw createNewProductSalePlanItem(Mc04IslmYcSalePlanItem item, Collection<IccProductDetailDTO> productList) {
        ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw = new ProductSalePlanItemQgSnSw();
        productSalePlanItemQgSnSw.setMc04SalePlanId(item.getMc04SalePlanId());
        productSalePlanItemQgSnSw.setAcCgtName(item.getAcCgtName());
        productSalePlanItemQgSnSw.setAcCgtCartonCode(item.getAcCgtCartonCode());
        productSalePlanItemQgSnSw.setAcCgtTaxAllotPrice(item.getAcCgtTaxAllotPrice());
        productSalePlanItemQgSnSw.setAcCgtTradePrice(item.getAcCgtTradePrice());
        // 做烟卷合并需要的信息
        Optional<IccProductDetailDTO> productOptional = productList.stream().filter(product -> product.getProductName().equals(item.getAcCgtName())).findFirst();
        if (productOptional.isPresent()) {
            productSalePlanItemQgSnSw.setAcCigBrandCode(productOptional.get().getAcCigBrandCode());
        } else {
            throw new CustomException("没有找到对应的卷烟信息");
        }
        productSalePlanItemQgSnSw.setAcCgtPriceSegmentCode(item.getAcCgtPriceSegmentCode());
        productSalePlanItemQgSnSw.setAcCgtTinyFlag(item.getAcCgtTinyFlag());
        productSalePlanItemQgSnSw.setAcCgtMediumBranceFlag(item.getAcCgtMediumBranceFlag());
        return productSalePlanItemQgSnSw;
    }

    private void processSnPlanItem(String periodType, String periodCode, String zaOccurrenceYear,
                                   Mc04IslmYcSalePlanItem item, ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw) {
        if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "01")) {
            // 省内1月
            // 1月销售计划 用户手动填写 mc04_cgt_sale_plan_origin_qty
            productSalePlanItemQgSnSw.setNextYearFirstMonthSalesPlanIn(item.getMc04CgtSalePlanOriginQty());
            // 1月调拨计划 用户手动填写 ma02_cgt_pl_resol_origplan_qty
            productSalePlanItemQgSnSw.setNextYearFirstMonthAllocationPlanIn(item.getMa02CgtPlResolOrigplanQty());
            // 1月期末库存：1月期初库存 + 1月调拨计划 - 1月销售计划 md03_cgt_10th_com_end_stk_qty
            productSalePlanItemQgSnSw.setNextYearFirstMonthEndingStockIn(item.getMd03Cgt10thComEndStkQty());
        } else if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "02")) {
            // 省内2月
            processSnFebruaryItem(item, productSalePlanItemQgSnSw);
        }
    }

    private void processSnFebruaryItem(Mc04IslmYcSalePlanItem item, ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw) {
        productSalePlanItemQgSnSw.setNextYearSecondMonthSalesPlanIn(item.getMc04CgtSalePlanOriginQty());
        productSalePlanItemQgSnSw.setNextYearSecondMonthAllocationPlanIn(item.getMa02CgtPlResolOrigplanQty());
        productSalePlanItemQgSnSw.setNextYearSecondMonthEndingStockIn(item.getMd03Cgt10thComEndStkQty());
    }

    private void processSwPlanItem(String periodType, String periodCode, String zaOccurrenceYear,
                                   Mc04IslmYcSalePlanItem item, ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw) {
        if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "01")) {
            // 1月销售计划 用户手动填写 mc04_cgt_sale_plan_origin_qty
            productSalePlanItemQgSnSw.setNextYearFirstMonthSalesPlanOut(item.getMc04CgtSalePlanOriginQty());
            // 1月调拨计划 用户手动填写 ma02_cgt_pl_resol_origplan_qty
            productSalePlanItemQgSnSw.setNextYearFirstMonthAllocationPlanOut(item.getMa02CgtPlResolOrigplanQty());
            // 1月期末库存：1月期初库存 + 1月调拨计划 - 1月销售计划 md03_cgt_10th_com_end_stk_qty
            productSalePlanItemQgSnSw.setNextYearFirstMonthEndingStockOut(item.getMd03Cgt10thComEndStkQty());
        } else if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "02")) {
            // 省外2月
            processSwFebruaryItem(item, productSalePlanItemQgSnSw);
        }
    }

    private void processSwFebruaryItem(Mc04IslmYcSalePlanItem item, ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw) {
        productSalePlanItemQgSnSw.setNextYearSecondMonthSalesPlanOut(item.getMc04CgtSalePlanOriginQty());
        productSalePlanItemQgSnSw.setNextYearSecondMonthAllocationPlanOut(item.getMa02CgtPlResolOrigplanQty());
        productSalePlanItemQgSnSw.setNextYearSecondMonthEndingStockOut(item.getMd03Cgt10thComEndStkQty());
    }

    private Collection<IccProductDetailDTO> getProductList() {
        IccGetProductListRequest iccGetProductListRequest = new IccGetProductListRequest();
        String icomCode = IcomUtils.getIcomCode();
        iccGetProductListRequest.setProviderCustomerId(Collections.singletonList(icomCode));
        iccGetProductListRequest.setAcOneLevelClassTypeCode(Collections.singletonList("01"));
        iccGetProductListRequest.setAcCigSalFlag(Collections.singletonList("1"));
        iccGetProductListRequest.setAcCigBusiTypeCode("A");
        IccMultiResponse<IccProductDetailDTO> productListResponse =
                productServiceApi.getProductList(iccGetProductListRequest);
        Assert.notNull(productListResponse, () -> new CustomException("商品中心获取商品信息失败"));
        Assert.isTrue(productListResponse.isSuccess(), () -> {
            log.error("商品中心获取商品信息失败, 商品中心返回失败信息:{}", productListResponse.getMessage());
            return new CustomException("商品中心获取商品信息失败");
        });
        IccMultiDataDTO<IccProductDetailDTO> data = productListResponse.getData();
        Assert.notNull(data, () -> new CustomException("商品中心获取商品信息失败"));
        return data.getItems();
    }


    private void canYcSalePlanSubmit(Mc04IslmYcSalePlan mc04IslmYcSalePlan) {
        Assert.notNull(mc04IslmYcSalePlan.getMc04SalePlanId(), () -> new CustomException("元春计划不能为空"));
        Assert.notNull(mc04IslmYcSalePlan.getYcSalePlanDetailList(), () -> new CustomException("元春计划详情不能为空"));
        List<ProductSalePlanItemQgSnSw> productSalePlanItemQgSnSwList = mc04IslmYcSalePlan.getYcSalePlanDetailList();
        // 校验规范
        // N年末预计库存+(N+1年1月调拨计划)-(N+1年1月销售计划)=(N+1年1月末商业库存)
        // (N+1年1月末商业库存)>=0
        // (N+1年1月末商业库存)+(N+1年2月调拨计划)-(N+1年2月销售计划)=(N+1年2月末商业库存)
        // (N+1年2月末商业库存)>=0
        for (ProductSalePlanItemQgSnSw item : productSalePlanItemQgSnSwList) {
            validateFirstMonthData(item);
            validateSecondMonthData(item);
        }
    }

    /**
     * 校验第一个月的数据
     * @param item 产品销售计划项
     */
    private void validateFirstMonthData(ProductSalePlanItemQgSnSw item) {
        // 校验1月数据：N年末预计库存+(N+1年1月调拨计划)-(N+1年1月销售计划)=(N+1年1月末商业库存)
        BigDecimal calculatedFirstMonthEndingStock = item.getCurrentYearEndInventory()
                .add(item.getNextYearFirstMonthAllocationPlan())
                .subtract(item.getNextYearFirstMonthSalesPlan());

        if (calculatedFirstMonthEndingStock.compareTo(item.getNextYearFirstMonthEndingStock()) != 0) {
            throw new CustomException("校验失败：全国N年末预计库存+(N+1年1月调拨计划)-(N+1年1月销售计划)≠(N+1年1月末商业库存)，卷烟名称：" + item.getAcCgtName());
        }

        // 校验1月末商业库存>=0
        if (item.getNextYearFirstMonthEndingStock().compareTo(BigDecimal.ZERO) < 0) {
            throw new CustomException("校验失败：(N+1年1月末商业库存)不能小于0，卷烟名称：" + item.getAcCgtName());
        }
    }

    /**
     * 校验第二个月的数据
     * @param item 产品销售计划项
     */
    private void validateSecondMonthData(ProductSalePlanItemQgSnSw item) {
        // 校验2月数据：(N+1年1月末商业库存)+(N+1年2月调拨计划)-(N+1年2月销售计划)=(N+1年2月末商业库存)
        BigDecimal calculatedSecondMonthEndingStock = item.getNextYearFirstMonthEndingStock()
                .add(item.getNextYearSecondMonthAllocationPlan())
                .subtract(item.getNextYearSecondMonthSalesPlan());

        if (calculatedSecondMonthEndingStock.compareTo(item.getNextYearSecondMonthEndingStock()) != 0) {
            throw new CustomException("校验失败：(N+1年1月末商业库存)+(N+1年2月调拨计划)-(N+1年2月销售计划)≠(N+1年2月末商业库存)，卷烟名称：" + item.getAcCgtName());
        }

        // 校验2月末商业库存>=0
        if (item.getNextYearSecondMonthEndingStock().compareTo(BigDecimal.ZERO) < 0) {
            throw new CustomException("校验失败：(N+1年2月末商业库存)不能小于0，卷烟名称：" + item.getAcCgtName());
        }
    }


    private List<Mc04IslmYcSalePlanItem> buildSnProvincePlanItems(List<Mc04IslmYcSalePlan> snYcPlanList, List<Mc04IslmYcSalePlanItem> snYcPlanItemList, List<Mc04IslmYcSalePlan> provinceYcPlanList, String periodType, String periodCode) {


        // 获取对应周期的
        Mc04IslmYcSalePlan plan = snYcPlanList.stream()
                .filter(item -> item.getMc04CgtSaleFoPeriodType().equals(periodType) && item.getMc04CgtSaleFoPeriodCode().equals(periodCode)).findFirst().orElse( null);
        Assert.notNull(plan, () -> new CustomException("元春计划获取省内失败！请联系管理员"));
        Mc04IslmYcSalePlan provincePlan = provinceYcPlanList.stream()
                .filter(item -> item.getMc04CgtSaleFoPeriodType().equals(periodType) && item.getMc04CgtSaleFoPeriodCode().equals(periodCode)).findFirst().orElse( null);
        Assert.notNull(provincePlan, () -> new CustomException("元春计划获取本省失败！请联系管理员"));
        List<Mc04IslmYcSalePlanItem> planItemList = snYcPlanItemList.stream()
                .filter(item -> item.getMc04SalePlanId().equals(plan.getMc04SalePlanId())).collect(Collectors.toList());
        Assert.notEmpty(planItemList, () -> new CustomException("元春计划详情获取省内详情失败！请联系管理员"));


        List<Mc04IslmYcSalePlanItem> provinceYcPlanDetailList = new ArrayList<>();

        for (Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem : planItemList) {
            Mc04IslmYcSalePlanItem newItem = buildMc04IslmYcSalePlanItem(mc04IslmYcSalePlanItem, provincePlan);
            provinceYcPlanDetailList.add(newItem);
        }
        return provinceYcPlanDetailList;
    }

    private Mc04IslmYcSalePlanItem buildMc04IslmYcSalePlanItem(Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem, Mc04IslmYcSalePlan provincePlan) {
        Mc04IslmYcSalePlanItem newItem = new Mc04IslmYcSalePlanItem();
        newItem.setMc04SalePlanId(provincePlan.getMc04SalePlanId());
        newItem.setAcCgtCartonCode(mc04IslmYcSalePlanItem.getAcCgtCartonCode());
        newItem.setAcCgtName(mc04IslmYcSalePlanItem.getAcCgtName());
        newItem.setAcCgtPriceSegmentCode(mc04IslmYcSalePlanItem.getAcCgtPriceSegmentCode());
        newItem.setAcCgtPriceSegmentName(mc04IslmYcSalePlanItem.getAcCgtPriceSegmentName());
        newItem.setAcCgtTaxAllotPrice(mc04IslmYcSalePlanItem.getAcCgtTaxAllotPrice());
        newItem.setAcCgtTradePrice(mc04IslmYcSalePlanItem.getAcCgtTradePrice());
        newItem.setAcCgtMediumBranceFlag(mc04IslmYcSalePlanItem.getAcCgtMediumBranceFlag());
        newItem.setAcCgtTinyFlag(mc04IslmYcSalePlanItem.getAcCgtTinyFlag());
        newItem.setAcCgtTarVal(mc04IslmYcSalePlanItem.getAcCgtTarVal());
        newItem.setMd03Cgt10thComInitStkQty(mc04IslmYcSalePlanItem.getMd03Cgt10thComInitStkQty());
        newItem.setMc04CgtSalePlanOriginQty(mc04IslmYcSalePlanItem.getMc04CgtSalePlanOriginQty());
        newItem.setMa02CgtPlResolOrigplanQty(mc04IslmYcSalePlanItem.getMa02CgtPlResolOrigplanQty());
        newItem.setMd03Cgt10thComEndStkQty(mc04IslmYcSalePlanItem.getMd03Cgt10thComEndStkQty());
        newItem.setIcomCode(mc04IslmYcSalePlanItem.getIcomCode());
        return newItem;
    }

    /**
     * 保存其他元春计划信息
     *
     * @param ycSalePlan
     */
    private void saveOtherYcPlan(Mc04IslmYcSalePlan ycSalePlan) {

        List<Mc04IslmYcSalePlan> ycSalePlanList = new ArrayList<>();
        String zaOccurrenceYear = ycSalePlan.getZaOccurrenceYear();
        // 新增全国1月计划
        ycSalePlanList.add(buildYcPlan(ycSalePlan, PeriodTypeEnum.MONTH.getCode(), zaOccurrenceYear + "01", OrgTypeEnum.QG));
        // 新增全国2月计划
        ycSalePlanList.add(buildYcPlan(ycSalePlan, PeriodTypeEnum.MONTH.getCode(), zaOccurrenceYear + "02", OrgTypeEnum.QG));
        // 新增省内元春计划
        ycSalePlanList.add(buildYcPlan(ycSalePlan, PeriodTypeEnum.YC.getCode(), zaOccurrenceYear, OrgTypeEnum.SN));
        // 新增省内1月计划
        ycSalePlanList.add(buildYcPlan(ycSalePlan, PeriodTypeEnum.MONTH.getCode(), zaOccurrenceYear + "01", OrgTypeEnum.SN));
        // 新增省内2月计划
        ycSalePlanList.add(buildYcPlan(ycSalePlan, PeriodTypeEnum.MONTH.getCode(), zaOccurrenceYear + "02", OrgTypeEnum.SN));
        // 新增省外元春计划
        ycSalePlanList.add(buildYcPlan(ycSalePlan, PeriodTypeEnum.YC.getCode(), zaOccurrenceYear, OrgTypeEnum.SW));
        // 新增省外1月计划
        ycSalePlanList.add(buildYcPlan(ycSalePlan, PeriodTypeEnum.MONTH.getCode(), zaOccurrenceYear + "01", OrgTypeEnum.SW));
        // 新增省外2月计划
        ycSalePlanList.add(buildYcPlan(ycSalePlan, PeriodTypeEnum.MONTH.getCode(), zaOccurrenceYear + "02", OrgTypeEnum.SW));

        ycSalePlanRepository.saveBatch(ycSalePlanList);

    }

    private Mc04IslmYcSalePlan buildProvinceYcPlan(Mc04IslmYcSalePlan mc04IslmYcSalePlan, String periodType, String periodCode, BusiComDto com) {

        Mc04IslmYcSalePlan ycSalePlan = new Mc04IslmYcSalePlan();
        ycSalePlan.setMa02TobaProdTradeTypeCode(mc04IslmYcSalePlan.getMa02TobaProdTradeTypeCode());
        ycSalePlan.setMc04CgtSalePlanVersion(mc04IslmYcSalePlan.getMc04CgtSalePlanVersion());
        ycSalePlan.setMc04IsLastestVersion(mc04IslmYcSalePlan.getMc04IsLastestVersion());
        ycSalePlan.setZaOccurrenceYear(mc04IslmYcSalePlan.getZaOccurrenceYear());
        ycSalePlan.setMc04PlanSubjectType(mc04IslmYcSalePlan.getMc04PlanSubjectType());
        ycSalePlan.setMc04PlanSubjectName(mc04IslmYcSalePlan.getMc04PlanSubjectName());
        ycSalePlan.setMc04PlanSubjectBeginDate(mc04IslmYcSalePlan.getMc04PlanSubjectBeginDate());
        ycSalePlan.setMc04PlanSubjectEndDate(mc04IslmYcSalePlan.getMc04PlanSubjectEndDate());
        ycSalePlan.setMc04SalePlanStatus(mc04IslmYcSalePlan.getMc04SalePlanStatus());

        ycSalePlan.setMc04CgtSaleFoPeriodType(periodType);
        ycSalePlan.setMc04CgtSaleFoPeriodCode(periodCode);
        ycSalePlan.setMc04OrgTypeKind(OrgTypeEnum.PROVINCE.getKind());
        ycSalePlan.setMc04OrgTypeCode(com.getBaComOrgCode());
        ycSalePlan.setMc04OrgTypeName(com.getPcomShortName());
        ycSalePlan.setIcomCode(mc04IslmYcSalePlan.getIcomCode());
        return ycSalePlan;
    }

    private Mc04IslmYcSalePlan buildYcPlan(Mc04IslmYcSalePlan mc04IslmYcSalePlan, String periodType, String periodCode, OrgTypeEnum orgTypeEnum) {
        Mc04IslmYcSalePlan ycSalePlan = new Mc04IslmYcSalePlan();
        ycSalePlan.setMa02TobaProdTradeTypeCode(mc04IslmYcSalePlan.getMa02TobaProdTradeTypeCode());
        ycSalePlan.setMc04CgtSalePlanVersion(mc04IslmYcSalePlan.getMc04CgtSalePlanVersion());
        ycSalePlan.setMc04IsLastestVersion(mc04IslmYcSalePlan.getMc04IsLastestVersion());
        ycSalePlan.setZaOccurrenceYear(mc04IslmYcSalePlan.getZaOccurrenceYear());
        ycSalePlan.setMc04PlanSubjectType(mc04IslmYcSalePlan.getMc04PlanSubjectType());
        ycSalePlan.setMc04PlanSubjectName(mc04IslmYcSalePlan.getMc04PlanSubjectName());
        ycSalePlan.setMc04PlanSubjectBeginDate(mc04IslmYcSalePlan.getMc04PlanSubjectBeginDate());
        ycSalePlan.setMc04PlanSubjectEndDate(mc04IslmYcSalePlan.getMc04PlanSubjectEndDate());
        ycSalePlan.setMc04SalePlanStatus(mc04IslmYcSalePlan.getMc04SalePlanStatus());
        ycSalePlan.setMc04CgtSaleFoPeriodType(periodType);
        ycSalePlan.setMc04CgtSaleFoPeriodCode(periodCode);
        ycSalePlan.setMc04OrgTypeKind(orgTypeEnum.getKind());
        ycSalePlan.setMc04OrgTypeCode(orgTypeEnum.getCode());
        ycSalePlan.setMc04OrgTypeName(orgTypeEnum.getName());
        ycSalePlan.setIcomCode(mc04IslmYcSalePlan.getIcomCode());
        return ycSalePlan;
    }

    /**
     * 获取计划列表,因为元春计划对应多行数据，需要根据一行的id获取共同信息查询其他计划
     *
     * @param
     * @return
     */
    private List<Mc04IslmYcSalePlan> getPlanListByYcPlan(Mc04IslmYcSalePlan ycSalePlan) {
        Mc04IslmYcSalePlan plan = new Mc04IslmYcSalePlan();
        plan.setMc04IsLastestVersion(LatestVersionEnum.CURRENT.getCode());
        plan.setZaOccurrenceYear(ycSalePlan.getZaOccurrenceYear());
        plan.setMc04CgtSalePlanVersion(ycSalePlan.getMc04CgtSalePlanVersion());
        // 根据版本以及业务年份获取最新的元春计划列表
        return ycSalePlanRepository.getYcPlanList(plan);
    }

    private void saveSalePlanItemList(Collection<IccProductDetailDTO> productList, List<Mc04IslmYcSalePlan> mc04IslmYcSalePlanList, List<ProductSalePlanItemQgSnSw> ycSalePlanDetailList) {
        List<Mc04IslmYcSalePlanItem> itemList = new ArrayList<>();
        // 这是规格列表，每个规格行需要根据9个计划进行拆解
        for (ProductSalePlanItemQgSnSw item : ycSalePlanDetailList) {
            // 通过产品列表获取产品详情
            IccProductDetailDTO productDetail = getProductDetail(productList, item.getAcCgtCartonCode());
            for (Mc04IslmYcSalePlan salePlan : mc04IslmYcSalePlanList) {
                Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem = new Mc04IslmYcSalePlanItem();
                mc04IslmYcSalePlanItem.setMc04SalePlanId(salePlan.getMc04SalePlanId());
                mc04IslmYcSalePlanItem.setAcCgtCartonCode(productDetail.getProductCode());
                mc04IslmYcSalePlanItem.setAcCgtName(productDetail.getProductName());
                mc04IslmYcSalePlanItem.setAcCgtPriceSegmentCode(productDetail.getAcCigRangeCode());
                mc04IslmYcSalePlanItem.setAcCgtPriceSegmentName(productDetail.getAcCigRangeName());
                mc04IslmYcSalePlanItem.setAcCgtTaxAllotPrice(new BigDecimal(productDetail.getAcMateTaxTranPr()));
                mc04IslmYcSalePlanItem.setAcCgtTradePrice(new BigDecimal(productDetail.getWholeSalePrice()));
                mc04IslmYcSalePlanItem.setAcCgtMediumBranceFlag(productDetail.getIsMedium());
                mc04IslmYcSalePlanItem.setAcCgtTinyFlag(productDetail.getIsTiny());
                mc04IslmYcSalePlanItem.setAcCgtTarVal(new BigDecimal(productDetail.getTarQuantity()));
                // 数量值根据不同的计划存的也不同
                buildSalePlanItemQtyData(item, mc04IslmYcSalePlanItem, salePlan);
                mc04IslmYcSalePlanItem.setIcomCode(salePlan.getIcomCode());
                itemList.add(mc04IslmYcSalePlanItem);
            }
        }
        ycSalePlanItemRepository.saveBatch(itemList);
    }

    private void buildSalePlanItemQtyData(ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem, Mc04IslmYcSalePlan salePlan) {
        // 先区分是全国还是省内、省外
        String orgTypeKind = salePlan.getMc04OrgTypeKind();
        String periodType = salePlan.getMc04CgtSaleFoPeriodType();
        String periodCode = salePlan.getMc04CgtSaleFoPeriodCode();
        String zaOccurrenceYear = salePlan.getZaOccurrenceYear();

        // N库存 + N1调拨 - N1销售 = N1库存
        if (orgTypeKind.equals(OrgTypeEnum.QG.getKind())) {
            buildQgPlanItemData(productSalePlanItemQgSnSw, mc04IslmYcSalePlanItem, periodType, periodCode, zaOccurrenceYear);
        } else if (orgTypeKind.equals(OrgTypeEnum.SN.getKind())) {
            buildSnPlanItemData(productSalePlanItemQgSnSw, mc04IslmYcSalePlanItem, periodType, periodCode, zaOccurrenceYear);
        } else if (orgTypeKind.equals(OrgTypeEnum.SW.getKind())) {
            buildSwPlanItemData(productSalePlanItemQgSnSw, mc04IslmYcSalePlanItem, periodType, periodCode, zaOccurrenceYear);
        } else {
            throw new CustomException("组织类型异常！请联系管理员");
        }
    }

    /**
     * 构建全国计划项数据
     */
    private void buildQgPlanItemData(ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw,
                                     Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem,
                                     String periodType, String periodCode, String zaOccurrenceYear) {
        if (periodType.equals(PeriodTypeEnum.YC.getCode())) {
            setYcPeriodQgData(productSalePlanItemQgSnSw, mc04IslmYcSalePlanItem);
        } else if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "01")) {
            setJanuaryQgData(productSalePlanItemQgSnSw, mc04IslmYcSalePlanItem);
        } else if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "02")) {
            setFebruaryQgData(productSalePlanItemQgSnSw, mc04IslmYcSalePlanItem);
        } else {
            throw new CustomException("计划周期异常！请联系管理员");
        }
    }

    private void setYcPeriodQgData(ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem) {
        // 全国元春
        // N年末预计库存 N年末预计库存：(N-1年末商业库存)+(N年预计调拨)-(N年预计销售)
        mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(productSalePlanItemQgSnSw.getCurrentYearEndInventory());
        // 1+2月销量
        mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(productSalePlanItemQgSnSw.getNextYearFirstTwoMonthSalesPlan());
        // 1+2月调拨
        mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(productSalePlanItemQgSnSw.getNextYearFirstTwoMonthAllocationPlan());
        // 2月末商业库存
        mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(productSalePlanItemQgSnSw.getNextYearSecondMonthEndingStock());
    }

    private void setJanuaryQgData(ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem) {
        // 全国1月
        // 1月期初商业库存
        mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(productSalePlanItemQgSnSw.getCurrentYearEndInventory());
        // 1月期初调拨
        mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(productSalePlanItemQgSnSw.getNextYearFirstMonthSalesPlan());
        // 1月期初销售
        mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(productSalePlanItemQgSnSw.getNextYearFirstMonthAllocationPlan());
        // 1月期末商业库存
        mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(productSalePlanItemQgSnSw.getNextYearFirstMonthEndingStock());
    }

    private void setFebruaryQgData(ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem) {
        // 全国2月
        mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(productSalePlanItemQgSnSw.getNextYearFirstMonthEndingStock());
        mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(productSalePlanItemQgSnSw.getNextYearSecondMonthSalesPlan());
        mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(productSalePlanItemQgSnSw.getNextYearSecondMonthAllocationPlan());
        mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(productSalePlanItemQgSnSw.getNextYearSecondMonthEndingStock());
    }

    /**
     * 构建省内计划项数据
     */
    private void buildSnPlanItemData(ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw,
                                     Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem,
                                     String periodType, String periodCode, String zaOccurrenceYear) {
        if (periodType.equals(PeriodTypeEnum.YC.getCode())) {
            setYcPeriodSnData(productSalePlanItemQgSnSw, mc04IslmYcSalePlanItem);
        } else if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "01")) {
            setJanuarySnData(productSalePlanItemQgSnSw, mc04IslmYcSalePlanItem);
        } else if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "02")) {
            setFebruarySnData(productSalePlanItemQgSnSw, mc04IslmYcSalePlanItem);
        } else {
            throw new CustomException("计划周期异常！请联系管理员");
        }
    }

    private void setYcPeriodSnData(ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem) {
        // 省内元春
        mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(productSalePlanItemQgSnSw.getCurrentYearEndInventoryIn());
        mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(productSalePlanItemQgSnSw.getNextYearFirstTwoMonthSalesPlanIn());
        mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(productSalePlanItemQgSnSw.getNextYearFirstTwoMonthAllocationPlanIn());
        mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(productSalePlanItemQgSnSw.getNextYearSecondMonthEndingStockIn());
    }

    private void setJanuarySnData(ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem) {
        // 省内1月
        // 省内1月期初库存：上一年年末库存 md03_cgt_10th_com_init_stk_qty
        mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(productSalePlanItemQgSnSw.getCurrentYearEndInventoryIn());
        // 1月销售计划 用户手动填写 mc04_cgt_sale_plan_origin_qty
        mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(productSalePlanItemQgSnSw.getNextYearFirstMonthSalesPlanIn());
        // 1月调拨计划 用户手动填写 ma02_cgt_pl_resol_origplan_qty
        mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(productSalePlanItemQgSnSw.getNextYearFirstMonthAllocationPlanIn());
        // 1月期末库存：1月期初库存 + 1月调拨计划 - 1月销售计划 md03_cgt_10th_com_end_stk_qty
        mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(productSalePlanItemQgSnSw.getNextYearFirstMonthEndingStockIn());
    }

    private void setFebruarySnData(ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem) {
        // 省内2月
        mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(productSalePlanItemQgSnSw.getNextYearFirstMonthEndingStockIn());
        mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(productSalePlanItemQgSnSw.getNextYearSecondMonthSalesPlanIn());
        mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(productSalePlanItemQgSnSw.getNextYearSecondMonthAllocationPlanIn());
        mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(productSalePlanItemQgSnSw.getNextYearSecondMonthEndingStockIn());
    }

    /**
     * 构建省外计划项数据
     */
    private void buildSwPlanItemData(ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw,
                                     Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem,
                                     String periodType, String periodCode, String zaOccurrenceYear) {
        if (periodType.equals(PeriodTypeEnum.YC.getCode())) {
            setYcPeriodSwData(productSalePlanItemQgSnSw, mc04IslmYcSalePlanItem);
        } else if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "01")) {
            setJanuarySwData(productSalePlanItemQgSnSw, mc04IslmYcSalePlanItem);
        } else if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "02")) {
            setFebruarySwData(productSalePlanItemQgSnSw, mc04IslmYcSalePlanItem);
        } else {
            throw new CustomException("计划周期异常！请联系管理员");
        }
    }

    private void setYcPeriodSwData(ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem) {
        // 省外元春
        mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(productSalePlanItemQgSnSw.getLastYearStkQtyOut());
        mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(productSalePlanItemQgSnSw.getNextYearFirstTwoMonthSalesPlanOut());
        mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(productSalePlanItemQgSnSw.getNextYearFirstTwoMonthAllocationPlanOut());
        mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(productSalePlanItemQgSnSw.getNextYearSecondMonthEndingStockOut());
    }

    private void setJanuarySwData(ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem) {
        // 省外1月期初库存：上一年年末库存 md03_cgt_10th_com_init_stk_qty
        mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(productSalePlanItemQgSnSw.getLastYearStkQtyOut());
        // 1月销售计划 用户手动填写 mc04_cgt_sale_plan_origin_qty
        mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(productSalePlanItemQgSnSw.getNextYearFirstMonthSalesPlanOut());
        // 1月调拨计划 用户手动填写 ma02_cgt_pl_resol_origplan_qty
        mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(productSalePlanItemQgSnSw.getNextYearFirstMonthAllocationPlanOut());
        // 1月期末库存：1月期初库存 + 1月调拨计划 - 1月销售计划 md03_cgt_10th_com_end_stk_qty
        mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(productSalePlanItemQgSnSw.getNextYearFirstMonthEndingStockOut());
    }

    private void setFebruarySwData(ProductSalePlanItemQgSnSw productSalePlanItemQgSnSw, Mc04IslmYcSalePlanItem mc04IslmYcSalePlanItem) {
        // 省外2月
        mc04IslmYcSalePlanItem.setMd03Cgt10thComInitStkQty(productSalePlanItemQgSnSw.getNextYearFirstMonthEndingStockOut());
        mc04IslmYcSalePlanItem.setMc04CgtSalePlanOriginQty(productSalePlanItemQgSnSw.getNextYearSecondMonthSalesPlanOut());
        mc04IslmYcSalePlanItem.setMa02CgtPlResolOrigplanQty(productSalePlanItemQgSnSw.getNextYearSecondMonthAllocationPlanOut());
        mc04IslmYcSalePlanItem.setMd03Cgt10thComEndStkQty(productSalePlanItemQgSnSw.getNextYearSecondMonthEndingStockOut());
    }

    private IccProductDetailDTO getProductDetail(Collection<IccProductDetailDTO> productList, String productCode) {
        for (IccProductDetailDTO product : productList) {
            if (product.getProductCode().equals(productCode)) {
                return product;
            }
        }
        throw new CustomException("产品详情获取失败！不存在的产品Code" + productCode);
    }

    private BigDecimal getAcCgtTaxAllotPrice(Integer packageQty2, BigDecimal acCgtTaxAllotPrice) {
        if (packageQty2 <= 0) {
            return null;
        }
        return acCgtTaxAllotPrice
                .divide(new BigDecimal(packageQty2), 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(5000));

    }

    private BigDecimal getAcCgtTradePrice(Integer packageQty2, BigDecimal acCgtTradePrice) {
        if (packageQty2 <= 0) {
            return null;
        }
        return acCgtTradePrice
                .divide(new BigDecimal(packageQty2), 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(5000));
    }


    private PlanDataContext initPlanDataContext(YcPlanAllotProvinceReq ycPlanAllotProvinceReq) {
        PlanDataContext context = new PlanDataContext();

        // 获取基础信息
        String mc04SalePlanId = ycPlanAllotProvinceReq.getMc04SalePlanId();
        context.ycSalePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(mc04SalePlanId);
        context.zaOccurrenceYear = context.ycSalePlan.getZaOccurrenceYear();

        // 获取城市列表
        GetBusiComListREQ q = new GetBusiComListREQ();
        q.setMc04ComOrgLevel("02");
        q.setIcomCode(IcomUtils.getIcomCode());
        context.cityList = comServiceAPI.getBusiComList(q).getData().getItems();

        // 解析数据
        context.dataList = ycPlanAllotProvinceReq.getDataList();
        if (context.dataList == null || context.dataList.isEmpty()) {
            throw new CustomException("数据不能为空");
        }

        // 初始化集合
        context.planList = new ArrayList<>();
        context.itemList = new ArrayList<>();
        context.createdPlans = new HashMap<>();

        // 获取省一级城市编码
        GetBusiComListREQ provinceQuery = new GetBusiComListREQ();
        provinceQuery.setMc04ComOrgLevel("01");
        provinceQuery.setIcomCode(IcomUtils.getIcomCode());
        context.provinceCodes = comServiceAPI.getBusiComList(provinceQuery).getData().getItems().stream()
                .map(BusiComDto::getBaComOrgCode)
                .collect(Collectors.toSet());

        return context;
    }

    private void deleteExistingCityPlans(PlanDataContext context) {
        Mc04IslmYcSalePlan query = new Mc04IslmYcSalePlan();
        query.setMc04OrgTypeKind(OrgTypeEnum.CITY.getKind());
        query.setZaOccurrenceYear(context.zaOccurrenceYear);

        List<Mc04IslmYcSalePlan> existingPlans = ycSalePlanRepository.getYcPlanList(query);
        List<String> existingPlanIds = existingPlans.stream()
                .map(Mc04IslmYcSalePlan::getMc04SalePlanId)
                .collect(Collectors.toList());

        if (!existingPlanIds.isEmpty()) {
            ycSalePlanItemRepository.deleteByYcPlanIdList(existingPlanIds);
            ycSalePlanRepository.deleteBatchByIds(existingPlanIds);
        }
    }

    private void processProductCityData(PlanDataContext context) {
        for (Map<String, Object> data : context.dataList) {
            String acCgtName = (String) data.get("acCgtName");
            String acCgtCartonCode = (String) data.get("acCgtCartonCode");

            Set<String> cityCodes = extractCityCodes(data, context.provinceCodes);

            for (String cityCode : cityCodes) {
                processCityPlanData(context, data, acCgtName, acCgtCartonCode, cityCode);
            }
        }
    }

    private Set<String> extractCityCodes(Map<String, Object> data, Set<String> provinceCodes) {
        Set<String> cityCodes = new HashSet<>();

        for (String key : data.keySet()) {
            if (key.contains("_")) {
                String[] parts = key.split("_");
                if (parts.length > 1) {
                    String cityCode = parts[parts.length - 1];
                    if (!provinceCodes.contains(cityCode)) {
                        cityCodes.add(cityCode);
                    }
                }
            }
        }

        return cityCodes;
    }

    private void processCityPlanData(PlanDataContext context, Map<String, Object> data,
                                     String acCgtName, String acCgtCartonCode, String cityCode) {
        // 处理1月计划
        processMonthlyPlan(context, data, acCgtName, acCgtCartonCode, cityCode, "01");

        // 处理2月计划
        processMonthlyPlan(context, data, acCgtName, acCgtCartonCode, cityCode, "02");

        // 处理元春计划
        processYcPlan(context, data, acCgtName, acCgtCartonCode, cityCode);
    }

    private void processMonthlyPlan(PlanDataContext context, Map<String, Object> data,
                                    String acCgtName, String acCgtCartonCode,
                                    String cityCode, String month) {
        String periodCode = context.zaOccurrenceYear + month;

        // 获取或创建计划
        Mc04IslmYcSalePlan plan = getOrCreateCityPlan(context, PeriodTypeEnum.MONTH.getCode(),
                periodCode, cityCode, month);

        // 创建计划详情
        Mc04IslmYcSalePlanItem item = new Mc04IslmYcSalePlanItem();
        item.setMc04SalePlanId(plan.getMc04SalePlanId());
        item.setAcCgtName(acCgtName);
        item.setAcCgtCartonCode(acCgtCartonCode);
        item.setIcomCode(context.ycSalePlan.getIcomCode());

        // 设置数据
        setMonthlyPlanData(item, data, cityCode, month);

        context.itemList.add(item);
    }

    private void setMonthlyPlanData(Mc04IslmYcSalePlanItem item, Map<String, Object> data,
                                    String cityCode, String month) {
        String prefix = "nextYear" + ("01".equals(month) ? "Jan" : "Feb");

        item.setMd03Cgt10thComInitStkQty(getBigDecimalValue(data,
                "01".equals(month) ? "lastYearEndStock_" + cityCode :
                        prefix + "EndStock_" + cityCode));

        item.setMc04CgtSalePlanOriginQty(getBigDecimalValue(data, prefix + "SalePlan_" + cityCode));
        item.setMa02CgtPlResolOrigplanQty(getBigDecimalValue(data, prefix + "AllotPlan_" + cityCode));
        item.setMd03Cgt10thComEndStkQty(getBigDecimalValue(data, prefix + "EndStock_" + cityCode));
    }

    private void processYcPlan(PlanDataContext context, Map<String, Object> data,
                               String acCgtName, String acCgtCartonCode, String cityCode) {
        // 获取或创建计划
        Mc04IslmYcSalePlan plan = getOrCreateCityPlan(context, PeriodTypeEnum.YC.getCode(),
                context.zaOccurrenceYear, cityCode, "YC");

        // 创建计划详情
        Mc04IslmYcSalePlanItem item = new Mc04IslmYcSalePlanItem();
        item.setMc04SalePlanId(plan.getMc04SalePlanId());
        item.setAcCgtName(acCgtName);
        item.setAcCgtCartonCode(acCgtCartonCode);
        item.setIcomCode(context.ycSalePlan.getIcomCode());

        // 设置数据
        item.setMc04CgtSalePlanOriginQty(getBigDecimalValue(data, "nextYearFirstTwoMonthSalePlan_" + cityCode));
        item.setMa02CgtPlResolOrigplanQty(getBigDecimalValue(data, "nextYearFirstTwoMonthAllotPlan_" + cityCode));

        context.itemList.add(item);
    }

    private Mc04IslmYcSalePlan getOrCreateCityPlan(PlanDataContext context, String periodType,
                                                   String periodCode, String cityCode, String planKey) {
        return context.createdPlans.computeIfAbsent(
                cityCode + "_" + planKey,
                k -> buildCityPlan(context.ycSalePlan, periodType, periodCode, cityCode, (List<BusiComDto>) context.cityList)
        );
    }

    private void saveAllPlanItems(PlanDataContext context) {
        if (!context.itemList.isEmpty()) {
            ycSalePlanItemRepository.saveBatch(context.itemList);
        }
    }

    private void validateCityPlanData(YcPlanAllotProvinceReq req) {
        // 基础校验
        validateBasicRequirements(req);

        // 获取省级计划
        Mc04IslmYcSalePlan provincePlan = getProvincePlan(req.getMc04SalePlanId());

        // 校验数据格式
        validateDataFormat(req.getDataList());

        // 校验市级数据总和是否等于省级数据
        validateCityProvinceDataConsistency(req.getDataList(), provincePlan);
    }

    private void validateBasicRequirements(YcPlanAllotProvinceReq req) {
        Assert.notNull(req.getMc04SalePlanId(), () -> new CustomException("元春计划ID不能为空"));
        Assert.notEmpty(req.getDataList(), () -> new CustomException("数据列表不能为空"));
    }

    private Mc04IslmYcSalePlan getProvincePlan(String mc04SalePlanId) {
        Mc04IslmYcSalePlan provincePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(mc04SalePlanId);
        if (provincePlan == null) {
            throw new CustomException("省级计划不存在");
        }
        return provincePlan;
    }

    private void validateDataFormat(List<Map<String, Object>> dataList) {
        for (Map<String, Object> data : dataList) {
            if (!data.containsKey("acCgtName") || !data.containsKey("acCgtCartonCode")) {
                throw new CustomException("数据格式错误，缺少必要字段");
            }
        }
    }

    private void validateCityProvinceDataConsistency(List<Map<String, Object>> dataList, Mc04IslmYcSalePlan provincePlan) {
        Set<String> cityCodes = extractCityCodes(dataList, provincePlan.getMc04OrgTypeCode());

        for (Map<String, Object> data : dataList) {
            String acCgtName = (String) data.get("acCgtName");

            // 计算市级数据总和
            CityDataSums sums = calculateCityDataSums(data, cityCodes);

            // 获取省级数据
            ProvinceData provinceData = getProvinceData(provincePlan.getMc04OrgTypeCode(), data);

            // 校验各项数据
            validateDataConsistency(acCgtName, sums, provinceData);
        }
    }

    private Set<String> extractCityCodes(List<Map<String, Object>> dataList, String mc04OrgTypeCode) {
        return dataList.stream()
                .flatMap(data -> data.keySet().stream()
                        .filter(key -> key.contains("_"))
                        .map(key -> key.split("_")[1])
                        .filter(cityCode -> !cityCode.equals(mc04OrgTypeCode))) // 过滤掉省级code
                .collect(Collectors.toSet());
    }

    private CityDataSums calculateCityDataSums(Map<String, Object> data, Set<String> cityCodes) {
        CityDataSums sums = new CityDataSums();

        for (String cityCode : cityCodes) {
            // 1月数据 - 使用setScale(2)确保精度一致
            sums.janSaleSum = sums.janSaleSum.add(
                    getBigDecimalValue(data, "nextYearJanSalePlan_" + cityCode).setScale(2, RoundingMode.HALF_UP)
            );
            sums.janAllotSum = sums.janAllotSum.add(
                    getBigDecimalValue(data, "nextYearJanAllotPlan_" + cityCode).setScale(2, RoundingMode.HALF_UP)
            );

            // 2月数据 - 使用setScale(2)确保精度一致
            sums.febSaleSum = sums.febSaleSum.add(
                    getBigDecimalValue(data, "nextYearFebSalePlan_" + cityCode).setScale(2, RoundingMode.HALF_UP)
            );
            sums.febAllotSum = sums.febAllotSum.add(
                    getBigDecimalValue(data, "nextYearFebAllotPlan_" + cityCode).setScale(2, RoundingMode.HALF_UP)
            );

            // 元春数据 - 使用setScale(2)确保精度一致
            sums.ycSaleSum = sums.ycSaleSum.add(
                    getBigDecimalValue(data, "nextYearFirstTwoMonthSalePlan_" + cityCode).setScale(2, RoundingMode.HALF_UP)
            );
            sums.ycAllotSum = sums.ycAllotSum.add(
                    getBigDecimalValue(data, "nextYearFirstTwoMonthAllotPlan_" + cityCode).setScale(2, RoundingMode.HALF_UP)
            );
        }

        return sums;
    }
    private ProvinceData getProvinceData(String mc04OrgTypeCode, Map<String, Object> data) {
        ProvinceData provinceData = new ProvinceData();
        provinceData.janSale = getBigDecimalValue(data, "nextYearJanSalePlan"+"_" + mc04OrgTypeCode);
        provinceData.febSale = getBigDecimalValue(data, "nextYearFebSalePlan"+"_" + mc04OrgTypeCode);
        provinceData.ycSale = getBigDecimalValue(data, "nextYearFirstTwoMonthSalePlan"+"_" + mc04OrgTypeCode);
        provinceData.janAllot = getBigDecimalValue(data, "nextYearJanAllotPlan"+"_" + mc04OrgTypeCode);
        provinceData.febAllot = getBigDecimalValue(data, "nextYearFebAllotPlan"+"_" + mc04OrgTypeCode);
        provinceData.ycAllot = getBigDecimalValue(data, "nextYearFirstTwoMonthAllotPlan"+"_" + mc04OrgTypeCode);

        // 当ycSale为空或0时，使用1月和2月销售计划的和
        if (provinceData.ycSale == null || provinceData.ycSale.compareTo(BigDecimal.ZERO) == 0) {
            provinceData.ycSale = provinceData.janSale.add(provinceData.febSale);
        }

        // 当ycAllot为空或0时，使用1月和2月调拨计划的和
        if (provinceData.ycAllot == null || provinceData.ycAllot.compareTo(BigDecimal.ZERO) == 0) {
            provinceData.ycAllot = provinceData.janAllot.add(provinceData.febAllot);
        }
        return provinceData;
    }

    private void validateDataConsistency(String acCgtName, CityDataSums sums, ProvinceData provinceData) {
        // 校验1月销售数据
        if (sums.janSaleSum.compareTo(provinceData.janSale) != 0) {
            throw new CustomException(String.format("卷烟%s的1月销售计划市级总和(%.2f)不等于省级数据(%.2f)",
                    acCgtName, sums.janSaleSum, provinceData.janSale));
        }

        // 校验1月调拨数据
        if (sums.janAllotSum.compareTo(provinceData.janAllot) != 0) {
            throw new CustomException(String.format("卷烟%s的1月调拨计划市级总和(%.2f)不等于省级数据(%.2f)",
                    acCgtName, sums.janAllotSum, provinceData.janAllot));
        }

        // 校验2月销售数据
        if (sums.febSaleSum.compareTo(provinceData.febSale) != 0) {
            throw new CustomException(String.format("卷烟%s的2月销售计划市级总和(%.2f)不等于省级数据(%.2f)",
                    acCgtName, sums.febSaleSum, provinceData.febSale));
        }

        // 校验2月调拨数据
        if (sums.febAllotSum.compareTo(provinceData.febAllot) != 0) {
            throw new CustomException(String.format("卷烟%s的2月调拨计划市级总和(%.2f)不等于省级数据(%.2f)",
                    acCgtName, sums.febAllotSum, provinceData.febAllot));
        }

        // 校验元春销售数据
        if (sums.ycSaleSum.compareTo(provinceData.ycSale) != 0) {
            throw new CustomException(String.format("卷烟%s的元春销售计划市级总和(%.2f)不等于省级数据(%.2f)",
                    acCgtName, sums.ycSaleSum, provinceData.ycSale));
        }

        // 校验元春调拨数据
        if (sums.ycAllotSum.compareTo(provinceData.ycAllot) != 0) {
            throw new CustomException(String.format("卷烟%s的元春调拨计划市级总和(%.2f)不等于省级数据(%.2f)",
                    acCgtName, sums.ycAllotSum, provinceData.ycAllot));
        }
    }

    public List<Map<String, String>> getCityListByProvince(String mc04OrgTypeCode) {
        GetBusiComListREQ q = new GetBusiComListREQ();
        q.setMc04ComOrgLevel("02");
        q.setPcomCodeArr(Collections.singletonList(mc04OrgTypeCode));
        q.setIcomCode(IcomUtils.getIcomCode());
        KCMultiResponse<BusiComDto> comList = comServiceAPI.getBusiComList(q);
        Collection<BusiComDto> items = comList.getData().getItems();
        return items.stream()
                .map(item -> {
                    Map<String, String> cityMap = new HashMap<>();
                    cityMap.put("K", item.getBaComOrgCode());
                    cityMap.put("V", item.getMc04ComOrgShortName());
                    return cityMap;
                })
                .collect(Collectors.toList());
    }

    // 辅助类，用于存储市级数据总和
    private static class CityDataSums {
        BigDecimal janSaleSum = BigDecimal.ZERO;
        BigDecimal janAllotSum = BigDecimal.ZERO;
        BigDecimal febSaleSum = BigDecimal.ZERO;
        BigDecimal febAllotSum = BigDecimal.ZERO;
        BigDecimal ycSaleSum = BigDecimal.ZERO;
        BigDecimal ycAllotSum = BigDecimal.ZERO;
    }

    // 辅助类，用于存储省级数据
    private static class ProvinceData {
        BigDecimal janSale;
        BigDecimal febSale;
        BigDecimal ycSale;
        BigDecimal janAllot;
        BigDecimal febAllot;
        BigDecimal ycAllot;
    }
    private void updateRelatedPlansStatus(String mc04SalePlanId) {
        // 获取省级计划
        Mc04IslmYcSalePlan provincePlan = ycSalePlanRepository.getLatestVersionYcSalePlanById(mc04SalePlanId);


        // 根据省mc04OrgTypeCode获取所有市mc04OrgTypeCode
        GetBusiComListREQ q = new GetBusiComListREQ();
        q.setPcomCodeArr(Collections.singletonList(provincePlan.getMc04OrgTypeCode()));
        q.setIcomCode(IcomUtils.getIcomCode());
        q.setMc04ComOrgLevel("02");
        KCMultiResponse<BusiComDto> comList = comServiceAPI.getBusiComList(q);
        Collection<BusiComDto> items = comList.getData().getItems();


        List<String> cityCodes = items.stream()
                .map(BusiComDto::getBaComOrgCode)
                .collect(Collectors.toList());


        ycSalePlanRepository.updateProvinceAndCityPlansStatus(
                provincePlan.getMc04OrgTypeCode(),
                provincePlan.getZaOccurrenceYear(),
                LatestVersionEnum.CURRENT.getCode(),
                SalePlanStatusEnum.PUBLISHED.getCode(),
                cityCodes
        );

    }

    // 上下文类，用于封装方法参数
    private static class PlanDataContext {
        Mc04IslmYcSalePlan ycSalePlan;
        String zaOccurrenceYear;
        Collection<BusiComDto> cityList;
        List<Map<String, Object>> dataList;
        Set<String> provinceCodes;
        List<Mc04IslmYcSalePlan> planList;
        List<Mc04IslmYcSalePlanItem> itemList;
        Map<String, Mc04IslmYcSalePlan> createdPlans;
    }
    private Mc04IslmYcSalePlan buildCityPlan(Mc04IslmYcSalePlan template, String periodType, String periodCode, String cityCode, List<BusiComDto> cityList) {
        Mc04IslmYcSalePlan plan = new Mc04IslmYcSalePlan();
        plan.setMc04SalePlanId(String.valueOf(IdWorker.getId()));
        plan.setMa02TobaProdTradeTypeCode(template.getMa02TobaProdTradeTypeCode());
        plan.setMc04CgtSalePlanVersion(template.getMc04CgtSalePlanVersion());
        plan.setMc04IsLastestVersion(template.getMc04IsLastestVersion());
        plan.setZaOccurrenceYear(template.getZaOccurrenceYear());
        plan.setMc04PlanSubjectType(template.getMc04PlanSubjectType());
        plan.setMc04PlanSubjectName(template.getMc04PlanSubjectName());
        plan.setMc04PlanSubjectBeginDate(template.getMc04PlanSubjectBeginDate());
        plan.setMc04PlanSubjectEndDate(template.getMc04PlanSubjectEndDate());
        plan.setMc04SalePlanStatus(SalePlanStatusEnum.CITY_PENDING.getCode());
        plan.setMc04CgtSaleFoPeriodType(periodType);
        plan.setMc04CgtSaleFoPeriodCode(periodCode);
        plan.setMc04OrgTypeKind(OrgTypeEnum.CITY.getKind());
        plan.setMc04OrgTypeCode(cityCode);
        // 从城市列表中查找对应的城市信息
        String cityName = cityList.stream()
                .filter(city -> city.getBaComOrgCode().equals(cityCode))
                .findFirst()
                .map(BusiComDto::getMc04ComOrgShortName)
                .orElse("未知城市");
        plan.setMc04OrgTypeName(cityName);
        plan.setIcomCode(template.getIcomCode());
        this.ycSalePlanRepository.save(plan);
        return plan;
    }

    private BigDecimal getBigDecimalValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        return new BigDecimal(value.toString());
    }

    private void fillPlanItemData(Mc04IslmYcSalePlan plan, List<Mc04IslmYcSalePlanItem> items, YcPlanAllotItemProvince resultItem) {
        String periodType = plan.getMc04CgtSaleFoPeriodType();
        String periodCode = plan.getMc04CgtSaleFoPeriodCode();
        String zaOccurrenceYear = plan.getZaOccurrenceYear();

        for (Mc04IslmYcSalePlanItem item : items) {
            if (periodType.equals(PeriodTypeEnum.YC.getCode())) {
                // 元春计划
                resultItem.setNextYearFirstTwoMonthSalePlan(item.getMc04CgtSalePlanOriginQty());
                resultItem.setNextYearFirstTwoMonthAllotPlan(item.getMa02CgtPlResolOrigplanQty());
            } else if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "01")) {
                // 1月计划
                resultItem.setLastYearEndStock(item.getMd03Cgt10thComInitStkQty());
                resultItem.setNextYearJanSalePlan(item.getMc04CgtSalePlanOriginQty());
                resultItem.setNextYearJanAllotPlan(item.getMa02CgtPlResolOrigplanQty());
                resultItem.setNextYearJanEndStock(item.getMd03Cgt10thComEndStkQty());
            } else if (periodType.equals(PeriodTypeEnum.MONTH.getCode()) && periodCode.equals(zaOccurrenceYear + "02")) {
                // 2月计划
                resultItem.setNextYearFebSalePlan(item.getMc04CgtSalePlanOriginQty());
                resultItem.setNextYearFebAllotPlan(item.getMa02CgtPlResolOrigplanQty());
                resultItem.setNextYearFebEndStock(item.getMd03Cgt10thComEndStkQty());
            }
        }
    }

    private void fillYearPlanData(Mc04IslmYcSalePlan plan, YcPlanAllotItemProvince resultItem) {

        // 我需要地市，年月，就能拿到那个年度计划,item,再找到烟名的那一条就行了
        // 年度
        String zaOccurrenceYear = plan.getZaOccurrenceYear();
        String prevYear = String.valueOf(Integer.parseInt(zaOccurrenceYear) - 1);

        // 地市code
        String mc04OrgTypeCode = plan.getMc04OrgTypeCode();
        // 例如获得武汉市的年度计划
        Mc04IslmSalePlanModel yearPlans = ycSalePlanRepository.getYearPlan(prevYear, mc04OrgTypeCode);

        if (yearPlans == null) {
            resultItem.setLastYearEndStock(BigDecimal.ZERO);
            resultItem.setCurrentYearSalePlan(BigDecimal.ZERO);
            resultItem.setCurrentYearAllotPlan(BigDecimal.ZERO);
            resultItem.setCurrentYearEndStock(BigDecimal.ZERO);
            return;
        }
        List<Mc04IslmSalePlanItemModel> yearPlanItems = ycSalePlanRepository.getYearPlanItems(yearPlans.getMc04SalePlanId());

        String acCgtCartonCode = resultItem.getAcCgtCartonCode();
        // 找到烟名的那一条
        Mc04IslmSalePlanItemModel yearPlanItem = yearPlanItems.stream()
                .filter(item -> item.getAcCgtCartonCode().equals(acCgtCartonCode))
                .findFirst()
                .orElse(null);

        if (yearPlanItem != null) {
            resultItem.setLastYearEndStock(yearPlanItem.getMd03Cgt10thComInitStkQty());
            resultItem.setCurrentYearSalePlan(yearPlanItem.getMc04CgtSalePlanOriginQty());
            resultItem.setCurrentYearAllotPlan(yearPlanItem.getMa02CgtPlResolOrigplanQty());
            resultItem.setCurrentYearEndStock(yearPlanItem.getMd03Cgt10thComEndStkQty());
        }
    }


    private List<YcPlanAllotItemProvince> calculateCityData(
            Mc04IslmYcSalePlan provincePlan,
            List<BusiComDto> cities,
            Map<String, Map<String, BigDecimal>> cityProvinceRatios,
            YcPlanAllotItemProvince provinceItem) {

        List<YcPlanAllotItemProvince> cityItems = new ArrayList<>();
        Map<String, BigDecimal> provinceValues = extractProvinceValues(provinceItem);

        for (BusiComDto city : cities) {
            YcPlanAllotItemProvince cityItem = createCityItem(city, provinceItem);
            calculateCityValues(cityItem, city, cityProvinceRatios, provinceValues);
            cityItems.add(cityItem);
        }

        return cityItems;
    }

    private Map<String, BigDecimal> extractProvinceValues(YcPlanAllotItemProvince provinceItem) {
        Map<String, BigDecimal> values = new HashMap<>();

        // 提取年度数据
        extractYearData(values, provinceItem);

        // 提取月度数据
        extractMonthData(values, provinceItem);

        return values;
    }

    private void extractYearData(Map<String, BigDecimal> values, YcPlanAllotItemProvince provinceItem) {
        values.put("lastYearEndStock", getSafeBigDecimal(provinceItem.getLastYearEndStock()));
        values.put("currentYearSalePlan", getSafeBigDecimal(provinceItem.getCurrentYearSalePlan()));
        values.put("currentYearAllotPlan", getSafeBigDecimal(provinceItem.getCurrentYearAllotPlan()));
        values.put("currentYearEndStock", getSafeBigDecimal(provinceItem.getCurrentYearEndStock()));
    }

    private void extractMonthData(Map<String, BigDecimal> values, YcPlanAllotItemProvince provinceItem) {
        values.put("nextYearJanSalePlan", getSafeBigDecimal(provinceItem.getNextYearJanSalePlan()));
        values.put("nextYearJanAllotPlan", getSafeBigDecimal(provinceItem.getNextYearJanAllotPlan()));
        values.put("nextYearJanEndStock", getSafeBigDecimal(provinceItem.getNextYearJanEndStock()));
        values.put("nextYearFebSalePlan", getSafeBigDecimal(provinceItem.getNextYearFebSalePlan()));
        values.put("nextYearFebAllotPlan", getSafeBigDecimal(provinceItem.getNextYearFebAllotPlan()));
        values.put("nextYearFebEndStock", getSafeBigDecimal(provinceItem.getNextYearFebEndStock()));
    }

    private BigDecimal getSafeBigDecimal(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }
    private YcPlanAllotItemProvince createCityItem(BusiComDto city, YcPlanAllotItemProvince provinceItem) {
        YcPlanAllotItemProvince cityItem = new YcPlanAllotItemProvince();
        cityItem.setMc04OrgTypeName(city.getMc04ComOrgShortName());
        cityItem.setMc04OrgTypeCode(city.getBaComOrgCode());
        cityItem.setIsCity(true);
        cityItem.setAcCgtName(provinceItem.getAcCgtName());
        cityItem.setAcCgtCartonCode(provinceItem.getAcCgtCartonCode());
        return cityItem;
    }

    private void calculateCityValues(YcPlanAllotItemProvince cityItem,
                                     BusiComDto city,
                                     Map<String, Map<String, BigDecimal>> cityProvinceRatios,
                                     Map<String, BigDecimal> provinceValues) {

        String cityCode = city.getBaComOrgCode();
        Map<String, BigDecimal> ratios = cityProvinceRatios.getOrDefault(cityCode, new HashMap<>());
        String cgtKey = "_" + cityItem.getAcCgtCartonCode();

        // 计算年度数据
        BigDecimal ratioSum = ratios.getOrDefault("01" + cgtKey, BigDecimal.ZERO)
                .add(ratios.getOrDefault("02" + cgtKey, BigDecimal.ZERO));

        cityItem.setLastYearEndStock(provinceValues.get("lastYearEndStock").multiply(ratioSum));
        cityItem.setCurrentYearSalePlan(provinceValues.get("currentYearSalePlan").multiply(ratioSum));
        cityItem.setCurrentYearAllotPlan(provinceValues.get("currentYearAllotPlan").multiply(ratioSum));
        cityItem.setCurrentYearEndStock(provinceValues.get("currentYearEndStock").multiply(ratioSum));

        // 计算1月数据
        BigDecimal janRatio = ratios.getOrDefault("01" + cgtKey, BigDecimal.ZERO);
        cityItem.setNextYearJanSalePlan(provinceValues.get("nextYearJanSalePlan").multiply(janRatio));
        cityItem.setNextYearJanAllotPlan(provinceValues.get("nextYearJanAllotPlan").multiply(janRatio));
        cityItem.setNextYearJanEndStock(provinceValues.get("nextYearJanEndStock").multiply(janRatio));

        // 计算2月数据
        BigDecimal febRatio = ratios.getOrDefault("02" + cgtKey, BigDecimal.ZERO);
        cityItem.setNextYearFebSalePlan(provinceValues.get("nextYearFebSalePlan").multiply(febRatio));
        cityItem.setNextYearFebAllotPlan(provinceValues.get("nextYearFebAllotPlan").multiply(febRatio));
        cityItem.setNextYearFebEndStock(provinceValues.get("nextYearFebEndStock").multiply(febRatio));

        // 计算1-2月合计
        cityItem.setNextYearFirstTwoMonthSalePlan(
                cityItem.getNextYearJanSalePlan().add(cityItem.getNextYearFebSalePlan()));
        cityItem.setNextYearFirstTwoMonthAllotPlan(
                cityItem.getNextYearJanAllotPlan().add(cityItem.getNextYearFebAllotPlan()));
    }

}
