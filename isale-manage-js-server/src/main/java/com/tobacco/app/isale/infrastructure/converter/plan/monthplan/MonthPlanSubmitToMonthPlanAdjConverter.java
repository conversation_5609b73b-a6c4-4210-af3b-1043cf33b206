/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.plan.monthplan;


import com.tobacco.app.isale.domain.model.plan.monthplan.monthplanadj.MonthPlanAdj;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmit;
import com.tobacco.app.isale.domain.model.plan.monthplan.monthplansubmit.MonthPlanSubmitBasicItem;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: jinfuli
 * @Date: 2025/6/13
 * @Description:
 */
@Mapper
public interface MonthPlanSubmitToMonthPlanAdjConverter extends StructureBaseConverter<MonthPlanSubmit, MonthPlanAdj> {

    MonthPlanSubmitToMonthPlanAdjConverter INSTANCE =
            Mappers.getMapper(MonthPlanSubmitToMonthPlanAdjConverter.class);
}
