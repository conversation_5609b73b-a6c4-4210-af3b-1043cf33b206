/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.monthplan;


import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.lamboegg.common.base.BaseResultConstant;
import com.tobacco.app.isale.domain.repository.plan.monthplan.MonthSalePlanRepository;
import com.tobacco.app.isale.tools.utils.InterHttpUtils;
import com.tobacco.app.isalecenter.client.dto.plan.weekplan.WeekSalePlanDTO;
import com.tobacco.app.isalecenter.common.exception.CustomException;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: jinfuli
 * @Date: 2025/6/13
 * @Description:
 */
@Component("MonthSalePlanRepository")
@RequiredArgsConstructor
public class MonthSalePlanRepositoryImpl implements MonthSalePlanRepository {

    private static Logger logger = LoggerFactory.getLogger(MonthSalePlanRepositoryImpl.class);

    private final InterHttpUtils interHttpUtils;

    @Override
    public void push(List<WeekSalePlanDTO> plans) throws CustomException {

//        URI uri = UriComponentsBuilder.fromHttpUrl(interHost + ":" + interPort + weekAllotInventoryUrl)
//                .build()
//                .toUri();
//
//        HashMap<String, Object> params = new HashMap<>();
//        params.put("DATA", plans);
//        ResponseEntity<Map> mapResponseEntity = restTemplate.postForEntity(uri, params, Map.class);
//
//        Map body = mapResponseEntity.getBody();
//        if (ObjUtil.isEmpty(body)) {
//            if (logger.isErrorEnabled()) {
//                logger.error("推送失败，错误信息：{}", "无返回值");
//            }
//            throw new CustomException("获取产销存数据接口失败，错误信息：无返回值");
//        }
//
//        if (body.get("code") == null || Integer.parseInt(body.get("code").toString()) == BaseResultConstant.FAILED.code) {
//            if (logger.isErrorEnabled()) {
//                logger.error("推送失败，错误信息：{}", JSON.toJSONString(body));
//            }
//            throw new CustomException("推送失败");
//        }
    }

}
