/**
 * Copyright (C) 2025 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.repository.plan.yearplan;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.inspur.ind.util.IDUtils;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanItem;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanItemModel;
import com.tobacco.app.isale.domain.repository.plan.yearplan.IslmSalePlanItemRepository;
import com.tobacco.app.isale.dto.plan.yearplan.Mc04SalePlanItemDto;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmSalePlanItemDOToMc04IslmSalePlanItemConverter;
import com.tobacco.app.isale.infrastructure.converter.plan.yearplan.Mc04IslmSalePlanItemDoConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSalePlanItemDO;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmSalePlanItemService;
import com.tobacco.app.isale.tools.utils.OrderedIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 年度销售计划明细领域仓库实现
 *
 * @Author: longxi
 * @Since: 2025-08-07
 */
@Component("IslmSalePlanItemRepository")
public class IslmSalePlanItemRepositoryImpl implements IslmSalePlanItemRepository {

    @Autowired
    private Mc04IslmSalePlanItemService mc04IslmSalePlanItemService;

    @Override
    public List<Mc04IslmSalePlanItemModel> listByPlanId(String mc04IslmSalePlanId) {
        LambdaQueryChainWrapper<Mc04IslmSalePlanItemDO> lambdaQuery = mc04IslmSalePlanItemService.lambdaQuery();
        List<Mc04IslmSalePlanItemDO> doList = lambdaQuery.eq(Mc04IslmSalePlanItemDO::getMc04SalePlanId, mc04IslmSalePlanId).list();
        return Mc04IslmSalePlanItemDoConverter.INSTANCE.converterDosToModels(doList);
    }

    @Override
    public List<Mc04IslmSalePlanItemModel> listByPlanIds(List<String> mc04IslmSalePlanIds) {
        if (mc04IslmSalePlanIds == null || mc04IslmSalePlanIds.isEmpty()) {
            return new ArrayList<>();
        }
        LambdaQueryChainWrapper<Mc04IslmSalePlanItemDO> lambdaQuery = mc04IslmSalePlanItemService.lambdaQuery();
        List<Mc04IslmSalePlanItemDO> doList = lambdaQuery.in(Mc04IslmSalePlanItemDO::getMc04SalePlanId, mc04IslmSalePlanIds).list();
        return Mc04IslmSalePlanItemDoConverter.INSTANCE.converterDosToModels(doList);
    }

    @Override
    public boolean saveBatch(List<Mc04IslmSalePlanItemModel> salePlanItemModelList) {
        if (CollectionUtil.isNotEmpty(salePlanItemModelList)) {
            salePlanItemModelList.forEach(item -> item.setMc04SalePlanItemId(OrderedIdGenerator.generateOrderedId32()));
        }
        List<Mc04IslmSalePlanItemDO> mc04IslmSalePlanItemDoS = Mc04IslmSalePlanItemDoConverter.INSTANCE.converterModelsToDos(salePlanItemModelList);
        return mc04IslmSalePlanItemService.saveBatch(mc04IslmSalePlanItemDoS);
    }

    @Override
    public boolean updateBatchById(List<Mc04IslmSalePlanItemModel> salePlanItemModelList) {
        List<Mc04IslmSalePlanItemDO> mc04IslmSalePlanItemDoS = Mc04IslmSalePlanItemDoConverter.INSTANCE.converterModelsToDos(salePlanItemModelList);
        return mc04IslmSalePlanItemService.updateBatchById(mc04IslmSalePlanItemDoS);
    }

    @Override
    public boolean saveOrUpdateBatch(List<Mc04IslmSalePlanItemModel> salePlanItemModelList) {
        if (CollectionUtil.isEmpty(salePlanItemModelList)) {
            return false;
        }
        for (Mc04IslmSalePlanItemModel salePlanItemModel : salePlanItemModelList) {
            if (salePlanItemModel.getMc04SalePlanItemId() == null) {
                salePlanItemModel.setMc04SalePlanItemId(OrderedIdGenerator.generateOrderedId32());
            }
        }
        List<Mc04IslmSalePlanItemDO> mc04IslmSalePlanItemDoS = Mc04IslmSalePlanItemDoConverter.INSTANCE.converterModelsToDos(salePlanItemModelList);
        return mc04IslmSalePlanItemService.saveOrUpdateBatch(mc04IslmSalePlanItemDoS);
    }

    @Override
    public List<Mc04IslmSalePlanItemModel> getAllotProvinceDetailData(List<String> mc04SalePlanIds) {
        if (mc04SalePlanIds == null || mc04SalePlanIds.isEmpty()) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Mc04IslmSalePlanItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Mc04IslmSalePlanItemDO::getMc04SalePlanId, mc04SalePlanIds);
        List<Mc04IslmSalePlanItemDO> itemList = mc04IslmSalePlanItemService.list(queryWrapper);
        return Mc04IslmSalePlanItemDoConverter.INSTANCE.converterDosToModels(itemList);
    }

    @Override
    public boolean deleteBatchBySalePlanId(List<String> mc04SalePlanIds) {
        if (mc04SalePlanIds != null && !mc04SalePlanIds.isEmpty()) {
            LambdaQueryWrapper<Mc04IslmSalePlanItemDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(Mc04IslmSalePlanItemDO::getMc04SalePlanId, mc04SalePlanIds);
            return mc04IslmSalePlanItemService.remove(queryWrapper);
        }
        return false;
    }

    @Override
    public Long countByPlanId(String mc04SalePlanId) {
        LambdaQueryWrapper<Mc04IslmSalePlanItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Mc04IslmSalePlanItemDO::getMc04SalePlanId, mc04SalePlanId);
        return mc04IslmSalePlanItemService.count(queryWrapper);
    }


    private Map<String, Object> sumDataMaps(Map<String, Object> map1, Map<String, Object> map2) {
        Map<String, Object> result = new HashMap<>();

        // 数值型字段相加
        result.put("n1YearEndStock", toBigDecimal(map1.get("n1YearEndStock")).add(toBigDecimal(map2.get("n1YearEndStock"))));
        result.put("nYearSalePlan", toBigDecimal(map1.get("nYearSalePlan")).add(toBigDecimal(map2.get("nYearSalePlan"))));
        // 其他数值字段...

        // 非数值型字段，取第一个非空值
        result.put("ac_cgt_carton_code", firstNonNull(
                map1.get("ac_cgt_carton_code"),
                map2.get("ac_cgt_carton_code")));
        result.put("ac_cgt_name", firstNonNull(
                map1.get("ac_cgt_name"),
                map2.get("ac_cgt_name")));

        return result;
    }

    private Object firstNonNull(Object... objects) {
        for (Object obj : objects) {
            if (obj != null) {
                return obj;
            }
        }
        return null;
    }

    private BigDecimal toBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }


    @Override
    public List<Mc04IslmSalePlanItem> getListByPlanIdList(List<String> planIdList) {

        if (planIdList != null && !planIdList.isEmpty()) {
            LambdaQueryWrapper<Mc04IslmSalePlanItemDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(Mc04IslmSalePlanItemDO::getMc04SalePlanId, planIdList);
            List<Mc04IslmSalePlanItemDO> mc04IslmSalePlanItemDOList = mc04IslmSalePlanItemService.list(queryWrapper);
            return Mc04IslmSalePlanItemDOToMc04IslmSalePlanItemConverter.INSTANCE.converterDosToModels(mc04IslmSalePlanItemDOList);
        } else {
            return Collections.emptyList();
        }

    }

    @Override
    public List<Mc04IslmSalePlanItem> getListByPlan(String planId) {
        if (planId != null && !planId.isEmpty()) {
            LambdaQueryWrapper<Mc04IslmSalePlanItemDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Mc04IslmSalePlanItemDO::getMc04SalePlanId, planId);
            List<Mc04IslmSalePlanItemDO> mc04IslmSalePlanItemDOList = mc04IslmSalePlanItemService.list(queryWrapper);
            return Mc04IslmSalePlanItemDOToMc04IslmSalePlanItemConverter.INSTANCE.converterDosToModels(mc04IslmSalePlanItemDOList);
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public boolean removeByIds(List<String> itemIdList) {
        return mc04IslmSalePlanItemService.removeByIds(itemIdList);
    }

    @Override
    public boolean batchAddOrSaveSalePlanItem(List<Mc04IslmSalePlanItem> salePlanItemList) {
        return mc04IslmSalePlanItemService.saveOrUpdateBatch(Mc04IslmSalePlanItemDOToMc04IslmSalePlanItemConverter.INSTANCE.converterModelsToDos(salePlanItemList));
    }

    @Override
    public boolean updateBatchItemById(List<Mc04IslmSalePlanItem> itemsToUpdate) {
        return mc04IslmSalePlanItemService.updateBatchById(Mc04IslmSalePlanItemDOToMc04IslmSalePlanItemConverter.INSTANCE.converterModelsToDos(itemsToUpdate));
    }

    @Override
    public boolean saveOrUpdateItem(Mc04IslmSalePlanItem itemToUpdate) {
        return mc04IslmSalePlanItemService.updateById(Mc04IslmSalePlanItemDOToMc04IslmSalePlanItemConverter.INSTANCE.converterModelToDo(itemToUpdate));
    }
}
