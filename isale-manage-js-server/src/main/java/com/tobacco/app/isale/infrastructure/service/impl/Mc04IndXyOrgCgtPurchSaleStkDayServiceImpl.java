/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.domain.model.agreement.sign.ManageIndXyOrgCgtPurchSaleStkDay;
import com.tobacco.app.isale.infrastructure.entity.Mc04IndXyOrgCgtPurchSaleStkDayDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IndXyOrgCgtPurchSaleStkDayMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IndXyOrgCgtPurchSaleStkDayService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;


/**
 * @description : 服务实现类
 *
 * <AUTHOR> wangluhao01
 * @since : 2025-05-13
 * @email : wanglu<PERSON><EMAIL>
 * @create_time : 2025-05-13
 */
@Service
public class Mc04IndXyOrgCgtPurchSaleStkDayServiceImpl extends ServiceImpl<Mc04IndXyOrgCgtPurchSaleStkDayMapper, Mc04IndXyOrgCgtPurchSaleStkDayDO> implements Mc04IndXyOrgCgtPurchSaleStkDayService {

    /**
     * @param baComOrgCode 地市编码
     * @param periodCode   卷烟协议周期
     * @return List<ManageIndXyOrgCgtPurchSaleStkDay>
     * <AUTHOR> wangluhao01
     * @create_time : 2025-05-13 19:11:42
     * @description : 获取前半年销售量
     */
    @Override
    public List<ManageIndXyOrgCgtPurchSaleStkDay> getFirstHalfYearSalesGroupByProduct(
            String baComOrgCode, String periodCode) {
        String tableEndDate = this.baseMapper.getEndDate("mc04_ind_xy_org_cgt_purch_sale_stk_day");
        String beginDate = periodCode.substring(0, 4) + "0101";
        String endDate = periodCode.substring(0, 4) + "0630";
        // 开始时间大于最后汇总时间 则返回空
        if (beginDate.compareTo(tableEndDate) > 0) {
            return Collections.emptyList();
        }
        // 结束时间大于最后汇总时间 则截取最后汇总时间
        if (endDate.compareTo(tableEndDate) > 0) {
            endDate = tableEndDate;
        }
        return this.baseMapper.getFirstHalfYearSalesGroupByProduct(baComOrgCode, endDate);
    }
}