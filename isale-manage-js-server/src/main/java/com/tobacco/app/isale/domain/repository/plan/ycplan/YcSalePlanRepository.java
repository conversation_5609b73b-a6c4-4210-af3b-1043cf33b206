/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.domain.repository.plan.ycplan;


import com.alibaba.bizworks.core.specification.Method;
import com.alibaba.bizworks.core.specification.Parameter;
import com.alibaba.bizworks.core.specification.ReturnValue;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tobacco.app.isale.domain.model.plan.ycplan.*;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanItemModel;
import com.tobacco.app.isale.domain.model.plan.yearplan.Mc04IslmSalePlanModel;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSalePlanDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmSalePlanItemDO;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmYcSalePlanDO;
import com.tobacco.sc.icust.dto.com.BusiComDto;
import com.tobacco.sc.icust.dto.com.ComTree;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 元春销售调拨计划
 * </p>
 *
 * @Author: loongxi
 * @Since: 2025-08-07
 */
public interface YcSalePlanRepository {

    @ReturnValue(desc = "元春计划分页数据")
    @Method(name = "获取元春计划分页数据")
    Page<Mc04IslmYcSalePlan> queryYcPlanPage(
            @Parameter(desc = "元春计划分页查询条件", name = "元春计划分页查询条件", required = true) YcSalePlanPage ycSalePlanPage);

    @ReturnValue(desc = "最新版本的元春计划")
    @Method(name = "根据id获取最新版本的元春计划")
    Mc04IslmYcSalePlan getLatestVersionYcSalePlanById(@Parameter(desc = "元春计划id", name = "元春计划id", required = true) String mc04SalePlanId);

    @ReturnValue(desc = "元春计划列表")
    @Method(name = "获取元春计划列表")
    List<Mc04IslmYcSalePlan> getYcPlanList(@Parameter(desc = "元春计划", name = "元春计划", required = true) Mc04IslmYcSalePlan mc04IslmYcSalePlan);

    @ReturnValue(desc = "无")
    @Method(name = "批量保存元春计划")
    void saveBatch(List<Mc04IslmYcSalePlan> ycSalePlanList);

    @ReturnValue(desc = "无")
    @Method(name = "批量更新元春计划")
    void updateBatch(List<Mc04IslmYcSalePlan> ycSalePlanList);

    @ReturnValue(desc = "无")
    @Method(name = "根据id批量删除元春计划")
    void deleteBatchByIds(List<String> existingPlanIds);

    @ReturnValue(desc = "各个地市的占比数据")
    @Method(name = "获取各个地市的占比数据")
    Map<String, Map<String, BigDecimal>> getCityProvinceRatios(String provinceCode, String year, List<BusiComDto> cityList);

    List<BusiComDto> getValidCities(Mc04IslmYcSalePlan provincePlan);

    Mc04IslmSalePlanModel getYearPlan(String zaOccurrenceYear, String mc04OrgTypeCode);

    List<Mc04IslmSalePlanItemModel> getYearPlanItems(String mc04SalePlanId);

    void save(Mc04IslmYcSalePlan plan);

    /**
     * 获取某市 已提交最新的元春计划
     *
     * @param zaOccurrenceYear 编制年份
     * @param mc04OrgTypeCode
     * @return 元春计划 全国
     */
    Mc04IslmYcSalePlan latestByYear4City(String zaOccurrenceYear, String mc04OrgTypeCode);

    /**
     * 获取某市 所有元春计划 指定版本
     *
     * @param zaOccurrenceYear 编制年份
     * @param mc04CgtSalePlanVersion 元春计划版本
     * @param mc04OrgTypeCode 地市code
     * @return 元春计划
     */
    List<Mc04IslmYcSalePlan> listYcByVersion(String zaOccurrenceYear, String mc04CgtSalePlanVersion, String mc04OrgTypeCode);

    /**
     * 获取某市 指定版本 指定月份的元春计划
     *
     * @param zaOccurrenceYear 编制年份
     * @param mc04CgtSalePlanVersion 元春计划版本
     * @param mc04OrgTypeCode 地市code
     * @param periodCode 1-12
     * @return 元春计划
     */
    Mc04IslmYcSalePlan getCityMonthPlanByVersion(String zaOccurrenceYear, String mc04CgtSalePlanVersion, String mc04OrgTypeCode, String periodCode);

    /**
     * 获取元春计划省内省外所有卷烟规格年相关信息
     * N-1年末商业库存
     * N年预计销售
     * N年预计调拨
     * N年末预计库存
     */
    @ReturnValue(desc = "元春计划省内省外所有卷烟规格年相关信息")
    @Method(name = "获取元春计划省内省外所有卷烟规格年相关信息")
    SnSwEndStkSaleAllotInfoForYear getSnSwEndStkSaleAllotInfoForYear(String zaOccurrenceYear);

    /**
     * 获取元春计划各个省所有卷烟规格年相关信息
     * N-1年末商业库存
     * N年预计销售
     * N年预计调拨
     * N年末预计库存
     */
    @ReturnValue(desc = "元春计划各个省所有卷烟规格年相关信息")
    @Method(name = "获取元春计划各个省所有卷烟规格年相关信息")
    ProvinceEndStkSaleAllotInfoForYear getProvinceEndStkSaleAllotInfoForYear(String zaOccurrenceYear);

    /**
     * 获取最近三年一月和2月省内省外最大商业末库存
     * @return
     */
    @ReturnValue(desc = "最近三年一月和2月省内省外最大商业末库存数据")
    @Method(name = "获取最近三年一月和2月省内省外最大商业末库存")
    SnSwLastThreeYearMaxEndStkJanAndFeb getLastThreeYearSnSwJanAndFebMaxEndStkQtyMap(String zaOccurrenceYear);

    Map<String, Object> getMonthSaleData(String baComOrgCode, String acCgtCartonCode, String yearMonth);

    Map<String, Object> getSpecialProvinceMonthSaleData(String baComOrgCode, String acCgtCartonCode, String yearMonth);

    Map<String, Object> getNormalProvinceMonthSaleData(String baComOrgCode, String acCgtCartonCode, String yearMonth);

    List<BusiComDto> getRawCityList(String provinceCode);

    List<Mc04IslmYcSalePlan> queryRelatedPlans(Mc04IslmYcSalePlan provincePlan, List<BusiComDto> cities);

    List<Mc04IslmSalePlanItemModel> getYearPlanItems(List<Mc04IslmSalePlanModel> yearPlans);

    List<Mc04IslmSalePlanModel> getYearPlans(List<Mc04IslmYcSalePlan> ycPlans, int currentYear, int prevYear);

    @ReturnValue(desc = "是否成功")
    @Method(name = "批量更新省和市的计划状态")
    void updateProvinceAndCityPlansStatus(String provinceCode, String zaOccurrenceYear, String version, String status, List<String> cityCodes);

}