/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.parm;

import com.tobacco.app.isale.domain.model.order.dist.parm.DistParm;
import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isalecenter.client.req.dist.parm.DistParmREQ;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @create_time : 2025/06/03 19:18
 * @description : 配货参数数据转化
 */
@Mapper
public interface DistParmToDistParmREQConverter extends StructureBaseConverter<DistParmREQ, DistParm> {

    DistParmToDistParmREQConverter INSTANCE =
            Mappers.getMapper(DistParmToDistParmREQConverter.class);

}
