/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.converter.dist.order;

import com.tobacco.app.isale.infrastructure.converter.StructureBaseConverter;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmCgtDistOrderDO;
import com.tobacco.app.isalecenter.client.req.distOrder.Mc04IslmcCgtDistOrderREQ;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> wangluhao01
 * @email : <EMAIL>
 * @create_time : 2025/09/03 11:20
 * @description : 配货订单dp转换销售中心api参数req
 */
@Mapper
public interface Mc04IslmCgtDistOrderDOToMc04IslmcCgtDistOrderREQConverter extends StructureBaseConverter<Mc04IslmCgtDistOrderDO, Mc04IslmcCgtDistOrderREQ> {
    Mc04IslmCgtDistOrderDOToMc04IslmcCgtDistOrderREQConverter SELF = Mappers.getMapper(Mc04IslmCgtDistOrderDOToMc04IslmcCgtDistOrderREQConverter.class);
}