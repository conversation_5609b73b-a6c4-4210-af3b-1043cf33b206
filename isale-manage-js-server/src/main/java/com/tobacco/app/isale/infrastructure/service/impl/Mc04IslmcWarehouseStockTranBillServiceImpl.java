/*
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 *  版权所有 (C) 浪潮软件股份有限公司
 */

package com.tobacco.app.isale.infrastructure.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcWarehouseStockTranBillDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcWarehouseStockTranBillMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcWarehouseStockTranBillService;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: renyonghui
 * @Since: 2025-05-17
 * @Email: <EMAIL>
 * @Create: 2025-05-17
 */
@Service
public class Mc04IslmcWarehouseStockTranBillServiceImpl extends ServiceImpl<Mc04IslmcWarehouseStockTranBillMapper, Mc04IslmcWarehouseStockTranBillDO> implements Mc04IslmcWarehouseStockTranBillService {

}