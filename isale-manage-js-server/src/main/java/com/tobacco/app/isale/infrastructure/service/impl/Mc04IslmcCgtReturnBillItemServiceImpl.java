/**
 * Copyright (C) 2024 Inspur
 * All rights reserved.
 * <p>
 * 版权所有 (C) 浪潮软件股份有限公司
 */
package com.tobacco.app.isale.infrastructure.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tobacco.app.isale.infrastructure.entity.Mc04IslmcCgtReturnBillItemDO;
import com.tobacco.app.isale.infrastructure.mapper.Mc04IslmcCgtReturnBillItemMapper;
import com.tobacco.app.isale.infrastructure.service.api.Mc04IslmcCgtReturnBillItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @Description: 服务实现类
 *
 * @Author: qifengyu
 * @Since: 2025-07-25
 * @Email: <EMAIL>
 * @Create: 2025-07-25
 */
@Service
public class Mc04IslmcCgtReturnBillItemServiceImpl extends ServiceImpl<Mc04IslmcCgtReturnBillItemMapper, Mc04IslmcCgtReturnBillItemDO> implements Mc04IslmcCgtReturnBillItemService {

    private Mc04IslmcCgtReturnBillItemMapper mc04IslmcCgtReturnBillItemMapper;

    @Autowired
    public void setMc04IslmcCgtReturnBillItemMapper(Mc04IslmcCgtReturnBillItemMapper mc04IslmcCgtReturnBillItemMapper) {
        this.mc04IslmcCgtReturnBillItemMapper = mc04IslmcCgtReturnBillItemMapper;
    }

}