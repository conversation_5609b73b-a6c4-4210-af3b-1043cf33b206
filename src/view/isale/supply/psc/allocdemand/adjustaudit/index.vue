<template>
  <LamboNPageView>
    <LamboNTable
      v-model="tableData"
      ref="table"
      defaultUnitType="WZ"
      defaultTableUnitType="X"
      :xScale="5"
      :wzScale="4"
      :columns.sync="tableColumns"
      :loading="tableLoading"
      :dataApi="dataApi"
      :search-params="searchParams"
      :defaultSortable="false"
      auto-refresh
      @on-select-change="changeTableSelect"
      @on-data-load="tableLoading = false"
    >
      <template #search>
        <LamboNForm
          v-model="form"
          formType="search"
          :fieldList="fieldList"
          :searchLoading="tableLoading"
          @doSearch="query"
          @reset="reset"
        />
      </template>
    </LamboNTable>
    <!--    点击编码 展示详情数据-->
    <Drawer title="明细展示" v-model="showDialog" width="75%" :showCancel="true" :closable="false">
      <div>
        <LamboNTable
          height="400"
          ref="showTable"
          defaultUnitType="WZ"
          defaultTableUnitType="X"
          :xScale="5"
          :wzScale="4"
          :columns="showColumns"
          auto-refresh
          v-model="showData"
          :loading="showLoading"
          disablePage
          :showTableOption="false"
        >
        </LamboNTable>
      </div>
    </Drawer>
    <!--    审核当前选中条-->
    <Drawer title="审核" v-model="reviewDialog" width="75%" :showCancel="true" :closable="false">
      <div>
        <LamboNTable
          height="400"
          ref="reviewTable"
          defaultUnitType="WZ"
          defaultTableUnitType="X"
          :xScale="5"
          :wzScale="4"
          :columns="reviewColumns"
          auto-refresh
          :loading="reviewLoading"
          v-model="reviewData"
          disablePage
          :showTableOption="false"
        >
        </LamboNTable>
      </div>
      <div class="demo-drawer-footer">
        <div slot="right-footer">
          <div class="demo-drawer-footer">
            <Button style="margin-right: 8px" type="primary" @click="verifyReview">审核</Button>
            <Button type="primary" @click="rejection">驳回</Button>
          </div>
        </div>
      </div>
    </Drawer>
    <!--    填写驳回意见-->
    <Modal
      v-model="rejectionDialog"
      title="驳回意见"
      @on-ok="okRejection"
      @on-cancel="cancelRejection"
    >
      <Input v-model="approvalInfo" type="textarea" :rows="4" placeholder="请填写驳回意见..." />
    </Modal>

    <template #footer>
      <LamboNButtonGroup>
        <LamboNButton bizType="add" @click="batchReview">批量审核</LamboNButton>
        <LamboNButton bizType="add" @click="batchRejection">批量驳回</LamboNButton>
      </LamboNButtonGroup>
    </template>
  </LamboNPageView>
</template>

<script>
import {
  batchRejectionApi,
  batchReviewApi,
  queryAllocdemandAdjustauditPageApi,
  getAdjustauditShowDataApi,
  verifyReviewApi,
  cancelSubmissionApi,
  submissionApi,
} from '@/api/isale/supply/psc/allocdemand/adjustaudit'
import { getDictMapApi } from '@indfnd/common-pro'
import dayjs from 'dayjs'
import { calculateSumRows } from '@/view/isale/plan/common/calculateSum'
import { getTotalSumIndex } from '@/api/isale/plan/yearplan/salefo'

const form = {}
export default {
  data() {
    // 获取当前年月 YYYY-MM 格式
    return {
      tableData: [],
      selectTableDataIds: [],
      loading: true,
      typeList: [],
      statusList: [],
      sumIndex: [],
      dataApi: queryAllocdemandAdjustauditPageApi,
      searchParams: {
        ...form,
      },
      tableLoading: false,
      form: {
        ...form,
      },

      showDialog: false,
      showData: [],
      showLoading: false,
      reviewDialog: false,
      reviewLoading: false,
      reviewData: [],
      rejectionDialog: false,
      planAdjIdList: [],
      approvalInfo: '',
      currentPlanAdjId: '',
    }
  },
  computed: {
    tableColumns() {
      return [
        {
          title: '选择',
          type: 'checkbox',
          key: 'mc04CgtTranDemandAdjId',
          minWidth: 60,
          width: 60,
          flex: 1,
          fixed: 'left',
        },
        {
          title: '序号',
          key: 'id',
          type: 'index',
          width: 80,
          pinned: 'left',
        },
        {
          title: '变更编码',
          key: 'mc04MonthSalePlanAdjId',
          type: 'link',
          cellRendererParams: {
            tooltip: true,
            onClick: (row) => {
              this.openDetailDialog(row)
            },
          },
        },
        {
          title: '计划年月',
          key: 'ma02PlanMonth',
          cellRenderer: (params) => {
            return `<div>${dayjs(params.data['ma02PlanMonth']).format('YYYY-MM-DD')}</div>`
          },
        },
        // {
        //   title: '版本',
        //   key: 'mc03CgtProdPlanVersion',
        // },
        {
          title: '计划类型',
          key: 'mc03CgtProdPlType',
          type: 'enum',
          align: 'center',
          cellRendererParams: {
            enumList: this.typeList,
          },
        },
        {
          title: '变更日期',
          key: 'mc04CgtTranDemandAdjDate',
        },
        {
          title: '调拨需求变更',
          key: 'ma02CgtPlAdjusQtySum',
          isSwitchUnit: true,
        },
        {
          title: '调拨需求变更确认',
          key: 'mc04CgtAllotPlanPdConfirmAdjQtySum',
          isSwitchUnit: true,
        },
        {
          title: '制单人',
          key: 'createName',
        },
        {
          title: '制单日期',
          key: 'createTime',
        },
        {
          title: '状态',
          key: 'mc04CgtTranDemandAdjStatus',
          type: 'enum',
          align: 'center',
          cellRendererParams: {
            enumList: this.statusList,
          },
        },
        {
          title: '操作',
          align: 'center',
          flex: 1,
          fixed: 'right',
          width: 150,
          type: 'button',
          cellRendererParams: {
            datas: (row) => {
              if (row.mc04CgtTranDemandAdjStatus === '20') {
                return [
                  {
                    btnName: '审核',
                    bizType: 'table-view',
                    btnClick: (row) => this.review(row),
                  },
                  {
                    btnName: '驳回',
                    bizType: 'table-view',
                    btnClick: (row) => this.rejection(row),
                  },
                ]
              } else if (row.mc04CgtTranDemandAdjStatus === '30') {
                return [
                  {
                    btnName: '递交',
                    bizType: 'table-view',
                    btnClick: (row) => this.submission(row),
                  },
                ]
              } else if (row.mc04CgtTranDemandAdjStatus === '40') {
                return [
                  {
                    btnName: '取消递交',
                    bizType: 'table-view',
                    btnClick: (row) => this.cancelSubmission(row),
                  },
                ]
              }
            },
          },
        },
      ]
    },
    fieldList() {
      return [
        {
          title: '计划年月',
          type: 'datepicker',
          formKey: 'ma02PlanMonth',
          props: { type: 'month', clearable: false },
        },
        {
          title: '计划类型',
          formKey: 'mc03CgtProdPlType',
          type: 'select',
          flex: 2,
          align: 'center',
          enumList: this.typeList,
          props: {
            multiple: false,
            clearable: false,
          },
        },
        {
          title: '变更日期',
          formKey: 'mc04CgtTranDemandAdjDate',
          type: 'datepicker',
        },
        {
          title: '状态',
          formKey: 'mc04CgtTranDemandAdjStatus',
          type: 'select',
          flex: 2,
          align: 'center',
          enumList: this.statusList,
          props: {
            multiple: false,
            clearable: false,
          },
        },
      ]
    },
    showColumns() {
      return [
        {
          title: '卷烟编码',
          key: 'acTwoLevelCigCode',
          width: 120,
        },
        {
          title: '卷烟名称',
          key: 'acTwoLevelCigName',
          width: 200,
        },
        {
          title: '调拨条价',
          key: 'acCgtTaxAllotPrice',
          width: 120,
        },
        {
          title: '原始备货量',
          key: 'ma02CgtPlResolOrigplanQty',
          isSwitchUnit: true,
          width: 150,
        },
        {
          title: '当前备货量',
          key: 'ma02CgtPlAdjustedQty',
          isSwitchUnit: true,
          width: 150,
        },
        {
          title: '调整量',
          key: 'ma02CgtPlAdjusQty',
          isSwitchUnit: true,
          width: 150,
        },
        {
          title: '调确认整量',
          key: 'mc04CgtAllotPlanPdConfirmAdjQty',
          isSwitchUnit: true,
          width: 150,
        },
        {
          title: '备注',
          key: 'zaRemark',
          width: 120,
        },
      ]
    },
    reviewColumns() {
      return [
        {
          title: '卷烟编码',
          key: 'acTwoLevelCigCode',
          width: 120,
        },
        {
          title: '卷烟名称',
          key: 'acTwoLevelCigName',
          width: 200,
        },
        {
          title: '调拨条价',
          key: 'acCgtTaxAllotPrice',
          width: 120,
        },
        {
          title: '原始备货量',
          key: 'ma02CgtPlResolOrigplanQty',
          isSwitchUnit: true,
          width: 150,
        },
        {
          title: '当前备货量',
          key: 'ma02CgtPlAdjustedQty',
          isSwitchUnit: true,
          width: 150,
        },
        {
          title: '调整量',
          key: 'ma02CgtPlAdjusQty',
          isSwitchUnit: true,
          width: 150,
        },
        {
          title: '调确认整量',
          key: 'mc04CgtAllotPlanPdConfirmAdjQty',
          isSwitchUnit: true,
          width: 150,
          type: 'input',
          cellRendererParams: {
            props: {
              number: true,
              type: 'number',
              textCondition: (row) => row.isSumRow === true,
            },
          },
        },
        {
          title: '备注',
          key: 'zaRemark',
          width: 120,
          type: 'input',
          cellRendererParams: {
            props: {
              number: true,
              type: 'number',
              textCondition: (row) => row.isSumRow === true,
            },
          },
        },
      ]
    },
  },
  created() {
    this.initialize()
    this.getTotalSumIndex()
  },
  watch: {
    reviewData: {
      handler(newVal, oldVal) {
        // 1. 排除初始化场景（oldVal为undefined时不处理）
        if (!oldVal) return
        // 2. 过滤掉合计行，只比较实际数据行
        const filterSumRow = (arr) => arr.filter((item) => !item.isSumRow)
        const newData = filterSumRow(newVal)
        const oldData = filterSumRow(oldVal)
        // 3. 比较关键字段是否变化（仅比较调整量和影响合计的字段）
        const isChanged = this.isDataChanged(newData, oldData, ['mc04CgtAllotPlanPdConfirmAdjQty'])
        // 4. 只有变化时才更新合计
        if (isChanged) {
          this.calculateEndStock()
        }
      },
      deep: true,
      immediate: false,
    },
  },
  methods: {
    // 核心：判断两个数据数组的指定字段是否有变化
    isDataChanged(newArr, oldArr, compareFields) {
      // 长度不同直接判定为变化
      if (newArr.length !== oldArr.length) return true
      // 逐个比较指定字段
      for (let i = 0; i < newArr.length; i++) {
        const newItem = newArr[i]
        const oldItem = oldArr[i]
        // 主键不同（如卷烟编码），视为不同行
        if (newItem.acTwoLevelCigCode !== oldItem.acTwoLevelCigCode) {
          return true
        }
        // 比较关键字段值
        for (const field of compareFields) {
          // 数值比较（避免0和"0"的误判）
          if (Number(newItem[field]) !== Number(oldItem[field])) {
            return true
          }
        }
      }
      // 所有字段均未变化
      return false
    },
    calculateEndStock() {
      const normalData = this.reviewData.filter((item) => !item.isSumRow)
      const sumFields = [
        'ma02CgtPlResolOrigplanQty',
        'ma02CgtPlAdjustedQty',
        'ma02CgtPlAdjusQty',
        'mc04CgtAllotPlanPdConfirmAdjQty',
      ]
      const sumData = calculateSumRows(normalData, sumFields, this.sumIndex, 'acTwoLevelCigName')
      this.reviewData = [...sumData, ...normalData]
    },

    async getTotalSumIndex() {
      const { data } = await getTotalSumIndex({ mz12InfoSystemResourceCode: 'allocdemandMange' })
      this.sumIndex = data
    },
    async review(row) {
      //点击单个审核按钮  弹出审核界面 并且进行审核
      this.reviewDialog = true
      this.reviewLoading = true
      let request = {
        mc04MonthSalePlanAdjId: row.mc04MonthSalePlanAdjId,
      }
      this.planAdjIdList.push(row.mc04MonthSalePlanAdjId)
      this.currentPlanAdjId = row.mc04MonthSalePlanAdjId
      let data = await getAdjustauditShowDataApi(request)
      if (data && data.data && data.data.length > 0) {
        const sumFields = [
          'ma02CgtPlResolOrigplanQty',
          'ma02CgtPlAdjustedQty',
          'ma02CgtPlAdjusQty',
          'mc04CgtAllotPlanPdConfirmAdjQty',
        ]
        const sumData = calculateSumRows(data.data, sumFields, this.sumIndex, 'acTwoLevelCigName')
        this.reviewData = [...sumData, ...data.data]
      }
      this.reviewLoading = false
    },
    verifyReview() {
      this.$Modal.confirm({
        title: '您是否确认审核该调拨任务？',
        onOk: async () => {
          if (!this.validateAdjustQty(this.reviewData)) {
            // 校验失败，不继续执行
            return
          }
          let tableData = this.$refs.reviewTable.getWZData()
          this.reviewLoading = true
          try {
            let request = {
              mc04MonthSalePlanAdjId: this.currentPlanAdjId,
              adjustauditItemList: tableData,
            }
            await verifyReviewApi(request)
            this.$Message.success('审核成功')
            this.currentPlanAdjId = ''
            this.reviewData = []
            this.reviewDialog = false
            await this.query()
          } catch (e) {
            return
          }
        },
        onCancel: () => {},
      })
    },
    rejection() {
      this.rejectionDialog = true
    },
    okRejection() {
      this.$Modal.confirm({
        title: '您是否确认驳回该调拨任务？',
        onOk: async () => {
          try {
            let request = {
              mc04MonthSalePlanAdjIdList: this.planAdjIdList,
              rejectionOpinion: this.approvalInfo,
            }
            await batchRejectionApi(request)
            this.$Message.success('驳回成功')
            await this.query()
          } catch (e) {
            return
          } finally {
            this.approvalInfo = ''
            this.planAdjIdList = []
            this.rejectionDialog = false
            this.reviewDialog = false
          }
        },
        onCancel: () => {},
      })
    },
    cancelRejection() {
      this.approvalInfo = ''
      this.planAdjIdList = []
      this.rejectionDialog = false
    },
    async openDetailDialog(row) {
      //点击编号打开详情界面
      this.showDialog = true
      this.showLoading = true
      let request = {
        mc04MonthSalePlanAdjId: row.mc04MonthSalePlanAdjId,
      }
      let data = await getAdjustauditShowDataApi(request)
      if (data && data.data && data.data.length > 0) {
        const sumFields = ['ma02CgtPlResolOrigplanQty', 'ma02CgtPlAdjustedQty', 'ma02CgtPlAdjusQty']
        const sumData = calculateSumRows(data.data, sumFields, this.sumIndex, 'acTwoLevelCigName')
        this.showData = [...sumData, ...data.data]
      }
      this.showLoading = false
    },
    submission(row) {
      this.$Modal.confirm({
        title: '您是否确认递交该调拨任务？',
        onOk: async () => {
          this.reviewLoading = true
          try {
            let request = {
              mc04MonthSalePlanAdjId: row.mc04MonthSalePlanAdjId,
            }
            await submissionApi(request)
            this.$Message.success('递交成功')
            this.reviewLoading = false
            await this.query()
          } catch (e) {
            return
          }
        },
        onCancel: () => {},
      })
    },
    cancelSubmission(row) {
      this.$Modal.confirm({
        title: '您是否确认取消递交调拨任务？',
        onOk: async () => {
          this.reviewLoading = true
          try {
            let request = {
              mc04MonthSalePlanAdjId: row.mc04MonthSalePlanAdjId,
            }
            await cancelSubmissionApi(request)
            this.$Message.success('取消递交成功')
            this.reviewLoading = false
            await this.query()
          } catch (e) {
            return
          }
        },
        onCancel: () => {},
      })
    },
    changeTableSelect(selection) {
      this.selectTableDataIds = selection || []
    },
    async initialize() {
      const dictDatas = await getDictMapApi([
        'ISALE_SUPPLY_ALLOC_DEMAND_TYPE',
        'ISALE_SUPPLY_DEMAND_AJ_STATUS',
      ])
      this.typeList = dictDatas['ISALE_SUPPLY_ALLOC_DEMAND_TYPE'].renderData
      this.statusList = dictDatas['ISALE_SUPPLY_DEMAND_AJ_STATUS'].renderData
    },
    async query() {
      this.tableLoading = true
      try {
        let queryData = {
          ma02PlanMonth: this.form.ma02PlanMonth,
          mc03CgtProdPlType: this.form.mc03CgtProdPlType,
          mc04CgtTranDemandAdjDate: this.form.mc04CgtTranDemandAdjDate,
          mc04CgtTranDemandAdjStatus: this.form.mc04CgtTranDemandAdjStatus,
        }
        let { data } = await queryAllocdemandAdjustauditPageApi(queryData)
        if (data && data.records) {
          this.tableData = data.records
        }
      } catch (error) {
        this.tableLoading = false
      } finally {
        this.tableLoading = false
      }
    },
    reset() {
      this.form = {}
      this.query()
    },
    batchReview() {
      if (this.selectTableDataIds.length < 1) {
        this.$Message.info('请选择至少一个调拨需求单')
        return false
      }
      this.$Modal.confirm({
        title: '您是否确认批量审核勾选的调拨计划？',
        onOk: async () => {
          this.tableLoading = true
          try {
            let rows = this.$refs.table.getSelection()
            let requestList = []
            rows.forEach((item) => {
              requestList.push(item.mc04MonthSalePlanAdjId)
            })
            await batchReviewApi({ mc04MonthSalePlanAdjIdList: requestList })
            this.$Message.success('批量审核成功')
            await this.query()
          } catch (e) {
            /* empty */
          } finally {
            this.tableLoading = false
          }
        },
        onCancel: () => {},
      })
    },
    batchRejection() {
      if (this.selectTableDataIds.length < 1) {
        this.$Message.info('请选择至少一个调拨需求单')
        return false
      }
      let rows = this.$refs.table.getSelection()
      rows.forEach((item) => {
        this.planAdjIdList.push(item.mc04MonthSalePlanAdjId)
      })
      this.rejectionDialog = true
    },
    // 校验 modifyData 中的调整量是否为有效数字
    validateAdjustQty(data) {
      for (let i = 0; i < data.length; i++) {
        const item = data[i]
        // 跳过合计行
        if (item.isSumRow) continue

        // 检查 mc04CgtAllotPlanPdConfirmAdjQty 是否为有效数字
        if (
          item.mc04CgtAllotPlanPdConfirmAdjQty !== undefined &&
          item.mc04CgtAllotPlanPdConfirmAdjQty !== null &&
          item.mc04CgtAllotPlanPdConfirmAdjQty !== ''
        ) {
          // 如果含有字母和特殊字符（除了数字、小数点、正负号）都直接判定无效
          const stringValue = String(item.mc04CgtAllotPlanPdConfirmAdjQty).trim()
          const validNumberPattern = /^[+-]?\d*\.?\d+$/
          if (!validNumberPattern.test(stringValue)) {
            this.$Message.error(`第${i + 1}行的调整量包含无效字符，请输入正确的数值`)
            return false
          }
          const parsedValue = parseFloat(item.mc04CgtAllotPlanPdConfirmAdjQty)
          if (isNaN(parsedValue)) {
            this.$Message.error(`第${i + 1}行的调整量不是有效数字，请输入正确的数值`)
            return false
          }
        }
      }
      return true
    },
  },
}
</script>

<style scoped lang="less"></style>
