<template>
  <LamboNPageView>
    <LamboNTable
      v-model="tableData"
      ref="table"
      defaultUnitType="WZ"
      defaultTableUnitType="X"
      :xScale="5"
      :wzScale="4"
      :columns.sync="tableColumns"
      :dataApi="dataApi"
      :search-params="searchParams"
      :defaultSortable="false"
      :disablePage="true"
      auto-refresh
      @on-data-load="tableLoading = false"
      @on-select-change="changeTableSelect"
    >
      <template #search>
        <LamboNForm
          v-model="form"
          ref="searchForm"
          formType="search"
          :fieldList="fieldList"
          :loading="tableLoading"
          @doSearch="query"
          @reset="reset"
        />
      </template>
    </LamboNTable>
    <template #footer>
      <LamboNButtonGroup>
        <LamboNButton bizType="add" @click="submitAudit">提交</LamboNButton>
      </LamboNButtonGroup>
    </template>
  </LamboNPageView>
</template>

<script>
import dayjs from 'dayjs'
import { getComTreeApi } from '@/api/isale/agreement/basic/batchlock'
import {
  centerBatchAuditApi,
  queryRetBillCenterAuditListApi,
} from '@/api/isale/order/ret/cigarbill/centeraudit'

const beginDate = dayjs().subtract(7, 'day').format('YYYYMMDD')
const endDate = dayjs().format('YYYYMMDD')
const searchParamsDefault = {}
const defaultForm = {
  // 日期范围：今天之前7天，今天
  mc04CgtReturnBillDateRange: [beginDate, endDate],
}

export default {
  name: 'index',
  data() {
    return {
      selectTableDataIds: [],
      outStorehouseCodeList: [],
      inStorehouseCodeList: [],
      dataApi: queryRetBillCenterAuditListApi,
      searchParams: {
        ...searchParamsDefault,
      },
      tableData: [],
      tableLoading: false,
      canSave: false,
      form: {
        ...defaultForm,
      },
      fieldList: [
        {
          //默认前后七天
          title: '退货日期',
          formKey: 'mc04CgtReturnBillDateRange',
          type: 'datepicker',
          maxDate: beginDate,
          props: { type: 'date-range', required: true, clearable: false },
        },
        {
          title: '公司',
          formKey: 'baComOrgCode',
          type: 'treeselect',
          props: {
            // 'select-leaf-only': false,
            placeholder: '全部',
            showCheckbox: true,
            dataApi: getComTreeApi,
            urlParams: {
              treeType: 'Pro_City',
              comIsAgreeUnit: '1',
              comSceneCode: 'DEFAULT', // 数据权限
            },
          },
        },
      ],
    }
  },
  computed: {
    tableColumns() {
      return [
        {
          title: '选择',
          type: 'checkbox',
          key: 'baComOrgName',
          minWidth: 60,
          width: 60,
          flex: 1,
          fixed: 'left',
        },
        {
          title: '序号',
          key: 'id',
          type: 'index',
          minWidth: 80,
          width: 80,
          align: 'center',
          pinned: 'left',
        },
        {
          title: '公司',
          key: 'baComOrgName',
          minWidth: 80,
          width: 80,
          pinned: 'left',
          autoMergeRow: true,
        },
        {
          title: '退货日期',
          key: 'mc04CgtReturnBillDate',
          align: 'center',
          cellRenderer: (params) => {
            return `<div>${dayjs(params.data['mc04CgtReturnBillDate']).format('YYYY-MM-DD')}</div>`
          },
        },
        {
          title: '发货仓库',
          hide: true,
          key: 'md02CgtOutStorehouseCode',
        },
        {
          title: '到货仓库',
          hide: true,
          key: 'md02CgtInStorehouseCode',
        },
        {
          title: '发货仓库',
          key: 'md02CgtOutStorehouseName',
        },
        {
          title: '到货仓库',
          key: 'md02CgtInStorehouseName',
        },
        {
          title: '交易名称',
          key: 'acCgtName',
          align: 'center',
        },
        {
          title: '上报卷烟编码',
          key: 'acTwoLevelCigCode',
          align: 'center',
        },
        {
          title: '上报卷烟名称',
          key: 'acTwoLevelCigName',
        },
        {
          title: '含税调拨价(元/条)',
          key: 'acCgtTaxAllotPrice',
          align: 'right',
          width: 150,
        },
        {
          title: '上报数量',
          key: 'mc04CgtReturnBillSaleAreaReqQty',
          align: 'right',
          width: 100,
          isSwitchUnit: true,
        },
        {
          title: '确认数量',
          key: 'mc04CgtReturnBillSaleAreaAuditQty',
          align: 'right',
          width: 100,
          isSwitchUnit: true,
        },
        {
          title: '审核数量',
          key: 'mc04CgtReturnBillSaleCenterAuditQty',
          align: 'right',
          type: 'input',
          isSwitchUnit: true,
        },
      ]
    },
  },
  async created() {
    await this.initialize()
  },
  methods: {
    async initialize() {
      this.form.mc04CgtReturnBillDateRange = [beginDate, endDate]
    },
    async query() {
      if (!this.form.mc04CgtReturnBillDateRange) {
        this.$Message.error('请选择退货日期')
        return
      }
      this.tableLoading = true
      try {
        let queryData = {
          mc04CgtReturnBillDateRange: this.form.mc04CgtReturnBillDateRange,
        }
        if (this.form.baComOrgCode && this.form.baComOrgCode.length > 0) {
          queryData.baComOrgCode = this.form.baComOrgCode.map((item) => item.id).join(',')
        }
        const { data } = await queryRetBillCenterAuditListApi(queryData)
        this.tableData = data
        this.$refs.table.setUnitType('X')
        this.$refs.table.toggleUnitIfNeeded()
        await this.$nextTick()
        this.tableLoading = false
      } catch (e) {
        this.tableLoading = false
        this.$Message.error('系统异常，请联系管理员')
      }
    },
    reset() {
      this.form = { ...defaultForm }
      this.query()
    },
    changeTableSelect(selection) {
      this.selectTableDataIds = selection || []
    },
    async submitAudit() {
      if (this.selectTableDataIds.length < 1) {
        this.$Message.info('请选择至少一个退货申请单')
        return false
      }
      this.$Modal.confirm({
        title: '您是否确认提交？',
        onOk: async () => {
          try {
            let wzData = this.$refs.table.getWZData()
            let selectCodesSet = new Set(this.selectTableDataIds.map((item) => item.baComOrgCode))
            let wzSelectData = wzData.filter((item) => selectCodesSet.has(item.baComOrgCode))
            let request = wzSelectData.map((item) => ({
              baComOrgCode: item.baComOrgCode,
              mc04CgtReturnBillId: item.mc04CgtReturnBillId,
              mc04CgtReturnBillItemId: item.mc04CgtReturnBillItemId,
              mc04CgtReturnBillSaleCenterAuditQty: item.mc04CgtReturnBillSaleCenterAuditQty,
              mc04CgtReturnBillSaleAreaAuditQty: item.mc04CgtReturnBillSaleAreaAuditQty,
            }))
            if (!this.validateQty(request)) {
              // 校验失败，不继续执行
              return
            }
            this.tableLoading = true
            await centerBatchAuditApi(request)
            this.$Message.success('提交成功')
            this.tableLoading = false
            this.reset()
          } catch (e) {
            this.tableLoading = false
            return
          }
        },
        onCancel: () => {},
      })
    },
    validateQty(data) {
      for (let i = 0; i < data.length; i++) {
        const item = data[i]
        // 检查是否为有效数字
        if (
          item.mc04CgtReturnBillSaleCenterAuditQty !== undefined &&
          item.mc04CgtReturnBillSaleCenterAuditQty !== null &&
          item.mc04CgtReturnBillSaleCenterAuditQty !== ''
        ) {
          // 如果含有字母和特殊字符（除了数字、小数点、正负号）都直接判定无效
          const stringValue = String(item.mc04CgtReturnBillSaleCenterAuditQty).trim()
          const validNumberPattern = /^[+-]?\d*\.?\d+$/
          if (!validNumberPattern.test(stringValue)) {
            this.$Message.error(`第${i + 1}行的调整量包含无效字符，请输入正确的数值`)
            return false
          }
          const parsedValue = parseFloat(item.mc04CgtReturnBillSaleCenterAuditQty)
          if (isNaN(parsedValue)) {
            this.$Message.error(`第${i + 1}行的调整量不是有效数字，请输入正确的数值`)
            return false
          }
        }
      }
      return true
    },
  },
}
</script>

<style scoped lang="less"></style>
