<template>
  <LamboNPageView>
    <div class="button-container">
      <div class="left-content">
        <p class="title-text">生成合同</p>
      </div>
      <div class="right-content">
        <LamboNButton bizType="save" @click="contSplit()">单张拆分</LamboNButton>
      </div>
    </div>
    <hr class="divider" />
    <LamboNTable
      v-model="tableData"
      ref="table"
      :columns.sync="tableColumns"
      :defaultSortable="false"
      :disablePage="true"
      :loading="tableLoading"
      @on-select-change="changeTableSelect"
    >
      <template #search>
        <LamboNForm
          v-model="form"
          formType="search"
          :fieldList="fieldList"
          :searchLoading="tableLoading"
          @doSearch="query"
          @reset="reset"
        />
      </template>
      <template #options>
        <span>单位：万只</span>
      </template>
    </LamboNTable>
  </LamboNPageView>
</template>

<script>
import dayjs from 'dayjs'
import { queryContStartListApi } from '@/api/isale/order/ret/bill/contstart'
import { getComTreeApi } from '@/api/isale/agreement/basic/batchlock'

const beginDate = dayjs().subtract(7, 'day').format('YYYYMMDD')
const endDate = dayjs().format('YYYYMMDD')

const searchParamsDefault = {}
const defaultForm = {
  // 日期范围：今天之前7天，今天
  mc04CgtReturnBillDateRange: [beginDate, endDate],
}

export default {
  name: 'index',
  data() {
    return {
      selectTableDataIds: [],
      tableColumns: [
        {
          title: '选择',
          type: 'checkbox',
          key: 'baComOrgName',
          minWidth: 60,
          width: 60,
          flex: 1,
          fixed: 'left',
        },
        {
          title: '序号',
          key: 'id',
          type: 'index',
          minWidth: 80,
          width: 80,
          align: 'center',
          pinned: 'left',
        },
        {
          title: '公司',
          key: 'baComOrgName',
          minWidth: 80,
          width: 80,
          pinned: 'left',
          autoMergeRow: false,
        },
        {
          title: '备注',
          key: 'zaRemark',
          align: 'center',
        },
        {
          title: '合计',
          key: 'rebackInQtySum',
          align: 'center',
        },
      ],
      dataApi: queryContStartListApi,
      searchParams: {
        ...searchParamsDefault,
      },
      tableData: [],
      tableLoading: false,
      canSave: false,
      form: {
        ...defaultForm,
      },
      fieldList: [
        {
          //默认前后七天
          title: '退货日期',
          formKey: 'mc04CgtReturnBillDateRange',
          type: 'datepicker',
          maxDate: beginDate,
          props: { type: 'date-range' },
        },
        {
          title: '公司',
          formKey: 'baComOrgCode',
          type: 'treeselect',
          props: {
            // 'select-leaf-only': false,
            placeholder: '全部',
            showCheckbox: true,
            dataApi: getComTreeApi,
            urlParams: {
              treeType: 'Pro_City',
              comIsAgreeUnit: '1',
              comSceneCode: 'DEFAULT', // 数据权限
            },
          },
        },
      ],
    }
  },
  created() {
    this.query()
  },
  mounted() {
    // 在组件挂载后执行一次查询，确保UI已经准备好
    this.$nextTick(() => {
      this.query()
    })
  },
  watch: {
    // 监听路由变化
    $route(to, from) {
      // 可以根据实际需求添加判断条件
      // 比如：当从子组件路由返回时才刷新
      if (from.path.includes('/contSplit')) {
        this.query()
      }
    },
  },
  methods: {
    async query() {
      if (!this.form.mc04CgtReturnBillDateRange) {
        this.$Message.error('请选择退货日期')
        return
      }
      // 设置加载状态
      this.tableLoading = true
      try {
        // 准备查询参数
        let req = {}
        if (this.form && this.form.mc04CgtReturnBillDateStartTime) {
          ;(req.mc04CgtReturnBillDateStartTime = this.form.mc04CgtReturnBillDateRange[0]),
            (req.mc04CgtReturnBillDateEndTime = this.form.mc04CgtReturnBillDateRange[1])
        }
        if (this.form.baComOrgCode && this.form.baComOrgCode.length > 0) {
          req.baComOrgCode = this.form.baComOrgCode.map((item) => item.id).join(',')
        }
        // 发起API请求
        const response = await queryContStartListApi(req)
        const data = response.data || []
        // 重置动态列，只保留固定列
        this.tableColumns = this.tableColumns.filter((col) =>
          ['baComOrgName', 'id', 'baComOrgCode', 'zaRemark', 'rebackInQtySum'].includes(col.key),
        )

        // 如果没有数据，清空表格并结束加载
        if (!data || data.length === 0) {
          this.tableData = []
          this.tableLoading = false
          return
        }
        // 收集所有可能的动态列
        const dynamicColumns = new Map()
        data.forEach((datum) => {
          if (datum.mc04CgtReturnBillSpec && Array.isArray(datum.mc04CgtReturnBillSpec)) {
            datum.mc04CgtReturnBillSpec.forEach((item) => {
              if (item.acTwoLevelCigCode && !dynamicColumns.has(item.acTwoLevelCigCode)) {
                dynamicColumns.set(item.acTwoLevelCigCode, {
                  title: item.acTwoLevelCigName || item.acTwoLevelCigCode,
                  key: item.acTwoLevelCigCode,
                  align: 'center',
                })
              }
            })
          }
        })
        // 添加动态列到表格
        dynamicColumns.forEach((column) => {
          this.tableColumns.push(column)
        })

        // 处理每行数据，将动态列的值填充到对应的属性中
        const processedData = data.map((datum) => {
          const newDatum = { ...datum }

          // 初始化所有动态列的值为null（不显示）
          dynamicColumns.forEach((_, key) => {
            newDatum[key] = null
          })

          // 填充动态列的实际值
          if (newDatum.mc04CgtReturnBillSpec && Array.isArray(newDatum.mc04CgtReturnBillSpec)) {
            newDatum.mc04CgtReturnBillSpec.forEach((item) => {
              if (item.acTwoLevelCigCode && item.mc04CgtReturnBillSaleCenterAuditQty) {
                // 只有当值存在且不为0时才显示
                newDatum[item.acTwoLevelCigCode] = item.mc04CgtReturnBillSaleCenterAuditQty
              }
            })
          }
          return newDatum
        })
        // 更新表格数据
        this.tableData = processedData
      } catch (e) {
        console.error('查询数据出错:', e)
        this.$Message.error('系统异常，请联系管理员')
      } finally {
        // 无论成功或失败，都结束加载状态
        this.tableLoading = false
      }
    },
    contSplit() {
      if (this.selectTableDataIds.length !== 1) {
        this.$Message.info('请勾选一个退货申请单')
        return false
      }
      this.$router.push({
        path: '/isale/order/ret/bill/contstart/contSplit',
        query: { mc04CgtReturnBillId: this.selectTableDataIds[0].mc04CgtReturnBillId },
      })
    },
    reset() {
      this.form = { ...defaultForm }
      // 重置后重新查询数据，确保动态列也被重置
      this.query()
    },
    changeTableSelect(selection) {
      this.selectTableDataIds = selection || []
    },
  },
}
</script>

<style scoped lang="less">
.button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  margin-bottom: 10px;
}

.left-content {
  display: flex;
  align-items: center;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: #333;
}

.right-content {
  display: flex;
  align-items: center;
}

.divider {
  border: none;
  height: 1px;
  background-color: #e8e8e8;
  margin: 10px 0 20px 0;
}
</style>
