<template>
  <LamboNPageView>
    <template #leftContent>
      <LamboNTree
        ref="comTree"
        :data-api="treeApi"
        :url-params="treeParams"
        :statusName="statusName"
        :statusColor="statusColor"
        :statusDataApi="statusDataApi"
        :statusUrlParams="form"
        :isShowHeader="false"
        :is-show-search="true"
        select-leaf-only
        select-first-leaf
        expandAll
        @on-load="onTreeLoad"
        @on-select-change="selectCom"
      />
    </template>
    <div class="main">
      <LamboNTable
        class="table"
        ref="table"
        :columns="tableColumns"
        v-model="tableData"
        disablePage
        :showTableOption="true"
        :loading="loading"
        :rowBuffer="10000"
        defaultUnitType="WZ"
        defaultTableUnitType="WZ"
      >
        <template #search>
          <LamboNForm
            v-model="form"
            formType="search"
            :fieldList="fieldList"
            :searchLoading="loading"
            :resetBtn="false"
            @doSearch="query"
          />
        </template>
      </LamboNTable>
      <Input
        v-model="zaRemark"
        type="textarea"
        placeholder="请输入内容"
        :maxlength="500"
        show-word-limit
        :disabled="!canSave"
      />
    </div>
    <template v-if="canSave" #footer>
      <LamboNButtonGroup>
        <LamboNButton bizType="save" :loading="loading" @click="save">保存</LamboNButton>
        <LamboNButton bizType="submit" :loading="loading" @click="submit">提交</LamboNButton>
      </LamboNButtonGroup>
    </template>
  </LamboNPageView>
</template>

<script>
import { getBusiComTree } from '@/api/isale/common/index'
import dayjs from 'dayjs'
import { datas2ToRows, rowsToDatas2, getMonthPlanAdjCols, validateQty } from '../utils/adj-utils'
import {
  queryMonthPlanAdjReport,
  queryOrgStatus,
  saveMonthPlanAdjReport,
} from '@/api/isale/plan/month-plan/month-plan-adj'
import { queryStockSaleRadio } from '@/api/isale/plan/month-plan/month-plan-submit'
import { getTotalSumIndex } from '@/api/isale/plan/yearplan/salefo/index'
import { calculateSumRows } from '../../common/calculateSum.js'
import _ from 'lodash'

const monthCode = dayjs().format('YYYYMM')
const stepCode = '10'
const deafultForm = {
  cgtType: '0',
  ma02PlanMonth: monthCode,
  baComOrgCodes: '',
  mc04MonthSalePlanStatus: stepCode,
}
const nonQtyColKeys = [
  null,
  undefined,
  '',
  'brandName',
  'acTwoLevelCigCode',
  'acTwoLevelCigName',
  'acMateTaxTranPr',
  'wholeSalePrice',
  'mf04ApprovalInfo',
]

export default {
  data() {
    return {
      saveLoading: false,
      canSave: true,
      treeApi: getBusiComTree,
      statusDataApi: queryOrgStatus,
      statusName: {
        10: '保存',
        11: '驳回',
        20: '待区域确认',
        30: '待品牌部审核',
        40: '待计划部审核',
        80: '待发布',
        90: '已发布',
      },
      statusColor: {
        10: 'red',
        11: 'red',
        20: 'green',
        30: 'green',
        40: 'green',
        80: 'green',
        90: 'green',
      },
      treeParams: {
        comIsAgreeUnit: '1',
        // comSceneCode: 'DEFAULT',
        treeType: 'Pro_City',
      },
      tableLoading: false,
      loading: false,
      form: {
        ...deafultForm,
      },
      queryParams: {
        ...deafultForm,
      },
      baseTableData: [],
      zaRemark: '',
      fieldList: [
        {
          title: '计划月份',
          type: 'datepicker',
          formKey: 'ma02PlanMonth',
          props: { type: 'month', clearable: false, required: true },
        },
      ],
      dictDatas: {},
      sumIndex: [],
      stockSaleRadio: {},
      statusCode: '',
    }
  },
  computed: {
    tableColumns() {
      const planMonth = dayjs(`${this.queryParams.ma02PlanMonth}`, 'YYYYMM')
      const halfYearName = planMonth.format('YYYY年') + (planMonth.month() < 6 ? '上' : '下')
      const prevHalfYearMonth = planMonth.add(-6, 'month')
      const prevHalfYearName =
        prevHalfYearMonth.format('YYYY年') + (prevHalfYearMonth.month() < 6 ? '上' : '下')
      const quarterName = `第${Math.ceil(planMonth.month() / 3 + 1)}季度`
      let cols = [
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 60,
          fixed: 'left',
        },
        {
          title: '品牌名称',
          field: 'brandName',
          align: 'center',
          minWidth: 120,
          width: 120,
          fixed: 'left',
          autoMergeRow: true,
        },
        {
          title: '卷烟编码',
          field: 'acTwoLevelCigCode',
          align: 'center',
          width: 100,
          fixed: 'left',
        },
        {
          title: '卷烟名称',
          field: 'acTwoLevelCigName',
          align: 'left',
          minWidth: 180,
          width: 180,
          fixed: 'left',
        },
        {
          title: '含税调拨价(元/条)',
          field: 'acMateTaxTranPr',
          width: 100,
          align: 'center',
        },
        {
          title: '统一批发价(元/条)',
          field: 'wholeSalePrice',
          width: 100,
          align: 'center',
        },
      ]
      if (['1', '7'].includes(planMonth.format('M'))) {
        cols.push(
          {
            title: `半年协议(${prevHalfYearName})`,
            field: 'lastSelfYearQty',
            width: 100,
            align: 'right',
          },
          {
            title: `剩余协议(${prevHalfYearName})`,
            field: 'lastSelfYearRemainQty',
            width: 100,
            align: 'right',
          },
        )
      }
      cols.push(
        {
          title: `半年协议(${halfYearName})`,
          field: 'nowSelfYearQty',
          width: 100,
          align: 'right',
        },
        {
          title: `剩余协议(${halfYearName})`,
          field: 'nowSelfYearRemainQty',
          width: 100,
          align: 'right',
        },
        {
          title: '全年销售计划',
          children: [
            {
              title: '目标量',
              field: 'mc04CgtPlanYSaleQty',
              width: 100,
              align: 'right',
            },
            {
              title: '实际销量',
              field: 'mc04CgtPlanYSaleActualQty',
              width: 100,
              align: 'right',
            },
            {
              title: '剩余销量',
              field: 'mc04CgtPlanYSaleRemainQty',
              width: 100,
              align: 'right',
            },
            {
              title: '收入目标',
              field: 'mc04CgtPlanYSaleIncome',
              width: 100,
              align: 'right',
            },
            {
              title: '实际收入',
              field: 'mc04CgtPlanYSaleActualIncome',
              width: 100,
              align: 'right',
            },
            {
              title: '剩余收入',
              field: 'mc04CgtPlanYSaleRemainIncome',
              width: 100,
              align: 'right',
            },
          ],
        },
        {
          title: `季度销售计划`,
          children: [
            {
              title: `${quarterName}目标量`,
              field: 'mc04CgtPlanQSaleQty',
              width: 100,
              align: 'right',
            },
            {
              title: `${quarterName}实际销量`,
              field: 'mc04CgtPlanQSaleActualQty',
              width: 100,
              align: 'right',
            },
            {
              title: `${quarterName}剩余销量`,
              field: 'mc04CgtPlanQSaleRemainQty',
              width: 100,
              align: 'right',
            },
          ],
        },
      )

      cols.push(...getMonthPlanAdjCols(planMonth.format('YYYYMM'), stepCode, this))
      if (this.statusCode < '20') {
        cols.push({
          title: '驳回意见',
          field: 'mf04ApprovalInfo',
          width: 100,
          align: 'left',
          autoMergeRow: true,
        })
      }
      const setUnitSwitch = (columns) => {
        columns.forEach((column) =>
          column.children
            ? setUnitSwitch(column.children)
            : nonQtyColKeys.includes(column.field)
            ? ''
            : (column.isSwitchUnit = true),
        )
      }
      setUnitSwitch(cols)
      return cols
    },
    sumTableData() {
      if (!this.baseTableData.length) {
        return []
      }
      const getFields = (columns) => {
        return columns.map((column) =>
          column.children ? getFields(column.children) : column.field,
        )
      }
      const sumFields = getFields(this.tableColumns)
        .flat(Infinity)
        .filter((field) => !nonQtyColKeys.includes(field))
      return calculateSumRows(this.baseTableData, sumFields, this.sumIndex, 'acTwoLevelCigName')
    },
    tableData: {
      get() {
        return [...this.sumTableData, ...this.baseTableData]
      },
      set(val) {
        this.baseTableData = val.filter((row) => !row.isSumRow)
      },
    },
  },
  created() {
    this.form.cgtType = this.$route.query.cgtType
    this.getTotalSumIndex()
  },
  methods: {
    async getTotalSumIndex() {
      const { data } = await getTotalSumIndex({ mz12InfoSystemResourceCode: 'monthSalePlan' })
      this.sumIndex = data
    },
    async query() {
      this.$refs.comTree.refreshStatus()
      try {
        this.loading = true
        this.queryParams = _.cloneDeep(this.form)
        const { data } = await queryMonthPlanAdjReport(this.queryParams)
        this.canSave = data.isSubmit
        this.baseTableData = datas2ToRows(
          this.queryParams.ma02PlanMonth,
          '10',
          data.monthPlanAdjList || [],
          this,
        )
        this.$refs.table.toggleUnitIfNeeded()
        this.statusCode = this.baseTableData[0]?.mc04MonthSalePlanStatus
        this.zaRemark = this.baseTableData[0]?.zaRemark
        this.loading = false
      } catch (e) {
        this.loading = false
        this.$Message.error('系统异常，请联系管理员')
        console.log('error: ', e)
      }
    },
    async getStockSaleRadio() {
      try {
        this.stockSaleRadio = (await queryStockSaleRadio(this.queryParams)).data
      } catch (error) {
        console.log('error: ', error)
      }
    },
    selectCom(nodes) {
      console.log('node:come in ', nodes)
      if (nodes.length) {
        this.form.baComOrgCodes = nodes[0].id
        this.query()
        this.getStockSaleRadio()
      }
    },
    save() {
      this.saveOrSubmit('0')
    },
    submit() {
      this.saveOrSubmit('1')
    },
    async saveOrSubmit(submitStatus) {
      try {
        const saveData = this.$refs.table.getWZData().filter((row) => !row.isSumRow)
        if (saveData.length) {
          const errMsg = validateQty(this.queryParams.ma02PlanMonth, stepCode, saveData)
          if (errMsg) {
            this.$Message.warning(errMsg)
            return
          }
          this.loading = true
          await saveMonthPlanAdjReport({
            monthPlanAdjList: rowsToDatas2(this.queryParams.ma02PlanMonth, saveData).map((row) => ({
              ...row,
              ma02TobaProdTradeTypeCode: this.queryParams.cgtType,
              zaRemark: this.zaRemark,
            })),
            submitStatus,
          })
          this.$Message.success(submitStatus === '0' ? '保存成功' : '提交成功')
          this.dataStatus === '1' ? (this.canSave = false) : ''
          this.loading = false
          this.query()
        }
      } catch (e) {
        console.log('error: ', e)
        this.$Message.error('系统异常，请联系管理员')
        this.loading = false
      }
    },
  },
}
</script>
<style lang="less" scoped>
.main {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  > .table {
    flex: 1;
    width: 100%;
  }
}
</style>
