<template>
  <LamboNPageView :isShowBackBtn="true">
    <div>
      <span>主题名称：{{ subjectName }}</span> &nbsp;&nbsp;&nbsp;&nbsp;
      <span>填报时间限制：{{ subjectDate }}</span>
    </div>
    <LamboNLoadingPanel :loading="loading">
      <LamboNForm
        ref="basicForm"
        formType="form"
        class="ivu-biz-form-row"
        v-model="form"
        :fieldList="fieldList"
        :gridColumns="4"
      />
      <LamboNTable
        ref="table"
        :columns="tableColumns"
        v-model="tableData"
        :disablePage="true"
        :loading="loading"
        @on-select-change="changeTableSelect"
      >
        <template #options><span>单位:万支</span></template>
      </LamboNTable>
      <Input
        v-model="approvalInfo"
        type="textarea"
        placeholder="请输入驳回意见"
        :maxlength="500"
        show-word-limit
      />
    </LamboNLoadingPanel>
    <template v-if="canSave" #footer>
      <LamboNButtonGroup>
        <LamboNButton bizType="add" :loading="loading" @click="reject">驳回</LamboNButton>
        <LamboNButton bizType="add" :loading="loading" @click="submit">提交</LamboNButton>
      </LamboNButtonGroup>
    </template>
  </LamboNPageView>
</template>
<script>
import {
  // eslint-disable-next-line no-unused-vars
  itemListBySalePlanId,
  getTotalSumIndex,
  auditPlan,
} from '@/api/isale/plan/yearplan/yearplanallotcenter'
import dayjs from 'dayjs'
import { calculateSumRows } from '@/view/isale/plan/common/calculateSum'
// eslint-disable-next-line no-unused-vars
import { getBusiComTree, getProductTreeNew } from '@/api/isale/common'

// 定义需要合计的字段
const commonSumHead = [
  'acCgtTaxAllotPrice',
  'acCgtTradePrice',
  'md03Cgt10thComInitStkQty',
  'mc04CgtSalePlanAdjustedQty',
  'ma02CgtPlAdjustedQty',
  'md03Cgt10thComEndStkQty',
]

export default {
  data() {
    return {
      loading: false,
      fieldList: [
        {
          title: '卷烟',
          formKey: 'acCgtCartonCode',
          type: 'treeselect',
          props: {
            placeholder: '全部',
            'select-leaf-only': false,
            showCheckbox: false,
            dataApi: getProductTreeNew,
            urlParams: {
              boxType: 'Icom_Brand_Product',
              isUse: '1',
              productIsShow: '0',
              providerCustomerId: '20420001',
            },
          },
        },
        {
          title: '公司',
          formKey: 'baComOrgCode',
          type: 'treeselect',
          props: {
            'select-leaf-only': false,
            placeholder: '全国',
            showCheckbox: true,
            dataApi: getBusiComTree,
            urlParams: {
              treeType: 'Pro',
              comIsAgreeUnit: '1',
              comIsImported: '1',
              comSceneCode: 'DEFAULT', // 数据权限
            },
          },
        },
      ],
      form: {},
      defaultTransTypeCode: '',
      submitLoading: false,
      oldTableData: [],
      tableData: [],
      headerList: [],
      year: '',
      provinceList: [], // 存储省份列表
      ycSalePlan: {},
      mc04SalePlanId: '',
      subjectName: '',
      subjectDate: '',
      approvalInfo: '',
      canSave: true,
      mc04OrgTypeCode: '',
      acTwoLevelCigCode: '',
      acLevel: '',
      zaOccurrenceYear: '',
      sumIndex: [], // 添加sumIndex用于合计计算
    }
  },
  created() {
    let formatBeginDate = dayjs(this.$route.query.mc04PlanSubjectBeginDate).format('YYYY-MM-DD')
    let formatEndDate = dayjs(this.$route.query.mc04PlanSubjectEndDate).format('YYYY-MM-DD')
    console.log('query计划分解中心-明细', this.$route.query)
    this.mc04SalePlanId = this.$route.query.mc04SalePlanId
    this.form.mc04PlanSubjectName = this.$route.query.mc04PlanSubjectName
    this.subjectName = this.$route.query.mc04PlanSubjectName
    this.form.mc04PlanSubjectDate = formatBeginDate + ' 至 ' + formatEndDate
    this.subjectDate = formatBeginDate + ' 至 ' + formatEndDate
    this.zaOccurrenceYear = this.$route.query.zaOccurrenceYear
    if (this.mc04SalePlanId) {
      this.getTotalSumIndex().then(() => {
        this.initData()
      })
    } else {
      this.$Message.error('获取数据失败，请联系管理员')
    }
  },
  watch: {
    'form.baComOrgCode'(newVal) {
      if (newVal && newVal.length > 0) {
        //遍历newVal，去除全国即id=ALL，用都好拼接id作为参数进行表头和数据的查询
        this.mc04OrgTypeCode = newVal
          .filter((item) => item.id !== 'ALL')
          .map((item) => item.id)
          .join(',')
        this.initData()
      }
    },
    //acCgtCartonCode
    'form.acCgtCartonCode'(newVal) {
      if (newVal && newVal.length > 0) {
        console.log(newVal, 'form.acCgtCartonCode')
        //遍历newVal，去除全国即id=ALL，用都好拼接id作为参数进行表头和数据的查询
        this.acTwoLevelCigCode = newVal[0].id
        this.acLevel = newVal[0].level
        this.initData()
      }
    },
  },
  // 计算属性
  computed: {
    tableColumns() {
      // 添加动态列
      const dynamicColumns =
        this.headerList?.map((header) => ({
          title: header.title,
          align: 'center',
          children: header.children?.map((child) => ({
            title: child.title,
            key: child.field,
            width: 150,
            align: 'right',
            type: child.type,
            isSwitchUnit: false,
            cellRendererParams: {
              props: {
                number: true,
                type: 'number',
                textCondition: (row) => row.isSumRow === true,
              },
            },
          })),
        })) || []
      return [
        {
          title: '序号',
          type: 'index',
          minWidth: 180,
          align: 'center',
          flex: 1,
          pinned: 'left',
        },
        {
          title: '规格',
          type: 'acCgtName',
          key: 'acCgtName',
          minWidth: 180,
          align: 'center',
          flex: 1,
          pinned: 'left',
        },
        {
          title: '调拨价(元/标准条)',
          key: 'acCgtTaxAllotPrice',
          minWidth: 170,
          align: 'right',
          hide: true,
          flex: 1,
        },
        {
          title: '批发价(元/标准条)',
          key: 'acCgtTradePrice',
          minWidth: 170,
          align: 'right',
          hide: true,
          flex: 1,
        },
        ...dynamicColumns,
      ]
    },
  },
  //getYearPlanDetail
  methods: {
    // 获取需要合计的字段列表
    getSumFields(headerList) {
      return [
        ...(headerList?.flatMap((header) => header.children?.map((child) => child.field)) || []),
        ...commonSumHead,
      ]
    },

    async initData() {
      this.loading = true
      if (!this.mc04SalePlanId) {
        return
      }
      try {
        const { data } = await itemListBySalePlanId({
          mc04SalePlanId: this.mc04SalePlanId,
          zaOccurrenceYear: this.zaOccurrenceYear,
          acTwoLevelCigCode: this.acTwoLevelCigCode,
          acLevel: this.acLevel,
          baComOrgCode: this.mc04OrgTypeCode,
        })
        if (data) {
          console.log(data, 'data')
        }

        let tableData = data.salePlanDetailList ? data.salePlanDetailList : []
        this.headerList = data.headerList

        // 计算合计行
        const sumFields = this.getSumFields(data.headerList)
        const sumData = calculateSumRows(tableData, sumFields, this.sumIndex)

        // 将合计行放在数据最前面
        this.tableData = [...sumData, ...tableData]

        console.log(this.headerList, 'headerList')
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },

    getTotalSumIndex() {
      return new Promise((resolve) => {
        getTotalSumIndex({ mz12InfoSystemResourceCode: 'yearplanCenter' }).then(({ data }) => {
          this.sumIndex = data
          resolve()
        })
      })
    },

    changeTableSelect(selection) {
      this.selectTableDataIds = selection?.map((item) => item) || []
    },

    //点击【驳回】按钮，可根据需要填写审核意见，计划状态改成"待分解到省级区域"。
    // 点击【提交】按钮，审核通过，计划状态改成"待征求销区意见及审议"
    reject() {
      this.saveOrSubmit('21')
    },

    submit() {
      this.saveOrSubmit('22')
    },

    async saveOrSubmit(status) {
      this.loading = true
      try {
        const params = {
          mc04SalePlanId: this.mc04SalePlanId,
          mc04SalePlanStatus: status,
          baComOrgCode: this.mc04OrgTypeCode,
          approvalInfo: this.approvalInfo,
        }
        auditPlan(params).then(() => {
          this.$Message.success(status === '21' ? '驳回成功' : '提交成功')
          this.$router.go(-1)
        })
        // 这里可以根据需要添加页面跳转或其他操作
      } catch (e) {
        console.log('error: ', e)
        this.$Message.error('操作失败: ' + (e.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
  },
}
</script>
<style lang="less" scoped>
.main {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  > .table {
    flex: 1;
    width: 100%;
  }
}
/deep/ .ind-form-wrap {
  width: 100% !important;
}

.footer-box {
  width: 100%;
}

.btn-group {
  text-align: center;
  width: 100% !important;
}

/deep/ .ind-yellows {
  font-weight: 500;
  color: #d3d630 !important;
}

.footer-box {
  width: 100%;
}
</style>
