<template>
  <LamboNPageView>
    <template #leftContent>
      <LamboNTree
        ref="comTree"
        :data-api="treeApi"
        :url-params="treeParams"
        :statusName="statusName"
        :statusColor="statusColor"
        :statusDataApi="statusDataApi"
        :statusUrlParams="statusUrlParams"
        @on-load="tableLoading = false"
        :isShowHeader="false"
        :is-show-search="false"
        expandAll
        show-checkbox
        :select-leaf-only="false"
        select-first-leaf
        @on-check-change="changeTreeNode"
      ></LamboNTree>
    </template>
    <LamboNForm
      v-model="form"
      formType="search"
      :fieldList="fieldList"
      :searchLoading="tableLoading"
      @doSearch="query"
      @reset="reset"
    />
    <LamboNTable
      v-model="tableData"
      ref="table"
      :columns.sync="tableColumns"
      :defaultSortable="false"
      defaultUnitType="WZ"
      defaultTableUnitType="X"
      :isShowUnitSwitch="false"
      :loading="tableLoading"
      disablePage
    >
      <template #options>
        <span>单位：箱</span>
      </template>
    </LamboNTable>
    <div style="display: flex; justify-content: center" v-if="form.mc04DemandFoStatus === '20'">
      <LamboNButtonGroup>
        <LamboNButton bizType="save" @click="save" :disabled="tableLoading">保存</LamboNButton>
        <LamboNButton bizType="save" @click="submit" :disabled="tableLoading">提交</LamboNButton>
        <LamboNButton bizType="cancel" @click="cancel" :disabled="tableLoading">驳回</LamboNButton>
      </LamboNButtonGroup>
    </div>
  </LamboNPageView>
</template>
<script>
import { renderFieldEnums } from '@indfnd/common-pro'
import { getBusiComTree, getProductTreeNew } from '@/api/isale/common'
import {
  demandFoAsDic,
  getDemandFoTreeStatusApi,
  getMostRecentSubjectYear,
} from '@/api/isale/plan/yearplan/demandfo'
import {
  getDemandFoReviewDataAdjustment,
  getDemandFoReviewDataPreparation,
  reviewCancel,
  savePreparation,
  submitPreparation,
} from '@/api/isale/plan/yearplan/demandFoReview'
import { calculateSumRows } from '@/view/isale/plan/common/calculateSum'
import { getTotalSumIndex } from '@/api/isale/plan/ycplan/ycplanformulate'
export default {
  name: 'edit',
  watch: {
    'form.zaOccurrenceYear': {
      handler() {
        this.getDemandFoDicByYear()
      },
    },
    'form.mc04PlanSubjectName': {
      handler(newValue) {
        if (newValue.includes('编制')) {
          this.tableColumns = this.preparationAndCorrectionColumns
          this.demandFoSubjectType = 'Preparation'
        } else if (newValue.includes('调整')) {
          this.tableColumns = this.adjustmentColumns
          this.demandFoSubjectType = 'Adjustment'
        } else if (newValue.includes('修正')) {
          this.tableColumns = this.preparationAndCorrectionColumns
          this.demandFoSubjectType = 'Correction'
        }
      },
    },
  },
  computed: {
    preparationAndCorrectionColumns() {
      const nowYear = parseInt(this.form.zaOccurrenceYear) - 1
      return [
        {
          title: '公司',
          key: 'baComOrgName',
          width: 120,
          align: 'center',
          fixed: 'left',
          autoMergeRow: true,
        },
        {
          title: '规格',
          key: 'acCgtName',
          width: 220,
          pinned: 'left',
        },
        {
          title: '含税调拨价(元/标准条)',
          key: 'acCgtTaxAllotPrice',
          width: 100,
          hide: true,
        },
        {
          title: '统一批发价(元/标准条)',
          key: 'acCgtTradePrice',
          width: 100,
          hide: true,
        },
        {
          title: nowYear - 2 + '年',
          key: 'twoYearsAgo',
          hide: true,
          align: 'center',
          width: 100,
          flex: 1,
          children: [
            {
              title: '全年实际销量',
              key: 'twoYearsAgoActualSales',
              align: 'right',
              hide: true,
            },
            {
              title: '元春实际销量',
              key: 'twoYearsAgoYcActualSales',
              align: 'right',
              hide: true,
            },
          ],
        },
        {
          title: nowYear - 1 + '年',
          key: 'lastYear',
          hide: true,
          align: 'center',
          width: 100,
          flex: 1,
          children: [
            {
              title: '全年实际销量',
              key: 'lastYearActualSales',
              align: 'right',
              hide: true,
            },
            {
              title: '元春实际销量',
              key: 'lastYearYcActualSales',
              align: 'right',
              hide: true,
            },
          ],
        },
        {
          title: nowYear + '年',
          key: 'nowYearSelf',
          align: 'center',
          width: 100,
          flex: 1,
          children: [
            {
              title: '全年销售计划',
              key: 'nowYearSalesPlan',
              align: 'right',
              hide: true,
            },
            {
              title: '元春销售',
              key: 'nowYearYcSalesPlan',
              align: 'right',
              hide: true,
            },
            {
              title: '全年销售增幅(%)',
              key: 'nowYearSalesGrowth',
              align: 'right',
              hide: true,
            },
            {
              title: '元春销售增幅(%)',
              key: 'nowYearYcSalesGrowth',
              align: 'right',
              hide: true,
            },
          ],
        },
        {
          title: nowYear + 1 + '年市场需求预测',
          key: 'nextYear',
          align: 'center',
          type: 'datepicker',
          props: {
            type: 'date',
          },
          flex: 1,
          children: [
            {
              title: '推荐量', //推荐量取近三年的平均值
              key: 'mc04DemandFoRecomQty',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
            },
            {
              title: '上报量',
              key: 'mc04DemandFoReportQty',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
            },
            {
              title: '区域确认量',
              key: 'mc04DemandFoConfirmQty',
              type: 'input',
              headerClass: 'ind-red',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
              cellRendererParams: {
                props: {
                  number: true,
                  type: 'number',
                  textCondition: (row) => {
                    return !!row.isSumRow || !!row.isProvinceSumRow
                  },
                },
              },
            },
          ],
        },
        {
          title: nowYear + 1 + '年元春市场需求预测',
          key: 'nextYearYc',
          align: 'center',
          flex: 1,
          children: [
            {
              title: '推荐量', //推荐量取近三年的平均值
              key: 'ycMc04DemandFoRecomQty',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
            },
            {
              title: '上报量', //元春上报量自动根据1月、2月上报量进行合计
              key: 'ycMc04DemandFoReportQty',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
            },
            {
              title: '区域确认量',
              key: 'ycMc04DemandFoConfirm',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
              calcRule: 'januaryPredictionConfirm+februaryPredictionConfirm',
            },
            {
              title: '上报全年占比(%)',
              key: 'ycDemandFoReportProportion',
              align: 'right',
              width: 120,
              calcRule: '(ycMc04DemandFoReportQty/mc04DemandFoReportQty)*100',
            },
            {
              title: '确认全年占比(%)',
              key: 'ycDemandFoConfirmProportion',
              align: 'right',
              width: 120,
              calcRule:
                '((januaryPredictionConfirm+februaryPredictionConfirm)/mc04DemandFoConfirmQty)*100',
            },
          ],
        },
        {
          title: '1月市场需求预测',
          key: 'januaryPrediction',
          align: 'center',
          flex: 1,
          children: [
            {
              title: '推荐量', //推荐量计算，1-2月根据前三年数据测算1月，2月占元春的比重，将元春推荐量分解到1月，2月，需要考虑1月过年，2月多年的差异
              key: 'januaryPredictionRecom',
              align: 'right',
              isSwitchUnit: true,
              width: 100,
            },
            {
              title: '上报量',
              key: 'januaryPredictionReport',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
            },
            {
              title: '区域确认量',
              key: 'januaryPredictionConfirm',
              type: 'input',
              headerClass: 'ind-red',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
              cellRendererParams: {
                props: {
                  number: true,
                  type: 'number',
                  textCondition: (row) => {
                    return !!row.isSumRow || !!row.isProvinceSumRow
                  },
                },
              },
            },
          ],
        },
        {
          title: '2月市场需求预测',
          key: 'februaryPrediction',
          align: 'center',
          flex: 1,
          children: [
            {
              title: '推荐量', //推荐量计算，1-2月根据前三年数据测算1月，2月占元春的比重，将元春推荐量分解到1月，2月，需要考虑1月过年，2月多年的差异
              key: 'februaryPredictionRecom',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
            },
            {
              title: '上报量',
              key: 'februaryPredictionReport',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
            },
            {
              title: '区域确认量',
              key: 'februaryPredictionConfirm',
              type: 'input',
              headerClass: 'ind-red',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
              cellRendererParams: {
                props: {
                  number: true,
                  type: 'number',
                  textCondition: (row) => {
                    return !!row.isSumRow || !!row.isProvinceSumRow
                  },
                },
              },
            },
          ],
        },
      ]
    },
    adjustmentColumns() {
      const nowYear = parseInt(this.form.zaOccurrenceYear) - 1
      return [
        {
          title: '公司',
          key: 'baComOrgName',
          width: 120,
          align: 'center',
          fixed: 'left',
          autoMergeRow: true,
        },
        {
          title: '规格',
          key: 'acCgtName',
          width: 220,
          pinned: 'left',
        },
        {
          title: '含税调拨价(元/标准条)',
          key: 'acCgtTaxAllotPrice',
          width: 100,
        },
        {
          title: '统一批发价(元/标准条)',
          key: 'acCgtTradePrice',
          width: 100,
        },
        {
          title: nowYear - 3 + '年销量',
          key: 'threeYearsAgoActualSales',
          hide: false,
          align: 'center',
        },
        {
          title: nowYear - 2 + '年销量',
          key: 'twoYearsAgoActualSales',
          hide: false,
          align: 'center',
        },
        {
          title: nowYear - 1 + '年销量',
          key: 'lastYearActualSales',
          hide: false,
          align: 'center',
        },
        {
          title: nowYear + '年',
          key: 'nowYearSelf',
          align: 'center',
          flex: 1,
          children: [
            {
              title: '累计销量',
              key: 'nowYearAccrued',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
            },
            {
              title: '当月',
              key: 'nowYearThisMonth',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
            },
            {
              title: '进度(%)',
              key: 'nowYearProgress',
              align: 'right',
              width: 100,
            },
          ],
        },
        {
          title: nowYear + 1 + '年',
          key: 'nowYearSelf',
          align: 'center',
          flex: 1,
          children: [
            {
              title: '推荐量', //推荐量取近三年的平均值
              key: 'mc04DemandFoRecomQty',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
            },
            {
              title: '原预测量',
              key: 'originReportQtyAdjustment',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
            },
            {
              title: '调整后预测量',
              key: 'mc04DemandFoReportQtyAdjustment',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
            },
            {
              title: '确认预测量',
              key: 'mc04DemandFoConfirmQty',
              type: 'input',
              headerClass: 'ind-red',
              align: 'right',
              width: 100,
              isSwitchUnit: true,
              cellRendererParams: {
                props: {
                  number: true,
                  type: 'number',
                  textCondition: (row) => {
                    return !!row.isSumRow || !!row.isProvinceSumRow
                  },
                },
              },
            },
          ],
        },
      ]
    },
  },
  created() {
    getMostRecentSubjectYear().then((res) => {
      this.mostRecentSubjectYear = res.data
      this.form.zaOccurrenceYear = this.mostRecentSubjectYear
    })
    //处理查询form 的 状态
    let demandFoStatusDic = [
      { K: '10', V: '已保存' },
      { K: '20', V: '待审核' },
      { K: '90', V: '已审核' },
    ]
    this.fieldList = renderFieldEnums(this.fieldList, {
      mc04DemandFoStatus: demandFoStatusDic,
    })
    this.$set(this.form, 'mc04DemandFoStatus', demandFoStatusDic[1].K)
    //合计列接口
    this.getTotalSumIndex()
  },
  data() {
    const defaultNowYear = new Date().getFullYear()
    return {
      tableLoading: false,
      treeApi: getBusiComTree,
      treeParams: {
        comIsAgreeUnit: '1',
        comSceneCode: 'DEFAULT',
        treeType: 'Pro_City',
      },
      statusDataApi: getDemandFoTreeStatusApi,
      statusUrlParams: {
        zaOccurrenceYear: '2026',
        mc04PlanSubjectName: '',
      },
      statusName: {
        0: '未保存',
        10: '已保存',
        20: '待审核',
        90: '已审核',
      },
      statusColor: {
        0: 'yellow',
        10: 'blue',
        20: 'red',
        90: 'green',
      },
      selectTreeNode: {},
      selectTreeNodeId: [], //选中的全部公司id []
      baComOrgCode: '11420101', //选中的全部公司idString
      newBaComOrgCode: '11420101', //当前的的某个公司
      //通用：form
      sumIndex: [],
      form: {
        zaOccurrenceYear: defaultNowYear,
        baComOrgCode: '11420101',
        mc04PlanSubjectName: '',
        mc04DemandFoStatus: '',
        makeDate: '',
        acCgtCartonCode: [],
      },
      fieldList: [
        {
          title: '年度',
          formKey: 'zaOccurrenceYear',
          type: 'datepicker',
          props: {
            type: 'year',
            required: true,
          },
          required: true,
        },
        {
          title: '填报主题',
          type: 'select',
          enumList: [],
          formKey: 'mc04PlanSubjectName',
          required: true,
        },
        {
          title: '审核状态',
          type: 'select',
          enumList: [],
          formKey: 'mc04DemandFoStatus',
          required: false,
        },
        {
          title: '填报时间',
          formKey: 'makeDate',
          type: 'datepicker',
          props: {
            type: 'date-range',
            clearable: false,
          },
        },
        {
          title: '卷烟',
          formKey: 'acCgtCartonCode',
          type: 'treeselect',
          props: {
            placeholder: '全部',
            showCheckbox: true,
            dataApi: getProductTreeNew,
            urlParams: {
              boxType: 'Icom_Brand_Product',
              isUse: '1',
              productIsShow: '0',
              providerCustomerId: '20420001',
            },
          },
        },
      ],
      demandFoSubjectType: '',
      demandFos: [],
      //table
      tableData: [],
      trueTableData: [],
      tableColumns: [],
    }
  },
  methods: {
    changeTreeNode(node) {
      this.selectTreeNode = node
      this.selectTreeNodeId = []
      if (node.length) {
        node.forEach((item) => {
          this.selectTreeNodeId.push(item.id)
        })
      }
      this.baComOrgCode = this.selectTreeNodeId.join(',')
      this.newBaComOrgCode = node[0].id
      //为form里的baComOrgCode赋值
      this.$set(this.form, 'baComOrgCode', this.baComOrgCode)
      //业务：去查填报主题字典
      this.getDemandFoDicByYear()
    },
    async query() {
      this.setTreeStatusParams()
      this.tableLoading = true
      this.tableData = []
      try {
        //制作查询条件
        let query = {
          ...this.form,
        }
        let makeData = query.makeDate
        if (makeData.length === 2) {
          query.makeDate = makeData[0] + '-' + makeData[1]
        }
        query.acCgtCartonCode = query.acCgtCartonCode.map((item) => item.id).join(',')
        //发起请求
        let res
        if (
          this.demandFoSubjectType === 'Preparation' ||
          this.demandFoSubjectType === 'Correction'
        ) {
          res = await getDemandFoReviewDataPreparation(query)
        } else if (this.demandFoSubjectType === 'Adjustment') {
          res = await getDemandFoReviewDataAdjustment(query)
        }
        let data = res.data
        this.demandFos = data.demandFos
        //制作合计列
        this.trueTableData = data.tablePreparation
        if (this.trueTableData.length > 0) {
          const sumFields = [
            'twoYearsAgoActualSales',
            'twoYearsAgoYcActualSales',
            'lastYearActualSales',
            'lastYearYcActualSales',
            'nowYearSalesPlan',
            'nowYearYcSalesPlan',
            'mc04DemandFoRecomQty',
            'mc04DemandFoReportQty',
            'mc04DemandFoConfirmQty',
            'ycMc04DemandFoConfirm',
            'ycMc04DemandFoRecomQty',
            'ycMc04DemandFoReportQty',
            'januaryPredictionRecom',
            'januaryPredictionReport',
            'januaryPredictionConfirm',
            'februaryPredictionRecom',
            'februaryPredictionReport',
            'februaryPredictionConfirm',
            'originReportQtyAdjustment',
            'mc04DemandFoReportQtyAdjustment',
          ]
          let provinceSummaryData = this.trueTableData.filter(
            (item) => item.baComOrgName === '省小计',
          )
          provinceSummaryData.forEach((item) => {
            item.provinceTmpId = generateUniqueId()
            item.isProvinceSumRow = true
          })
          let calculateTableData = calculateSumRows(provinceSummaryData, sumFields, this.sumIndex)
          calculateTableData.forEach((item) => {
            item.baComOrgCode = ''
            item.baComOrgName = '合计'
          })
          // this.tableData = tableData
          this.tableData = [...calculateTableData, ...this.trueTableData]
        } else {
          this.tableData = []
        }
      } catch (error) {
        this.$Message.error('系统异常，请联系管理员')
      } finally {
        this.$refs.comTree.refreshStatus()
        this.tableLoading = false
      }

      function generateUniqueId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2, 5)
      }
    },
    reset() {
      this.tableData = []
      this.form = {
        zaOccurrenceYear: this.mostRecentSubjectYear,
        baComOrgCode: '11420101',
        mc04PlanSubjectName: '',
        mc04DemandFoStatus: '20',
        makeDate: '',
        acCgtCartonCode: [],
      }
    },
    getDemandFoDicByYear() {
      demandFoAsDic({
        zaOccurrenceYear: this.form.zaOccurrenceYear,
        baComOrgCode: this.newBaComOrgCode,
      }).then((res) => {
        let demandFoList = res.data.demandFoList
        //拿出所有mc04PlanSubjectName 作为一个新的数组
        let formattedData = demandFoList.map((item) => ({
          K: item.mc04PlanSubjectName,
          V: item.mc04PlanSubjectName,
        }))
        this.demandFoAsDic = formattedData
        this.fieldList = renderFieldEnums(this.fieldList, {
          mc04PlanSubjectName: this.demandFoAsDic,
        })
        //默认值？
        this.$set(this.form, 'mc04PlanSubjectName', formattedData[0].K)
        //取第一个demandFo 的 demandFoSubjectType 为当前页面定性
        this.demandFoSubjectType = this.formatSubjectType(demandFoList[0].mc04PlanSubjectType)
      })
    },
    formatSubjectType(value) {
      if (value === '1' || value === 1) {
        return 'Preparation' //编制
      }
      if (value === '2' || value === 2) {
        return 'Correction' //修正
      }
      if (value === '3' || value === 3) {
        return 'Adjustment' //调整
      }
    },
    getTotalSumIndex() {
      return new Promise((resolve) => {
        getTotalSumIndex({ mz12InfoSystemResourceCode: 'demandFo' }).then(({ data }) => {
          this.sumIndex = data
          resolve()
        })
      })
    },
    setTreeStatusParams() {
      this.statusUrlParams.zaOccurrenceYear = this.form.zaOccurrenceYear
      this.statusUrlParams.baComOrgCode = this.form.baComOrgCode
      this.statusUrlParams.mc04PlanSubjectName = this.form.mc04PlanSubjectName
    },
    async save() {
      if (this.tableData.length === 0) {
        return
      }
      this.tableLoading = true
      try {
        let tableData = this.$refs.table.getWZData()
        let res = await savePreparation(tableData)
        if (res.code === 1) {
          this.$Message.success('保存成功')
          //通知父组件
          this.$emit('save-success')
        }
      } catch (error) {
        this.$Message.error('系统异常，请联系管理员')
      } finally {
        this.tableLoading = false
      }
    },
    submit() {
      if (this.tableData.length === 0) {
        return
      }
      let title = '您确定对页面显示的所有地市进行确认操作吗？'
      if (!this.autoCheckNullReportQty()) {
        title = '存在上报量为空的情况，是否继续？'
      }
      this.$Modal.confirm({
        title: title,
        onOk: async () => {
          this.tableLoading = true
          try {
            let tableData = this.$refs.table.getWZData()
            let res = await submitPreparation(tableData)
            if (res.code === 1) {
              this.$Message.success('提交成功')
              //通知父组件
              this.$emit('submit-success')
            }
          } catch (error) {
            this.$Message.error('系统异常，请联系管理员')
          } finally {
            this.tableLoading = false
          }
        },
        onCancel: () => {},
      })
    },
    cancel() {
      if (this.tableData.length === 0) {
        return
      }
      this.$Modal.confirm({
        title: '您确定对页面显示的所有地市进行驳回操作吗？',
        onOk: async () => {
          try {
            this.tableLoading = true
            reviewCancel().then((res) => {
              if (res.code === 1) {
                this.$Message.success('驳回成功')
                //通知父组件
                this.$emit('submit-success')
                this.tableLoading = false
              }
            })
          } catch (error) {
            this.$Message.error('系统异常，请联系管理员')
          } finally {
            this.tableLoading = false
          }
        },
        onCancel: () => {},
      })
    },
    autoCheckNullReportQty() {
      for (let item of this.tableData) {
        if (
          item.januaryPredictionConfirm === 0 ||
          item.januaryPredictionConfirm === '' ||
          item.januaryPredictionConfirm === '0' ||
          item.januaryPredictionConfirm === null ||
          item.januaryPredictionConfirm === undefined
        ) {
          return false
        }
        if (
          item.februaryPredictionConfirm === 0 ||
          item.februaryPredictionConfirm === '' ||
          item.februaryPredictionConfirm === '0' ||
          item.februaryPredictionConfirm === null ||
          item.februaryPredictionConfirm === undefined
        ) {
          return false
        }
        if (
          item.mc04DemandFoConfirmQty === 0 ||
          item.mc04DemandFoConfirmQty === '' ||
          item.mc04DemandFoConfirmQty === '0' ||
          item.mc04DemandFoConfirmQty === null ||
          item.mc04DemandFoConfirmQty === undefined
        ) {
          return false
        }
      }
      return true
    },
    getItemIndex(item) {
      let index = null
      for (let i = 0; i < this.tableData.length; i++) {
        if (item.mc04DemandFoItemId === this.tableData[i].mc04DemandFoItemId) {
          index = i
          break
        }
      }
      return index
    },
    getProvinceItemIndex(item) {
      let index = null
      for (let i = 0; i < this.tableData.length; i++) {
        if (item.provinceTmpId === this.tableData[i].provinceTmpId) {
          index = i
          break
        }
      }
      return index
    },
    calculateAdd(num1, num2) {
      let add = ((parseFloat(num1) || 0) + (parseFloat(num2) || 0)).toString()
      return add
    },
    calculateProportion(numerator, denominator) {
      return parseFloat(denominator) !== 0 ? (numerator / denominator) * 100 : 0
    },
    autoCalculate() {
      //计算1月＋2月 = 元春上报量。并计算元春上报全年占比
      for (let newItem of this.tableData) {
        if (
          newItem.mc04DemandFoItemId === 0 ||
          newItem.mc04DemandFoItemId === null ||
          newItem.mc04DemandFoItemId === '' ||
          newItem.mc04DemandFoItemId === undefined
        ) {
          continue
        }
        let index = this.getItemIndex(newItem)
        //计算逻辑：1月与2月相加，得出元春上报量
        let ycMc04DemandFoReportQty = (
          (parseFloat(newItem.januaryPredictionReport) || 0) +
          (parseFloat(newItem.februaryPredictionReport) || 0)
        ).toString()
        //计算逻辑：1月与2月相加，得出元春确认量
        let ycMc04DemandFoConfirm = (
          (parseFloat(newItem.januaryPredictionConfirm) || 0) +
          (parseFloat(newItem.februaryPredictionConfirm) || 0)
        ).toString()
        //计算逻辑：每次计算元春上报量后，自动计算元春市场需求预测上报全年占比
        let ycDemandFoReportProportion =
          parseFloat(ycMc04DemandFoReportQty) !== 0
            ? (ycMc04DemandFoReportQty / newItem.mc04DemandFoReportQty) * 100
            : 0
        //计算逻辑：每次计算元春上报量后，自动计算元春市场需求预测确认全年占比
        let ycDemandFoConfirmProportion =
          parseFloat(ycMc04DemandFoConfirm) !== 0
            ? (ycMc04DemandFoConfirm / newItem.mc04DemandFoConfirmQty) * 100
            : 0
        //更新所有数据
        this.$nextTick(() => {
          this.tableData[index].ycMc04DemandFoReportQty = ycMc04DemandFoReportQty
          this.tableData[index].ycMc04DemandFoConfirm = ycMc04DemandFoConfirm
          this.tableData[index].ycDemandFoReportProportion = ycDemandFoReportProportion.toFixed(2)
          this.tableData[index].ycDemandFoConfirmProportion = ycDemandFoConfirmProportion.toFixed(2)
        })
      }
      //计算省小计
      let provinceItems = this.tableData.filter((item) => item.baComOrgName === '省小计')
      let cityItems = this.tableData.filter(
        (item) => item.baComOrgName !== '省小计' && item.baComOrgName !== '合计',
      )
      for (let provinceItem of provinceItems) {
        let provinceIndex = this.getProvinceItemIndex(provinceItem)
        let cartonCode = provinceItem.acCgtCartonCode
        let citySameCartonItems = cityItems.filter((item) => item.acCgtCartonCode === cartonCode)
        let cityConfirmQtySum = citySameCartonItems.reduce((sum, item) => {
          return sum + parseFloat(item.mc04DemandFoConfirmQty)
        }, 0)
        let cityJanuaryConfirmQtySum = cityItems.reduce((sum, item) => {
          return sum + parseFloat(item.januaryPredictionConfirm)
        }, 0)
        let cityFebruaryConfirmQtySum = cityItems.reduce((sum, item) => {
          return sum + parseFloat(item.februaryPredictionConfirm)
        }, 0)
        let provinceYcConfirm = this.calculateAdd(
          cityJanuaryConfirmQtySum,
          cityFebruaryConfirmQtySum,
        )
        let provinceYcProportion = this.calculateProportion(provinceYcConfirm, cityConfirmQtySum)
        this.$nextTick(() => {
          this.tableData[provinceIndex].mc04DemandFoConfirmQty = cityConfirmQtySum.toString()
          this.tableData[provinceIndex].ycMc04DemandFoConfirm = provinceYcConfirm.toString()
          this.tableData[provinceIndex].januaryPredictionConfirm =
            cityJanuaryConfirmQtySum.toString()
          this.tableData[provinceIndex].februaryPredictionConfirm =
            cityFebruaryConfirmQtySum.toString()
          this.tableData[provinceIndex].ycDemandFoConfirmProportion =
            provinceYcProportion.toFixed(2)
        })
      }
    },
  },
}
</script>
<style scoped lang="less"></style>
