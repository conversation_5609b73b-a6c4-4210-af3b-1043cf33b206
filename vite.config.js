import path from 'path'
import { defineConfig, splitVendorChunkPlugin } from 'vite'
import { createVuePlugin } from 'vite-plugin-vue2'
import checker from 'vite-plugin-checker'
import externalGlobals from 'rollup-plugin-external-globals'
import { proxy } from './proxy.js'
let globals = externalGlobals({
  vue: 'Vue',
})

export default defineConfig({
  base: '/dev-leale-manage-js/',
  plugins: [
    createVuePlugin(),
    splitVendorChunkPlugin(),
    checker({
      eslint: {
        lintCommand: 'eslint src --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts',
      },
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
  },
  build: {
    reportCompressedSize: false,
    chunkSizeWarningLimit: 600,
    rollupOptions: {
      plugins: [globals],
      external: ['vue'],
      output: {
        assetFileNames(assetInfo) {
          let ext = assetInfo.name?.split('.')?.[1]
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
            ext = 'media'
          } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(assetInfo.name)) {
            ext = 'img'
          } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
            ext = 'fonts'
          }
          return `assets/${ext}/[name]-[hash][extname]`
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        globals: {
          vue: 'Vue',
        },
      },
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        paths: [path.resolve(__dirname, 'src'), path.resolve(__dirname, 'node_modules')],
      },
    },
  },
  server: {
    port: 3000,
    open: '/ibp-sc/login',
    proxy: {
      '/ind-uc-ext': {
        target: 'http://215.dev.yingxiao.ind.lambo.top',
        changeOrigin: true,
      },
      '/user-manage-server': {
        target: 'http://215.dev.yingxiao.ind.lambo.top',
        changeOrigin: true,
      },
      '/ibp-sc': {
        target: 'http://215.dev.yingxiao.ind.lambo.top',
        changeOrigin: true,
      },
      '/ddd-istrategy-manage-server': {
        target: 'http://215.dev.yingxiao.ind.lambo.top',
        // target: 'http://localhost:8081',
        changeOrigin: true,
      },
      '/ind-uc-ext-server': {
        target: 'http://215.dev.yingxiao.ind.lambo.top',
        changeOrigin: true,
      },
      '/ddd-isale-manage-js-server': {
        target: 'http://215.dev.yingxiao.ind.lambo.top',
        changeOrigin: true,
      },
      '/ddd-icustservice-manage-server': {
        target: 'http://215.dev.yingxiao.ind.lambo.top/ddd-icustservice-manage-server',
        rewrite: (path) => path.replace(/^\/ddd-icustservice-manage-server/, ''),
        changeOrigin: true,
      },
      ...proxy,
    },
    fs: {
      strict: false,
    },
  },
})
